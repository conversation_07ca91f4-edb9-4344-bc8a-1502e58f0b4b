<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment - History</title>
    <script nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <script type="module" crossorigin src="./assets/history-B5rF4IZ7.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/legacy-AoIeRrIA.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-mywmfXFR.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-Creeq9bS.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/host-qgbK079d.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-BdF7sLLk.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/feedback-rating-RDPz08ah.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-DZyIKjh7.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/input-DCBQtNgo.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-D2MYbf3a.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-B1LKPxPr.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/event-modifiers-Bz4QCcZc.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-DwIptXof.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-D7YBjBq5.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/SuccessfulButton-xgZ5Aax4.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/CopyButton-DfcpqvjI.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-Cb9MCs64.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/next-edit-types-904A5ehg.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-B_3jxWk_.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-CVoP4f0A.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-YWaRJ64J.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-ClvxyORV.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-DORgvEFm.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/SuccessfulButton-Do68LBjG.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/CopyButton-DjlvEF_S.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/history-Cwf2a8EN.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
