import{B as z,a as _}from"./BadgeRoot-CMDpgWKP.js";import{x as k,X as E,I as G,P as m,m as f,H as I,Q as K,z as e,J as N,K as R,y as T,L as X,B as A,b as d,R as H,o as J,N as L,a1 as j,u as M,G as O,f as P}from"./legacy-AoIeRrIA.js";import{l as b,p as u,f as Q,g as Y}from"./SpinnerAugment-mywmfXFR.js";import{b as t}from"./host-qgbK079d.js";import{C as Z}from"./IconButtonAugment-DZyIKjh7.js";var q=T("<div><!></div>");const s1={Root:_,IconButton:function(l,a){const p=b(a,["children","$$slots","$$events","$$legacy"]),r=b(p,["color","highContrast","disabled"]);k(a,!1);const n=f(),i=f(),o=E(z.CONTEXT_KEY);let $=u(a,"color",24,()=>{return s=o.color,C="neutral",typeof s=="string"&&["accent","neutral","error","success","warning","info"].includes(s)?s:C;var s,C}),w=u(a,"highContrast",8,!1),g=u(a,"disabled",8,!1),h=o.size===0?.5:o.size;G(()=>(e(n),e(i),K(r)),()=>{m(n,r.class),m(i,I(r,["class"]))}),N(),R();var c=q(),B=J(c);const x=H(()=>`c-badge-icon-btn__base-btn ${e(n)}`);Z(B,Q({get size(){return h},variant:"ghost",get color(){return $()},get highContrast(){return w()},get disabled(){return g()},get class(){return e(x)}},()=>e(i),{$$events:{click(s){t.call(this,a,s)},keyup(s){t.call(this,a,s)},keydown(s){t.call(this,a,s)},mousedown(s){t.call(this,a,s)},mouseover(s){t.call(this,a,s)},focus(s){t.call(this,a,s)},mouseleave(s){t.call(this,a,s)},blur(s){t.call(this,a,s)},contextmenu(s){t.call(this,a,s)}},children:(s,C)=>{var v=X(),y=A(v);Y(y,a,"default",{},null),d(s,v)},$$slots:{default:!0}})),L(()=>j(c,1,M(()=>`c-badge-icon-btn c-badge-icon-btn--${o.variant} c-badge-icon-btn--size-${h}`),"svelte-1im94um")),d(l,c),O()}};var D=P('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.49933 0.25C3.49635 0.25 0.25 3.49593 0.25 7.50024C0.25 10.703 2.32715 13.4206 5.2081 14.3797C5.57084 14.446 5.70302 14.2222 5.70302 14.0299C5.70302 13.8576 5.69679 13.4019 5.69323 12.797C3.67661 13.235 3.25112 11.825 3.25112 11.825C2.92132 10.9874 2.44599 10.7644 2.44599 10.7644C1.78773 10.3149 2.49584 10.3238 2.49584 10.3238C3.22353 10.375 3.60629 11.0711 3.60629 11.0711C4.25298 12.1788 5.30335 11.8588 5.71638 11.6732C5.78225 11.205 5.96962 10.8854 6.17658 10.7043C4.56675 10.5209 2.87415 9.89918 2.87415 7.12104C2.87415 6.32925 3.15677 5.68257 3.62053 5.17563C3.54576 4.99226 3.29697 4.25521 3.69174 3.25691C3.69174 3.25691 4.30015 3.06196 5.68522 3.99973C6.26337 3.83906 6.8838 3.75895 7.50022 3.75583C8.1162 3.75895 8.73619 3.83906 9.31523 3.99973C10.6994 3.06196 11.3069 3.25691 11.3069 3.25691C11.7026 4.25521 11.4538 4.99226 11.3795 5.17563C11.8441 5.68257 12.1245 6.32925 12.1245 7.12104C12.1245 9.9063 10.4292 10.5192 8.81452 10.6985C9.07444 10.9224 9.30633 11.3648 9.30633 12.0413C9.30633 13.0102 9.29742 13.7922 9.29742 14.0299C9.29742 14.2239 9.42828 14.4496 9.79591 14.3788C12.6746 13.4179 14.75 10.7025 14.75 7.50024C14.75 3.49593 11.5036 0.25 7.49933 0.25Z" fill="currentColor"></path></svg>');function a1(l){var a=D();d(l,a)}export{s1 as B,a1 as G};
