import{x as Y,S as L,T as Z,V as I,z as g,P as l,W as M,y as ee,B as te,F as b,o as m,E as f,D as z,b as S,G as ne,M as D,N as oe,O as ie}from"./legacy-AoIeRrIA.js";import{p as v,s as k,T as B}from"./SpinnerAugment-mywmfXFR.js";import{c as se,S as ae,M as re}from"./index-BdF7sLLk.js";import"./IconButtonAugment-DZyIKjh7.js";var de=ee('<div class="l-markdown-editor svelte-1r1jr5o"><div class="c-markdown-editor__header svelte-1r1jr5o"><!></div> <!> <div class="c-markdown-editor__content svelte-1r1jr5o"><!> <div class="c-monaco-editor svelte-1r1jr5o"><!></div></div></div> <div class="c-markdown-editor__status svelte-1r1jr5o"><!> <!></div>',1);function ve(F,s){Y(s,!0);let a=v(s,"value",15,""),y=v(s,"selectedText",15,""),C=v(s,"selectionStart",15,0),x=v(s,"selectionEnd",15,0),G=v(s,"monacoOptions",19,()=>({})),n=v(s,"editorInstance",15),r=L(Z(a())),h=!1;const R={readOnly:!1,wordWrap:"on",lineNumbers:"on",minimap:{enabled:!1},scrollBeyondLastLine:!1,automaticLayout:!0,theme:"vs-dark",language:"markdown",fontSize:14,lineHeight:20,padding:{top:16,bottom:16},...G()};let E,c,d,w=L(!1),p=L(void 0);const N=async()=>{n()&&await(async()=>{try{if(n()){const e=n().getValue();l(r,e,!0),a(e)}s.saveFunction(),l(w,!0),clearTimeout(E),E=setTimeout(()=>{l(w,!1)},1500)}catch(e){l(p,e instanceof Error?e.message:String(e),!0)}})()},W=()=>{if(!n()||h)return;const e=n().getValue();e!==g(r)&&(l(r,e,!0),a(e))},A=()=>{if(!n())return;let e,t;e=n().onDidChangeModelContent(W),t=n().addAction({id:"save-markdown-content",label:"Save Markdown Content",keybindings:[2097],contextMenuGroupId:"navigation",contextMenuOrder:1.5,run:()=>{N()}}),(()=>{if(typeof window>"u")return;const o=()=>{N()};window.addEventListener("blur",o),d=()=>{window.removeEventListener("blur",o)}})(),c=()=>{e==null||e.dispose(),t==null||t.dispose(),d==null||d()}};I(()=>{if(a()!==g(r))if(n()){if(!h){h=!0;const e=n().getPosition(),t=n().getSelection(),o=n().getModel();if(o&&o.getValue()!==a()){if(o.setValue(a()),l(r,a(),!0),e&&o.getLineCount()>=e.lineNumber){const i=o.getLineLength(e.lineNumber);e.column<=i+1&&n().setPosition(e)}if(t&&o.getLineCount()>=t.endLineNumber){const i=o.getLineLength(t.endLineNumber);t.endColumn<=i+1&&n().setSelection(t)}}setTimeout(()=>{h=!1},10)}}else l(r,a(),!0)}),I(()=>{if(n()){A();const e=n(),t=e.onDidChangeCursorSelection(()=>{const i=e.getSelection();if(i){const u=e.getModel();u&&(C(u.getOffsetAt(i.getStartPosition())),x(u.getOffsetAt(i.getEndPosition())),C()!==x()?y(u.getValueInRange(i)):y(""))}}),o=c;c=()=>{o==null||o(),t==null||t.dispose()}}}),M(()=>{c==null||c(),d==null||d()});var O=de(),T=te(O),V=m(T),H=m(V);k(H,()=>s.header??b);var _=f(V,2);k(_,()=>s.children??b);var P=f(_,2),$=m(P);k($,()=>s.title??b);var q=f($,2),J=m(q);se(J,()=>re.Root,(e,t)=>{t(e,{children:(o,i)=>{ae(o,{get text(){return g(r)},lang:"markdown",get options(){return R},height:void 0,get editorInstance(){return n()},set editorInstance(u){n(u)}})},$$slots:{default:!0}})});var K=f(T,2),j=m(K),Q=e=>{B(e,{size:1,weight:"light",color:"error",children:(t,o)=>{var i=D();oe(()=>ie(i,g(p))),S(t,i)},$$slots:{default:!0}})};z(j,e=>{g(p)&&e(Q)});var U=f(j,2),X=e=>{B(e,{size:1,weight:"light",color:"success",children:(t,o)=>{var i=D("Saved");S(t,i)},$$slots:{default:!0}})};z(U,e=>{g(w)&&e(X)}),S(F,O),ne()}export{ve as M};
