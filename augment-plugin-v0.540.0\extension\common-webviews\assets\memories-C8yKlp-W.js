import{f as Be,a as De,o as h,b as n,x as $e,a6 as Ee,Z as te,I as ge,J as Pe,K as we,L as Re,B as le,D as se,G as ye,z as e,m as $,P as g,Q as fe,y as O,M as q,N as Le,O as ze,u as H,R as N,E as oe,W as We,a8 as _e,ag as He,au as Ue}from"./legacy-AoIeRrIA.js";import{l as Ve,p as de,b as V,a as Se,k as ke,e as je,T as Te}from"./SpinnerAugment-mywmfXFR.js";import"./design-system-init-Creeq9bS.js";import{h as j,W as xe,e as qe,i as Je}from"./host-qgbK079d.js";import{M as be}from"./message-broker-EhzME3pO.js";import{M as Ke}from"./MonacoMarkdownEditor-DpNSkR_h.js";import{h as Qe}from"./IconButtonAugment-DZyIKjh7.js";import{C as Ze}from"./check-D2K2_syQ.js";import{C as Ne,E as Oe,D as U,R as Xe,M as he,b as Ye,c as et,d as tt}from"./index-BLDiLrXG.js";import{S as st}from"./SuccessfulButton-xgZ5Aax4.js";import{O as ot}from"./OpenFileButton-CWm-PsCk.js";import{F as at}from"./Filespan-BqOh8yIt.js";import{T as Fe,a as ce}from"./CardAugment-DwIptXof.js";import{B as Ge}from"./ButtonAugment-D7YBjBq5.js";import{C as Ie}from"./chevron-down-CUZr9PGN.js";import"./async-messaging-Dmg2N9Pf.js";import"./index-BdF7sLLk.js";import"./chat-types-BfwvR7Kn.js";import"./focusTrapStack-CaEmYw0i.js";import"./isObjectLike-CnrzNkq5.js";import"./input-DCBQtNgo.js";import"./BaseTextInput-D2MYbf3a.js";import"./chat-model-context-DPgWxlAp.js";import"./index-B528snJk.js";import"./index-CJlvVQMt.js";import"./remote-agents-client-BMR_qMG5.js";import"./types-CGlLNakm.js";import"./event-modifiers-Bz4QCcZc.js";var nt=Be("<svg><!></svg>");function Me(J,I){const w=Ve(I,["children","$$slots","$$events","$$legacy"]);var E=nt();De(E,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...w}));var l=h(E);Qe(l,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M440.6 273.4c4.7-4.5 7.4-10.8 7.4-17.4s-2.7-12.8-7.4-17.4l-176-168c-9.6-9.2-24.8-8.8-33.9.8s-8.8 24.8.8 33.9L364.1 232H24c-13.3 0-24 10.7-24 24s10.7 24 24 24h340.1L231.4 406.6c-9.6 9.2-9.9 24.3-.8 33.9s24.3 9.9 33.9.8l176-168z"/>',!0),n(J,E)}var rt=O("<!> <!>",1),it=O('<div class="rules-dropdown-content svelte-18wohv"><!> <!></div>'),lt=O("<!> <!>",1);function ct(J,I){$e(I,!1);const[w,E]=Se(),l=()=>V(s,"$rulesFiles",w),A=()=>V(L,"$selectedRule",w),y=()=>V(e(F),"$focusedIndex",w),R=$(),K=$(),Q=$();let ae=de(I,"onRuleSelected",8),ne=de(I,"disabled",8,!1);const ue=new be(j),me=new Ne,c=new Oe(j,ue,me),s=te([]),x=te(!0),L=te(void 0);let F=$(void 0),B=$(()=>{});Ee(()=>{(async function(){try{x.set(!0);const m=await c.findRules("",100);s.set(m)}catch(m){console.error("Failed to load rules:",m),s.set([])}finally{x.set(!1)}})();const u=m=>{var S;((S=m.data)==null?void 0:S.type)===xe.getRulesListResponse&&(s.set(m.data.data||[]),x.set(!1))};return window.addEventListener("message",u),()=>{window.removeEventListener("message",u)}});let D=$(),b=$(!1);function ve(u){g(b,u)}ge(()=>l(),()=>{g(R,l().length>0)}),ge(()=>(fe(ne()),e(R)),()=>{g(K,ne()||!e(R))}),ge(()=>e(R),()=>{g(Q,e(R)?"Move highlighted text to a .augment/rules file":"Please add at least 1 file to .augment/rules and reload VSCode")}),Pe(),we();var d=Re(),Z=le(d),X=u=>{var m=Re(),S=le(m),pe=G=>{U.Root(G,{onOpenChange:ve,get requestClose(){return e(B)},set requestClose(z){g(B,z)},get focusedIndex(){return e(F)},set focusedIndex(z){je(g(F,z),"$focusedIndex",w)},children:(z,t)=>{var o=lt(),a=le(o);U.Trigger(a,{children:(r,Y)=>{const P=N(()=>(fe(ce),H(()=>[ce.Hover]))),W=N(()=>!e(b)&&void 0);ke(Fe(r,{get content(){return e(Q)},get triggerOn(){return e(P)},side:"top",get open(){return e(W)},children:(T,ie)=>{Ge(T,{color:"neutral",variant:"soft",size:1,get disabled(){return e(K)},children:(i,f)=>{var M=q();Le(()=>ze(M,(A(),H(()=>A()?A().path:"Rules")))),n(i,M)},$$slots:{default:!0,iconLeft:(i,f)=>{Me(i,{slot:"iconLeft"})},iconRight:(i,f)=>{Ie(i,{slot:"iconRight"})}}})},$$slots:{default:!0},$$legacy:!0}),T=>g(D,T),()=>e(D))},$$slots:{default:!0}});var v=oe(a,2);U.Content(v,{side:"bottom",align:"start",children:(r,Y)=>{var P=it(),W=h(P);qe(W,1,l,Je,(i,f,M)=>{const C=N(()=>y()===M);U.Item(i,{onSelect:()=>function(p){L.set(p),ae()(p),e(B)()}(e(f)),get highlight(){return e(C)},children:(p,_)=>{at(p,{get filepath(){return e(f),H(()=>e(f).path)}})},$$slots:{default:!0}})});var T=oe(W,2),ie=i=>{var f=rt(),M=le(f);U.Separator(M,{});var C=oe(M,2);U.Label(C,{children:(p,_)=>{Te(p,{size:1,color:"neutral",children:(ee,Ce)=>{var k=q();Le(Ae=>ze(k,Ae),[()=>(l(),y(),H(()=>`Move to ${l()[y()].path}`))],N),n(ee,k)},$$slots:{default:!0}})},$$slots:{default:!0}}),n(i,f)};se(T,i=>{y(),l(),H(()=>y()!==void 0&&l()[y()])&&i(ie)}),n(r,P)},$$slots:{default:!0}}),n(z,o)},$$slots:{default:!0},$$legacy:!0})},re=G=>{const z=N(()=>(fe(ce),H(()=>[ce.Hover])));ke(Fe(G,{get content(){return e(Q)},get triggerOn(){return e(z)},side:"top",children:(t,o)=>{Ge(t,{color:"neutral",variant:"soft",size:1,disabled:!0,children:(a,v)=>{var r=q("Rules");n(a,r)},$$slots:{default:!0,iconLeft:(a,v)=>{Me(a,{slot:"iconLeft"})},iconRight:(a,v)=>{Ie(a,{slot:"iconRight"})}}})},$$slots:{default:!0},$$legacy:!0}),t=>g(D,t),()=>e(D))};se(S,G=>{e(R)?G(pe):G(re,!1)}),n(u,m)};se(Z,u=>{V(x,"$loading",w)||u(X)}),n(J,d),ye(),E()}var dt=O('<div class="c-move-text-btn__left_icon svelte-ipzk4b"><!></div>'),ut=O('<div class="l-file-controls svelte-ipzk4b"><div class="l-file-controls-left svelte-ipzk4b"><div class="c-move-text-btn svelte-ipzk4b"><!></div> <div class="c-move-text-btn svelte-ipzk4b"><!></div></div> <div class="l-file-controls-right svelte-ipzk4b"><!></div></div>'),mt=O('<div class="l-memories-editor svelte-ipzk4b"><div class="c-memories-editor__content svelte-ipzk4b"><!></div></div>'),vt=O('<div class="c-memories-container svelte-1vchs21"><!></div>');Ue(function(J,I){$e(I,!1);const[w,E]=Se(),l=()=>V(R,"$editorContent",w),A=()=>V(K,"$editorPath",w),y=new be(j),R=te(null),K=te(null),Q={handleMessageFromExtension(c){const s=c.data;if(s&&s.type===xe.loadFile){if(s.data.content!==void 0){const x=s.data.content.replace(/^\n+/,"");R.set(x)}s.data.pathName&&K.set(s.data.pathName)}return!0}};Ee(()=>{y.registerConsumer(Q),j.postMessage({type:xe.memoriesLoaded})}),We(()=>{y.dispose()}),we();var ae=vt();_e("message",He,function(...c){var s;(s=y.onMessageFromExtension)==null||s.apply(this,c)});var ne=h(ae),ue=c=>{(function(s,x){$e(x,!1);let L=de(x,"text",12),F=de(x,"path",8);const B=new be(j),D=new Ne,b=new Oe(j,B,D),ve=new Xe(B);let d=$(""),Z=$(0),X=$(0),u=$("neutral");const m=async()=>{F()&&b.saveFile({repoRoot:"",pathName:F(),content:L()})};async function S(t){if(!e(d))return;let o,a,v;const r=e(d).slice(0,20);if(t==="userGuidelines"?(o="Move Content to User Guidelines",a=`Are you sure you want to move the selected content "${r}" to your user guidelines?`,v=he.userGuidelines):t==="augmentGuidelines"?(o="Move Content to Workspace Guidelines",a=`Are you sure you want to move the selected content "${r}" to workspace guidelines?`,v=he.augmentGuidelines):(o="Move Content to Rule",a=`Are you sure you want to move the selected content "${r}" to rule file "${t.rule.path}"?`,v=he.rules),!await b.openConfirmationModal({title:o,message:a,confirmButtonText:"Move",cancelButtonText:"Cancel"}))return;t==="userGuidelines"?b.updateUserGuidelines(e(d)):t==="augmentGuidelines"?b.updateWorkspaceGuidelines(e(d)):(await ve.updateRuleContent({type:t.rule.type,path:t.rule.path,content:t.rule.content+`

`+e(d),description:t.rule.description}),b.showNotification({message:`Moved content "${r}" to rule file "${t.rule.path}"`,type:"info",openFileMessage:{repoRoot:"",pathName:`${et}/${tt}/${t.rule.path}`}}));const Y=L().substring(0,e(Z))+L().substring(e(X));return L(Y),await m(),b.reportAgentSessionEvent({eventName:Ye.memoriesMove,conversationId:"",eventData:{memoriesMoveData:{target:v}}}),"success"}async function pe(t){await S({rule:t})}we();var re=mt(),G=h(re),z=h(G);Ke(z,{saveFunction:m,get selectedText(){return e(d)},set selectedText(o){g(d,o)},get selectionStart(){return e(Z)},set selectionStart(o){g(Z,o)},get selectionEnd(){return e(X)},set selectionEnd(o){g(X,o)},get value(){return L()},set value(o){L(o)},header:o=>{var a=ut(),v=h(a),r=h(v),Y=h(r);const P=N(()=>!e(d));st(Y,{tooltip:{neutral:"Move highlighted text to user guidelines",success:"Text moved to user guidelines"},stateVariant:{success:"solid",neutral:"soft"},defaultColor:"neutral",onClick:()=>S("userGuidelines"),get disabled(){return e(P)},stickyColor:!1,persistOnTooltipClose:!0,replaceIconOnSuccess:!0,size:1,get state(){return e(u)},set state(C){g(u,C)},iconLeft:C=>{var p=dt(),_=h(p),ee=k=>{Ze(k,{})},Ce=k=>{Me(k,{})};se(_,k=>{e(u)==="success"?k(ee):k(Ce,!1)}),n(C,p)},children:(C,p)=>{var _=q("User Guidelines");n(C,_)},$$slots:{iconLeft:!0,default:!0},$$legacy:!0});var W=oe(r,2),T=h(W);const ie=N(()=>!e(d));ct(T,{onRuleSelected:pe,get disabled(){return e(ie)}});var i=oe(v,2),f=h(i);ot(f,{size:1,get path(){return F()},variant:"soft",onOpenLocalFile:async()=>(b.openFile({repoRoot:"",pathName:F()}),"success"),$$slots:{text:(M,C)=>{Te(M,{slot:"text",size:1,children:(p,_)=>{var ee=q("Augment-Memories.md");n(p,ee)},$$slots:{default:!0}})}}}),n(o,a)},$$slots:{header:!0},$$legacy:!0}),n(s,re),ye()})(c,{get text(){return l()},get path(){return A()}})},me=c=>{var s=q("Loading memories...");n(c,s)};se(ne,c=>{l()!==null&&A()!==null?c(ue):c(me,!1)}),n(J,ae),ye(),E()},{target:document.getElementById("app")});
