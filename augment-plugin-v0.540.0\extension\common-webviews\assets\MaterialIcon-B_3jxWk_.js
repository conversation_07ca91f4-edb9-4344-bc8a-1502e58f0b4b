import{x as g,y as d,N as w,a1 as k,a2 as u,z as i,A as r,S as x,a3 as y,O as A,b as G,G as L,o as j,P as o}from"./legacy-AoIeRrIA.js";import{p as t,i as N}from"./SpinnerAugment-mywmfXFR.js";var z=d("<span> </span>");function I(n,a){g(a,!0);let m=t(a,"class",3,""),c=t(a,"iconName",3,""),l=t(a,"fill",3,!1),p=t(a,"grade",3,"normal"),f=t(a,"title",19,()=>{}),h=r(()=>l()?"1":"0"),v=r(()=>l()?"700":"400"),e=x(void 0);N(()=>{switch(p()){case"low":o(e,"-25");break;case"normal":o(e,"0");break;case"high":o(e,"200")}});var s=z(),b=j(s);w(()=>{k(s,1,`material-symbols-outlined ${m()}`,"svelte-htlsjs"),u(s,`font-variation-settings: 'FILL' ${i(h)??""}, 'wght' ${i(v)??""}, 'GRAD' ${i(e)??""};`),y(s,"title",f()),A(b,c())}),G(n,s),L()}export{I as M};
