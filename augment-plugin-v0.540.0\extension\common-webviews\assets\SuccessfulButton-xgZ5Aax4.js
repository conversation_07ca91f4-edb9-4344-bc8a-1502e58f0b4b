import{x as et,m as v,I as z,J as lt,K as st,y as Q,L as k,B as g,D as at,b as m,P as a,z as l,R as ot,E,o as rt,G as it,u as nt,Q as w,H as ct}from"./legacy-AoIeRrIA.js";import{l as G,p as n,f as J,g as d}from"./SpinnerAugment-mywmfXFR.js";import{b as s}from"./host-qgbK079d.js";import{I as ut}from"./IconButtonAugment-DZyIKjh7.js";import{T as vt,a as K}from"./CardAugment-DwIptXof.js";import{B as ft}from"./ButtonAugment-D7YBjBq5.js";var ht=Q("<!> <!> <!>",1),mt=Q('<div class="c-successful-button svelte-1dvyzw2"><!></div>');function kt(S,e){var P;const j=G(e,["children","$$slots","$$events","$$legacy"]),T=G(j,["defaultColor","tooltip","stateVariant","onClick","tooltipDuration","icon","stickyColor","persistOnTooltipClose","tooltipNested"]);et(e,!1);const $=v(),y=v(),b=v();let C,x=n(e,"defaultColor",8),L=n(e,"tooltip",24,()=>{}),B=n(e,"stateVariant",24,()=>{}),A=n(e,"onClick",8),F=n(e,"tooltipDuration",8,1500),M=n(e,"icon",8,!1),H=n(e,"stickyColor",8,!0),U=n(e,"persistOnTooltipClose",8,!1),W=n(e,"tooltipNested",24,()=>{}),i=v("neutral"),p=v(x()),R=v(void 0),D=v((P=L())==null?void 0:P.neutral);async function I(o){var f;o.stopPropagation();try{a(i,await A()(o)??"neutral")}catch(u){console.error(u),a(i,"failure")}a(D,(f=L())==null?void 0:f[l(i)]),clearTimeout(C),C=setTimeout(()=>{var u;(u=l(R))==null||u(),H()||a(i,"neutral")},F())}z(()=>(l($),l(y),w(T)),()=>{a($,T.variant),a(y,ct(T,["variant"]))}),z(()=>(w(B()),l(i),l($)),()=>{var o;a(b,((o=B())==null?void 0:o[l(i)])??l($))}),z(()=>(l(i),w(x())),()=>{l(i)==="success"?a(p,"success"):l(i)==="failure"?a(p,"error"):a(p,x())}),lt(),st();var N=mt(),X=rt(N);const Y=ot(()=>(w(K),nt(()=>[K.Hover])));vt(X,{onOpenChange:function(o){var f;U()||o||(clearTimeout(C),C=void 0,a(D,(f=L())==null?void 0:f.neutral),H()||a(i,"neutral"))},get content(){return l(D)},get triggerOn(){return l(Y)},get nested(){return W()},get requestClose(){return l(R)},set requestClose(o){a(R,o)},children:(o,f)=>{var u=k(),Z=g(u),_=h=>{ut(h,J(()=>l(y),{get color(){return l(p)},get variant(){return l(b)},$$events:{click:I,keyup(t){s.call(this,e,t)},keydown(t){s.call(this,e,t)},mousedown(t){s.call(this,e,t)},mouseover(t){s.call(this,e,t)},focus(t){s.call(this,e,t)},mouseleave(t){s.call(this,e,t)},blur(t){s.call(this,e,t)},contextmenu(t){s.call(this,e,t)}},children:(t,q)=>{var r=ht(),c=g(r);d(c,e,"iconLeft",{},null);var V=E(c,2);d(V,e,"default",{},null);var tt=E(V,2);d(tt,e,"iconRight",{},null),m(t,r)},$$slots:{default:!0}}))},O=h=>{ft(h,J(()=>l(y),{get color(){return l(p)},get variant(){return l(b)},$$events:{click:I,keyup(t){s.call(this,e,t)},keydown(t){s.call(this,e,t)},mousedown(t){s.call(this,e,t)},mouseover(t){s.call(this,e,t)},focus(t){s.call(this,e,t)},mouseleave(t){s.call(this,e,t)},blur(t){s.call(this,e,t)},contextmenu(t){s.call(this,e,t)}},children:(t,q)=>{var r=k(),c=g(r);d(c,e,"default",{},null),m(t,r)},$$slots:{default:!0,iconLeft:(t,q)=>{var r=k(),c=g(r);d(c,e,"iconLeft",{},null),m(t,r)},iconRight:(t,q)=>{var r=k(),c=g(r);d(c,e,"iconRight",{},null),m(t,r)}}}))};at(Z,h=>{M()?h(_):h(O,!1)}),m(o,u)},$$slots:{default:!0},$$legacy:!0}),m(S,N),it()}export{kt as S};
