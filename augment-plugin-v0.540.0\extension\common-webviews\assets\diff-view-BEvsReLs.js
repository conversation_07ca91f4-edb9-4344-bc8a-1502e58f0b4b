var Hi=Object.defineProperty;var Zi=(p,t,i)=>t in p?Hi(p,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):p[t]=i;var d=(p,t,i)=>Zi(p,typeof t!="symbol"?t+"":t,i);import{Z as Ue,aS as ui,aO as ge,aT as Bt,x as st,y as H,N as Qe,O as Gt,z as l,o as oe,b as x,a1 as St,G as at,a4 as Gi,B as ee,E as K,D as pe,S as Ie,a2 as vi,a8 as Be,P as F,A as Le,f as Ji,a as Ki,a6 as bi,m as Fe,W as yi,I as Tt,J as Qi,K as Yi,L as Ot,M as Xi,u as Wt,Q as li,R as hi,ag as Ut,av as en,a0 as tn,au as nn}from"./legacy-AoIeRrIA.js";import{c as Et,n as rn,p as xe,b as re,a as xt,i as Lt,S as fi,l as on,e as Ci,k as It}from"./SpinnerAugment-mywmfXFR.js";import"./design-system-init-Creeq9bS.js";import{h as ot,W as ae,D as qt,e as ki,i as wi}from"./host-qgbK079d.js";import{a as sn,c as an,M as dn,g as cn,C as un}from"./index-BdF7sLLk.js";import{A as ln}from"./async-messaging-Dmg2N9Pf.js";import{d as gi,T as hn,a as fn}from"./CardAugment-DwIptXof.js";import{F as gn,C as Mi,S as Ei,a as pn}from"./folder-opened-D5l3axuC.js";import{r as _n,a as pi}from"./monaco-render-utils-DfwV7QLY.js";import{M as Si}from"./message-broker-EhzME3pO.js";import{a as Oi,I as mn,h as vn}from"./IconButtonAugment-DZyIKjh7.js";import{B as De}from"./ButtonAugment-D7YBjBq5.js";import{R as _i,K as bn,A as yn,P as Cn}from"./Keybindings-D5k530TZ.js";import{s as kn}from"./chat-model-context-DPgWxlAp.js";import"./event-modifiers-Bz4QCcZc.js";import"./index-BLDiLrXG.js";import"./chat-types-BfwvR7Kn.js";import"./focusTrapStack-CaEmYw0i.js";import"./isObjectLike-CnrzNkq5.js";import"./input-DCBQtNgo.js";import"./BaseTextInput-D2MYbf3a.js";import"./file-type-utils-D6OEcQY2.js";import"./types-CGlLNakm.js";import"./CalloutAugment-Db5scVK5.js";import"./exclamation-triangle-DKq0zBWN.js";import"./Filespan-BqOh8yIt.js";import"./MaterialIcon-B_3jxWk_.js";import"./pen-to-square-DPoLDlf4.js";import"./augment-logo-BqyYuvys.js";var Ht={exports:{}};(function(p,t){var i="__lodash_hash_undefined__",a=1,r=2,s=9007199254740991,c="[object Arguments]",u="[object Array]",b="[object AsyncFunction]",m="[object Boolean]",f="[object Date]",C="[object Error]",L="[object Function]",w="[object GeneratorFunction]",N="[object Map]",W="[object Number]",g="[object Null]",k="[object Object]",z="[object Promise]",Q="[object Proxy]",V="[object RegExp]",U="[object Set]",X="[object String]",ve="[object Symbol]",le="[object Undefined]",S="[object WeakMap]",P="[object ArrayBuffer]",M="[object DataView]",$=/^\[object .+?Constructor\]$/,E=/^(?:0|[1-9]\d*)$/,_={};_["[object Float32Array]"]=_["[object Float64Array]"]=_["[object Int8Array]"]=_["[object Int16Array]"]=_["[object Int32Array]"]=_["[object Uint8Array]"]=_["[object Uint8ClampedArray]"]=_["[object Uint16Array]"]=_["[object Uint32Array]"]=!0,_[c]=_[u]=_[P]=_[m]=_[M]=_[f]=_[C]=_[L]=_[N]=_[W]=_[k]=_[V]=_[U]=_[X]=_[S]=!1;var T=typeof Et=="object"&&Et&&Et.Object===Object&&Et,de=typeof self=="object"&&self&&self.Object===Object&&self,j=T||de||Function("return this")(),se=t&&!t.nodeType&&t,Y=se&&p&&!p.nodeType&&p,te=Y&&Y.exports===se,Z=te&&T.process,v=function(){try{return Z&&Z.binding&&Z.binding("util")}catch{}}(),_e=v&&v.isTypedArray;function ce(e,n){for(var o=-1,h=e==null?0:e.length;++o<h;)if(n(e[o],o,e))return!0;return!1}function be(e){var n=-1,o=Array(e.size);return e.forEach(function(h,A){o[++n]=[A,h]}),o}function he(e){var n=-1,o=Array(e.size);return e.forEach(function(h){o[++n]=h}),o}var ye,dt,ct,ut=Array.prototype,Nt=Function.prototype,He=Object.prototype,Ye=j["__core-js_shared__"],D=Nt.toString,q=He.hasOwnProperty,ie=(ye=/[^.]+$/.exec(Ye&&Ye.keys&&Ye.keys.IE_PROTO||""))?"Symbol(src)_1."+ye:"",Ce=He.toString,Ze=RegExp("^"+D.call(q).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ke=te?j.Buffer:void 0,Ne=j.Symbol,lt=j.Uint8Array,Jt=He.propertyIsEnumerable,Ii=ut.splice,Re=Ne?Ne.toStringTag:void 0,Kt=Object.getOwnPropertySymbols,xi=ke?ke.isBuffer:void 0,Ni=(dt=Object.keys,ct=Object,function(e){return dt(ct(e))}),jt=Ge(j,"DataView"),Xe=Ge(j,"Map"),At=Ge(j,"Promise"),$t=Ge(j,"Set"),Vt=Ge(j,"WeakMap"),et=Ge(Object,"create"),ji=Te(jt),Ai=Te(Xe),$i=Te(At),Vi=Te($t),Fi=Te(Vt),Qt=Ne?Ne.prototype:void 0,Ft=Qt?Qt.valueOf:void 0;function ze(e){var n=-1,o=e==null?0:e.length;for(this.clear();++n<o;){var h=e[n];this.set(h[0],h[1])}}function we(e){var n=-1,o=e==null?0:e.length;for(this.clear();++n<o;){var h=e[n];this.set(h[0],h[1])}}function Pe(e){var n=-1,o=e==null?0:e.length;for(this.clear();++n<o;){var h=e[n];this.set(h[0],h[1])}}function ht(e){var n=-1,o=e==null?0:e.length;for(this.__data__=new Pe;++n<o;)this.add(e[n])}function je(e){var n=this.__data__=new we(e);this.size=n.size}function Di(e,n){var o=pt(e),h=!o&&Wi(e),A=!o&&!h&&Dt(e),y=!o&&!h&&!A&&si(e),R=o||h||A||y,B=R?function(J,me){for(var Me=-1,ne=Array(J);++Me<J;)ne[Me]=me(Me);return ne}(e.length,String):[],fe=B.length;for(var G in e)!q.call(e,G)||R&&(G=="length"||A&&(G=="offset"||G=="parent")||y&&(G=="buffer"||G=="byteLength"||G=="byteOffset")||Ti(G,fe))||B.push(G);return B}function ft(e,n){for(var o=e.length;o--;)if(ii(e[o][0],n))return o;return-1}function tt(e){return e==null?e===void 0?le:g:Re&&Re in Object(e)?function(n){var o=q.call(n,Re),h=n[Re];try{n[Re]=void 0;var A=!0}catch{}var y=Ce.call(n);return A&&(o?n[Re]=h:delete n[Re]),y}(e):function(n){return Ce.call(n)}(e)}function Yt(e){return it(e)&&tt(e)==c}function Xt(e,n,o,h,A){return e===n||(e==null||n==null||!it(e)&&!it(n)?e!=e&&n!=n:function(y,R,B,fe,G,J){var me=pt(y),Me=pt(R),ne=me?u:Ae(y),Ee=Me?u:Ae(R),Je=(ne=ne==c?k:ne)==k,_t=(Ee=Ee==c?k:Ee)==k,Ke=ne==Ee;if(Ke&&Dt(y)){if(!Dt(R))return!1;me=!0,Je=!1}if(Ke&&!Je)return J||(J=new je),me||si(y)?ei(y,R,B,fe,G,J):function(I,O,mt,$e,Rt,ue,Se){switch(mt){case M:if(I.byteLength!=O.byteLength||I.byteOffset!=O.byteOffset)return!1;I=I.buffer,O=O.buffer;case P:return!(I.byteLength!=O.byteLength||!ue(new lt(I),new lt(O)));case m:case f:case W:return ii(+I,+O);case C:return I.name==O.name&&I.message==O.message;case V:case X:return I==O+"";case N:var Ve=be;case U:var rt=$e&a;if(Ve||(Ve=he),I.size!=O.size&&!rt)return!1;var vt=Se.get(I);if(vt)return vt==O;$e|=r,Se.set(I,O);var zt=ei(Ve(I),Ve(O),$e,Rt,ue,Se);return Se.delete(I),zt;case ve:if(Ft)return Ft.call(I)==Ft.call(O)}return!1}(y,R,ne,B,fe,G,J);if(!(B&a)){var nt=Je&&q.call(y,"__wrapped__"),ai=_t&&q.call(R,"__wrapped__");if(nt||ai){var qi=nt?y.value():y,Bi=ai?R.value():R;return J||(J=new je),G(qi,Bi,B,fe,J)}}return Ke?(J||(J=new je),function(I,O,mt,$e,Rt,ue){var Se=mt&a,Ve=ti(I),rt=Ve.length,vt=ti(O),zt=vt.length;if(rt!=zt&&!Se)return!1;for(var bt=rt;bt--;){var We=Ve[bt];if(!(Se?We in O:q.call(O,We)))return!1}var di=ue.get(I);if(di&&ue.get(O))return di==O;var yt=!0;ue.set(I,O),ue.set(O,I);for(var Pt=Se;++bt<rt;){var Ct=I[We=Ve[bt]],kt=O[We];if($e)var ci=Se?$e(kt,Ct,We,O,I,ue):$e(Ct,kt,We,I,O,ue);if(!(ci===void 0?Ct===kt||Rt(Ct,kt,mt,$e,ue):ci)){yt=!1;break}Pt||(Pt=We=="constructor")}if(yt&&!Pt){var wt=I.constructor,Mt=O.constructor;wt==Mt||!("constructor"in I)||!("constructor"in O)||typeof wt=="function"&&wt instanceof wt&&typeof Mt=="function"&&Mt instanceof Mt||(yt=!1)}return ue.delete(I),ue.delete(O),yt}(y,R,B,fe,G,J)):!1}(e,n,o,h,Xt,A))}function Ri(e){return!(!oi(e)||function(n){return!!ie&&ie in n}(e))&&(ni(e)?Ze:$).test(Te(e))}function zi(e){if(o=(n=e)&&n.constructor,h=typeof o=="function"&&o.prototype||He,n!==h)return Ni(e);var n,o,h,A=[];for(var y in Object(e))q.call(e,y)&&y!="constructor"&&A.push(y);return A}function ei(e,n,o,h,A,y){var R=o&a,B=e.length,fe=n.length;if(B!=fe&&!(R&&fe>B))return!1;var G=y.get(e);if(G&&y.get(n))return G==n;var J=-1,me=!0,Me=o&r?new ht:void 0;for(y.set(e,n),y.set(n,e);++J<B;){var ne=e[J],Ee=n[J];if(h)var Je=R?h(Ee,ne,J,n,e,y):h(ne,Ee,J,e,n,y);if(Je!==void 0){if(Je)continue;me=!1;break}if(Me){if(!ce(n,function(_t,Ke){if(nt=Ke,!Me.has(nt)&&(ne===_t||A(ne,_t,o,h,y)))return Me.push(Ke);var nt})){me=!1;break}}else if(ne!==Ee&&!A(ne,Ee,o,h,y)){me=!1;break}}return y.delete(e),y.delete(n),me}function ti(e){return function(n,o,h){var A=o(n);return pt(n)?A:function(y,R){for(var B=-1,fe=R.length,G=y.length;++B<fe;)y[G+B]=R[B];return y}(A,h(n))}(e,Ui,Pi)}function gt(e,n){var o,h,A=e.__data__;return((h=typeof(o=n))=="string"||h=="number"||h=="symbol"||h=="boolean"?o!=="__proto__":o===null)?A[typeof n=="string"?"string":"hash"]:A.map}function Ge(e,n){var o=function(h,A){return h==null?void 0:h[A]}(e,n);return Ri(o)?o:void 0}ze.prototype.clear=function(){this.__data__=et?et(null):{},this.size=0},ze.prototype.delete=function(e){var n=this.has(e)&&delete this.__data__[e];return this.size-=n?1:0,n},ze.prototype.get=function(e){var n=this.__data__;if(et){var o=n[e];return o===i?void 0:o}return q.call(n,e)?n[e]:void 0},ze.prototype.has=function(e){var n=this.__data__;return et?n[e]!==void 0:q.call(n,e)},ze.prototype.set=function(e,n){var o=this.__data__;return this.size+=this.has(e)?0:1,o[e]=et&&n===void 0?i:n,this},we.prototype.clear=function(){this.__data__=[],this.size=0},we.prototype.delete=function(e){var n=this.__data__,o=ft(n,e);return!(o<0)&&(o==n.length-1?n.pop():Ii.call(n,o,1),--this.size,!0)},we.prototype.get=function(e){var n=this.__data__,o=ft(n,e);return o<0?void 0:n[o][1]},we.prototype.has=function(e){return ft(this.__data__,e)>-1},we.prototype.set=function(e,n){var o=this.__data__,h=ft(o,e);return h<0?(++this.size,o.push([e,n])):o[h][1]=n,this},Pe.prototype.clear=function(){this.size=0,this.__data__={hash:new ze,map:new(Xe||we),string:new ze}},Pe.prototype.delete=function(e){var n=gt(this,e).delete(e);return this.size-=n?1:0,n},Pe.prototype.get=function(e){return gt(this,e).get(e)},Pe.prototype.has=function(e){return gt(this,e).has(e)},Pe.prototype.set=function(e,n){var o=gt(this,e),h=o.size;return o.set(e,n),this.size+=o.size==h?0:1,this},ht.prototype.add=ht.prototype.push=function(e){return this.__data__.set(e,i),this},ht.prototype.has=function(e){return this.__data__.has(e)},je.prototype.clear=function(){this.__data__=new we,this.size=0},je.prototype.delete=function(e){var n=this.__data__,o=n.delete(e);return this.size=n.size,o},je.prototype.get=function(e){return this.__data__.get(e)},je.prototype.has=function(e){return this.__data__.has(e)},je.prototype.set=function(e,n){var o=this.__data__;if(o instanceof we){var h=o.__data__;if(!Xe||h.length<199)return h.push([e,n]),this.size=++o.size,this;o=this.__data__=new Pe(h)}return o.set(e,n),this.size=o.size,this};var Pi=Kt?function(e){return e==null?[]:(e=Object(e),function(n,o){for(var h=-1,A=n==null?0:n.length,y=0,R=[];++h<A;){var B=n[h];o(B,h,n)&&(R[y++]=B)}return R}(Kt(e),function(n){return Jt.call(e,n)}))}:function(){return[]},Ae=tt;function Ti(e,n){return!!(n=n??s)&&(typeof e=="number"||E.test(e))&&e>-1&&e%1==0&&e<n}function Te(e){if(e!=null){try{return D.call(e)}catch{}try{return e+""}catch{}}return""}function ii(e,n){return e===n||e!=e&&n!=n}(jt&&Ae(new jt(new ArrayBuffer(1)))!=M||Xe&&Ae(new Xe)!=N||At&&Ae(At.resolve())!=z||$t&&Ae(new $t)!=U||Vt&&Ae(new Vt)!=S)&&(Ae=function(e){var n=tt(e),o=n==k?e.constructor:void 0,h=o?Te(o):"";if(h)switch(h){case ji:return M;case Ai:return N;case $i:return z;case Vi:return U;case Fi:return S}return n});var Wi=Yt(function(){return arguments}())?Yt:function(e){return it(e)&&q.call(e,"callee")&&!Jt.call(e,"callee")},pt=Array.isArray,Dt=xi||function(){return!1};function ni(e){if(!oi(e))return!1;var n=tt(e);return n==L||n==w||n==b||n==Q}function ri(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=s}function oi(e){var n=typeof e;return e!=null&&(n=="object"||n=="function")}function it(e){return e!=null&&typeof e=="object"}var si=_e?function(e){return function(n){return e(n)}}(_e):function(e){return it(e)&&ri(e.length)&&!!_[tt(e)]};function Ui(e){return(n=e)!=null&&ri(n.length)&&!ni(n)?Di(e):zi(e);var n}p.exports=function(e,n){return Xt(e,n)}})(Ht,Ht.exports);const wn=rn(Ht.exports);function Li(p){return function(t){return"unitOfCodeWork"in t&&!function(i){return i.children.length>0&&"childIds"in i}(t)}(p)?[p]:p.children.flatMap(Li)}var Oe=(p=>(p.edit="edit",p.instruction="instruction",p))(Oe||{}),Zt=(p=>(p[p.instructionDrawer=0]="instructionDrawer",p[p.chunkActionPanel=1]="chunkActionPanel",p))(Zt||{});class Mn{constructor(t,i,a){d(this,"_originalModel");d(this,"_modifiedModel");d(this,"_fullEdits",[]);d(this,"_currEdit");d(this,"_currOriginalEdit");d(this,"swapBaseModel",t=>{this._originalModel.setValue(t),this._modifiedModel.setValue(t),this._fullEdits.forEach(i=>{this._modifiedModel.applyEdits([i])}),this._currEdit&&this._modifiedModel.applyEdits([this._currEdit]),this._currOriginalEdit&&this._originalModel.applyEdits([this._currOriginalEdit])});d(this,"finish",()=>this._completeCurrEdit());d(this,"onReceiveChunk",t=>t.data.newChunkStart?this._startNewEdit(t.data.newChunkStart):t.data.chunkContinue&&this._currEdit?this._continueEdit(t.data.chunkContinue):t.data.chunkEnd&&this._currEdit?this._completeCurrEdit(t.data.chunkEnd):void 0);d(this,"_completeCurrEdit",t=>{const i={resetOriginal:[],original:[],modified:[]};if(!t)return i;if(this._currEdit){this._currEdit.range=new this._monaco.Range(t.stagedStartLine,0,t.stagedEndLine,0);const a=this._nextModifiedInsertPosition(),r=t.stagedEndLine-t.stagedStartLine,s={range:new this._monaco.Range(a.lineNumber,0,a.lineNumber+r,0),text:""};i.modified.push(s),this._modifiedModel.applyEdits([s]),this._fullEdits.push(this._currEdit),this._currEdit=void 0}return i});d(this,"_startNewEdit",t=>{const i={resetOriginal:[],original:[],modified:[]};return this._currOriginalEdit=void 0,this._currEdit={range:new this._monaco.Range(t.stagedStartLine,0,t.stagedStartLine,0),text:""},i.modified.push(this._currEdit),this._modifiedModel.applyEdits([this._currEdit]),i});d(this,"_continueEdit",t=>{if(!this._currEdit)throw new Error("No current edit");const i=this._nextModifiedInsertPosition(),a={...this._currEdit,text:t.newText,range:new this._monaco.Range(i.lineNumber,i.column,i.lineNumber,i.column)};return this._modifiedModel.applyEdits([a]),this._currEdit.text+=t.newText,{resetOriginal:[],original:[],modified:t.newText.length>0?[a]:[]}});d(this,"_nextModifiedInsertPosition",()=>{var i;if(!this._currEdit)throw new Error("No current edit");const t=this._modifiedModel.getOffsetAt({lineNumber:this._currEdit.range.startLineNumber,column:this._currEdit.range.startColumn})+(((i=this._currEdit.text)==null?void 0:i.length)??0);return this._modifiedModel.getPositionAt(t)});this.id=t,this.originalCode=i,this._monaco=a,this._originalModel=this._monaco.editor.createModel(i),this._modifiedModel=this._monaco.editor.createModel(i)}get hasReceivedFirstChunk(){return this._currEdit!==void 0||this._fullEdits.length>0}get originalValue(){return this._originalModel.getValue()}get modifiedValue(){return this._modifiedModel.getValue()}get currEdit(){return this._currEdit}}class En{constructor(t,i,a){d(this,"_asyncMsgSender");d(this,"_editor");d(this,"_chatModel");d(this,"_focusModel",new gn);d(this,"_hasScrolledOnInit",!1);d(this,"_markHasScrolledOnInit",gi(()=>{this._hasScrolledOnInit=!0},200));d(this,"_resetScrollOnInit",()=>{this._markHasScrolledOnInit.cancel(),this._hasScrolledOnInit=!1});d(this,"_subscribers",new Set);d(this,"_disposables",[]);d(this,"_rootChunk");d(this,"_keybindings",Ue({}));d(this,"_requestId",Ue(void 0));d(this,"requestId",this._requestId);d(this,"_disableResolution",Ue(!1));d(this,"disableResolution",ui(this._disableResolution));d(this,"_disableApply",Ue(!1));d(this,"disableApply",ui(this._disableApply));d(this,"_currStream");d(this,"_isLoadingDiffChunks",Ue(!1));d(this,"_selectionLines",Ue(void 0));d(this,"_mode",Ue(Oe.edit));d(this,"initializeEditor",t=>{var i,a,r,s,c,u,b,m,f,C,L,w;this._editor=this._monaco.editor.createDiffEditor(this._editorContainer,{automaticLayout:!0,theme:t,readOnly:!0,contextmenu:!1,renderSideBySide:!1,renderIndicators:!0,renderMarginRevertIcon:!1,originalEditable:!1,diffCodeLens:!1,renderOverviewRuler:!1,ignoreTrimWhitespace:!1,scrollBeyondLastLine:!0,maxComputationTime:0,minimap:{enabled:!1},padding:{top:16}}),this._editor.getOriginalEditor().updateOptions({lineNumbers:"off"}),this._chatModel=new Mi(new Si(ot),ot,new Ei),(a=(i=this._monaco.editor).registerCommand)==null||a.call(i,"acceptFocusedChunk",this.acceptFocusedChunk),(s=(r=this._monaco.editor).registerCommand)==null||s.call(r,"rejectFocusedChunk",this.rejectFocusedChunk),(u=(c=this._monaco.editor).registerCommand)==null||u.call(c,"acceptAllChunks",this.acceptAllChunks),(m=(b=this._monaco.editor).registerCommand)==null||m.call(b,"rejectAllChunks",this.rejectAllChunks),(C=(f=this._monaco.editor).registerCommand)==null||C.call(f,"focusNextChunk",this.focusNextChunk),(w=(L=this._monaco.editor).registerCommand)==null||w.call(L,"focusPrevChunk",this.focusPrevChunk),this._disposables.push(this._editor,this._editor.onDidUpdateDiff(this.onDidUpdateDiff),this._editor.getModifiedEditor().onMouseMove(this.onMouseMoveModified),{dispose:this._focusModel.subscribe(N=>this.notifySubscribers())}),this.initialize()});d(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));d(this,"dispose",()=>{this._editor.dispose(),this._subscribers.clear(),this._disposables.forEach(t=>t.dispose())});d(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});d(this,"onDidUpdateDiff",()=>{var t;if(this.updateCodeChunk(),!this._hasScrolledOnInit&&((t=this.leaves)==null?void 0:t.length)){this._markHasScrolledOnInit();const i=this.leaves[0];this.revealChunk(i)}this.notifyDiffViewUpdated(),this.notifySubscribers()});d(this,"onMouseMoveModified",t=>{var r,s,c,u,b,m;if(((r=t.target.position)==null?void 0:r.lineNumber)===void 0||this.leaves===void 0)return;const i=this.editorOffset,a=(s=t.target.position)==null?void 0:s.lineNumber;for(let f=0;f<this.leaves.length;f++){const C=this.leaves[f],L=(c=C.unitOfCodeWork.lineChanges)==null?void 0:c.lineChanges[0].modifiedStart,w=(u=C.unitOfCodeWork.lineChanges)==null?void 0:u.lineChanges[0].modifiedEnd,N=(b=C.unitOfCodeWork.lineChanges)==null?void 0:b.lineChanges[0].originalStart,W=(m=C.unitOfCodeWork.lineChanges)==null?void 0:m.lineChanges[0].originalEnd;if(L!==void 0&&w!==void 0&&N!==void 0&&W!==void 0){if(L!==w||a!==L){if(L<=a&&a<w){this.setCurrFocusedChunkIdx(f,!1);break}}else if(t.target.type===this._monaco.editor.MouseTargetType.CONTENT_VIEW_ZONE){const g=this._editor.getOriginalEditor(),k=g.getOption(this._monaco.editor.EditorOption.lineHeight),z=g.getScrolledVisiblePosition({lineNumber:N,column:0}),Q=g.getScrolledVisiblePosition({lineNumber:W+1,column:0});if(z===null||Q===null)continue;const V=z.top-k/2+i,U=Q.top-k/2+i;if(t.event.posy>=V&&t.event.posy<=U){this.setCurrFocusedChunkIdx(f,!1);break}break}}}});d(this,"updateIsWebviewFocused",async t=>{await this._asyncMsgSender.send({type:ae.diffViewWindowFocusChange,data:t})});d(this,"setCurrFocusedChunkIdx",(t,i=!0)=>{this._focusModel.focusedItemIdx!==t&&(this._focusModel.setFocusIdx(t),i&&this.revealCurrFocusedChunk(),this.notifySubscribers())});d(this,"revealCurrFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.revealChunk(t)});d(this,"revealChunk",t=>{var r;const i=(r=t.unitOfCodeWork.lineChanges)==null?void 0:r.lineChanges[0],a=i==null?void 0:i.modifiedStart;a!==void 0&&this._editor.revealLineNearTop(a-1)});d(this,"renderCentralOverlayWidget",t=>{const i=()=>({editor:this._editor,id:"central-overlay-widget"}),a=_n(t,i(),{monaco:this._monaco});return{update:()=>{a.update(i())},destroy:a.destroy}});d(this,"renderInstructionsDrawerViewZone",(t,i)=>{let a=!1,r=i;const s=i.autoFocus??!0,c=f=>{s&&!a&&(this._editor.revealLineNearTop(f),a=!0)},u=f=>({...f,ordinal:Zt.instructionDrawer,editor:this._editor,afterLineNumber:f.line}),b=pi(t,u(i)),m=[];return s&&m.push(this._editor.onDidUpdateDiff(()=>{c(r.line)})),{update:f=>{const C={...r,...f};wn(C,r)||(b.update(u(C)),r=C,c(C.line))},destroy:()=>{b.destroy(),m.forEach(f=>f.dispose())}}});d(this,"renderActionsViewZone",(t,i)=>{const a=s=>{var u;let c;return c=s.chunk?(u=s.chunk.unitOfCodeWork.lineChanges)==null?void 0:u.lineChanges[0].modifiedStart:1,{...s,ordinal:Zt.chunkActionPanel,editor:this._editor,afterLineNumber:c?c-1:void 0}},r=pi(t,a(i));return{update:s=>{r.update(a(s))},destroy:r.destroy}});d(this,"acceptAllChunks",()=>{this.leaves&&this.acceptChunks(this.leaves,!0)});d(this,"rejectAllChunks",()=>{this.leaves&&this.rejectChunks(this.leaves,!0)});d(this,"acceptFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.acceptChunk(t)});d(this,"rejectFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.rejectChunk(t)});d(this,"focusNextChunk",()=>{this._focusModel.focusNext(),this.revealCurrFocusedChunk()});d(this,"focusPrevChunk",()=>{this._focusModel.focusPrev(),this.revealCurrFocusedChunk()});d(this,"initialize",async()=>{var b;const t=await this._asyncMsgSender.send({type:ae.diffViewLoaded},2e3);this._resetScrollOnInit();const{file:i,instruction:a,keybindings:r,editable:s}=t.data;this._editor.updateOptions({readOnly:!s});const c=ge(this._keybindings);this._keybindings.set(r??c);const u=a==null?void 0:a.selection;u&&(u.start.line===u.end.line&&u.start.character===u.end.character&&this._mode.set(Oe.instruction),ge(this.selectionLines)===void 0&&this._selectionLines.set({start:u.start.line,end:u.end.line})),this.updateModels(i.originalCode??"",i.modifiedCode??"",{rootPath:i.repoRoot,relPath:i.pathName}),(b=this._currStream)==null||b.finish(),this._currStream=void 0,this._disableResolution.set(!!t.data.disableResolution),this._disableApply.set(!!t.data.disableApply),await this._tryFetchStream(),this._syncStreamToModels()});d(this,"disposeDiffViewPanel",async()=>{await this._asyncMsgSender.send({type:ae.disposeDiffView})});d(this,"_tryFetchStream",async()=>{var i,a,r;const t=this._asyncMsgSender.stream({type:ae.diffViewFetchPendingStream},15e3,6e4);for await(const s of t)switch(s.type){case ae.diffViewDiffStreamStarted:{this.setLoading(!0),this._requestId.set(s.data.requestId);const c=this._editor.getOriginalEditor().getValue();this._currStream=new Mn(s.data.streamId,c,this._monaco),this._syncStreamToModels();break}case ae.diffViewDiffStreamEnded:if(((i=this._currStream)==null?void 0:i.id)!==s.data.streamId)return;this.setLoading(!1),this._cleanupStream();break;case ae.diffViewDiffStreamChunk:{if(((a=this._currStream)==null?void 0:a.id)!==s.data.streamId)return;const c=this._editor.getOriginalEditor().getModel();if(!this._editor.getModifiedEditor().getModel()||!c)return this.setLoading(!1),void this._cleanupStream();const u=(r=this._currStream)==null?void 0:r.onReceiveChunk(s);u&&(this._applyDeltaDiff(u),ge(this._selectionLines)!=null&&this._selectionLines.set(null));break}}});d(this,"handleMessageFromExtension",async t=>{switch(t.data.type){case ae.diffViewNotifyReinit:this.setLoading(!1),this._cleanupStream(),this.initialize();break;case ae.diffViewAcceptAllChunks:this.acceptAllChunks();break;case ae.diffViewAcceptFocusedChunk:this.acceptFocusedChunk();break;case ae.diffViewRejectFocusedChunk:this.rejectFocusedChunk();break;case ae.diffViewFocusPrevChunk:this.focusPrevChunk();break;case ae.diffViewFocusNextChunk:this.focusNextChunk()}});d(this,"_applyDeltaDiff",t=>{const i=this._editor.getOriginalEditor().getModel(),a=this._editor.getModifiedEditor().getModel();i&&a&&(i.pushEditOperations([],t.resetOriginal,()=>[]),t.original.forEach(r=>{i.pushEditOperations([],[r],()=>[])}),t.modified.forEach(r=>{a.pushEditOperations([],[r],()=>[])}))});d(this,"_cleanupStream",()=>{var t;if(this._currStream){const i=(t=this._currStream)==null?void 0:t.finish();this._applyDeltaDiff(i),this._currStream=void 0,this._resetScrollOnInit()}});d(this,"_syncStreamToModels",()=>{var a,r;const t=(a=this._currStream)==null?void 0:a.originalValue,i=(r=this._currStream)==null?void 0:r.modifiedValue;t&&t!==this._editor.getOriginalEditor().getValue()&&this._editor.getOriginalEditor().setValue(t),i&&i!==this._editor.getModifiedEditor().getValue()&&this._editor.getModifiedEditor().setValue(i)});d(this,"acceptChunk",async t=>{ge(this._disableApply)||this.acceptChunks([t])});d(this,"acceptChunks",async(t,i=!1)=>{ge(this._disableApply)||(this.executeDiffChunks(t,!0),this.notifyResolvedChunks(t,qt.accept,i),await Bt(),this.areModelsEqual()&&!ge(this.isLoading)&&this.disposeDiffViewPanel())});d(this,"areModelsEqual",()=>{var a,r;const t=(a=this._editor.getModel())==null?void 0:a.original,i=(r=this._editor.getModel())==null?void 0:r.modified;return(t==null?void 0:t.getValue())===(i==null?void 0:i.getValue())});d(this,"rejectChunk",async t=>{this.rejectChunks([t])});d(this,"rejectChunks",async(t,i=!1)=>{this.executeDiffChunks(t,!1),this.notifyResolvedChunks(t,qt.reject,i),await Bt(),this.areModelsEqual()&&!ge(this.isLoading)&&this.disposeDiffViewPanel()});d(this,"notifyDiffViewUpdated",gi(()=>{this.notifyResolvedChunks([],qt.accept)},1e3));d(this,"notifyResolvedChunks",async(t,i,a=!1)=>{var s;const r=(s=this._editor.getModel())==null?void 0:s.original.uri.path;r&&await this._asyncMsgSender.send({type:ae.diffViewResolveChunk,data:{file:{repoRoot:"",pathName:r,originalCode:this._originalCode,modifiedCode:this._modifiedCode},changes:t.map(c=>c.unitOfCodeWork),resolveType:i,shouldApplyToAll:a}},2e3)});d(this,"executeDiffChunks",(t,i)=>{var f,C,L;if(ge(this._disableResolution)||i&&ge(this._disableApply))return;const a=(f=this._editor.getModel())==null?void 0:f.original,r=(C=this._editor.getModel())==null?void 0:C.modified;if(!a||!r||this._currStream!==void 0)return;const s=[],c=[];for(const w of t){const N=(L=w.unitOfCodeWork.lineChanges)==null?void 0:L.lineChanges[0];if(!N||w.unitOfCodeWork.originalCode===void 0||w.unitOfCodeWork.modifiedCode===void 0)continue;let W={startLineNumber:N.originalStart,startColumn:1,endLineNumber:N.originalEnd,endColumn:1},g={startLineNumber:N.modifiedStart,startColumn:1,endLineNumber:N.modifiedEnd,endColumn:1};const k=i?w.unitOfCodeWork.modifiedCode:w.unitOfCodeWork.originalCode;k!==void 0&&(s.push({range:W,text:k}),c.push({range:g,text:k}))}a.pushEditOperations([],s,()=>[]),r.pushEditOperations([],c,()=>[]);const u=this._focusModel.nextIdx({nowrap:!0});if(u===void 0)return;const b=u===this._focusModel.focusedItemIdx?u-1:u,m=this._focusModel.items[b];m&&this.revealChunk(m)});d(this,"updateCodeChunk",()=>{this._rootChunk=this.computeCodeChunk(),this._focusModel.setItems(this.leaves??[]),this._focusModel.initFocusIdx(0),this.notifySubscribers()});d(this,"handleInstructionSubmit",t=>{const i=this._editor.getModifiedEditor(),a=this.getSelectedCodeDetails(i);if(!a)throw Error("No selected code details found");this._chatModel.currentConversationModel.sendInstructionExchange(t,a)});d(this,"updateModels",(t,i,a)=>{var c,u;const r=(u=(c=this._editor.getModel())==null?void 0:c.original)==null?void 0:u.uri,s=(a&&this._monaco.Uri.file(a.relPath))??r;if(s)if((r==null?void 0:r.fsPath)!==s.fsPath||(r==null?void 0:r.authority)!==s.authority){const b=s.with({fragment:crypto.randomUUID()}),m=s.with({fragment:crypto.randomUUID()});this._editor.setModel({original:this._monaco.editor.createModel(t,void 0,b),modified:this._monaco.editor.createModel(i??"",void 0,m)})}else this._originalCode!==t&&this.getOriginalEditor().setValue(t),this._modifiedCode!==i&&this.getModifiedEditor().setValue(i??"");else console.warn("No URI found for diff view. Not updating models.")});d(this,"updateTheme",t=>{this._monaco.editor.setTheme(t)});this._editorContainer=t,this._monaco=a,this._asyncMsgSender=new ln(r=>ot.postMessage(r)),this.initializeEditor(i)}get editorOffset(){return this._editorContainer.getBoundingClientRect().top}get currFocusedChunkIdx(){return this._focusModel.focusedItemIdx}get selectionLines(){return this._selectionLines}get mode(){return this._mode}get keybindings(){return this._keybindings}getOriginalEditor(){return this._editor.getOriginalEditor()}getModifiedEditor(){return this._editor.getModifiedEditor()}get isLoading(){return{subscribe:this._isLoadingDiffChunks.subscribe}}setLoading(t){this._isLoadingDiffChunks.set(t)}get _originalCode(){var t;return((t=this._currStream)==null?void 0:t.originalCode)??this._editor.getOriginalEditor().getValue()}get _modifiedCode(){return this._editor.getModifiedEditor().getValue()}get leaves(){const t=this.codeChunk;if(t)return Li(t)}get codeChunk(){return this._rootChunk}computeCodeChunk(){var s,c;const t=[],i=this._editor.getLineChanges(),a=(s=this._editor.getModel())==null?void 0:s.original,r=(c=this._editor.getModel())==null?void 0:c.modified;if(i&&a&&r){for(const u of i){const b=mi({startLineNumber:u.originalStartLineNumber,endLineNumber:u.originalEndLineNumber}),m=mi({startLineNumber:u.modifiedStartLineNumber,endLineNumber:u.modifiedEndLineNumber}),f=Sn(this._editor,b,m);t.push(f)}return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],children:t,childIds:t.map(u=>u.id)}}}getSelectedCodeDetails(t){const i=t.getModel();if(!i)return null;const a=i.getLanguageId(),r=1,s=1,c={lineNumber:i.getLineCount(),column:i.getLineMaxColumn(i.getLineCount())},u=ge(this._selectionLines);if(!u)throw new Error("No selection lines found");const b=Math.min(u.end+1,c.lineNumber),m=new this._monaco.Range(u.start+1,1,b,i.getLineMaxColumn(b));let f=i.getValueInRange(m);b<i.getLineCount()&&(f+=i.getEOL());const C=new this._monaco.Range(r,s,m.startLineNumber,m.startColumn),L=Math.min(m.endLineNumber+1,c.lineNumber),w=new this._monaco.Range(L,1,c.lineNumber,c.column);return{selectedCode:f,prefix:i.getValueInRange(C),suffix:i.getValueInRange(w),path:i.uri.path,language:a,prefixBegin:C.startLineNumber-1,suffixEnd:w.endLineNumber-1}}}function Sn(p,t,i){var s,c;const a=(s=p.getModel())==null?void 0:s.original,r=(c=p.getModel())==null?void 0:c.modified;if(!a||!r)throw new Error("No models found");return function(u,b,m,f){return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],unitOfCodeWork:{repoRoot:"",pathName:"",originalCode:u,modifiedCode:b,lineChanges:{lineChanges:[{originalStart:m.startLineNumber,originalEnd:m.endLineNumber,modifiedStart:f.startLineNumber,modifiedEnd:f.endLineNumber}],lineOffset:0}},children:[],childIds:[]}}(a.getValueInRange(t),r.getValueInRange(i),t,i)}function mi(p){return p.endLineNumber===0?{startLineNumber:p.startLineNumber+1,startColumn:1,endLineNumber:p.startLineNumber+1,endColumn:1}:{startLineNumber:p.startLineNumber,startColumn:1,endLineNumber:p.endLineNumber+1,endColumn:1}}var On=H('<span class="c-keyboard-shortcut-hint__icon svelte-1txw16l"> </span>'),Ln=H("<span></span>");function qe(p,t){st(t,!0);let i=xe(t,"class",3,""),a=xe(t,"keybinding",19,()=>{}),r=xe(t,"icons",19,()=>{var c;return((c=a())==null?void 0:c.split("-"))??[]});var s=Ln();ki(s,21,r,wi,(c,u)=>{var b=On(),m=oe(b);Qe(()=>Gt(m,l(u))),x(c,b)}),Qe(()=>St(s,1,`c-keyboard-shortcut-hint ${i()}`,"svelte-1txw16l")),x(p,s),at()}var In=H("<!> Accept",1),xn=H("<!> Reject",1),Nn=H("<div></div>  <div><div><!> <!></div></div>",1);function jn(p,t){st(t,!0);const[i,a]=xt(),r=()=>re(b,"$keybindingsStore",i);let s=xe(t,"align",3,"right"),c=xe(t,"heightInPx",3,1),u=xe(t,"disableApply",3,!1);const b=t.diffViewModel.keybindings;let m=Ie(0);const f=S=>{F(m,S,!0)};let C,L=Ie(!1);function w(){C&&(clearTimeout(C),C=void 0),F(L,!1)}function N(S){S.target.closest(".c-button-container")?w():S.type==="mouseenter"||S.type==="mousemove"?(w(),C=setTimeout(()=>{F(L,!0)},400)):S.type==="mouseleave"&&w()}var W=Nn(),g=ee(W);let k;Oi(g,(S,P)=>{var M,$;return($=(M=t.diffViewModel).renderActionsViewZone)==null?void 0:$.call(M,S,P)},()=>({chunk:t.leaf,heightInPx:c(),onDomNodeTop:f}));var z=K(g,2);let Q;z.__mousemove=N;var V=oe(z);let U;var X=oe(V),ve=S=>{De(S,{size:1,variant:"ghost",color:"success",$$events:{click(...P){var M;(M=t.onAccept)==null||M.apply(this,P)}},children:(P,M)=>{var $=In();qe(ee($),{get keybinding(){return r().acceptFocusedChunk}}),x(P,$)},$$slots:{default:!0}})};pe(X,S=>{u()||S(ve)});var le=K(X,2);De(le,{size:1,variant:"ghost",color:"error",$$events:{click(...S){var P;(P=t.onReject)==null||P.apply(this,S)}},children:(S,P)=>{var M=xn();qe(ee(M),{get keybinding(){return r().rejectFocusedChunk}}),x(S,M)},$$slots:{default:!0}}),Qe((S,P,M)=>{k=St(g,1,"svelte-zm1705",null,k,S),Q=St(z,1,"c-chunk-action-panel-anchor svelte-zm1705",null,Q,P),vi(z,`top: ${l(m)??""}px;`),U=St(V,1,"c-button-container svelte-zm1705",null,U,M)},[()=>({"c-chunk-diff-border--focused":!!t.leaf&&t.isFocused}),()=>({"c-chunk-action-panel-anchor--left":s()==="left","c-chunk-action-panel-anchor--right":s()==="right","c-chunk-action-panel-anchor--focused":t.isFocused}),()=>({"c-button-container--focused":t.isFocused,"c-button-container--transparent":l(L)})]),Be("mouseenter",z,N),Be("mouseleave",z,N),x(p,W),at(),a()}Gi(["mousemove"]);var An=H('<span class="c-diff-page-counter svelte-1w94ymh">Generating changes <!></span>'),$n=H('<span class="c-diff-page-counter svelte-1w94ymh"> <!></span>'),Vn=H('<span class="c-diff-page-counter svelte-1w94ymh">No changes</span>'),Fn=H("<!> Back",1),Dn=H("<!> Next",1),Rn=H("<!> Accept All",1),zn=H("<!> Reject All",1),Pn=H("<!> <!>",1),Tn=H("<!> <!> <!>",1),Wn=H('<div class="c-top-action-panel-anchor svelte-1w94ymh"><div class="c-button-container svelte-1w94ymh"><!> <!> <!></div></div>');function Un(p,t){st(t,!0);const[i,a]=xt(),r=()=>re(t.diffViewModel,"$diffViewModel",i),s=()=>re(m,"$requestId",i),c=()=>re(l(N),"$isLoadingStore",i),u=()=>re(b,"$keybindingsStore",i),b=t.diffViewModel.keybindings,m=t.diffViewModel.requestId;let f=Le(()=>r().disableResolution),C=Le(()=>r().disableApply),L=Ie("x");Lt(()=>{r().currFocusedChunkIdx!==void 0?F(L,(r().currFocusedChunkIdx+1).toString(),!0):F(L,"x")});let w,N=Le(()=>r().isLoading),W=Le(()=>{var E;return!!((E=r().leaves)!=null&&E.length)}),g=Ie("Copy request ID"),k=Ie(()=>{});function z(E){E||(clearTimeout(w),w=void 0,F(g,"Copy request ID"))}async function Q(){s()&&(await navigator.clipboard.writeText(s()),F(g,"Copied!"),clearTimeout(w),w=setTimeout(l(k),1500))}var V=Wn(),U=oe(V),X=oe(U),ve=E=>{const _=Le(()=>[fn.Hover]);hn(E,{onOpenChange:z,get content(){return l(g)},get triggerOn(){return l(_)},get requestClose(){return l(k)},set requestClose(T){F(k,T,!0)},children:(T,de)=>{mn(T,{variant:"ghost",color:"neutral",size:1,onclick:Q,children:(j,se)=>{pn(j)},$$slots:{default:!0}})},$$slots:{default:!0}})};pe(X,E=>{s()&&E(ve)});var le=K(X,2),S=E=>{var _=An(),T=K(oe(_));fi(T,{size:1,get loading(){return c()}}),x(E,_)},P=(E,_)=>{var T=j=>{var se=$n(),Y=oe(se),te=K(Y);fi(te,{size:1,get loading(){return c()}}),Qe(()=>{var Z,v;return Gt(Y,`${l(L)??""} of ${((v=(Z=r())==null?void 0:Z.leaves)==null?void 0:v.length)??""} `)}),x(j,se)},de=j=>{var se=Vn();x(j,se)};pe(E,j=>{l(W)?j(T):j(de,!1)},_)};pe(le,E=>{!l(W)&&c()?E(S):E(P,!1)});var M=K(le,2),$=E=>{var _=Tn(),T=ee(_);De(T,{size:1,variant:"ghost",color:"neutral",get onclick(){return t.diffViewModel.focusPrevChunk},children:(Y,te)=>{var Z=Fn();qe(ee(Z),{get keybinding(){return u().focusPrevChunk}}),x(Y,Z)},$$slots:{default:!0}});var de=K(T,2);De(de,{size:1,variant:"ghost",color:"neutral",get onclick(){return t.diffViewModel.focusNextChunk},children:(Y,te)=>{var Z=Dn();qe(ee(Z),{get keybinding(){return u().focusNextChunk}}),x(Y,Z)},$$slots:{default:!0}});var j=K(de,2),se=Y=>{var te=Pn(),Z=ee(te),v=ce=>{De(ce,{size:1,variant:"ghost",color:"success",get onclick(){return t.diffViewModel.acceptAllChunks},children:(be,he)=>{var ye=Rn();qe(ee(ye),{get keybinding(){return u().acceptAllChunks}}),x(be,ye)},$$slots:{default:!0}})};pe(Z,ce=>{re(l(C),"$disableApply",i)||ce(v)});var _e=K(Z,2);De(_e,{size:1,variant:"ghost",color:"error",get onclick(){return t.diffViewModel.rejectAllChunks},children:(ce,be)=>{var he=zn();qe(ee(he),{get keybinding(){return u().rejectAllChunks}}),x(ce,he)},$$slots:{default:!0}}),x(Y,te)};pe(j,Y=>{re(l(f),"$disableResolution",i)||Y(se)}),x(E,_)};pe(M,E=>{l(W)&&E($)}),x(p,V),at(),a()}var qn=Ji("<svg><!></svg>"),Bn=H("<!> <!> <!> <!>",1),Hn=H("Close <!>",1),Zn=H('<div></div> <div class="instruction-drawer-panel svelte-1cxscce"><div class="instruction-drawer-panel__contents svelte-1cxscce" tabindex="0" role="button"><div class="l-input-area__input svelte-1cxscce"><!></div> <div class="c-instruction-drawer-panel__btn-container svelte-1cxscce"><!> <!></div></div></div>',1);function Gn(p,t){st(t,!1);const[i,a]=xt(),r=()=>re(ve,"$modeStore",i),s=()=>re(le,"$selectionLinesStore",i),c=()=>re(f(),"$diffViewModel",i),u=()=>re(l(m),"$isLoadingStore",i),b=Fe(),m=Fe();let f=xe(t,"diffViewModel",8),C=xe(t,"initialConversation",24,()=>{}),L=xe(t,"initialFlags",24,()=>{});const w=sn.getContext().monaco,N={isWholeLine:!0,marginClassName:"instruction-edit-area-margin"},W=new Si(ot);let g=new Ei;W.registerConsumer(g);let k=new Mi(W,ot,g,{initialConversation:C(),initialFlags:L()});const z=k.currentConversationModel;let Q;W.registerConsumer(k),kn(k);let V,U=Fe(),X=Fe("");const ve=f().mode,le=f().selectionLines;function S(){const v=f().getModifiedEditor(),_e=ge(w);if(!v||!_e||(V==null||V.clear(),!s()))return;const ce=s().start,be=s().end,he={range:new _e.Range(ce+1,1,be+1,1),options:N};V||(V=v.createDecorationsCollection()),V.set([he])}function P(){var v;return!!((v=l(X))!=null&&v.trim())&&(f().handleInstructionSubmit(l(X)),!0)}bi(async()=>{await Bt(),de(),F(M,f().editorOffset)}),yi(()=>{Q==null||Q.destroy(),V==null||V.clear()});let M=Fe(0),$=Fe(57),E=Fe(void 0),_=Fe(void 0);const T=()=>{var v;return(v=l(E))==null?void 0:v.requestFocus()},de=()=>{var v;return(v=l(E))==null?void 0:v.forceFocus()},j=v=>{z.saveDraftMentions(v.current)};function se(v){F(X,v.rawText)}Tt(()=>(r(),Oe),()=>{F(b,(r()===Oe.instruction?"Instruct":"Edit with")+" Augment... @ to focus on files or docs")}),Tt(()=>c(),()=>{Ci(F(m,c().isLoading),"$isLoadingStore",i)}),Tt(()=>(l(U),s()),()=>{if(l(U)){if(s()==null)F($,0);else{const v=l(U).scrollHeight;F($,Math.min(40+v,108))}Q==null||Q.update({heightInPx:l($)}),S()}}),Qi(),Yi();var Y=Ot(),te=ee(Y),Z=v=>{var _e=Zn(),ce=ee(_e);Oi(ce,D=>function(q){if(q){const ie=s()?s().start:1;Q=f().renderInstructionsDrawerViewZone(q,{line:ie,heightInPx:l($),onDomNodeTop:Ce=>{F(M,f().editorOffset+Ce)},autoFocus:!0}),S()}}(D));var be=K(ce,2),he=oe(be),ye=oe(he),dt=oe(ye);It(_i.Root(dt,{focusOnInit:!0,children:(D,q)=>{var ie=Bn(),Ce=ee(ie);bn(Ce,{shortcuts:{Enter:()=>P()}});var Ze=K(Ce,2);It(yn(Ze,{requestEditorFocus:T,onMentionItemsUpdated:j,$$legacy:!0}),lt=>F(_,lt),()=>l(_));var ke=K(Ze,2);_i.Content(ke,{get content(){return l(X)},onContentChanged:se});var Ne=K(ke,2);Cn(Ne,{get placeholder(){return l(b)}}),x(D,ie)},$$slots:{default:!0},$$legacy:!0}),D=>F(E,D),()=>l(E)),It(ye,D=>F(U,D),()=>l(U));var ct=K(ye,2),ut=oe(ct);De(ut,{id:"close",size:1,variant:"soft",color:"neutral",title:"Close",$$events:{click(...D){var q;(q=f().disposeDiffViewPanel)==null||q.apply(this,D)}},children:(D,q)=>{var ie=Hn();qe(K(ee(ie)),{keybinding:"esc"}),x(D,ie)},$$slots:{default:!0}});var Nt=K(ut,2);const He=hi(()=>(r(),li(Oe),Wt(()=>r()===Oe.instruction?"Instruct Augment":"Edit with Augment"))),Ye=hi(()=>(l(X),u(),Wt(()=>!l(X).trim()||u())));De(Nt,{id:"send",size:1,variant:"solid",color:"accent",get title(){return l(He)},get disabled(){return l(Ye)},$$events:{click:P},children:(D,q)=>{var ie=Xi();Qe(()=>Gt(ie,(r(),li(Oe),Wt(()=>r()===Oe.instruction?"Instruct":"Edit")))),x(D,ie)},$$slots:{default:!0,iconRight:(D,q)=>{(function(ie,Ce){const Ze=on(Ce,["children","$$slots","$$events","$$legacy"]);var ke=qn();Ki(ke,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...Ze}));var Ne=oe(ke);vn(Ne,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M133.9 232 65.8 95.9 383.4 232zm0 48h249.5L65.8 416.1l68-136.1zM44.6 34.6C32.3 29.3 17.9 32.3 8.7 42S-2.6 66.3 3.4 78.3L92.2 256 3.4 433.7c-6 12-3.9 26.5 5.3 36.3s23.5 12.7 35.9 7.5l448-192c11.8-5 19.4-16.6 19.4-29.4s-7.6-24.4-19.4-29.4l-448-192z"/>',!0),x(ie,ke)})(D,{slot:"iconRight"})}}}),Qe(()=>vi(be,`top: ${l(M)??""}px; height: ${l($)??""}px;`)),Be("click",he,de),Be("keydown",he,D=>{D.key==="Enter"&&(de(),D.stopPropagation(),D.preventDefault())}),x(v,_e)};pe(te,v=>{s()&&v(Z)}),x(p,Y),at(),a()}var Jn=H('<div class="sticky-top svelte-453n6i"><!></div> <!>',1),Kn=H('<div class="diff-view-container svelte-453n6i"><!> <div class="editor-container svelte-453n6i"><div class="editor svelte-453n6i"></div> <!></div></div>');nn(function(p,t){st(t,!0);const[i,a]=xt(),r=()=>re(en,"$themeStore",i),s=()=>re(l(c),"$diffViewModel",i);let c=Ie(void 0),u=Ie(void 0),b=Le(()=>{var g;return(g=l(c))==null?void 0:g.disableApply}),m=Le(()=>{var g;return(g=l(c))==null?void 0:g.disableResolution});function f(g){const k=tn.dark;return cn((g==null?void 0:g.category)||k,g==null?void 0:g.intensity)??un.get(k)}Lt(()=>{var k;const g=r();l(c)&&((k=l(c))==null||k.updateTheme(f(g)))});let C=Ie(void 0);Lt(()=>{l(C)&&l(u)&&!l(c)&&Ci(F(c,new En(l(u),f(r()),l(C)),!0),"$diffViewModel",i)}),bi(async()=>{F(C,await window.augmentDeps.monaco,!0),l(C)||console.error("Monaco not loaded. Diff view cannot be initialized.")}),yi(()=>{var g;(g=l(c))==null||g.dispose()});let L=Le(()=>{var g;return(g=s())==null?void 0:g.leaves}),w=Ie(!1);Lt(()=>{var g;(g=l(c))==null||g.updateIsWebviewFocused(l(w))});var N=Ot();Be("message",Ut,function(...g){var k,z;(z=(k=l(c))==null?void 0:k.handleMessageFromExtension)==null||z.apply(this,g)}),Be("focus",Ut,()=>F(w,!0)),Be("blur",Ut,()=>F(w,!1));var W=ee(N);an(W,()=>dn.Root,(g,k)=>{k(g,{children:(z,Q)=>{var V=Kn(),U=oe(V),X=M=>{var $=Jn(),E=ee($);Un(oe(E),{get diffViewModel(){return s()}}),Gn(K(E,2),{get diffViewModel(){return s()}}),x(M,$)};pe(U,M=>{s()&&M(X)});var ve=K(U,2),le=oe(ve);It(le,M=>F(u,M),()=>l(u));var S=K(le,2),P=M=>{var $=Ot(),E=ee($);ki(E,17,()=>l(L),wi,(_,T,de)=>{var j=Ot(),se=ee(j),Y=te=>{const Z=Le(()=>{var v;return((v=s())==null?void 0:v.currFocusedChunkIdx)===de});jn(te,{get isFocused(){return l(Z)},onAccept:()=>{var v;return(v=s())==null?void 0:v.acceptChunk(l(T))},onReject:()=>{var v;return(v=s())==null?void 0:v.rejectChunk(l(T))},get diffViewModel(){return s()},get leaf(){return l(T)},align:"right",get disableApply(){return re(l(b),"$disableApply",i)}})};pe(se,te=>{l(T).unitOfCodeWork.modifiedCode!==l(T).unitOfCodeWork.originalCode&&te(Y)}),x(_,j)}),x(M,$)};pe(S,M=>{var $;s()&&(($=l(L))!=null&&$.length)&&!re(l(m),"$disableResolution",i)&&M(P)}),x(z,V)},$$slots:{default:!0}})}),x(p,N),at(),a()},{target:document.getElementById("app")});
