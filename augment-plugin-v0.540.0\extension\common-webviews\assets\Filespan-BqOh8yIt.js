import{c as Q,a9 as R,l as U,aa as V,ab as W,ac as X,ad as Y,p as Z,r as aa,ae as sa,af as N,x as ea,L as y,B as x,b as p,G as na,a as la,y as k,D as I,E as $,N as B,O as D,z as c,A as m,o as u,F as G,a1 as ta}from"./legacy-AoIeRrIA.js";import{p as v,T as ra,s as S}from"./SpinnerAugment-mywmfXFR.js";import{g as ia,a as oa,n as pa}from"./focusTrapStack-CaEmYw0i.js";function ca(h,a,g,f,w,z){var r,i,t,n=null,_=h;Q(()=>{const s=a()||null;var b=g||s==="svg"?V:null;s!==r&&(t&&(s===null?Z(t,()=>{t=null,i=null}):s===i?aa(t):(sa(t),N(!1))),s&&s!==i&&(t=U(()=>{if(n=b?document.createElementNS(b,s):document.createElement(s),W(n,n),f){var d=n.appendChild(X());f(n,d)}Y.nodes_end=n,_.before(n)})),(r=s)&&(i=r),N(!0))},R)}var va=k('<div><div class="c-filespan__dir-text svelte-9pfhnp"> </div></div>'),fa=k('<span class="right-icons svelte-9pfhnp"><!></span>'),da=k('<!> <span class="c-filespan__filename svelte-9pfhnp"> </span> <!> <!>',1);function ga(h,a){ea(a,!0);let g=v(a,"class",3,""),f=v(a,"size",3,1),w=v(a,"nopath",3,!1),z=v(a,"growname",3,!0),r=v(a,"onClick",19,()=>{}),i=m(()=>pa(a.filepath)),t=m(()=>ia(c(i))),n=m(()=>oa(c(i))),_=m(()=>r()?"button":"div");ra(h,{get size(){return f()},children:(s,b)=>{var d=y();ca(x(d),()=>c(_),!1,(T,j)=>{la(T,()=>({class:`c-filespan ${g()}`,role:r()?"button":"",tabindex:"0",onclick:r()}),void 0,"svelte-9pfhnp");var E=da(),A=x(E),L=e=>{var l=y(),o=x(l);S(o,()=>a.leftIcon??G),p(e,l)};I(A,e=>{a.leftIcon&&e(L)});var C=$(A,2),O=u(C),F=$(C,2),q=e=>{var l=va();let o;var K=u(l),M=u(K);B(P=>{o=ta(l,1,"c-filespan__dir svelte-9pfhnp",null,o,P),D(M,c(n))},[()=>({growname:z()})]),p(e,l)};I(F,e=>{w()||e(q)});var H=$(F,2),J=e=>{var l=fa(),o=u(l);S(o,()=>a.rightIcon??G),p(e,l)};I(H,e=>{a.rightIcon&&e(J)}),B(()=>D(O,c(t))),p(j,E)}),p(s,d)},$$slots:{default:!0}}),na()}export{ga as F,ca as e};
