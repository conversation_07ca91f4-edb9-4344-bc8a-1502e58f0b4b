const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./next-edit-suggestions-IW1pin9L.css","./NextEditSuggestions-DS-04BEk.js","./legacy-AoIeRrIA.js","./SpinnerAugment-mywmfXFR.js","./SpinnerAugment-DoxdFmoV.css","./next-edit-types-904A5ehg.js","./host-qgbK079d.js","./IconFilePath-DsS2-quS.js","./LanguageIcon-D_oFq7vE.js","./LanguageIcon-D78BqCXT.css","./IconButtonAugment-DZyIKjh7.js","./IconButtonAugment-B4afvB2A.css","./IconFilePath-BVaLv7mP.css","./async-messaging-Dmg2N9Pf.js","./Drawer-KXH87plr.js","./index-Dtf_gCqL.js","./ellipsis-5yZhsJie.js","./Drawer-u8LRIFRf.css","./keypress-DD1aQVr0.js","./VSCodeCodicon-Bh752rpS.js","./VSCodeCodicon-DVaocTud.css","./index-BdF7sLLk.js","./index-BlHvDt2c.css","./monaco-render-utils-DfwV7QLY.js","./toggleHighContrast-Cb9MCs64.js","./preload-helper-Dv6uf1Os.js","./toggleHighContrast-D4zjdeIP.css","./isObjectLike-CnrzNkq5.js","./ButtonAugment-D7YBjBq5.js","./ButtonAugment-DORgvEFm.css","./NextEditSuggestions-Q98kphIR.css"])))=>i.map(i=>d[i]);
import{x as E,S as J,T as X,W as Q,a6 as Y,L as Z,B as ee,y as _,o as l,E as u,M as k,N as b,O as P,b as o,z as O,G as D,P as ae,K as te,au as ie}from"./legacy-AoIeRrIA.js";import{p as c,T as p,S as ne,f as oe}from"./SpinnerAugment-mywmfXFR.js";import"./design-system-init-Creeq9bS.js";import{_ as G}from"./preload-helper-Dv6uf1Os.js";import{a as re}from"./await-BdVIougb.js";import{c as se,M as le}from"./index-BdF7sLLk.js";import{t as S,a as C}from"./index-Dtf_gCqL.js";import{A as q}from"./augment-logo-BqyYuvys.js";const x={messages:["Untangling strings...","Warming up GPUs...","Initializing quantum compiler...","Procuring topological qubits...","Releasing AI pigeons...","Building mechanical keyboards...","Downloading more RAM...","Solving P vs. NP...","Summoning code wizards...","Folding origami...","Caffeinating the algorithms...","Phoning home...","Popping bubble wrap...","Dividing by zero...","Refactoring the matrix...","Petting cat...","Counting to infinity...","Knitting tea cozy...","Planting syntax tree...","Touching grass...","Code whispering...","Simulating quantum foam...","Aligning eigenspaces...","Reticulating splines...","Calculating terminal velocity...","Preparing jump to lightspeed...","Charging hyperdrive coils...","Aligning dilithium crystals...","Negotiating with Jawas...","Searching for droids...","Launching Kamehameha wave...","Modulating shield frequencies...","Fixing hyperdrive, again...","Computing odds of survival...","Getting a snack...","Assembling rubber ducks...","Overflowing stacks...","Waking up agents...","Searching haystacks...","Plugging in guitars...","Winding back the tape...","Onboarding stakeholders...","Thinking outside the box...","Moving the needle...","Dusting the backlog...","Calculating story points...","Putting it all on black...","Betting the farm...","Generating more loading messages...","Consulting Deep Thought...","Stretching hammies...","Grinding for XP...","Loading save point...","Replacing vacuum tubes...","Checking internet weather...","Turning it off and on again...","Searching gitblame..."],errors:["That didn't quite work. Let me try again.","Something went wrong, sorry about that. Trying again.","Hmm this isn't working. Looking for another way.","I seem to have encountered an issue, sorry about that. Let me try again.","That didn't go as planned. Recalibrating...","I need to take a different approach. One moment...","Hmm, something is not right. Let me find a better solution.","Looks like I need to rethink this. Finding alternatives.","Sorry for the delay, let me try again.","I need one more minute, thanks for your patience. Trying again now.","Something didn't work, giving it another try now.","One moment, let me see if I can try again.","I think I got something wrong, thanks for your patience while I take another look.","Give me one second to think this through - I need to try again.","Something doesn't look right, let me give it another shot."]};var ge=_('<div class="l-component svelte-1foy1hj"><!></div>'),de=_('<code class="svelte-1foy1hj"> </code>'),me=_('<div class="l-loader svelte-1foy1hj"><div class="l-loader__logo svelte-1foy1hj"><!> <!></div> <div class="l-loader__message-container l-loader-error-message svelte-1foy1hj"><!> <!></div></div>'),ce=_('<div class="l-loader svelte-1foy1hj"><div class="l-loader__logo svelte-1foy1hj"><!> <!></div> <div class="l-loader__message-container svelte-1foy1hj"><!> <!></div></div>');ie(function(F,B){E(B,!1);const N=async()=>(await G(()=>Promise.resolve({}),__vite__mapDeps([0]),import.meta.url),(await G(async()=>{const{default:T}=await import("./NextEditSuggestions-DS-04BEk.js");return{default:T}},__vite__mapDeps([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30]),import.meta.url)).default);te(),le.Root(F,{children:(T,he)=>{(function(W,t){E(t,!0);let K=c(t,"minDisplayTime",3,1e3),$=c(t,"title",3,"Augment Code"),V=c(t,"randomize",3,!0),z=c(t,"retryCount",3,3),I=c(t,"loadingMessages",19,()=>x.messages),A=c(t,"errorMessages",19,()=>x.errors),L=c(t,"errorMessage",3,"An error occurred while loading. Please try again later."),v=I().slice(1),M=J(X(I()[0])),f="loading",R=new AbortController;Q(()=>R.abort()),Y(async function(){v.length===0&&(v=[...f==="retry"?A():I()]),ae(M,f==="error"?L():v.splice(f!=="retry"&&V()?Math.floor(Math.random()*v.length):0,1)[0]??"",!0)});var j=Z(),H=ee(j);re(H,async function g(i=0){try{const[n]=await Promise.all([t.loader(),(e=K(),a=R.signal,new Promise(r=>{const h=setTimeout(r,e);a&&a.addEventListener("abort",()=>{clearTimeout(h),r()})}))]);return n}catch(n){if(console.error("Failed to load component",n),f="retry",i===0&&(v=[...A()]),z()&&i<=z())return await g(i+1);throw f="error",new Error("Failed to load component after retrying. Please try again later.")}var e,a},g=>{var i=ce(),e=l(i),a=l(e);q(a);var n=u(a,2);p(n,{size:2,children:(y,d)=>{var s=k();b(()=>P(s,$())),o(y,s)},$$slots:{default:!0}});var r=u(e,2),h=l(r);ne(h,{});var w=u(h,2);p(w,{size:1,color:"secondary",children:(y,d)=>{var s=k();b(()=>P(s,O(M))),o(y,s)},$$slots:{default:!0}}),S(3,i,()=>C),o(g,i)},(g,i)=>{var e=ge(),a=l(e);se(a,()=>O(i),(n,r)=>{r(n,oe(()=>t.componentProps))}),S(3,e,()=>C),o(g,e)},(g,i)=>{var e=me(),a=l(e),n=l(a);q(n);var r=u(n,2);p(r,{size:3,children:(d,s)=>{var m=k();b(()=>P(m,$())),o(d,m)},$$slots:{default:!0}});var h=u(a,2),w=l(h);p(w,{size:3,children:(d,s)=>{var m=k("An Error Occurred.");o(d,m)},$$slots:{default:!0}});var y=u(w,2);p(y,{size:1,children:(d,s)=>{var m=de(),U=l(m);b(()=>P(U,L())),o(d,m)},$$slots:{default:!0}}),S(3,e,()=>C),o(g,e)}),o(W,j),D()})(T,{loader:N,componentProps:{}})},$$slots:{default:!0}}),D()},{target:document.getElementById("app")});
