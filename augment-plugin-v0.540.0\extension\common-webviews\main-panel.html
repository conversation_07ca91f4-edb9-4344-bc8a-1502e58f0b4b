<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment</title>
    <script nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <script type="module" crossorigin src="./assets/main-panel-D4e3QZhY.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/legacy-AoIeRrIA.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-mywmfXFR.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-Creeq9bS.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/host-qgbK079d.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-BdF7sLLk.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-DZyIKjh7.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-Dmg2N9Pf.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-EhzME3pO.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-Dtf_gCqL.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-Db5scVK5.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/event-modifiers-Bz4QCcZc.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-DwIptXof.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/chat-types-BfwvR7Kn.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/focusTrapStack-CaEmYw0i.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-CnrzNkq5.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/input-DCBQtNgo.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-D2MYbf3a.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-BLDiLrXG.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/lodash-CwNMWAFx.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-BqOh8yIt.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-Cb9MCs64.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-CJlvVQMt.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/diff-operations-DEY-phZ8.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/file-type-utils-D6OEcQY2.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/chat-model-context-DPgWxlAp.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-D5l3axuC.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/await-BdVIougb.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-B528snJk.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/remote-agents-client-BMR_qMG5.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-D7YBjBq5.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/SuccessfulButton-xgZ5Aax4.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/OpenFileButton-CWm-PsCk.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-BXWAqMp6.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/partner-mcp-utils-BH31APX7.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-B_3jxWk_.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-B1LKPxPr.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/CopyButton-DfcpqvjI.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-5yZhsJie.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-D_oFq7vE.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-BqyYuvys.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/BadgeRoot-CMDpgWKP.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/repository-utils-DzBkqZ7a.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/AugmentMessage-DuZeVL4h.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/github-BdMD2Kls.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/StatusIndicator-CWKt8jxE.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/extension-client-context-CnKF1ct_.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-DKq0zBWN.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-DPoLDlf4.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/Keybindings-D5k530TZ.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/ModalAugment-CfGbMl4M.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/next-edit-types-904A5ehg.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconFilePath-DsS2-quS.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/check-D2K2_syQ.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-CUZr9PGN.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-Bh752rpS.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/feedback-rating-RDPz08ah.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-left-h-peZKbD.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-D1H89BMr.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-ClvxyORV.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-D9au8v71.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-CMEPAZfs.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/diff-operations-CM0oZCbE.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/SuccessfulButton-Do68LBjG.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/OpenFileButton-bH4F3VXH.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-BIwUn_6v.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-DORgvEFm.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/CopyButton-DjlvEF_S.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-YWaRJ64J.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/BadgeRoot-dXUx3CYB.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/AugmentMessage-DaYUYhgg.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/StatusIndicator-BR3GMq1u.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/extension-client-context-C6sZj1GX.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/Keybindings-Boakap-a.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/github-DbpbIXze.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/ModalAugment-CM3byOYD.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconFilePath-BVaLv7mP.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-CVoP4f0A.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/main-panel-BnjYV-YL.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
