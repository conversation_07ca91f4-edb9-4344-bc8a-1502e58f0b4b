var to=Object.defineProperty;var eo=(e,t,n)=>t in e?to(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var M=(e,t,n)=>eo(e,typeof t!="symbol"?t+"":t,n);import{aV as no,aW as oo,Y as On,Z as Tn,aO as ro,x as Zt,I as Le,J as _n,K as ye,L as pe,B as $t,b as it,G as Gt,Q as ke,X as Cn,y as Ht,a7 as fe,N as le,a1 as An,o as Xt,a8 as Et,w as io,W as ao,ag as xe,R as je,a3 as Ye,D as Me,u as Ke,z as yt,m as so,E as co,a2 as uo,M as po,O as fo,P as lo,a as Je,A as Ee,F as Ze}from"./legacy-AoIeRrIA.js";import{c as ae,n as vo,p as D,g as Yt,b as Ge,a as ho,h as mo,k as go,T as yo,j as bo,s as Qe,d as wo}from"./SpinnerAugment-mywmfXFR.js";import{a as Dn}from"./IconButtonAugment-DZyIKjh7.js";import{b as He}from"./host-qgbK079d.js";import{s as tn}from"./event-modifiers-Bz4QCcZc.js";function de(e,t,n){var o=no(e,t);o&&o.set&&(e[t]=n,oo(()=>{e[t]=null}))}var en=NaN,xo="[object Symbol]",Eo=/^\s+|\s+$/g,Oo=/^[-+]0x[0-9a-f]+$/i,To=/^0b[01]+$/i,_o=/^0o[0-7]+$/i,Co=parseInt,Ao=typeof ae=="object"&&ae&&ae.Object===Object&&ae,Do=typeof self=="object"&&self&&self.Object===Object&&self,Lo=Ao||Do||Function("return this")(),ko=Object.prototype.toString,jo=Math.max,Mo=Math.min,Oe=function(){return Lo.Date.now()};function Se(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function nn(e){if(typeof e=="number")return e;if(function(o){return typeof o=="symbol"||function(s){return!!s&&typeof s=="object"}(o)&&ko.call(o)==xo}(e))return en;if(Se(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=Se(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=e.replace(Eo,"");var n=To.test(e);return n||_o.test(e)?Co(e.slice(2),n?2:8):Oo.test(e)?en:+e}const on=vo(function(e,t,n){var o,s,i,a,c,p,v=0,d=!1,l=!1,E=!0;if(typeof e!="function")throw new TypeError("Expected a function");function y(b){var r=o,T=s;return o=s=void 0,v=b,a=e.apply(T,r)}function x(b){var r=b-p;return p===void 0||r>=t||r<0||l&&b-v>=i}function g(){var b=Oe();if(x(b))return h(b);c=setTimeout(g,function(r){var T=t-(r-p);return l?Mo(T,i-(r-v)):T}(b))}function h(b){return c=void 0,E&&o?y(b):(o=s=void 0,a)}function C(){var b=Oe(),r=x(b);if(o=arguments,s=this,p=b,r){if(c===void 0)return function(T){return v=T,c=setTimeout(g,t),d?y(T):a}(p);if(l)return c=setTimeout(g,t),y(p)}return c===void 0&&(c=setTimeout(g,t)),a}return t=nn(t)||0,Se(n)&&(d=!!n.leading,i=(l="maxWait"in n)?jo(nn(n.maxWait)||0,t):i,E="trailing"in n?!!n.trailing:E),C.cancel=function(){c!==void 0&&clearTimeout(c),v=0,o=p=s=c=void 0},C.flush=function(){return c===void 0?a:h(Oe())},C});var Y="top",ot="bottom",rt="right",K="left",Pe="auto",Qt=[Y,ot,rt,K],At="start",Kt="end",Ho="clippingParents",Ln="viewport",Vt="popper",So="reference",rn=Qt.reduce(function(e,t){return e.concat([t+"-"+At,t+"-"+Kt])},[]),kn=[].concat(Qt,[Pe]).reduce(function(e,t){return e.concat([t,t+"-"+At,t+"-"+Kt])},[]),Po=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function lt(e){return e?(e.nodeName||"").toLowerCase():null}function Z(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Tt(e){return e instanceof Z(e).Element||e instanceof Element}function nt(e){return e instanceof Z(e).HTMLElement||e instanceof HTMLElement}function $e(e){return typeof ShadowRoot<"u"&&(e instanceof Z(e).ShadowRoot||e instanceof ShadowRoot)}const jn={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var o=t.styles[n]||{},s=t.attributes[n]||{},i=t.elements[n];nt(i)&&lt(i)&&(Object.assign(i.style,o),Object.keys(s).forEach(function(a){var c=s[a];c===!1?i.removeAttribute(a):i.setAttribute(a,c===!0?"":c)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(o){var s=t.elements[o],i=t.attributes[o]||{},a=Object.keys(t.styles.hasOwnProperty(o)?t.styles[o]:n[o]).reduce(function(c,p){return c[p]="",c},{});nt(s)&&lt(s)&&(Object.assign(s.style,a),Object.keys(i).forEach(function(c){s.removeAttribute(c)}))})}},requires:["computeStyles"]};function ft(e){return e.split("-")[0]}var Ot=Math.max,ve=Math.min,Dt=Math.round;function Re(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function Mn(){return!/^((?!chrome|android).)*safari/i.test(Re())}function Lt(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var o=e.getBoundingClientRect(),s=1,i=1;t&&nt(e)&&(s=e.offsetWidth>0&&Dt(o.width)/e.offsetWidth||1,i=e.offsetHeight>0&&Dt(o.height)/e.offsetHeight||1);var a=(Tt(e)?Z(e):window).visualViewport,c=!Mn()&&n,p=(o.left+(c&&a?a.offsetLeft:0))/s,v=(o.top+(c&&a?a.offsetTop:0))/i,d=o.width/s,l=o.height/i;return{width:d,height:l,top:v,right:p+d,bottom:v+l,left:p,x:p,y:v}}function Ie(e){var t=Lt(e),n=e.offsetWidth,o=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-o)<=1&&(o=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:o}}function Hn(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&$e(n)){var o=t;do{if(o&&e.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}function ht(e){return Z(e).getComputedStyle(e)}function Ro(e){return["table","td","th"].indexOf(lt(e))>=0}function bt(e){return((Tt(e)?e.ownerDocument:e.document)||window.document).documentElement}function be(e){return lt(e)==="html"?e:e.assignedSlot||e.parentNode||($e(e)?e.host:null)||bt(e)}function an(e){return nt(e)&&ht(e).position!=="fixed"?e.offsetParent:null}function te(e){for(var t=Z(e),n=an(e);n&&Ro(n)&&ht(n).position==="static";)n=an(n);return n&&(lt(n)==="html"||lt(n)==="body"&&ht(n).position==="static")?t:n||function(o){var s=/firefox/i.test(Re());if(/Trident/i.test(Re())&&nt(o)&&ht(o).position==="fixed")return null;var i=be(o);for($e(i)&&(i=i.host);nt(i)&&["html","body"].indexOf(lt(i))<0;){var a=ht(i);if(a.transform!=="none"||a.perspective!=="none"||a.contain==="paint"||["transform","perspective"].indexOf(a.willChange)!==-1||s&&a.willChange==="filter"||s&&a.filter&&a.filter!=="none")return i;i=i.parentNode}return null}(e)||t}function Ue(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Bt(e,t,n){return Ot(e,ve(t,n))}function Sn(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Pn(e,t){return t.reduce(function(n,o){return n[o]=e,n},{})}const No={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,o=e.name,s=e.options,i=n.elements.arrow,a=n.modifiersData.popperOffsets,c=ft(n.placement),p=Ue(c),v=[K,rt].indexOf(c)>=0?"height":"width";if(i&&a){var d=function(j,L){return Sn(typeof(j=typeof j=="function"?j(Object.assign({},L.rects,{placement:L.placement})):j)!="number"?j:Pn(j,Qt))}(s.padding,n),l=Ie(i),E=p==="y"?Y:K,y=p==="y"?ot:rt,x=n.rects.reference[v]+n.rects.reference[p]-a[p]-n.rects.popper[v],g=a[p]-n.rects.reference[p],h=te(i),C=h?p==="y"?h.clientHeight||0:h.clientWidth||0:0,b=x/2-g/2,r=d[E],T=C-l[v]-d[y],f=C/2-l[v]/2+b,w=Bt(r,f,T),_=p;n.modifiersData[o]=((t={})[_]=w,t.centerOffset=w-f,t)}},effect:function(e){var t=e.state,n=e.options.element,o=n===void 0?"[data-popper-arrow]":n;o!=null&&(typeof o!="string"||(o=t.elements.popper.querySelector(o)))&&Hn(t.elements.popper,o)&&(t.elements.arrow=o)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function kt(e){return e.split("-")[1]}var Wo={top:"auto",right:"auto",bottom:"auto",left:"auto"};function sn(e){var t,n=e.popper,o=e.popperRect,s=e.placement,i=e.variation,a=e.offsets,c=e.position,p=e.gpuAcceleration,v=e.adaptive,d=e.roundOffsets,l=e.isFixed,E=a.x,y=E===void 0?0:E,x=a.y,g=x===void 0?0:x,h=typeof d=="function"?d({x:y,y:g}):{x:y,y:g};y=h.x,g=h.y;var C=a.hasOwnProperty("x"),b=a.hasOwnProperty("y"),r=K,T=Y,f=window;if(v){var w=te(n),_="clientHeight",j="clientWidth";w===Z(n)&&ht(w=bt(n)).position!=="static"&&c==="absolute"&&(_="scrollHeight",j="scrollWidth"),(s===Y||(s===K||s===rt)&&i===Kt)&&(T=ot,g-=(l&&w===f&&f.visualViewport?f.visualViewport.height:w[_])-o.height,g*=p?1:-1),(s===K||(s===Y||s===ot)&&i===Kt)&&(r=rt,y-=(l&&w===f&&f.visualViewport?f.visualViewport.width:w[j])-o.width,y*=p?1:-1)}var L,P=Object.assign({position:c},v&&Wo),H=d===!0?function(V,W){var I=V.x,F=V.y,S=W.devicePixelRatio||1;return{x:Dt(I*S)/S||0,y:Dt(F*S)/S||0}}({x:y,y:g},Z(n)):{x:y,y:g};return y=H.x,g=H.y,p?Object.assign({},P,((L={})[T]=b?"0":"",L[r]=C?"0":"",L.transform=(f.devicePixelRatio||1)<=1?"translate("+y+"px, "+g+"px)":"translate3d("+y+"px, "+g+"px, 0)",L)):Object.assign({},P,((t={})[T]=b?g+"px":"",t[r]=C?y+"px":"",t.transform="",t))}var se={passive:!0},Vo={left:"right",right:"left",bottom:"top",top:"bottom"};function ce(e){return e.replace(/left|right|bottom|top/g,function(t){return Vo[t]})}var Bo={start:"end",end:"start"};function cn(e){return e.replace(/start|end/g,function(t){return Bo[t]})}function Fe(e){var t=Z(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function Ne(e){return Lt(bt(e)).left+Fe(e).scrollLeft}function ze(e){var t=ht(e),n=t.overflow,o=t.overflowX,s=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+s+o)}function Rn(e){return["html","body","#document"].indexOf(lt(e))>=0?e.ownerDocument.body:nt(e)&&ze(e)?e:Rn(be(e))}function It(e,t){var n;t===void 0&&(t=[]);var o=Rn(e),s=o===((n=e.ownerDocument)==null?void 0:n.body),i=Z(o),a=s?[i].concat(i.visualViewport||[],ze(o)?o:[]):o,c=t.concat(a);return s?c:c.concat(It(be(a)))}function We(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function un(e,t,n){return t===Ln?We(function(o,s){var i=Z(o),a=bt(o),c=i.visualViewport,p=a.clientWidth,v=a.clientHeight,d=0,l=0;if(c){p=c.width,v=c.height;var E=Mn();(E||!E&&s==="fixed")&&(d=c.offsetLeft,l=c.offsetTop)}return{width:p,height:v,x:d+Ne(o),y:l}}(e,n)):Tt(t)?function(o,s){var i=Lt(o,!1,s==="fixed");return i.top=i.top+o.clientTop,i.left=i.left+o.clientLeft,i.bottom=i.top+o.clientHeight,i.right=i.left+o.clientWidth,i.width=o.clientWidth,i.height=o.clientHeight,i.x=i.left,i.y=i.top,i}(t,n):We(function(o){var s,i=bt(o),a=Fe(o),c=(s=o.ownerDocument)==null?void 0:s.body,p=Ot(i.scrollWidth,i.clientWidth,c?c.scrollWidth:0,c?c.clientWidth:0),v=Ot(i.scrollHeight,i.clientHeight,c?c.scrollHeight:0,c?c.clientHeight:0),d=-a.scrollLeft+Ne(o),l=-a.scrollTop;return ht(c||i).direction==="rtl"&&(d+=Ot(i.clientWidth,c?c.clientWidth:0)-p),{width:p,height:v,x:d,y:l}}(bt(e)))}function qo(e,t,n,o){var s=t==="clippingParents"?function(p){var v=It(be(p)),d=["absolute","fixed"].indexOf(ht(p).position)>=0&&nt(p)?te(p):p;return Tt(d)?v.filter(function(l){return Tt(l)&&Hn(l,d)&&lt(l)!=="body"}):[]}(e):[].concat(t),i=[].concat(s,[n]),a=i[0],c=i.reduce(function(p,v){var d=un(e,v,o);return p.top=Ot(d.top,p.top),p.right=ve(d.right,p.right),p.bottom=ve(d.bottom,p.bottom),p.left=Ot(d.left,p.left),p},un(e,a,o));return c.width=c.right-c.left,c.height=c.bottom-c.top,c.x=c.left,c.y=c.top,c}function Nn(e){var t,n=e.reference,o=e.element,s=e.placement,i=s?ft(s):null,a=s?kt(s):null,c=n.x+n.width/2-o.width/2,p=n.y+n.height/2-o.height/2;switch(i){case Y:t={x:c,y:n.y-o.height};break;case ot:t={x:c,y:n.y+n.height};break;case rt:t={x:n.x+n.width,y:p};break;case K:t={x:n.x-o.width,y:p};break;default:t={x:n.x,y:n.y}}var v=i?Ue(i):null;if(v!=null){var d=v==="y"?"height":"width";switch(a){case At:t[v]=t[v]-(n[d]/2-o[d]/2);break;case Kt:t[v]=t[v]+(n[d]/2-o[d]/2)}}return t}function Jt(e,t){t===void 0&&(t={});var n=t,o=n.placement,s=o===void 0?e.placement:o,i=n.strategy,a=i===void 0?e.strategy:i,c=n.boundary,p=c===void 0?Ho:c,v=n.rootBoundary,d=v===void 0?Ln:v,l=n.elementContext,E=l===void 0?Vt:l,y=n.altBoundary,x=y!==void 0&&y,g=n.padding,h=g===void 0?0:g,C=Sn(typeof h!="number"?h:Pn(h,Qt)),b=E===Vt?So:Vt,r=e.rects.popper,T=e.elements[x?b:E],f=qo(Tt(T)?T:T.contextElement||bt(e.elements.popper),p,d,a),w=Lt(e.elements.reference),_=Nn({reference:w,element:r,placement:s}),j=We(Object.assign({},r,_)),L=E===Vt?j:w,P={top:f.top-L.top+C.top,bottom:L.bottom-f.bottom+C.bottom,left:f.left-L.left+C.left,right:L.right-f.right+C.right},H=e.modifiersData.offset;if(E===Vt&&H){var V=H[s];Object.keys(P).forEach(function(W){var I=[rt,ot].indexOf(W)>=0?1:-1,F=[Y,ot].indexOf(W)>=0?"y":"x";P[W]+=V[F]*I})}return P}function $o(e,t){t===void 0&&(t={});var n=t,o=n.placement,s=n.boundary,i=n.rootBoundary,a=n.padding,c=n.flipVariations,p=n.allowedAutoPlacements,v=p===void 0?kn:p,d=kt(o),l=d?c?rn:rn.filter(function(x){return kt(x)===d}):Qt,E=l.filter(function(x){return v.indexOf(x)>=0});E.length===0&&(E=l);var y=E.reduce(function(x,g){return x[g]=Jt(e,{placement:g,boundary:s,rootBoundary:i,padding:a})[ft(g)],x},{});return Object.keys(y).sort(function(x,g){return y[x]-y[g]})}const Io={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name;if(!t.modifiersData[o]._skip){for(var s=n.mainAxis,i=s===void 0||s,a=n.altAxis,c=a===void 0||a,p=n.fallbackPlacements,v=n.padding,d=n.boundary,l=n.rootBoundary,E=n.altBoundary,y=n.flipVariations,x=y===void 0||y,g=n.allowedAutoPlacements,h=t.options.placement,C=ft(h),b=p||(C===h||!x?[ce(h)]:function(R){if(ft(R)===Pe)return[];var B=ce(R);return[cn(R),B,cn(B)]}(h)),r=[h].concat(b).reduce(function(R,B){return R.concat(ft(B)===Pe?$o(t,{placement:B,boundary:d,rootBoundary:l,padding:v,flipVariations:x,allowedAutoPlacements:g}):B)},[]),T=t.rects.reference,f=t.rects.popper,w=new Map,_=!0,j=r[0],L=0;L<r.length;L++){var P=r[L],H=ft(P),V=kt(P)===At,W=[Y,ot].indexOf(H)>=0,I=W?"width":"height",F=Jt(t,{placement:P,boundary:d,rootBoundary:l,altBoundary:E,padding:v}),S=W?V?rt:K:V?ot:Y;T[I]>f[I]&&(S=ce(S));var N=ce(S),G=[];if(i&&G.push(F[H]<=0),c&&G.push(F[S]<=0,F[N]<=0),G.every(function(R){return R})){j=P,_=!1;break}w.set(P,G)}if(_)for(var Q=function(R){var B=r.find(function(st){var ct=w.get(st);if(ct)return ct.slice(0,R).every(function(mt){return mt})});if(B)return j=B,"break"},tt=x?3:1;tt>0&&Q(tt)!=="break";tt--);t.placement!==j&&(t.modifiersData[o]._skip=!0,t.placement=j,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function pn(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function fn(e){return[Y,rt,ot,K].some(function(t){return e[t]>=0})}const Uo={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,o=e.name,s=n.offset,i=s===void 0?[0,0]:s,a=kn.reduce(function(d,l){return d[l]=function(E,y,x){var g=ft(E),h=[K,Y].indexOf(g)>=0?-1:1,C=typeof x=="function"?x(Object.assign({},y,{placement:E})):x,b=C[0],r=C[1];return b=b||0,r=(r||0)*h,[K,rt].indexOf(g)>=0?{x:r,y:b}:{x:b,y:r}}(l,t.rects,i),d},{}),c=a[t.placement],p=c.x,v=c.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=p,t.modifiersData.popperOffsets.y+=v),t.modifiersData[o]=a}},Fo={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name,s=n.mainAxis,i=s===void 0||s,a=n.altAxis,c=a!==void 0&&a,p=n.boundary,v=n.rootBoundary,d=n.altBoundary,l=n.padding,E=n.tether,y=E===void 0||E,x=n.tetherOffset,g=x===void 0?0:x,h=Jt(t,{boundary:p,rootBoundary:v,padding:l,altBoundary:d}),C=ft(t.placement),b=kt(t.placement),r=!b,T=Ue(C),f=T==="x"?"y":"x",w=t.modifiersData.popperOffsets,_=t.rects.reference,j=t.rects.popper,L=typeof g=="function"?g(Object.assign({},t.rects,{placement:t.placement})):g,P=typeof L=="number"?{mainAxis:L,altAxis:L}:Object.assign({mainAxis:0,altAxis:0},L),H=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,V={x:0,y:0};if(w){if(i){var W,I=T==="y"?Y:K,F=T==="y"?ot:rt,S=T==="y"?"height":"width",N=w[T],G=N+h[I],Q=N-h[F],tt=y?-j[S]/2:0,R=b===At?_[S]:j[S],B=b===At?-j[S]:-_[S],st=t.elements.arrow,ct=y&&st?Ie(st):{width:0,height:0},mt=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},St=mt[I],dt=mt[F],wt=Bt(0,_[S],ct[S]),ee=r?_[S]/2-tt-wt-St-P.mainAxis:R-wt-St-P.mainAxis,ne=r?-_[S]/2+tt+wt+dt+P.mainAxis:B+wt+dt+P.mainAxis,_t=t.elements.arrow&&te(t.elements.arrow),oe=_t?T==="y"?_t.clientTop||0:_t.clientLeft||0:0,Pt=(W=H==null?void 0:H[T])!=null?W:0,re=N+ne-Pt,Rt=Bt(y?ve(G,N+ee-Pt-oe):G,N,y?Ot(Q,re):Q);w[T]=Rt,V[T]=Rt-N}if(c){var Nt,Wt=T==="x"?Y:K,ie=T==="x"?ot:rt,et=w[f],u=f==="y"?"height":"width",m=et+h[Wt],O=et-h[ie],A=[Y,K].indexOf(C)!==-1,k=(Nt=H==null?void 0:H[f])!=null?Nt:0,q=A?m:et-_[u]-j[u]-k+P.altAxis,U=A?et+_[u]+j[u]-k-P.altAxis:O,z=y&&A?function($,vt,X){var J=Bt($,vt,X);return J>X?X:J}(q,et,U):Bt(y?q:m,et,y?U:O);w[f]=z,V[f]=z-et}t.modifiersData[o]=V}},requiresIfExists:["offset"]};function zo(e,t,n){n===void 0&&(n=!1);var o,s,i=nt(t),a=nt(t)&&function(l){var E=l.getBoundingClientRect(),y=Dt(E.width)/l.offsetWidth||1,x=Dt(E.height)/l.offsetHeight||1;return y!==1||x!==1}(t),c=bt(t),p=Lt(e,a,n),v={scrollLeft:0,scrollTop:0},d={x:0,y:0};return(i||!i&&!n)&&((lt(t)!=="body"||ze(c))&&(v=(o=t)!==Z(o)&&nt(o)?{scrollLeft:(s=o).scrollLeft,scrollTop:s.scrollTop}:Fe(o)),nt(t)?((d=Lt(t,!0)).x+=t.clientLeft,d.y+=t.clientTop):c&&(d.x=Ne(c))),{x:p.left+v.scrollLeft-d.x,y:p.top+v.scrollTop-d.y,width:p.width,height:p.height}}function Xo(e){var t=new Map,n=new Set,o=[];function s(i){n.add(i.name),[].concat(i.requires||[],i.requiresIfExists||[]).forEach(function(a){if(!n.has(a)){var c=t.get(a);c&&s(c)}}),o.push(i)}return e.forEach(function(i){t.set(i.name,i)}),e.forEach(function(i){n.has(i.name)||s(i)}),o}var ln={placement:"bottom",modifiers:[],strategy:"absolute"};function dn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(o){return!(o&&typeof o.getBoundingClientRect=="function")})}function Yo(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,o=n===void 0?[]:n,s=t.defaultOptions,i=s===void 0?ln:s;return function(a,c,p){p===void 0&&(p=i);var v,d,l={placement:"bottom",orderedModifiers:[],options:Object.assign({},ln,i),modifiersData:{},elements:{reference:a,popper:c},attributes:{},styles:{}},E=[],y=!1,x={state:l,setOptions:function(h){var C=typeof h=="function"?h(l.options):h;g(),l.options=Object.assign({},i,l.options,C),l.scrollParents={reference:Tt(a)?It(a):a.contextElement?It(a.contextElement):[],popper:It(c)};var b,r,T=function(f){var w=Xo(f);return Po.reduce(function(_,j){return _.concat(w.filter(function(L){return L.phase===j}))},[])}((b=[].concat(o,l.options.modifiers),r=b.reduce(function(f,w){var _=f[w.name];return f[w.name]=_?Object.assign({},_,w,{options:Object.assign({},_.options,w.options),data:Object.assign({},_.data,w.data)}):w,f},{}),Object.keys(r).map(function(f){return r[f]})));return l.orderedModifiers=T.filter(function(f){return f.enabled}),l.orderedModifiers.forEach(function(f){var w=f.name,_=f.options,j=_===void 0?{}:_,L=f.effect;if(typeof L=="function"){var P=L({state:l,name:w,instance:x,options:j}),H=function(){};E.push(P||H)}}),x.update()},forceUpdate:function(){if(!y){var h=l.elements,C=h.reference,b=h.popper;if(dn(C,b)){l.rects={reference:zo(C,te(b),l.options.strategy==="fixed"),popper:Ie(b)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach(function(L){return l.modifiersData[L.name]=Object.assign({},L.data)});for(var r=0;r<l.orderedModifiers.length;r++)if(l.reset!==!0){var T=l.orderedModifiers[r],f=T.fn,w=T.options,_=w===void 0?{}:w,j=T.name;typeof f=="function"&&(l=f({state:l,options:_,name:j,instance:x})||l)}else l.reset=!1,r=-1}}},update:(v=function(){return new Promise(function(h){x.forceUpdate(),h(l)})},function(){return d||(d=new Promise(function(h){Promise.resolve().then(function(){d=void 0,h(v())})})),d}),destroy:function(){g(),y=!0}};if(!dn(a,c))return x;function g(){E.forEach(function(h){return h()}),E=[]}return x.setOptions(p).then(function(h){!y&&p.onFirstUpdate&&p.onFirstUpdate(h)}),x}}var Ko=Yo({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,o=e.options,s=o.scroll,i=s===void 0||s,a=o.resize,c=a===void 0||a,p=Z(t.elements.popper),v=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&v.forEach(function(d){d.addEventListener("scroll",n.update,se)}),c&&p.addEventListener("resize",n.update,se),function(){i&&v.forEach(function(d){d.removeEventListener("scroll",n.update,se)}),c&&p.removeEventListener("resize",n.update,se)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Nn({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,o=n.gpuAcceleration,s=o===void 0||o,i=n.adaptive,a=i===void 0||i,c=n.roundOffsets,p=c===void 0||c,v={placement:ft(t.placement),variation:kt(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:s,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,sn(Object.assign({},v,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:p})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,sn(Object.assign({},v,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:p})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},jn,Uo,Io,Fo,No,{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,o=t.rects.reference,s=t.rects.popper,i=t.modifiersData.preventOverflow,a=Jt(t,{elementContext:"reference"}),c=Jt(t,{altBoundary:!0}),p=pn(a,o),v=pn(c,s,i),d=fn(p),l=fn(v);t.modifiersData[n]={referenceClippingOffsets:p,popperEscapeOffsets:v,isReferenceHidden:d,hasPopperEscaped:l},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":d,"data-popper-escaped":l})}}]}),Wn="tippy-content",Jo="tippy-backdrop",Vn="tippy-arrow",Bn="tippy-svg-arrow",xt={passive:!0,capture:!0},qn=function(){return document.body};function Te(e,t,n){if(Array.isArray(e)){var o=e[t];return o??(Array.isArray(n)?n[t]:n)}return e}function Xe(e,t){var n={}.toString.call(e);return n.indexOf("[object")===0&&n.indexOf(t+"]")>-1}function $n(e,t){return typeof e=="function"?e.apply(void 0,t):e}function vn(e,t){return t===0?e:function(o){clearTimeout(n),n=setTimeout(function(){e(o)},t)};var n}function Ct(e){return[].concat(e)}function hn(e,t){e.indexOf(t)===-1&&e.push(t)}function he(e){return[].slice.call(e)}function mn(e){return Object.keys(e).reduce(function(t,n){return e[n]!==void 0&&(t[n]=e[n]),t},{})}function Ut(){return document.createElement("div")}function we(e){return["Element","Fragment"].some(function(t){return Xe(e,t)})}function Zo(e){return we(e)?[e]:function(t){return Xe(t,"NodeList")}(e)?he(e):Array.isArray(e)?e:he(document.querySelectorAll(e))}function _e(e,t){e.forEach(function(n){n&&(n.style.transitionDuration=t+"ms")})}function gn(e,t){e.forEach(function(n){n&&n.setAttribute("data-state",t)})}function Ce(e,t,n){var o=t+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(s){e[o](s,n)})}function yn(e,t){for(var n=t;n;){var o;if(e.contains(n))return!0;n=n.getRootNode==null||(o=n.getRootNode())==null?void 0:o.host}return!1}var pt={isTouch:!1},bn=0;function Go(){pt.isTouch||(pt.isTouch=!0,window.performance&&document.addEventListener("mousemove",In))}function In(){var e=performance.now();e-bn<20&&(pt.isTouch=!1,document.removeEventListener("mousemove",In)),bn=e}function Qo(){var e,t=document.activeElement;if((e=t)&&e._tippy&&e._tippy.reference===e){var n=t._tippy;t.blur&&!n.state.isVisible&&t.blur()}}var tr=typeof window<"u"&&typeof document<"u"&&!!window.msCrypto,at=Object.assign({appendTo:qn,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),er=Object.keys(at);function Un(e){var t=(e.plugins||[]).reduce(function(n,o){var s,i=o.name,a=o.defaultValue;return i&&(n[i]=e[i]!==void 0?e[i]:(s=at[i])!=null?s:a),n},{});return Object.assign({},e,t)}function wn(e,t){var n=Object.assign({},t,{content:$n(t.content,[e])},t.ignoreAttributes?{}:function(o,s){return(s?Object.keys(Un(Object.assign({},at,{plugins:s}))):er).reduce(function(i,a){var c=(o.getAttribute("data-tippy-"+a)||"").trim();if(!c)return i;if(a==="content")i[a]=c;else try{i[a]=JSON.parse(c)}catch{i[a]=c}return i},{})}(e,t.plugins));return n.aria=Object.assign({},at.aria,n.aria),n.aria={expanded:n.aria.expanded==="auto"?t.interactive:n.aria.expanded,content:n.aria.content==="auto"?t.interactive?null:"describedby":n.aria.content},n}var nr=function(){return"innerHTML"};function Ve(e,t){e[nr()]=t}function xn(e){var t=Ut();return e===!0?t.className=Vn:(t.className=Bn,we(e)?t.appendChild(e):Ve(t,e)),t}function En(e,t){we(t.content)?(Ve(e,""),e.appendChild(t.content)):typeof t.content!="function"&&(t.allowHTML?Ve(e,t.content):e.textContent=t.content)}function Be(e){var t=e.firstElementChild,n=he(t.children);return{box:t,content:n.find(function(o){return o.classList.contains(Wn)}),arrow:n.find(function(o){return o.classList.contains(Vn)||o.classList.contains(Bn)}),backdrop:n.find(function(o){return o.classList.contains(Jo)})}}function Fn(e){var t=Ut(),n=Ut();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var o=Ut();function s(i,a){var c=Be(t),p=c.box,v=c.content,d=c.arrow;a.theme?p.setAttribute("data-theme",a.theme):p.removeAttribute("data-theme"),typeof a.animation=="string"?p.setAttribute("data-animation",a.animation):p.removeAttribute("data-animation"),a.inertia?p.setAttribute("data-inertia",""):p.removeAttribute("data-inertia"),p.style.maxWidth=typeof a.maxWidth=="number"?a.maxWidth+"px":a.maxWidth,a.role?p.setAttribute("role",a.role):p.removeAttribute("role"),i.content===a.content&&i.allowHTML===a.allowHTML||En(v,e.props),a.arrow?d?i.arrow!==a.arrow&&(p.removeChild(d),p.appendChild(xn(a.arrow))):p.appendChild(xn(a.arrow)):d&&p.removeChild(d)}return o.className=Wn,o.setAttribute("data-state","hidden"),En(o,e.props),t.appendChild(n),n.appendChild(o),s(e.props,e.props),{popper:t,onUpdate:s}}Fn.$$tippy=!0;var or=1,ue=[],Ae=[];function rr(e,t){var n,o,s,i,a,c,p,v,d=wn(e,Object.assign({},at,Un(mn(t)))),l=!1,E=!1,y=!1,x=!1,g=[],h=vn(_t,d.interactiveDebounce),C=or++,b=(v=d.plugins).filter(function(u,m){return v.indexOf(u)===m}),r={id:C,reference:e,popper:Ut(),popperInstance:null,props:d,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:b,clearDelayTimeouts:function(){clearTimeout(n),clearTimeout(o),cancelAnimationFrame(s)},setProps:function(u){if(!r.state.isDestroyed){N("onBeforeUpdate",[r,u]),ee();var m=r.props,O=wn(e,Object.assign({},m,mn(u),{ignoreAttributes:!0}));r.props=O,wt(),m.interactiveDebounce!==O.interactiveDebounce&&(tt(),h=vn(_t,O.interactiveDebounce)),m.triggerTarget&&!O.triggerTarget?Ct(m.triggerTarget).forEach(function(A){A.removeAttribute("aria-expanded")}):O.triggerTarget&&e.removeAttribute("aria-expanded"),Q(),S(),w&&w(m,O),r.popperInstance&&(Rt(),Wt().forEach(function(A){requestAnimationFrame(A._tippy.popperInstance.forceUpdate)})),N("onAfterUpdate",[r,u])}},setContent:function(u){r.setProps({content:u})},show:function(){var u=r.state.isVisible,m=r.state.isDestroyed,O=!r.state.isEnabled,A=pt.isTouch&&!r.props.touch,k=Te(r.props.duration,0,at.duration);if(!(u||m||O||A)&&!V().hasAttribute("disabled")&&(N("onShow",[r],!1),r.props.onShow(r)!==!1)){if(r.state.isVisible=!0,H()&&(f.style.visibility="visible"),S(),ct(),r.state.isMounted||(f.style.transition="none"),H()){var q=I();_e([q.box,q.content],0)}c=function(){var U;if(r.state.isVisible&&!x){if(x=!0,f.offsetHeight,f.style.transition=r.props.moveTransition,H()&&r.props.animation){var z=I(),$=z.box,vt=z.content;_e([$,vt],k),gn([$,vt],"visible")}G(),Q(),hn(Ae,r),(U=r.popperInstance)==null||U.forceUpdate(),N("onMount",[r]),r.props.animation&&H()&&function(X,J){St(X,J)}(k,function(){r.state.isShown=!0,N("onShown",[r])})}},function(){var U,z=r.props.appendTo,$=V();U=r.props.interactive&&z===qn||z==="parent"?$.parentNode:$n(z,[$]),U.contains(f)||U.appendChild(f),r.state.isMounted=!0,Rt()}()}},hide:function(){var u=!r.state.isVisible,m=r.state.isDestroyed,O=!r.state.isEnabled,A=Te(r.props.duration,1,at.duration);if(!(u||m||O)&&(N("onHide",[r],!1),r.props.onHide(r)!==!1)){if(r.state.isVisible=!1,r.state.isShown=!1,x=!1,l=!1,H()&&(f.style.visibility="hidden"),tt(),mt(),S(!0),H()){var k=I(),q=k.box,U=k.content;r.props.animation&&(_e([q,U],A),gn([q,U],"hidden"))}G(),Q(),r.props.animation?H()&&function(z,$){St(z,function(){!r.state.isVisible&&f.parentNode&&f.parentNode.contains(f)&&$()})}(A,r.unmount):r.unmount()}},hideWithInteractivity:function(u){W().addEventListener("mousemove",h),hn(ue,h),h(u)},enable:function(){r.state.isEnabled=!0},disable:function(){r.hide(),r.state.isEnabled=!1},unmount:function(){r.state.isVisible&&r.hide(),r.state.isMounted&&(Nt(),Wt().forEach(function(u){u._tippy.unmount()}),f.parentNode&&f.parentNode.removeChild(f),Ae=Ae.filter(function(u){return u!==r}),r.state.isMounted=!1,N("onHidden",[r]))},destroy:function(){r.state.isDestroyed||(r.clearDelayTimeouts(),r.unmount(),ee(),delete e._tippy,r.state.isDestroyed=!0,N("onDestroy",[r]))}};if(!d.render)return r;var T=d.render(r),f=T.popper,w=T.onUpdate;f.setAttribute("data-tippy-root",""),f.id="tippy-"+r.id,r.popper=f,e._tippy=r,f._tippy=r;var _=b.map(function(u){return u.fn(r)}),j=e.hasAttribute("aria-expanded");return wt(),Q(),S(),N("onCreate",[r]),d.showOnCreate&&ie(),f.addEventListener("mouseenter",function(){r.props.interactive&&r.state.isVisible&&r.clearDelayTimeouts()}),f.addEventListener("mouseleave",function(){r.props.interactive&&r.props.trigger.indexOf("mouseenter")>=0&&W().addEventListener("mousemove",h)}),r;function L(){var u=r.props.touch;return Array.isArray(u)?u:[u,0]}function P(){return L()[0]==="hold"}function H(){var u;return!((u=r.props.render)==null||!u.$$tippy)}function V(){return p||e}function W(){var u,m,O=V().parentNode;return O&&(m=Ct(O)[0])!=null&&(u=m.ownerDocument)!=null&&u.body?m.ownerDocument:document}function I(){return Be(f)}function F(u){return r.state.isMounted&&!r.state.isVisible||pt.isTouch||i&&i.type==="focus"?0:Te(r.props.delay,u?0:1,at.delay)}function S(u){u===void 0&&(u=!1),f.style.pointerEvents=r.props.interactive&&!u?"":"none",f.style.zIndex=""+r.props.zIndex}function N(u,m,O){var A;O===void 0&&(O=!0),_.forEach(function(k){k[u]&&k[u].apply(k,m)}),O&&(A=r.props)[u].apply(A,m)}function G(){var u=r.props.aria;if(u.content){var m="aria-"+u.content,O=f.id;Ct(r.props.triggerTarget||e).forEach(function(A){var k=A.getAttribute(m);if(r.state.isVisible)A.setAttribute(m,k?k+" "+O:O);else{var q=k&&k.replace(O,"").trim();q?A.setAttribute(m,q):A.removeAttribute(m)}})}}function Q(){!j&&r.props.aria.expanded&&Ct(r.props.triggerTarget||e).forEach(function(u){r.props.interactive?u.setAttribute("aria-expanded",r.state.isVisible&&u===V()?"true":"false"):u.removeAttribute("aria-expanded")})}function tt(){W().removeEventListener("mousemove",h),ue=ue.filter(function(u){return u!==h})}function R(u){if(!pt.isTouch||!y&&u.type!=="mousedown"){var m=u.composedPath&&u.composedPath()[0]||u.target;if(!r.props.interactive||!yn(f,m)){if(Ct(r.props.triggerTarget||e).some(function(O){return yn(O,m)})){if(pt.isTouch||r.state.isVisible&&r.props.trigger.indexOf("click")>=0)return}else N("onClickOutside",[r,u]);r.props.hideOnClick===!0&&(r.clearDelayTimeouts(),r.hide(),E=!0,setTimeout(function(){E=!1}),r.state.isMounted||mt())}}}function B(){y=!0}function st(){y=!1}function ct(){var u=W();u.addEventListener("mousedown",R,!0),u.addEventListener("touchend",R,xt),u.addEventListener("touchstart",st,xt),u.addEventListener("touchmove",B,xt)}function mt(){var u=W();u.removeEventListener("mousedown",R,!0),u.removeEventListener("touchend",R,xt),u.removeEventListener("touchstart",st,xt),u.removeEventListener("touchmove",B,xt)}function St(u,m){var O=I().box;function A(k){k.target===O&&(Ce(O,"remove",A),m())}if(u===0)return m();Ce(O,"remove",a),Ce(O,"add",A),a=A}function dt(u,m,O){O===void 0&&(O=!1),Ct(r.props.triggerTarget||e).forEach(function(A){A.addEventListener(u,m,O),g.push({node:A,eventType:u,handler:m,options:O})})}function wt(){var u;P()&&(dt("touchstart",ne,{passive:!0}),dt("touchend",oe,{passive:!0})),(u=r.props.trigger,u.split(/\s+/).filter(Boolean)).forEach(function(m){if(m!=="manual")switch(dt(m,ne),m){case"mouseenter":dt("mouseleave",oe);break;case"focus":dt(tr?"focusout":"blur",Pt);break;case"focusin":dt("focusout",Pt)}})}function ee(){g.forEach(function(u){var m=u.node,O=u.eventType,A=u.handler,k=u.options;m.removeEventListener(O,A,k)}),g=[]}function ne(u){var m,O=!1;if(r.state.isEnabled&&!re(u)&&!E){var A=((m=i)==null?void 0:m.type)==="focus";i=u,p=u.currentTarget,Q(),!r.state.isVisible&&Xe(u,"MouseEvent")&&ue.forEach(function(k){return k(u)}),u.type==="click"&&(r.props.trigger.indexOf("mouseenter")<0||l)&&r.props.hideOnClick!==!1&&r.state.isVisible?O=!0:ie(u),u.type==="click"&&(l=!O),O&&!A&&et(u)}}function _t(u){var m=u.target,O=V().contains(m)||f.contains(m);u.type==="mousemove"&&O||function(A,k){var q=k.clientX,U=k.clientY;return A.every(function(z){var $=z.popperRect,vt=z.popperState,X=z.props.interactiveBorder,J=vt.placement.split("-")[0],ut=vt.modifiersData.offset;if(!ut)return!0;var zn=J==="bottom"?ut.top.y:0,Xn=J==="top"?ut.bottom.y:0,Yn=J==="right"?ut.left.x:0,Kn=J==="left"?ut.right.x:0,Jn=$.top-U+zn>X,Zn=U-$.bottom-Xn>X,Gn=$.left-q+Yn>X,Qn=q-$.right-Kn>X;return Jn||Zn||Gn||Qn})}(Wt().concat(f).map(function(A){var k,q=(k=A._tippy.popperInstance)==null?void 0:k.state;return q?{popperRect:A.getBoundingClientRect(),popperState:q,props:d}:null}).filter(Boolean),u)&&(tt(),et(u))}function oe(u){re(u)||r.props.trigger.indexOf("click")>=0&&l||(r.props.interactive?r.hideWithInteractivity(u):et(u))}function Pt(u){r.props.trigger.indexOf("focusin")<0&&u.target!==V()||r.props.interactive&&u.relatedTarget&&f.contains(u.relatedTarget)||et(u)}function re(u){return!!pt.isTouch&&P()!==u.type.indexOf("touch")>=0}function Rt(){Nt();var u=r.props,m=u.popperOptions,O=u.placement,A=u.offset,k=u.getReferenceClientRect,q=u.moveTransition,U=H()?Be(f).arrow:null,z=k?{getBoundingClientRect:k,contextElement:k.contextElement||V()}:e,$=[{name:"offset",options:{offset:A}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!q}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(vt){var X=vt.state;if(H()){var J=I().box;["placement","reference-hidden","escaped"].forEach(function(ut){ut==="placement"?J.setAttribute("data-placement",X.placement):X.attributes.popper["data-popper-"+ut]?J.setAttribute("data-"+ut,""):J.removeAttribute("data-"+ut)}),X.attributes.popper={}}}}];H()&&U&&$.push({name:"arrow",options:{element:U,padding:3}}),$.push.apply($,(m==null?void 0:m.modifiers)||[]),r.popperInstance=Ko(z,f,Object.assign({},m,{placement:O,onFirstUpdate:c,modifiers:$}))}function Nt(){r.popperInstance&&(r.popperInstance.destroy(),r.popperInstance=null)}function Wt(){return he(f.querySelectorAll("[data-tippy-root]"))}function ie(u){r.clearDelayTimeouts(),u&&N("onTrigger",[r,u]),ct();var m=F(!0),O=L(),A=O[0],k=O[1];pt.isTouch&&A==="hold"&&k&&(m=k),m?n=setTimeout(function(){r.show()},m):r.show()}function et(u){if(r.clearDelayTimeouts(),N("onUntrigger",[r,u]),r.state.isVisible){if(!(r.props.trigger.indexOf("mouseenter")>=0&&r.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(u.type)>=0&&l)){var m=F(!1);m?o=setTimeout(function(){r.state.isVisible&&r.hide()},m):s=requestAnimationFrame(function(){r.hide()})}}else mt()}}function qt(e,t){t===void 0&&(t={});var n=at.plugins.concat(t.plugins||[]);document.addEventListener("touchstart",Go,xt),window.addEventListener("blur",Qo);var o=Object.assign({},t,{plugins:n}),s=Zo(e).reduce(function(i,a){var c=a&&rr(a,o);return c&&i.push(c),i},[]);return we(e)?s[0]:s}qt.defaultProps=at,qt.setDefaultProps=function(e){Object.keys(e).forEach(function(t){at[t]=e[t]})},qt.currentInput=pt,Object.assign({},jn,{effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow)}}),qt.setDefaultProps({render:Fn});var jt=(e=>(e.Hover="hover",e.Click="click",e))(jt||{});const Ft=class Ft extends Event{constructor(){super(Ft.eventType,{bubbles:!0})}static isEvent(t){return t.type===Ft.eventType}};M(Ft,"eventType","augment-ds-event__close-tooltip-request");let gt=Ft;const ge=class ge{constructor(t){M(this,"debouncedHoverStart");M(this,"debouncedHoverEnd");M(this,"handleMouseEnter",()=>{var t,n;(t=this.debouncedHoverEnd)==null||t.cancel(),(n=this.debouncedHoverStart)==null||n.call(this)});M(this,"handleMouseLeave",()=>{var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.call(this)});M(this,"handleMouseMove",()=>{var t,n;(t=this.debouncedHoverEnd)==null||t.cancel(),(n=this.debouncedHoverStart)==null||n.call(this)});M(this,"cancelHovers",()=>{var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.cancel()});this.debouncedHoverStart=on(t.onHoverStart,t.hoverTriggerDuration),this.debouncedHoverEnd=on(t.onHoverEnd,ge.DEFAULT_HOVER_END_DEBOUNCE_MS)}destroy(){var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.cancel()}};M(ge,"DEFAULT_HOVER_END_DEBOUNCE_MS",67);let me=ge;function qe(e,t){return e.addEventListener("mouseenter",t.handleMouseEnter),e.addEventListener("mouseleave",t.handleMouseLeave),e.addEventListener("mousemove",t.handleMouseMove),{destroy(){e.removeEventListener("mouseenter",t.handleMouseEnter),e.removeEventListener("mouseleave",t.handleMouseLeave),e.removeEventListener("mousemove",t.handleMouseMove)}}}const ir=Symbol("hover-action-context");function Er(e=100){const t=Tn(!1);On(ir,t);const n=new me({onHoverStart(){t.set(!0)},onHoverEnd(){t.set(!1)},hoverTriggerDuration:e});return function(o){return qe(o,n)}}const zt=class zt{constructor(t){M(this,"_state");M(this,"_tippy");M(this,"_triggerElement");M(this,"_contentElement");M(this,"_contentProps");M(this,"_hoverContext");M(this,"_referenceClientRect");M(this,"_hasPointerEvents",!0);M(this,"_setOpen",t=>{var n,o;this._isOpen!==t&&(this._state.update(s=>({...s,open:t})),(o=(n=this._opts).onOpenChange)==null||o.call(n,t))});M(this,"openTooltip",()=>{this.internalControlSetOpen(!0)});M(this,"closeTooltip",()=>{this.internalControlSetOpen(!1)});M(this,"toggleTooltip",()=>{this.internalControlSetOpen(!this._isOpen)});M(this,"forceControlSetOpen",t=>{t!==void 0&&this._setOpen(t)});M(this,"externalControlSetOpen",t=>{this._opts.open=t,this.forceControlSetOpen(t)});M(this,"updateTippyTheme",t=>{this._opts.tippyTheme!==t&&(this._opts.tippyTheme=t,this._updateTippy())});M(this,"internalControlSetOpen",t=>{this._isExternallyControlled||this._setOpen(t)});M(this,"_updateTippy",()=>{var n;if(!this._triggerElement||!this._contentElement||!this._contentProps)return(n=this._tippy)==null||n.destroy(),void(this._tippy=void 0);const t={trigger:"manual",showOnCreate:this._isOpen,offset:this._opts.offset??[0,2],interactive:this._hasPointerEvents,content:this._contentElement,popperOptions:{strategy:"fixed",modifiers:[{name:"preventOverflow",options:{padding:this._opts.nested?12:0}}]},duration:0,delay:0,placement:ar(this._contentProps),hideOnClick:!0,appendTo:this._opts.nested?this._triggerElement:document.body,theme:this._opts.tippyTheme};if(this._referenceClientRect!==void 0){const o=this._referenceClientRect;t.getReferenceClientRect=()=>o}if(this._tippy!==void 0)this._tippy.setProps(t);else{const o=this._state.subscribe(s=>{var i,a;s.open?(i=this._tippy)==null||i.show():(a=this._tippy)==null||a.hide()});this._tippy=qt(this._triggerElement,{...t,onDestroy:o})}});M(this,"update",()=>{var t,n;(n=(t=this._tippy)==null?void 0:t.popperInstance)==null||n.update()});M(this,"registerTrigger",(t,n)=>{this._triggerElement=t,this._referenceClientRect=n;const o=this._hoverContext&&qe(this._triggerElement,this._hoverContext);return this._updateTippy(),{update:s=>{this._referenceClientRect=s,this._updateTippy()},destroy:()=>{o==null||o.destroy(),this._triggerElement=void 0,this._updateTippy()}}});M(this,"registerContents",(t,n)=>{t.remove(),this._contentElement=t,this._contentProps=n;const o=this._hoverContext&&qe(this._contentElement,this._hoverContext);this._updateTippy();const s=function(i,a){const c=new ResizeObserver(()=>a());return c.observe(i),()=>c.disconnect()}(t,this.update);return{destroy:()=>{o==null||o.destroy(),this._contentElement=void 0,this._updateTippy(),s()},update:i=>{n={...n,...i},this._contentProps=n,this._updateTippy()}}});M(this,"requestClose",()=>{var t;(t=this._contentElement)==null||t.dispatchEvent(new gt)});this._opts=t,this._state=Tn({open:this._opts.open??this._opts.defaultOpen??!1}),this.supportsHover&&(this._hoverContext=new me({hoverTriggerDuration:this.delayDurationMs,onHoverStart:()=>{this.openTooltip(),this._opts.onHoverStart()},onHoverEnd:()=>{this.closeTooltip(),this._opts.onHoverEnd()}})),this._hasPointerEvents=this._opts.hasPointerEvents??!0}get supportsHover(){return this._opts.triggerOn.includes(jt.Hover)}get supportsClick(){return this._opts.triggerOn.includes(jt.Click)}get triggerElement(){return this._triggerElement}get contentElement(){return this._contentElement}get state(){return this._state}get delayDurationMs(){return this._opts.delayDurationMs??zt.DEFAULT_DELAY_DURATION_MS}get _isExternallyControlled(){const{defaultOpen:t,open:n}=this._opts;return n!==void 0&&(t!==void 0&&console.warn("`defaultOpen` has no effect when `open` is provided"),!0)}get _isOpen(){return ro(this._state).open}};M(zt,"CONTEXT_KEY","augment-tooltip-context"),M(zt,"DEFAULT_DELAY_DURATION_MS",250);let Mt=zt;function ar(e){return e.align==="center"?e.side:`${e.side}-${e.align}`}function sr(e,t){Zt(t,!1);let n=D(t,"defaultOpen",24,()=>{}),o=D(t,"open",24,()=>{}),s=D(t,"onOpenChange",24,()=>{}),i=D(t,"delayDurationMs",24,()=>{}),a=D(t,"nested",8,!0),c=D(t,"hasPointerEvents",8,!0),p=D(t,"offset",24,()=>{}),v=D(t,"onHoverStart",8,()=>{}),d=D(t,"onHoverEnd",8,()=>{}),l=D(t,"triggerOn",24,()=>[jt.Hover,jt.Click]);const E=()=>g.openTooltip(),y=()=>g.closeTooltip();let x=D(t,"tippyTheme",24,()=>{});const g=new Mt({defaultOpen:n(),open:o(),onOpenChange:s(),delayDurationMs:i(),nested:a(),onHoverStart:v(),onHoverEnd:d(),triggerOn:l(),tippyTheme:x(),hasPointerEvents:c(),offset:p()});On(Mt.CONTEXT_KEY,g),Le(()=>ke(o()),()=>{g.externalControlSetOpen(o())}),Le(()=>ke(x()),()=>{g.updateTippyTheme(x())}),_n(),ye();var h=pe(),C=$t(h);return Yt(C,t,"default",{},null),it(e,h),de(t,"requestOpen",E),de(t,"requestClose",y),Gt({requestOpen:E,requestClose:y})}var cr=Ht('<div role="button" tabindex="-1"><!></div>');function ur(e,t){Zt(t,!1);let n=D(t,"referenceClientRect",24,()=>{}),o=D(t,"class",8,"");const s=Cn(Mt.CONTEXT_KEY),i=p=>{s.supportsClick&&(s.toggleTooltip(),p.stopPropagation())};ye();var a=cr(),c=Xt(a);Yt(c,t,"default",{},null),fe(()=>Et("click",a,i)),fe(()=>Et("keydown",a,function(p){He.call(this,t,p)})),Dn(a,(p,v)=>{var d;return(d=s.registerTrigger)==null?void 0:d.call(s,p,v)},n),le(()=>An(a,1,`l-tooltip-trigger ${o()}`)),it(e,a),Gt()}var pr=Ht('<div role="button" tabindex="-1"><!></div>');function fr(e,t){Zt(t,!1);const[n,o]=ho(),s=()=>Ge(E,"$state",n),i=()=>Ge(C,"$openState",n);let a=D(t,"onEscapeKeyDown",8,()=>{}),c=D(t,"onClickOutside",8,()=>{}),p=D(t,"onRequestClose",8,()=>{}),v=D(t,"side",8,"top"),d=D(t,"align",8,"center");const l=Cn(Mt.CONTEXT_KEY),E=l.state,y=f=>{f.target!==null&&f.target instanceof Node&&l.contentElement&&l.triggerElement&&s().open&&(f.composedPath().includes(l.contentElement)||f.composedPath().includes(l.triggerElement)||(l.closeTooltip(),c()(f)))},x=f=>{f.target!==null&&f.target instanceof Node&&l.contentElement&&s().open&&f.key==="Escape"&&(l.closeTooltip(),a()(f))},g=f=>{var w;if(gt.isEvent(f)&&f.target&&((w=l.contentElement)!=null&&w.contains(f.target)))return l.closeTooltip(),p()(f),void f.stopPropagation()},h=f=>{f.target===window&&l.requestClose()},C=io(E,f=>f.open);ao(()=>{var f;(f=l.contentElement)==null||f.removeEventListener(gt.eventType,g)}),Le(()=>(i(),gt),()=>{l.contentElement&&(i()?l.contentElement.addEventListener(gt.eventType,g):l.contentElement.removeEventListener(gt.eventType,g))}),_n(),ye();var b=pr();let r;Et("click",xe,function(...f){var w;(w=s().open?y:void 0)==null||w.apply(this,f)},!0),Et("keydown",xe,function(...f){var w;(w=s().open?x:void 0)==null||w.apply(this,f)},!0),Et("blur",xe,function(...f){var w;(w=s().open?h:void 0)==null||w.apply(this,f)},!0);var T=Xt(b);Yt(T,t,"default",{},null),Dn(b,(f,w)=>{var _;return(_=l.registerContents)==null?void 0:_.call(l,f,w)},()=>({side:v(),align:d()})),fe(()=>Et("click",b,tn(function(f){He.call(this,t,f)}))),fe(()=>Et("keydown",b,tn(function(f){He.call(this,t,f)}))),le(f=>{r=An(b,1,"l-tooltip-contents svelte-1s7j18e",null,r,f),Ye(b,"data-position-side",v()),Ye(b,"data-position-align",d())},[()=>({"l-tooltip-contents--open":s().open})],je),it(e,b),Gt(),o()}const De={Root:sr,Trigger:ur,Content:fr};var lr=Ht('<div class="svelte-hdzv5n"><!></div>'),dr=Ht("<!> <!>",1);function Or(e,t){const n=mo(t);Zt(t,!1);let o=D(t,"content",24,()=>{}),s=D(t,"width",24,()=>{}),i=D(t,"minWidth",24,()=>{}),a=D(t,"maxWidth",8,"250px"),c=D(t,"delayDurationMs",24,()=>{}),p=D(t,"triggerOn",24,()=>[jt.Hover]),v=D(t,"side",8,"top"),d=D(t,"nested",8,!1),l=D(t,"hasPointerEvents",24,()=>{}),E=D(t,"offset",24,()=>v()==="top"||v()==="bottom"?[0,5]:[5,0]),y=D(t,"open",24,()=>{}),x=D(t,"align",8,"center"),g=D(t,"class",8,""),h=D(t,"onOpenChange",24,()=>{}),C=D(t,"referenceClientRect",24,()=>{}),b=D(t,"theme",8,""),r=so(void 0);const T=()=>{var _;return(_=yt(r))==null?void 0:_.requestOpen()},f=()=>{var _;return(_=yt(r))==null?void 0:_.requestClose()};ye();const w=je(()=>b()||"");return go(De.Root(e,{get delayDurationMs(){return c()},get onOpenChange(){return h()},get triggerOn(){return p()},get nested(){return d()},get hasPointerEvents(){return l()},get offset(){return E()},get open(){return y()},get tippyTheme(){return`default text-tooltip-augment ${yt(w)??""}`},children:(_,j)=>{var L=dr(),P=$t(L);De.Trigger(P,{get referenceClientRect(){return C()},get class(){return g()},children:(W,I)=>{var F=pe(),S=$t(F);Yt(S,t,"default",{},null),it(W,F)},$$slots:{default:!0}});var H=co(P,2),V=W=>{De.Content(W,{get side(){return v()},get align(){return x()},children:(I,F)=>{var S=lr();let N;var G=Xt(S),Q=R=>{var B=pe(),st=$t(B);Yt(st,t,"content",{},null),it(R,B)},tt=R=>{yo(R,{size:1,class:"tooltip-text",children:(B,st)=>{var ct=po();le(()=>fo(ct,o())),it(B,ct)},$$slots:{default:!0}})};Me(G,R=>{Ke(()=>n.content)?R(Q):R(tt,!1)}),le(R=>N=uo(S,"",N,R),[()=>({width:s(),"min-width":i(),"max-width":a()})],je),it(I,S)},$$slots:{default:!0}})};Me(H,W=>{ke(o()),Ke(()=>o()||n.content)&&W(V)}),it(_,L)},$$slots:{default:!0},$$legacy:!0}),_=>lo(r,_),()=>yt(r)),de(t,"requestOpen",T),de(t,"requestClose",f),Gt({requestOpen:T,requestClose:f})}var vr=Ht("<div><!></div>"),hr=Ht("<div><!></div>");function Tr(e,t){Zt(t,!0);const n=bo();let o=D(t,"size",3,1),s=D(t,"insetContent",3,!1),i=D(t,"variant",3,"surface"),a=D(t,"interactive",3,!1),c=D(t,"includeBackground",3,!0),p=D(t,"borderless",3,!1),v=Ee(()=>t.class),d=Ee(()=>["c-card",`c-card--size-${o()}`,`c-card--${i()}`,s()?"c-card--insetContent":"",a()?"c-card--interactive":"",c()?"c-card--with-background":"",p()?"c-card--borderless":"",yt(v)]),l=Ee(()=>({...wo("accent"),class:yt(d).join(" ")}));var E=pe(),y=$t(E),x=h=>{var C=vr();Je(C,(r,T,f,w,_,j,L,P,H)=>({...yt(l),role:"button",tabindex:"0",onclick:r,onkeyup:T,onkeydown:f,onmousedown:w,onmouseover:_,onfocus:j,onmouseleave:L,onblur:P,oncontextmenu:H}),[()=>n("click"),()=>n("keyup"),()=>n("keydown"),()=>n("mousedown"),()=>n("mouseover"),()=>n("focus"),()=>n("mouseleave"),()=>n("blur"),()=>n("contextmenu")],"svelte-x444gv");var b=Xt(C);Qe(b,()=>t.children??Ze),it(h,C)},g=h=>{var C=hr();Je(C,()=>({...yt(l)}),void 0,"svelte-x444gv");var b=Xt(C);Qe(b,()=>t.children??Ze),it(h,C)};Me(y,h=>{a()?h(x):h(g,!1)}),it(e,E),Gt()}export{Tr as C,me as H,sr as R,Or as T,jt as a,de as b,gt as c,on as d,Mt as e,fr as f,ur as g,Er as h,qe as o,qt as t};
