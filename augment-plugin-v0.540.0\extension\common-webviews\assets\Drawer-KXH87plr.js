import{x as A,m as u,a6 as ee,z as e,P as a,I as E,J as ie,K as ae,y as j,a8 as y,ag as N,a2 as O,o as w,E as I,D as te,N as se,R as re,a1 as P,b as Q,G as oe,Q as p}from"./legacy-AoIeRrIA.js";import{p as n,g as S,k as T}from"./SpinnerAugment-mywmfXFR.js";import{a as de,I as ne}from"./IconButtonAugment-DZyIKjh7.js";import{t as le,f as ce}from"./index-Dtf_gCqL.js";import{E as ve}from"./ellipsis-5yZhsJie.js";const X=(g,{onResize:t,options:_})=>{const v=new ResizeObserver(t);return v.observe(g,_),{destroy(){v.unobserve(g),v.disconnect()}}};var me=j('<div class="c-drawer__hidden-indicator svelte-18f0m3o"><!></div>'),ue=j('<div><div class="c-drawer__left svelte-18f0m3o"><div class="c-drawer__left-content svelte-18f0m3o"><!></div></div> <div aria-hidden="true"></div> <div class="c-drawer__right svelte-18f0m3o"><!></div> <!></div>');function ze(g,t){A(t,!1);let _,v,B=n(t,"initialWidth",8,300),z=n(t,"expandedMinWidth",8,50),D=n(t,"minimizedWidth",8,0),s=n(t,"minimized",12,!1),q=n(t,"class",8,""),C=n(t,"showButton",8,!0),F=n(t,"deadzone",8,0),H=n(t,"columnLayoutThreshold",8,600),o=n(t,"layoutMode",28,()=>{}),x=u(),f=u(),l=u(!1),c=u(B()),k=u(B()),r=u(!1);function G(){s(!s())}function R(){if(e(f)){if(o()!==void 0)return a(r,o()==="column"),void(e(r)&&a(l,!1));a(r,e(f).clientWidth<H()),e(r)&&a(l,!1)}}ee(R),E(()=>(p(s()),p(o())),()=>{s()?(o("row"),a(r,!1)):o()!=="row"||s()||(o(void 0),R())}),E(()=>(p(o()),e(r)),()=>{o()!==void 0&&(a(r,o()==="column"),e(r)&&a(l,!1))}),E(()=>(p(s()),p(D()),e(c)),()=>{a(k,s()?D():e(c))}),ie(),ae();var h=ue();let J;y("mousemove",N,function(i){if(!e(l)||!e(x)||e(r))return;const d=i.clientX-_,b=e(f).clientWidth-200,m=v+d;m<z()?m<z()-F()?s(!0):(a(c,z()),s(!1)):m>b?(a(c,b),s(!1)):(a(c,m),s(!1))}),y("mouseup",N,function(){a(l,!1),a(c,Math.max(e(c),z()))});var W=w(h),M=w(W);O(M,"",{},{width:"var(--augment-drawer-width)","min-width":"var(--augment-drawer-width)","max-width":"var(--augment-drawer-width)"});var U=w(M);S(U,t,"left",{},null),T(W,i=>a(x,i),()=>e(x));var $=I(W,2);let K;var L=I($,2),V=w(L);S(V,t,"right",{},null);var Y=I(L,2),Z=i=>{var d=me(),b=w(d);ne(b,{variant:"solid",color:"accent",size:2,radius:"full",title:"Show panel",$$events:{click:G},children:(m,fe)=>{ve(m,{})},$$slots:{default:!0}}),le(3,d,()=>ce,()=>({y:0,x:0,duration:200})),Q(i,d)};te(Y,i=>{s()&&C()&&i(Z)}),T(h,i=>a(f,i),()=>e(f)),de(h,(i,d)=>X==null?void 0:X(i,d),()=>({onResize:()=>o()===void 0&&R()})),se((i,d)=>{J=P(h,1,`c-drawer ${q()??""}`,"svelte-18f0m3o",J,i),O(W,`--augment-drawer-width:${e(k)??""}px;`),M.inert=e(l),K=P($,1,"c-drawer__handle svelte-18f0m3o",null,K,d)},[()=>({"is-dragging":e(l),"is-hidden":!e(k),"is-column":e(r)}),()=>({"is-locked":e(r)})],re),y("mousedown",$,function(i){e(r)||(a(l,!0),_=i.clientX,v=e(x).offsetWidth,i.preventDefault())}),y("dblclick",$,G),Q(g,h),oe()}export{ze as D,X as r};
