var j=Object.defineProperty;var q=(i,t,o)=>t in i?j(i,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):i[t]=o;var w=(i,t,o)=>q(i,typeof t!="symbol"?t+"":t,o);import{x as A,Y as F,K as H,y as h,a as S,C as I,o as d,E as b,D as m,u as p,a8 as n,b as v,G as J}from"./legacy-AoIeRrIA.js";import{h as L,p as r,d as M,m as P,g,T as Q}from"./SpinnerAugment-mywmfXFR.js";import{b as l}from"./host-qgbK079d.js";class _{constructor(t){this._opts=t}get color(){return this._opts.color}get size(){return this._opts.size??1}get variant(){return this._opts.variant}}w(_,"CONTEXT_KEY","augment-badge");var R=h('<div class="c-badge__left-buttons svelte-103286g"><!></div>'),U=h('<div class="c-badge-body svelte-103286g"><!></div>'),V=h('<div class="c-badge__right-buttons svelte-103286g"><!></div>'),W=h("<div><!> <!> <!> <!></div>");function at(i,t){const o=L(t);A(t,!1);let f=r(t,"color",8,"accent"),z=r(t,"variant",8,"soft"),u=r(t,"size",8,1),y=r(t,"radius",8,"medium"),k=r(t,"highContrast",8,!1),x=r(t,"inset",8,!1);const K=u()===0?0:u()===3?2:1,Y=new _({color:f(),size:u(),variant:z()});F(_.CONTEXT_KEY,Y),H();var a=W();S(a,(s,e,c)=>({...s,...e,class:`c-badge c-badge--${f()} c-badge--${z()} c-badge--size-${u()}`,role:"button",tabindex:"0",[I]:c}),[()=>M(f()),()=>P(y()),()=>({"c-badge--highContrast":k(),"c-badge--inset":x()})],"svelte-103286g");var B=d(a);g(B,t,"chaser",{},null);var C=b(B,2),N=s=>{var e=R(),c=d(e);g(c,t,"leftButtons",{},null),v(s,e)};m(C,s=>{p(()=>o.leftButtons)&&s(N)});var E=b(C,2),O=s=>{Q(s,{get size(){return K},weight:"medium",children:(e,c)=>{var $=U(),G=d($);g(G,t,"default",{},null),v(e,$)},$$slots:{default:!0}})};m(E,s=>{p(()=>o.default)&&s(O)});var X=b(E,2),D=s=>{var e=V(),c=d(e);g(c,t,"rightButtons",{},null),v(s,e)};m(X,s=>{p(()=>o.rightButtons)&&s(D)}),n("click",a,function(s){l.call(this,t,s)}),n("keydown",a,function(s){l.call(this,t,s)}),n("keyup",a,function(s){l.call(this,t,s)}),n("mousedown",a,function(s){l.call(this,t,s)}),n("mouseover",a,function(s){l.call(this,t,s)}),n("focus",a,function(s){l.call(this,t,s)}),n("mouseleave",a,function(s){l.call(this,t,s)}),n("blur",a,function(s){l.call(this,t,s)}),v(i,a),J()}export{_ as B,at as a};
