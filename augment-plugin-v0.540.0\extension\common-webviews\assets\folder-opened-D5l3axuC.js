var Cs=Object.defineProperty;var ze=h=>{throw TypeError(h)};var Ss=(h,t,e)=>t in h?Cs(h,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):h[t]=e;var a=(h,t,e)=>Ss(h,typeof t!="symbol"?t+"":t,e),me=(h,t,e)=>t.has(h)||ze("Cannot "+e);var n=(h,t,e)=>(me(h,t,"read from private field"),e?e.call(h):t.get(h)),x=(h,t,e)=>t.has(h)?ze("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(h):t.set(h,e),y=(h,t,e,s)=>(me(h,t,"write to private field"),s?s.call(h,e):t.set(h,e),e),p=(h,t,e)=>(me(h,t,"access private method"),e);var Kt=(h,t,e,s)=>({set _(i){y(h,t,i,e)},get _(){return n(h,t,s)}});import{M as bs}from"./message-broker-EhzME3pO.js";import{S as xs,W as xt}from"./host-qgbK079d.js";import{d as ws}from"./CardAugment-DwIptXof.js";import{Z as lt,aO as se,f as Vt,a as as,o as hs,b as Bt}from"./legacy-AoIeRrIA.js";import{i as Is,c as Ot,d as Jt,j as _t,A as _e,F as dt,a as Xt,k as xe,l as Ts,m as we,n as $e,o as Es,p as Ms,q as ls,r as As,s as Fs,t as qs,u as Ls,C as ks,R as Hs,E as Us,v as Ds,w as Rs,x as Os,y as Ns,z as Ps,B as Ge,G as zs,H as We,I as je,J as $s,K as Ve,N as Gs,O as Be,P as Ws,Q as Ze,U as ge}from"./index-BLDiLrXG.js";import{f as js,i as cs}from"./file-type-utils-D6OEcQY2.js";import{C as Vs}from"./types-CGlLNakm.js";import{R as Nt,C as F,b as pe,I as Yt,a as G,i as L,A as At,E as M,h as Bs,c as qt,S as mt,d as Tt,P as Pt,e as Ie,f as Lt,g as Gt,j as Zs,s as fe}from"./chat-types-BfwvR7Kn.js";import{T as A,a as Qe,b as ye}from"./chat-model-context-DPgWxlAp.js";import{h as ds}from"./IconButtonAugment-DZyIKjh7.js";import{l as us}from"./SpinnerAugment-mywmfXFR.js";class Qs{constructor(t=[]){a(this,"_items",[]);a(this,"_focusedItemIdx");a(this,"_subscribers",new Set);a(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));a(this,"setItems",t=>{this._items=t,this._items.length===0?this.setFocusIdx(void 0):this._focusedItemIdx!==void 0&&this._focusedItemIdx>=this._items.length?this.setFocusIdx(this._items.length-1):this._focusedItemIdx===void 0?this.setFocusIdx(void 0):this.setFocusIdx(this._focusedItemIdx)});a(this,"setFocus",t=>{if(t!==void 0&&t===this.focusedItem)return;const e=t?this._items.indexOf(t):-1;e===-1?this.setFocusIdx(void 0):this.setFocusIdx(e)});a(this,"setFocusIdx",t=>{if(t===this._focusedItemIdx||this._items.length===0)return;if(t===void 0)return this._focusedItemIdx=void 0,void this.notifySubscribers();const e=Math.floor(t/this._items.length)*this._items.length;this._focusedItemIdx=(t-e)%this._items.length,this.notifySubscribers()});a(this,"initFocusIdx",t=>this._focusedItemIdx===void 0&&(this.setFocusIdx(t),!0));a(this,"focusNext",()=>{const t=this.nextIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});a(this,"focusPrev",()=>{const t=this.prevIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});a(this,"prevIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?this._items.length-1:t.nowrap&&this._focusedItemIdx===0?0:(this._focusedItemIdx-1+this._items.length)%this._items.length});a(this,"nextIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?0:t.nowrap&&this._focusedItemIdx===this._items.length-1?this._items.length-1:(this._focusedItemIdx+1)%this._items.length});a(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});this._items=t}get items(){return this._items}get focusedItem(){if(this._focusedItemIdx!==void 0)return this._items[this._focusedItemIdx]}get focusedItemIdx(){return this._focusedItemIdx}}class te{constructor(){this._disposables=[]}add(t){if(t===void 0)throw new Error("Attempt to add undefined disposable to DisposableCollection");return this._disposables.push(t),t}addAll(...t){t.forEach(e=>this.add(e))}adopt(t){this._disposables.push(...t._disposables),t._disposables.length=0}dispose(){for(const t of this._disposables)t.dispose();this._disposables.length=0}}class Ks{constructor(t=new te,e=new te){this._disposables=new te,this._priorityDisposables=new te,this._disposables.adopt(t),this._priorityDisposables.adopt(e)}addDisposable(t,e=!1){return e?this._priorityDisposables.add(t):this._disposables.add(t)}addDisposables(...t){this._disposables.addAll(...t)}dispose(){this._priorityDisposables.dispose(),this._disposables.dispose()}}const Js=[".md",".mdc"],Ke=[{directory:".cursor/rules",file:".cursorrules",name:"Cursor"},{directory:".windsurf/rules",file:".windsurfrules",name:"Windsurf"},{directory:".github/instructions",file:".github/copilot-instructions.md",name:"GitHub Copilot"},{directory:".clinerules",file:".clinerules",name:"Cline"},{directory:".roo/rules",file:".roorules",name:"Roo Code"},{directory:".trae/rules",name:"Trae"}];class Re extends Ks{constructor(){super(),this._logger=Is()}async loadRules({includeGuidelines:t=!1,query:e,maxResults:s,contextRules:i}={}){this._logger.debug(`Loading rules with includeGuidelines=${t}, query=${e}, maxResults=${s}`),this._logger.debug("Using file system approach to load rules");const r=await this.loadDirectory((void 0)(Ot,Jt));let o;if(this._logger.debug(`Loaded ${r.length} rules from directory`),t){const l=await this.loadGuidelinesFiles();this._logger.debug(`Loaded ${l.length} guidelines rules`),o=[...l,...r]}else o=r;if(e&&e.trim()){const l=e.toLowerCase().trim();o=o.filter(c=>{const d=c.path.toLowerCase().includes(l),u=c.content.toLowerCase().includes(l);return d||u}),this._logger.debug(`Filtered to ${o.length} rules matching query: ${e}`)}return s&&s>0&&(o=o.slice(0,s),this._logger.debug(`Limited to ${o.length} rules`)),this._logger.debug(`Returning ${o.length} total rules`),i!==void 0&&i.length>0&&(o=Re.filterRulesByContext(o,i),this._logger.debug(`Filtered to ${o.length} rules based on context`)),o}static filterRulesByContext(t,e){return[...t.filter(s=>s.type!==Nt.MANUAL),...t.filter(s=>s.type===Nt.MANUAL&&e.some(i=>i.path===s.path))]}static calculateRulesAndGuidelinesCharacterCount(t){const{rules:e,workspaceGuidelinesContent:s,contextRules:i=[]}=t,r=e.filter(d=>d.type===Nt.ALWAYS_ATTACHED).reduce((d,u)=>d+u.content.length+u.path.length,0),o=e.filter(d=>d.type===Nt.AGENT_REQUESTED).reduce((d,u)=>{var m;return d+100+(((m=u.description)==null?void 0:m.length)??0)+u.path.length},0),l=r+e.filter(d=>d.type===Nt.MANUAL).filter(d=>i.some(u=>u.path===d.path)).reduce((d,u)=>d+u.content.length+u.path.length,0)+o+s.length,c=t.rulesAndGuidelinesLimit&&l>t.rulesAndGuidelinesLimit;return{totalCharacterCount:l,isOverLimit:c,warningMessage:c&&t.rulesAndGuidelinesLimit?`Total number of characters in included rules and workspace guidelines (${l} chars)
        exceeds the limit of ${t.rulesAndGuidelinesLimit} characters, remove some rules
        or reduce the length of your guidelines.`:void 0}}async loadGuidelinesFiles(){const t=[],e=_t();if(!e)return this._logger.warn("Client workspaces not initialized"),t;const s=await e.getWorkspaceRoot();if(!s)return t;const i=(void 0)(s,_e),r=await e.getPathInfo(i);if(r.exists&&r.type===dt.File)try{const o=(await e.readFile(i)).contents;if(!o)return this._logger.warn(`Guidelines file is empty: ${i}`),t;const l=Xt.parseRuleFile(o,_e);t.push({path:_e,content:l.content,type:l.type,description:l.description})}catch(o){this._logger.error(`Error loading guidelines file ${i}: ${String(o)}`)}return t}async loadDirectory(t){const e=[];try{const s=_t();if(!s)return this._logger.warn("Client workspaces not initialized"),e;const i=await s.getWorkspaceRoot();if(!i)return this._logger.warn("No workspace root found"),e;const r=(void 0)(i,t);this._logger.debug(`Looking for rules in: ${r}`);const o=await s.getPathInfo(r);return this._logger.debug(`Path info for ${r}: ${JSON.stringify(o)}`),o.exists&&o.type===dt.Directory?(this._logger.debug(`Rules folder exists at ${r}`),await this.processRuleDirectory(s,r,e,""),this._logger.debug(`Loaded ${e.length} rules from ${r}`),e):(this._logger.debug(`Rules folder not found at ${r}`),e)}catch(s){return this._logger.error(`Error loading rules: ${String(s)}`),e}}async loadDirectoryFromPath(t){const e=[];try{const s=_t();if(!s)return this._logger.warn("Client workspaces not initialized"),e;let i;if(!(void 0)(t)){const o=await s.getWorkspaceRoot();if(!o)return this._logger.warn("No workspace root found"),e;i=(void 0)(o,t),this._logger.debug(`Loading rules from workspace-relative path: ${i}`)}const r=await s.getPathInfo(i);return r.exists&&r.type===dt.Directory?(this._logger.debug(`Rules folder exists at ${i}`),await this.processRuleDirectory(s,i,e,""),this._logger.debug(`Loaded ${e.length} rules from ${i}`),e):(this._logger.debug(`Rules folder not found at ${i}`),e)}catch(s){return this._logger.error(`Error loading rules from path: ${String(s)}`),e}}async processRuleDirectory(t,e,s,i){const r=await t.listDirectory(e,1,!1);if(r.errorMessage)this._logger.error(`Error listing directory ${e}: ${r.errorMessage}`);else{this._logger.debug(`Processing directory: ${e}, found ${r.entries.length} entries`);for(const o of r.entries){const l=(void 0)(e,o),c=(void 0)(i,o),d=await t.getPathInfo(l);if(d.exists)if(d.type===dt.Directory)this._logger.debug(`Processing subdirectory: ${o}`),await this.processRuleDirectory(t,l,s,c);else if(d.type===dt.File&&Js.some(u=>o.endsWith(u))){this._logger.debug(`Processing rule file: ${o}`);try{const u=(await t.readFile(l)).contents||"",m=Xt.parseRuleFile(u,o);s.push({path:c,content:m.content,type:m.type,description:m.description}),this._logger.debug(`Successfully loaded rule: ${c}`)}catch(u){this._logger.error(`Error loading rule file ${l}: ${String(u)}`)}}else d.type===dt.File&&this._logger.debug(`Skipping non-markdown file: ${o}`)}}}async createRule(t,e=!1){const s=_t();if(!s)throw new Error("Client workspaces not initialized");const i=await s.getWorkspaceRoot();if(!i)throw new Error("No workspace root found");let r=(void 0)(i,Ot,Jt);e&&(r=(void 0)(r,"imported"));const o=t.path.endsWith(".md")?t.path:`${t.path}.md`,l=(void 0)(r,o),c=await s.getQualifiedPathName(l);if(!c)throw new Error(`Unable to get qualified path for: ${l}`);if((await s.getPathInfo(l)).exists)throw new Error(`Rule file already exists: ${o}`);const d=Xt.formatRuleFileForMarkdown(t);return await s.writeFile(c,d),{...t,path:o}}async deleteRule(t){if(typeof t!="string")throw new Error(`Expected rulePath to be a string, got ${typeof t}: ${String(t)}`);const e=_t();if(!e)throw new Error("Client workspaces not initialized");const s=await e.getWorkspaceRoot();if(!s)throw new Error("No workspace root found");let i;if((void 0)(t)||(i=(void 0)(s,Ot,Jt,t)),(await e.getPathInfo(i)).exists){const r=await e.getQualifiedPathName(i);r&&(await e.deleteFile(r),this._logger.debug(`Deleted rule file: ${i}`)),this._logger.debug(`Deleted rule file: ${i}`)}}async updateRuleFile(t,e){if(typeof t!="string")throw new Error(`Expected rulePath to be a string, got ${typeof t}: ${String(t)}`);const s=_t();if(!s)throw new Error("Client workspaces not initialized");const i=await s.getWorkspaceRoot();if(!i)throw new Error("No workspace root found");let r;(void 0)(t)||(r=t.startsWith(Ot)?(void 0)(i,t):(void 0)(i,Ot,Jt,t));const o=await s.getQualifiedPathName(r);if(!o)throw new Error(`Unable to get qualified path for: ${r}`);await s.writeFile(o,e),this._logger.debug(`Updated rule file: ${r}`)}async importFile(t,e){const s=_t();if(!s)throw new Error("Client workspaces not initialized");let i,r;if(!(void 0)(t)){const l=await s.getWorkspaceRoot();if(!l)throw new Error("No workspace root found");i=(void 0)(l,t),r=t,this._logger.debug(`Importing file from workspace-relative path: ${i}`)}const o=await s.getPathInfo(i);if(!o.exists||o.type!==dt.File)return this._logger.error(`File not found: ${i}`),{successfulImports:0,duplicates:0,totalAttempted:1};try{const l=(await s.readFile(i)).contents;if(!l)return this._logger.error(`File is empty: ${i}`),{successfulImports:0,duplicates:0,totalAttempted:1};const c=Xt.parseRuleFile(l,r),d=(void 0)(r).name.replace(".","");return await this.createRule({path:d,content:c.content,type:c.type},e),{successfulImports:1,duplicates:0,totalAttempted:1}}catch(l){return this._logger.error(`Error importing file ${t}: ${String(l)}`),{successfulImports:0,duplicates:String(l).includes("already exists")?1:0,totalAttempted:1}}}async importDirectory(t,e){try{const s=await this.loadDirectoryFromPath(t);if(s.length===0)return this._logger.debug(`No rules found in directory: ${t}`),{successfulImports:0,duplicates:0,totalAttempted:0};this._logger.debug(`Loaded ${s.length} existing rules from ${t}`);let i=0,r=0;const o=s.length;for(const l of s)try{const c=(void 0)(l.path).name,d=(void 0)(l.path),u=d==="."?c:(void 0)(d,c);await this.createRule({path:u,content:l.content,type:l.type},e),i++,this._logger.debug(`Successfully imported rule: ${l.path} -> ${u}`)}catch(c){this._logger.warn(`Failed to import rule ${l.path}: ${String(c)}`),String(c).includes("already exists")&&r++}return this._logger.info(`Imported ${i} rules from ${t}, ${r} duplicates skipped`),{successfulImports:i,duplicates:r,totalAttempted:o}}catch(s){return this._logger.error(`Error importing directory: ${String(s)}`),{successfulImports:0,duplicates:0,totalAttempted:0}}}async detectAutoImportOptions(){const t=_t();if(!t)return this._logger.warn("No workspace available for auto-import detection"),[];const e=await t.getWorkspaceRoot();if(!e)return this._logger.warn("No workspace root found for auto-import detection"),[];const s=[];for(const{directory:i,file:r,name:o}of Ke){let l=!1,c=!1;if(i)try{const d=(void 0)(e,i),u=await t.getPathInfo(d);l=u.exists===!0&&u.type===dt.Directory}catch(d){this._logger.debug(`Error checking directory ${i}: ${String(d)}`)}if(r)try{const d=(void 0)(e,r),u=await t.getPathInfo(d);c=u.exists===!0&&u.type===dt.File}catch(d){this._logger.debug(`Error checking file ${r}: ${String(d)}`)}l&&c?s.push({label:o,description:`Import existing rules from ${i} and ${r}`,directory:i,file:r}):l?s.push({label:o,description:`Import existing rules from ${i}`,directory:i}):c&&s.push({label:o,description:`Import existing rules from ${r}`,file:r})}return s}async processAutoImportSelection(t){const e=Ke.find(c=>c.name===t);if(!e)throw new Error(`Unknown auto-import option: ${t}`);const s=[];e.directory&&s.push(this.importDirectory(e.directory,!0)),e.file&&s.push(this.importFile(e.file,!0));const i=await Promise.all(s),r=i.reduce((c,d)=>c+d.successfulImports,0),o=i.reduce((c,d)=>c+d.duplicates,0),l=i.reduce((c,d)=>c+d.totalAttempted,0);return this._logger.debug(`Auto-import rules completed for ${t}, imported: ${r}, duplicates: ${o}, total attempted: ${l}`),{importedRulesCount:r,duplicatesCount:o,totalAttempted:l,source:t}}autoImportRules(){this._logger.debug("Auto import rules requested")}dispose(){super.dispose()}}function Xs(h,t,e=1e3){let s=null,i=0;const r=lt(t),o=()=>{const l=(()=>{const c=Date.now();if(s!==null&&c-i<e)return s;const d=h();return s=d,i=c,d})();r.set(l)};return{subscribe:r.subscribe,resetCache:()=>{s=null,o()},updateStore:o}}var ms=(h=>(h[h.unset=0]="unset",h[h.positive=1]="positive",h[h.negative=2]="negative",h))(ms||{});const ve=new Map,Ys=()=>{let h=Promise.resolve();const t=new Map,e=new Map,s=crypto.randomUUID(),i={end:r=>{const o=t.get(r);return console.debug("END LINK: ",r,s),o==null||o(),i},start:async(r,o)=>{const{promise:l,unlock:c,reject:d}=(m=>{let C=()=>{},g=()=>{},v=(b,w)=>()=>{w&&clearTimeout(w),b()};return{promise:new Promise((b,w)=>{let f,I=()=>{w("Chain was reset")};m&&m>0&&(f=setTimeout(I,m)),g=v(I,f),C=v(b,f)}),unlock:C,reject:g}})(o),u=h;return h=l.finally(()=>{t.delete(r),e.delete(r)}),t.set(r,()=>{c(),t.delete(r)}),e.set(r,()=>{d(),e.delete(r)}),await u,console.debug("START LINK: ",r,s),i},rejectAll:()=>{h=Promise.resolve();try{e.forEach(r=>{r(new Error("Chain was reset"))})}finally{t.clear(),e.clear()}},unlockAll:()=>{h=Promise.resolve();try{t.forEach(r=>{r()})}finally{t.clear(),e.clear()}}};return i};function Je(h,t){return function(e,s){if(e.length<=s||e.length===0)return{truncatedText:e};const i=e.split(`
`),r="... additional lines truncated ..."+(i[0].endsWith("\r")?"\r":"");let o,l="";if(i.length<2||i[0].length+i[i.length-1].length+r.length>s){const c=Math.floor(s/2);l=[e.slice(0,c),"<...>",e.slice(-c)].join(""),o=[1,1,i.length,i.length]}else{const c=[],d=[];let u=r.length+1;for(let m=0;m<Math.floor(i.length/2);m++){const C=i[m],g=i[i.length-1-m],v=C.length+g.length+2;if(u+v>s)break;u+=v,c.push(C),d.push(g)}o=[1,c.length,i.length-d.length+1,i.length],c.push(r),c.push(...d.reverse()),l=c.join(`
`)}return{truncatedText:l,shownRangeWhenTruncated:o}}(h,t).truncatedText}function ti(h){var e;if(!h)return Yt.IMAGE_FORMAT_UNSPECIFIED;switch((e=h.split("/")[1])==null?void 0:e.toLowerCase()){case"jpeg":case"jpg":return Yt.JPEG;case"png":return Yt.PNG;default:return Yt.IMAGE_FORMAT_UNSPECIFIED}}function ei(h,t,e){var i,r;if(h.phase!==A.cancelled&&h.phase!==A.completed&&h.phase!==A.error)return;let s;return(i=h.result)!=null&&i.contentNodes?(s=function(o,l){return o.map(c=>c.type===xe.ContentText?{type:pe.CONTENT_TEXT,text_content:c.text_content}:c.type===xe.ContentImage&&c.image_content&&l?{type:pe.CONTENT_IMAGE,image_content:{image_data:c.image_content.image_data,format:ti(c.image_content.media_type)}}:{type:pe.CONTENT_TEXT,text_content:"[Error: Invalid content node]"})}(h.result.contentNodes,e),{content:"",is_error:h.result.isError,request_id:h.result.requestId,tool_use_id:t,content_nodes:s}):((r=h.result)==null?void 0:r.text)!==void 0?{content:h.result.text,is_error:h.result.isError,request_id:h.result.requestId,tool_use_id:t}:void 0}function si(h=[]){let t;for(const e of h){if(e.type===F.TOOL_USE)return e;e.type===F.TOOL_USE_START&&(t=e)}return t}function ii(h,t,e,s){if(!h||!t)return[];let i=!1;return t.filter(r=>{var l;const o=s!=null&&s.isActive&&r.tool_use?s.getToolUseState(r.tool_use.tool_use_id):e.getToolUseState(r.requestId??h,(l=r.tool_use)==null?void 0:l.tool_use_id);return i===!1&&o.phase!==A.new&&o.phase!==A.unknown&&o.phase!==A.checkingSafety&&r.tool_use!==void 0||(o.phase===A.runnable&&(i=!0),!1)})}function Gi(h,t){if(h.contentNodes&&h.contentNodes.length>0){const e=h.contentNodes.map(s=>{if(s.type===xe.ContentText){let i="";return s.text_content&&(i=Je(s.text_content,t/h.contentNodes.length)),{...s,text_content:i}}return s});return{...h,contentNodes:e}}return{...h,text:Je(h.text,t)}}const gt="__NEW_AGENT__",Wi=h=>h.chatItemType===void 0,ji=(h,t)=>{var r;const e=h.chatHistory.at(-1);if(!e||!L(e))return At.notRunning;if(!(e.status===M.success||e.status===M.failed||e.status===M.cancelled))return At.running;const s=((r=e.structured_output_nodes)==null?void 0:r.filter(o=>o.type===F.TOOL_USE&&!!o.tool_use))??[];let i;if(t.enableParallelTools?(i=ii(e.request_id,s,h).at(-1),!i&&s.length>0&&(i=s.at(-1))):i=s.at(-1),!i||!i.tool_use)return At.notRunning;switch(h.getToolUseState(e.request_id,i.tool_use.tool_use_id).phase){case A.runnable:return At.awaitingUserAction;case A.cancelled:return At.notRunning;default:return At.running}},Te=h=>L(h)&&!!h.request_message,ri=h=>h.chatHistory.findLast(t=>Te(t)),Vi=(h,t)=>{const e=ri(h);return e!=null&&e.request_id?h.historyFrom(e.request_id,!0).filter(s=>L(s)&&(!t||t(s))):[]},Bi=h=>{var s;const t=h.chatHistory.at(-1);if(!(t!=null&&t.request_id)||!L(t))return!1;const e=((s=t.structured_output_nodes)==null?void 0:s.filter(i=>i.type===F.TOOL_USE))??[];for(const i of e)if(i.tool_use&&h.getToolUseState(t.request_id,i.tool_use.tool_use_id).phase===A.runnable)return h.updateToolUseState({requestId:t.request_id,toolUseId:i.tool_use.tool_use_id,phase:A.cancelled}),!0;return!1},k={triggerOnHistorySizeChars:0,historyTailSizeCharsToExclude:0,triggerOnHistorySizeCharsWhenCacheExpiring:0,prompt:"",cacheTTLMs:0,bufferTimeBeforeCacheExpirationMs:0,summaryNodeRequestMessageTemplate:`
<supervisor>
Conversation history between Agent(you) and the user and history of tool calls was abridged and summarized to reduce context size.
Abridged conversation history:
{abridged_history}

Summary was generated by Agent(you) so 'I' in the summary represents Agent(you).
Here is the summary:
{summary}

Continue the conversation and finish the task given by the user from this point.
</supervisor>`,summaryNodeResponseMessage:"Ok. I will continue the conversation from this point.",abridgedHistoryParams:{totalCharsLimit:1e4,userMessageCharsLimit:1e3,agentResponseCharsLimit:2e3,actionCharsLimit:200,numFilesModifiedLimit:10,numFilesCreatedLimit:10,numFilesDeletedLimit:10,numFilesViewedLimit:10,numTerminalCommandsLimit:10}};function Ee(h,t,e=.5,s=.5){if(h.length<=t||t<=0)return h;if(e+s>1)throw new Error("startRatio + endRatio cannot exceed 1.0");const i="...",r=t-3;if(r<=0)return i.substring(0,t);const o=Math.floor(r*e),l=Math.floor(r*s);return h.substring(0,o)+i+h.substring(h.length-l)}const z=(h,t)=>h!==void 0?h:t;function oi(h){for(const t of h.filesModified)h.filesViewed.delete(t)}function ni(h,t){try{const e=JSON.parse(h.input_json);switch(h.tool_name){case"str-replace-editor":e.path&&t.filesModified.add(e.path);break;case"save-file":e.path&&t.filesCreated.add(e.path);break;case"remove-files":if(e.file_paths&&Array.isArray(e.file_paths))for(const s of e.file_paths)t.filesDeleted.add(s);break;case"view":e.path&&t.filesViewed.add(e.path);break;case"launch-process":e.command&&t.terminalCommands.add(e.command)}}catch(e){console.warn("Failed to parse tool use input:",e)}}function zt(h,t,e,s="files"){if(h.size===0)return null;const i=Array.from(h).sort((c,d)=>c.localeCompare(d)),r=c=>Ee(c,e);if(i.length<=t)return i.map(r);const o=i.slice(0,t).map(r),l=i.length-t;return o.push(`... ${l} more ${s}`),o}function ai(h,t){let e=h.userMessage,s=h.agentFinalResponse;e.length>t.userMessageCharsLimit&&(e=Ee(e,t.userMessageCharsLimit)),s.length>t.agentResponseCharsLimit&&(s=Ee(s,t.agentResponseCharsLimit));const i=h.agentActionsSummary.filesModified.size>0||h.agentActionsSummary.filesCreated.size>0||h.agentActionsSummary.filesDeleted.size>0||h.agentActionsSummary.filesViewed.size>0||h.agentActionsSummary.terminalCommands.size>0,r={userMessage:e,agentResponse:s&&s.trim()!==""?s:null,hasActions:i,filesModified:zt(h.agentActionsSummary.filesModified,t.numFilesModifiedLimit,t.actionCharsLimit),filesCreated:zt(h.agentActionsSummary.filesCreated,t.numFilesCreatedLimit,t.actionCharsLimit),filesDeleted:zt(h.agentActionsSummary.filesDeleted,t.numFilesDeletedLimit,t.actionCharsLimit),filesViewed:zt(h.agentActionsSummary.filesViewed,t.numFilesViewedLimit,t.actionCharsLimit),terminalCommands:zt(h.agentActionsSummary.terminalCommands,t.numTerminalCommandsLimit,t.actionCharsLimit,"commands"),wasInterrupted:h.wasInterrupted,continues:h.continues};return Ts(r)}function hi(h){var e,s;let t=h.request_message||"";return(e=h.structured_request_nodes)!=null&&e.some(i=>i.image_node||i.image_id_node)&&(t+=`
[User attached image]`),(s=h.structured_request_nodes)!=null&&s.some(i=>i.file_node||i.file_id_node)&&(t+=`
[User attached document]`),t}function Xe(h){var t,e,s,i,r,o,l,c,d;try{if(!h)return console.log("historySummaryParams is empty. Using default params"),k;const u=JSON.parse(h),m={triggerOnHistorySizeChars:z(u.trigger_on_history_size_chars,k.triggerOnHistorySizeChars),historyTailSizeCharsToExclude:z(u.history_tail_size_chars_to_exclude,k.historyTailSizeCharsToExclude),triggerOnHistorySizeCharsWhenCacheExpiring:z(u.trigger_on_history_size_chars_when_cache_expiring,k.triggerOnHistorySizeCharsWhenCacheExpiring),prompt:z(u.prompt,k.prompt),cacheTTLMs:z(u.cache_ttl_ms,k.cacheTTLMs),bufferTimeBeforeCacheExpirationMs:z(u.buffer_time_before_cache_expiration_ms,k.bufferTimeBeforeCacheExpirationMs),summaryNodeRequestMessageTemplate:z(u.summary_node_request_message_template,k.summaryNodeRequestMessageTemplate),summaryNodeResponseMessage:z(u.summary_node_response_message,k.summaryNodeResponseMessage),abridgedHistoryParams:{totalCharsLimit:z((t=u.abridged_history_params)==null?void 0:t.total_chars_limit,k.abridgedHistoryParams.totalCharsLimit),userMessageCharsLimit:z((e=u.abridged_history_params)==null?void 0:e.user_message_chars_limit,k.abridgedHistoryParams.userMessageCharsLimit),agentResponseCharsLimit:z((s=u.abridged_history_params)==null?void 0:s.agent_response_chars_limit,k.abridgedHistoryParams.agentResponseCharsLimit),actionCharsLimit:z((i=u.abridged_history_params)==null?void 0:i.action_chars_limit,k.abridgedHistoryParams.actionCharsLimit),numFilesModifiedLimit:z((r=u.abridged_history_params)==null?void 0:r.num_files_modified_limit,k.abridgedHistoryParams.numFilesModifiedLimit),numFilesCreatedLimit:z((o=u.abridged_history_params)==null?void 0:o.num_files_created_limit,k.abridgedHistoryParams.numFilesCreatedLimit),numFilesDeletedLimit:z((l=u.abridged_history_params)==null?void 0:l.num_files_deleted_limit,k.abridgedHistoryParams.numFilesDeletedLimit),numFilesViewedLimit:z((c=u.abridged_history_params)==null?void 0:c.num_files_viewed_limit,k.abridgedHistoryParams.numFilesViewedLimit),numTerminalCommandsLimit:z((d=u.abridged_history_params)==null?void 0:d.num_terminal_commands_limit,k.abridgedHistoryParams.numTerminalCommandsLimit)}};m.summaryNodeRequestMessageTemplate.includes("{summary}")||(console.error("summaryNodeRequestMessageTemplate must contain {summary}. Using default template"),m.summaryNodeRequestMessageTemplate=k.summaryNodeRequestMessageTemplate);const C={...m,prompt:m.prompt.slice(0,10)+"..."};return console.log("historySummaryParams updated: ",C),m}catch(u){return console.error("Failed to parse history_summary_params:",u),k}}class li{constructor(){this._controllers=new Set,this._timeoutIds=new Set}addCallback(t,e){const s=new AbortController,i=setTimeout(()=>{t(s.signal),this._controllers.delete(s),this._timeoutIds.delete(i)},e);this._controllers.add(s),this._timeoutIds.add(i)}cancelAll(){this._controllers.forEach(t=>t.abort()),this._timeoutIds.forEach(t=>clearTimeout(t)),this._controllers.clear(),this._timeoutIds.clear()}}function Ce(h){return h.reduce((t,e)=>t+_s(e),0)}function _s(h){let t=0;return h.request_nodes?t+=JSON.stringify(h.request_nodes).length:t+=(h.request_message||"").length,h.response_nodes?t+=JSON.stringify(h.response_nodes).length:t+=(h.response_text||"").length,t}class ci{constructor(t,e,s){a(this,"historySummaryVersion",3);a(this,"_callbacksManager",new li);a(this,"_params");this._conversationModel=t,this._extensionClient=e,this._chatFlagModel=s,this._params=Xe(s.historySummaryParams),s.subscribe(i=>{this._params=Xe(i.historySummaryParams)})}cancelRunningOrScheduledSummarizations(){this._callbacksManager.cancelAll()}generateAbridgedHistoryText(t){const e=new Set(t.map(o=>o.request_id)),s=function(o){const l=[];let c=null;for(const d of o){if(!L(d))continue;const u=d;if(Bs(u)||(c&&(c.agentFinalResponse.trim()===""&&(c.wasInterrupted=!0),l.push(c)),c={userMessage:hi(u),agentActionsSummary:{filesModified:new Set,filesCreated:new Set,filesDeleted:new Set,filesViewed:new Set,terminalCommands:new Set},agentFinalResponse:"",wasInterrupted:!1,continues:!1}),!c)continue;let m=!1;if(u.structured_output_nodes)for(const C of u.structured_output_nodes)C.type===F.TOOL_USE&&C.tool_use&&(m=!0,ni(C.tool_use,c.agentActionsSummary));!m&&u.response_text&&(c.agentFinalResponse=u.response_text)}c&&(c.agentFinalResponse.trim()===""&&(c.continues=!0),l.push(c));for(const d of l)oi(d.agentActionsSummary);return l}(this._conversationModel.chatHistory.filter(o=>o.request_id&&e.has(o.request_id)));let i=0;const r=[];for(let o=s.length-1;o>=0;o--){const l=ai(s[o],this._params.abridgedHistoryParams);if(i+l.length>this._params.abridgedHistoryParams.totalCharsLimit)break;r.push(l),i+=l.length}return r.reverse(),r.join(`
`)}clearStaleHistorySummaryNodes(t){return t.filter(e=>!qt(e)||e.summaryVersion===this.historySummaryVersion)}maybeScheduleSummarization(t){if(!this._chatFlagModel.useHistorySummary||this._params.triggerOnHistorySizeCharsWhenCacheExpiring<=0)return;const e=this._params.cacheTTLMs-t-this._params.bufferTimeBeforeCacheExpirationMs;e>0&&this._callbacksManager.addCallback(s=>{this.maybeAddHistorySummaryNode(!0,s)},e)}preprocessChatHistory(t){const e=t.findLastIndex(s=>qt(s)&&s.summaryVersion===this.historySummaryVersion);return this._chatFlagModel.useHistorySummary?(e>0&&(console.info(`Using history summary node found at index ${e} with requestId: ${t[e].request_id}`),t=t.slice(e)),t=t.filter(s=>!qt(s)||s.summaryVersion===this.historySummaryVersion)):t=t.filter(s=>!qt(s)),t}async maybeAddHistorySummaryNode(t=!1,e){var tt,ct,bt;if(console.log("maybeAddHistorySummaryNode. isCacheAboutToExpire: ",t),!this._params.prompt||this._params.prompt.trim()==="")return console.log("maybeAddHistorySummaryNode. empty prompt"),!1;const s=this._conversationModel.convertHistoryToExchanges(this._conversationModel.chatHistory),i=t?this._params.triggerOnHistorySizeCharsWhenCacheExpiring:this._params.triggerOnHistorySizeChars;if(console.log("maybeAddHistorySummaryNode. maxCharsThreshold: ",i),i<=0)return!1;const{head:r,tail:o,headSizeChars:l,tailSizeChars:c}=function(Z,le,ys,vs){if(Z.length===0)return{head:[],tail:[],headSizeChars:0,tailSizeChars:0};const ce=[],Rt=[];let Zt=0,Ne=0,Pe=0;for(let de=Z.length-1;de>=0;de--){const ue=Z[de],Qt=_s(ue);Zt+Qt<le||Rt.length<vs?(Rt.push(ue),Pe+=Qt):(ce.push(ue),Ne+=Qt),Zt+=Qt}return Zt<ys?(Rt.push(...ce),{head:[],tail:Rt.reverse(),headSizeChars:0,tailSizeChars:Zt}):{head:ce.reverse(),tail:Rt.reverse(),headSizeChars:Ne,tailSizeChars:Pe}}(s,this._params.historyTailSizeCharsToExclude,i,1);if(console.log("maybeAddHistorySummaryNode. headSizeChars: ",l," tailSizeChars: ",c),r.length===0)return console.log("maybeAddHistorySummaryNode. head is empty. nothing to summarize"),!1;const d=Ce(s),u=Ce(r),m=Ce(o),C={totalHistoryCharCount:d,totalHistoryExchangeCount:s.length,headCharCount:u,headExchangeCount:r.length,headLastRequestId:((tt=r.at(-1))==null?void 0:tt.request_id)??"",tailCharCount:m,tailExchangeCount:o.length,tailLastRequestId:((ct=o.at(-1))==null?void 0:ct.request_id)??"",summaryCharCount:0,summarizationDurationMs:0,isCacheAboutToExpire:t,isAborted:!1};let g=((bt=r.at(-1))==null?void 0:bt.response_nodes)??[],v=g.filter(Z=>Z.type===F.TOOL_USE);v.length>0&&(r.at(-1).response_nodes=g.filter(Z=>Z.type!==F.TOOL_USE)),console.info("Summarizing %d turns of conversation history.",r.length);const b=this.generateAbridgedHistoryText(r);console.info("Abridged history text size: %d characters.",b.length);const w=Date.now(),f=await this._conversationModel.sendSilentExchange({request_message:this._params.prompt,disableRetrieval:!0,disableSelectedCodeDetails:!0,chatHistory:r}),I=Date.now();if(console.info("Summary text size: %d characters.",f.responseText.length),C.summaryCharCount=f.responseText.length,C.summarizationDurationMs=I-w,C.isAborted=!!(e!=null&&e.aborted),this._extensionClient.reportAgentRequestEvent({eventName:we.chatHistorySummarization,conversationId:this._conversationModel.conversationId,requestId:f.requestId??"UNKNOWN_REQUEST_ID",chatHistoryLength:this._conversationModel.chatHistory.length,eventData:{chatHistorySummarizationData:C}}),e==null?void 0:e.aborted)return console.log("maybeAddHistorySummaryNode. aborted"),!1;if(!f.requestId||f.responseText.trim()==="")return console.log("maybeAddHistorySummaryNode. no request id or empty response"),!1;let Y=this._params.summaryNodeRequestMessageTemplate.replace("{summary}",`<summary>
${f.responseText}
</summary>`);Y.includes("{abridged_history}")&&(Y=Y.replace("{abridged_history}",`<abridged_history>
${b}
</abridged_history>`));const W=this._params.summaryNodeResponseMessage,P={chatItemType:Tt.historySummary,summaryVersion:this.historySummaryVersion,request_id:f.requestId,request_message:Y,response_text:W,structured_output_nodes:[{id:v.map(Z=>Z.id).reduce((Z,le)=>Math.max(Z,le),-1)+1,type:F.RAW_RESPONSE,content:W},...v],status:M.success,seen_state:mt.seen,timestamp:new Date().toISOString()},U=this._conversationModel.chatHistory.findLastIndex(Z=>Z.request_id===r.at(-1).request_id)+1;return console.info("Adding a history summary node at index %d",U),this._conversationModel.insertChatItem(U,P),!0}}const Me="POP_ABORTED";class di{constructor(t=void 0){this.maxQueueSize=t,this.queue=new $e,this.waiters=new $e}push(t){this.maxQueueSize&&this.queue.length>=this.maxQueueSize&&this.waiters.length===0&&this.queue.shift(),this.waiters.length>0?this.waiters.shift()(t):this.queue.push(t)}async pop(t){if(t!=null&&t.aborted)throw new Error(Me);return this.queue.length>0?this.queue.shift():new Promise((e,s)=>{const i=()=>{const r=this.waiters.toArray().indexOf(e);r!==-1&&this.waiters.removeOne(r)};t==null||t.addEventListener("abort",()=>{i(),s(new Error(Me))},{once:!0}),this.waiters.push(e)})}get size(){return this.queue.length}clear(){this.queue.clear()}}const Ct=class Ct{constructor(){a(this,"_queues",new Map);a(this,"_activeConsumers",new Map)}static getInstance(){return Ct._instance||(Ct._instance=new Ct),Ct._instance}_getQueueForType(t){const e=this._queues.get(t);if(e)return e;const s=new di(500);return this._queues.set(t,s),s}addNotification(t){this._getQueueForType(t.type).push(t)}getStream(t){var i;this._activeConsumers.has(t)&&(console.error(`Consumer already exists for type ${t}. Only one consumer per type is allowed at any given time. Cancelling previous consumer.`),(i=this._activeConsumers.get(t))==null||i());const e=new AbortController,s=()=>{e.abort(),this._activeConsumers.delete(t)};return this._activeConsumers.set(t,s),{stream:(async function*(){try{const r=this._getQueueForType(t);for(;!e.signal.aborted;)try{const o=r.pop(e.signal),l=await o;if(e.signal.aborted)return;yield l}catch(o){if((o==null?void 0:o.message)!==Me)throw o}}catch(r){if(console.error("Error in stream for",t,r),!e.signal.aborted)throw r}finally{this._activeConsumers.delete(t)}}).call(this),cancel:s}}dispose(){this._activeConsumers.forEach(t=>t()),this._activeConsumers.clear(),this._queues.forEach(t=>t.clear()),this._queues.clear(),Ct._instance=void 0}};a(Ct,"_instance");let ae=Ct;function wt(h){var t;return((t=h.extraData)==null?void 0:t.isAgentConversation)===!0}var R=(h=>(h[h.active=0]="active",h[h.inactive=1]="inactive",h))(R||{});const Se="temp-fe";class q{constructor(t,e,s,i,r,o){a(this,"_state");a(this,"_subscribers",new Set);a(this,"_focusModel",new Qs);a(this,"_onSendExchangeListeners",[]);a(this,"_onNewConversationListeners",[]);a(this,"_onHistoryDeleteListeners",[]);a(this,"_onBeforeChangeConversationListeners",[]);a(this,"_eventTracker");a(this,"_totalCharactersCacheThrottleMs",1e3);a(this,"_totalCharactersStore");a(this,"_chatHistorySummarizationModel");a(this,"_disposers",[]);a(this,"_subscribeToConversationUpdates",()=>{const t=ae.getInstance(),{stream:e,cancel:s}=t.getStream("conversation_updated"),{stream:i,cancel:r}=t.getStream("exchange_updated");this._disposers.push(()=>{s(),r()}),(async()=>{for await(const o of e)this._handleConversationUpdated(o)})(),(async()=>{for await(const o of i){const{exchange:l}=o.data;l.request_id&&(this._maybeAppendExchange(l),this.updateExchangeById(l,l.request_id))}})()});a(this,"_handleConversationUpdated",t=>{if(t.data.conversationId!==this._state.id)return;const{exchanges:e}=t.data;e.forEach(this._maybeAppendExchange);const s=new Set(e.map(r=>r.request_id)),i=new Set;this._state.chatHistory.forEach(r=>{const o=r.request_id;o&&!s.has(o)&&i.add(o)}),e.forEach(r=>{r.request_id&&this.updateExchangeById(r,r.request_id)}),this._removeTurnsByRequestId(i)});a(this,"_maybeAppendExchange",t=>{t.request_id&&(this.exchangeWithRequestId(t.request_id)||this.addExchange({...t,request_message:t.request_message??"",status:M.success}))});a(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));a(this,"setConversation",(t,e=!0,s=!0)=>{const i=t.id!==this._state.id;i&&s&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([o,l])=>{if(l.requestId&&l.toolUseId){const{requestId:c,toolUseId:d}=Qe(o);return c===l.requestId&&d===l.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",o,"but object has ",ye(l)),[o,l]}return[o,{...l,...Qe(o)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),e&&i&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const r=q.isEmpty(t);if(i&&r){const o=this._state.draftExchange;o&&(t.draftExchange=o)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(L)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(o=>o(this)),this._saveConversation(this._state),i&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(o=>o())),!0});a(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});a(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});a(this,"setName",t=>{this.update({name:t})});a(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});a(this,"updateFeedback",(t,e)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:e}})});a(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[ye(t)]:t}})});a(this,"getToolUseState",(t,e)=>t===void 0||e===void 0||this.toolUseStates===void 0?{phase:A.unknown,requestId:t??"",toolUseId:e??""}:this.toolUseStates[ye({requestId:t,toolUseId:e})]||{phase:A.new});a(this,"getLastToolUseId",()=>{var s,i;const t=this.lastExchange;if(!t)return;const e=(((s=t==null?void 0:t.structured_output_nodes)==null?void 0:s.filter(r=>r.type===F.TOOL_USE))??[]).at(-1);return e?(i=e.tool_use)==null?void 0:i.tool_use_id:void 0});a(this,"getLastToolUseState",()=>{var s;const t=this.lastExchange;if(!t)return{phase:A.unknown};const e=function(i=[]){let r;for(const o of i){if(o.type===F.TOOL_USE)return o;o.type===F.TOOL_USE_START&&(r=o)}return r}(t==null?void 0:t.structured_output_nodes);return e?this.getToolUseState(t.request_id,(s=e.tool_use)==null?void 0:s.tool_use_id):{phase:A.unknown}});a(this,"addExchange",(t,e)=>{const s=this._state.chatHistory;let i,r;i=e===void 0?[...s,t]:e===-1?s.length===0?[t]:[...s.slice(0,-1),t,s[s.length-1]]:[...s.slice(0,e),t,...s.slice(e)],L(t)&&(r=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:ms.unset,feedbackNote:""}}:void 0),this.update({chatHistory:i,...r?{feedbackStates:r}:{},lastUrl:void 0})});a(this,"addExchangeBeforeLast",t=>{this.addExchange(t,-1)});a(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});a(this,"updateExchangeById",(t,e,s=!1)=>{var l;const i=this.exchangeWithRequestId(e);if(i===null)return console.warn("No exchange with this request ID found."),!1;s&&t.response_text!==void 0&&(t.response_text=(i.response_text??"")+(t.response_text??"")),s&&(t.structured_output_nodes=function(c=[]){const d=si(c);return d&&d.type===F.TOOL_USE?c.filter(u=>u.type!==F.TOOL_USE_START):c}([...i.structured_output_nodes??[],...t.structured_output_nodes??[]])),t.stop_reason!==i.stop_reason&&i.stop_reason&&t.stop_reason===Vs.REASON_UNSPECIFIED&&(t.stop_reason=i.stop_reason),s&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...i.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const r=(l=(t.structured_output_nodes||[]).find(c=>c.type===F.MAIN_TEXT_FINISHED))==null?void 0:l.content;r&&r!==t.response_text&&(t.response_text=r);let o=this._state.isShareable||Lt({...i,...t});return this.update({chatHistory:this.chatHistory.map(c=>c.request_id===e?{...c,...t}:c),isShareable:o}),!0});a(this,"clearMessagesFromHistory",t=>{const e=this._collectToolUseIdsFromMessages(this.chatHistory.filter(s=>s.request_id&&t.has(s.request_id)));this.update({chatHistory:this.chatHistory.filter(s=>!s.request_id||!t.has(s.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t),toolUseIds:e})});a(this,"clearHistory",()=>{const t=this._collectToolUseIdsFromMessages(this.chatHistory);this._extensionClient.clearMetadataFor({requestIds:this.requestIds,toolUseIds:t}),this.update({chatHistory:[]})});a(this,"clearHistoryFrom",async(t,e=!0)=>{const s=this.historyFrom(t,e),i=s.map(o=>o.request_id).filter(o=>o!==void 0),r=this._collectToolUseIdsFromMessages(s);this.update({chatHistory:this.historyTo(t,!e)}),this._extensionClient.clearMetadataFor({requestIds:i,toolUseIds:r}),s.forEach(o=>{this._onHistoryDeleteListeners.forEach(l=>l(o))})});a(this,"clearMessageFromHistory",t=>{const e=this.chatHistory.find(i=>i.request_id===t),s=e?this._collectToolUseIdsFromMessages([e]):[];this.update({chatHistory:this.chatHistory.filter(i=>i.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t],toolUseIds:s})});a(this,"_collectToolUseIdsFromMessages",t=>{var s;const e=[];for(const i of t)if(L(i)&&i.structured_output_nodes)for(const r of i.structured_output_nodes)r.type===F.TOOL_USE&&((s=r.tool_use)!=null&&s.tool_use_id)&&e.push(r.tool_use.tool_use_id);return e});a(this,"historyTo",(t,e=!1)=>{const s=this.chatHistory.findIndex(i=>i.request_id===t);return s===-1?[]:this.chatHistory.slice(0,e?s+1:s)});a(this,"historyFrom",(t,e=!0)=>{const s=this.chatHistory.findIndex(i=>i.request_id===t);return s===-1?[]:this.chatHistory.slice(e?s:s+1)});a(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});a(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:M.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id},!1,t.request_id)));a(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e!==t&&(!t.request_id||e.request_id!==t.request_id))})});a(this,"_removeTurnsByRequestId",t=>{this.update({chatHistory:this.chatHistory.filter(e=>!!e.request_id&&!t.has(e.request_id))})});a(this,"exchangeWithRequestId",t=>this.chatHistory.find(e=>e.request_id===t)||null);a(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});a(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(s=>s.request_id===t.request_id))return;const e={seen_state:mt.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t.request_id?{...s,...e}:s)})});a(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));a(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const e=t.filter(s=>!s.personality&&!s.task);this.update({draftExchange:{...this.draftExchange,mentioned_items:e}})});a(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(e=>e.id);this.update({draftActiveContextIds:t})});a(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),e=this._specialContextInputModel.recentItems.filter(i=>t.has(i.id)||i.recentFile||i.selection||i.sourceFolder),s=this._specialContextInputModel.recentItems.filter(i=>!(t.has(i.id)||i.recentFile||i.selection||i.sourceFolder));this._specialContextInputModel.markItemsActive(e.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});a(this,"saveDraftExchange",(t,e)=>{var o,l,c;const s=t!==((o=this.draftExchange)==null?void 0:o.request_message),i=e!==((l=this.draftExchange)==null?void 0:l.rich_text_json_repr);if(!s&&!i)return;const r=(c=this.draftExchange)==null?void 0:c.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:e,mentioned_items:r,status:M.draft}})});a(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});a(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const e=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:e,model_id:this.selectedModelId??void 0}).then(()=>{var s;if(!wt(this)){const i=!this.name&&this.chatHistory.length===1&&((s=this.firstExchange)==null?void 0:s.request_id)===this.chatHistory[0].request_id;this._chatFlagModel.summaryTitles&&i&&this.updateConversationTitle()}}).finally(()=>{var s;wt(this)&&this._extensionClient.reportAgentRequestEvent({eventName:we.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});a(this,"cancelMessage",async()=>{var e;if(!this.canCancelMessage||!((e=this.lastExchange)!=null&&e.request_id))return;const t=this.lastExchange.request_id;this.updateExchangeById({status:M.cancelled},t),await this._extensionClient.cancelChatStream(t)});a(this,"sendInstructionExchange",async(t,e)=>{let s=`${Se}-${crypto.randomUUID()}`;const i={status:M.sent,request_id:s,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:mt.unseen,timestamp:new Date().toISOString()};this.addExchange(i);for await(const r of this._extensionClient.sendInstructionMessage(i,e)){if(!this.updateExchangeById(r,s,!0))return;s=r.request_id||s}});a(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});a(this,"checkAndGenerateAgentTitle",()=>{var e;if(!(!wt(this)||!this._chatFlagModel.summaryTitles||this.name)){var t;!this.name&&(t=this.chatHistory,t.filter(s=>Te(s))).length===1&&!((e=this.extraData)!=null&&e.hasTitleGenerated)&&(this.update({extraData:{...this.extraData,hasTitleGenerated:!0}}),this.updateConversationTitle())}});a(this,"sendSummaryExchange",()=>{const t={status:M.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:Tt.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});a(this,"generateCommitMessage",async()=>{let t=`${Se}-${crypto.randomUUID()}`;const e={status:M.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:mt.unseen,chatItemType:Tt.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(e);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,t,!0))return;t=s.request_id||t}});a(this,"sendExchange",async(t,e=!1,s)=>{var m,C,g;this._chatHistorySummarizationModel.cancelRunningOrScheduledSummarizations(),this.updateLastInteraction();let i=`${Se}-${crypto.randomUUID()}`,r=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(q.isNew(this._state)){const v=crypto.randomUUID(),b=this._state.id;try{await this._extensionClient.migrateConversationId(b,v)}catch(w){console.error("Failed to migrate conversation checkpoints:",w)}this._state={...this._state,id:v},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(v),this._subscribers.forEach(w=>w(this))}t=ts(t);let o={status:M.sent,request_id:i,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:r,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:mt.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(o),this._loadContextFromExchange(o),this._onSendExchangeListeners.forEach(v=>v(o)),this._chatFlagModel.useHistorySummary&&!t.request_message&&await this._chatHistorySummarizationModel.maybeAddHistorySummaryNode()&&this.update({chatHistory:this._chatHistorySummarizationModel.clearStaleHistorySummaryNodes(this.chatHistory)}),o=await this._addIdeStateNode(o),this.updateExchangeById({structured_request_nodes:o.structured_request_nodes},i,!1);const l=Date.now();let c=!1,d=0;for await(const v of this.sendUserMessage(i,o,e,s)){if(((m=this.exchangeWithRequestId(i))==null?void 0:m.status)!==M.sent||!this.updateExchangeById(v,i,!0))return;if(i=v.request_id||i,!c&&wt(this)){const b=Date.now();d=b-l,this._extensionClient.reportAgentRequestEvent({eventName:we.firstTokenReceived,conversationId:this.id,requestId:i,chatHistoryLength:this.chatHistory.length,eventData:{firstTokenTimingData:{userMessageSentTimestampMs:l,firstTokenReceivedTimestampMs:b,timeToFirstTokenMs:d}}}),c=!0}}const u=Date.now()-l;(g=this._eventTracker)==null||g.trackEvent(Es.MESSAGE_SEND_TIMING,{requestId:i,timeToFirstTokenMs:d,timeToLastTokenMs:u,responseLength:(C=t==null?void 0:t.response_text)==null?void 0:C.length,chatHistoryLength:this.chatHistory.length,modelId:o.model_id}),this._chatHistorySummarizationModel.maybeScheduleSummarization(u)});a(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:M.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(Ms.chatUseSuggestedQuestion)});a(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});a(this,"recoverExchange",async t=>{var i;if(!t.request_id||t.status!==M.sent)return;let e=t.request_id;const s=(i=t.structured_output_nodes)==null?void 0:i.filter(r=>r.type===F.AGENT_MEMORY);this.updateExchangeById({...t,response_text:t.lastChunkId?t.response_text:"",structured_output_nodes:t.lastChunkId?t.structured_output_nodes??[]:s},e);for await(const r of this.getChatStream(t)){if(!this.updateExchangeById(r,e,!0))return;e=r.request_id||e}});a(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(e=>{L(e)&&this._loadContextFromExchange(e)})});a(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});a(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(e=>{L(e)&&this._unloadContextFromExchange(e)})});a(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});a(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});a(this,"_jsonToStructuredRequest",t=>{const e=[],s=r=>{var l;const o=e.at(-1);if((o==null?void 0:o.type)===G.TEXT){const c=((l=o.text_node)==null?void 0:l.content)??"",d={...o,text_node:{content:c+r}};e[e.length-1]=d}else e.push({id:e.length,type:G.TEXT,text_node:{content:r}})},i=r=>{var o,l,c,d,u;if(r.type==="doc"||r.type==="paragraph")for(const m of r.content??[])i(m);else if(r.type==="hardBreak")s(`
`);else if(r.type==="text")s(r.text??"");else if(r.type==="file"){if(typeof((o=r.attrs)==null?void 0:o.src)!="string")return void console.error("File source is not a string: ",(l=r.attrs)==null?void 0:l.src);if(r.attrs.isLoading)return;const m=(c=r.attrs)==null?void 0:c.title,C=js(m);cs(m)?e.push({id:e.length,type:G.IMAGE_ID,image_id_node:{image_id:r.attrs.src,format:C}}):e.push({id:e.length,type:G.FILE_ID,file_id_node:{file_id:r.attrs.src,file_name:m}})}else if(r.type==="mention"){const m=(d=r.attrs)==null?void 0:d.data;m&&ls(m)?e.push({id:e.length,type:G.TEXT,text_node:{content:As(this._chatFlagModel,m.personality.type)}}):m&&Fs(m)?e.push({id:e.length,type:G.TEXT,text_node:{content:qs.getTaskOrchestratorPrompt(m.task)}}):s(`@\`${(m==null?void 0:m.name)??(m==null?void 0:m.id)}\``)}else if(r.type==="askMode"){const m=(u=r.attrs)==null?void 0:u.prompt;m&&e.push({id:e.length,type:G.TEXT,text_node:{content:m}})}};return i(t),e});a(this,"dispose",()=>{this._disposers.forEach(t=>t()),this._disposers=[]});this._extensionClient=t,this._chatFlagModel=e,this._specialContextInputModel=s,this._saveConversation=i,this._rulesModel=r,this._state={...q.create(o!=null&&o.forceAgentConversation?{extraData:{isAgentConversation:!0,hasAgentOnboarded:!0}}:void 0)},this._totalCharactersStore=this._createTotalCharactersStore(),this._chatHistorySummarizationModel=new ci(this,t,e),this._subscribeToConversationUpdates()}get conversationId(){return this._state.id}insertChatItem(t,e){const s=[...this._state.chatHistory];s.splice(t,0,e),this.update({chatHistory:s})}_createTotalCharactersStore(){return Xs(()=>{let t=0;const e=this._state.chatHistory;return this.convertHistoryToExchanges(e).forEach(s=>{t+=JSON.stringify(s).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}setEventTracker(t){this._eventTracker=t}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((s,i)=>s+i,0))||0)<=4?Pt.PROTOTYPER:Pt.DEFAULT}catch(e){return console.error("Error determining persona type:",e),Pt.DEFAULT}}static create(t={}){const e=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:e,lastInteractedAtIso:e,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:Pt.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){if(t.name)return t.name;const e=t.chatHistory.find(L);return e&&e.request_message?q.toSentenceCase(e.request_message):wt(t)?"New Agent":"New Chat"}static isNew(t){return t.id===gt}static isEmpty(t){var i;const e=t.chatHistory.filter(r=>L(r)),s=t.chatHistory.filter(r=>Ie(r));return e.length===0&&s.length===0&&!((i=t.draftExchange)!=null&&i.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static getTime(t,e){return e==="lastMessageTimestamp"?q.lastMessageTimestamp(t):e==="lastInteractedAt"?q.lastInteractedAt(t):q.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){var s;const e=(s=t.chatHistory.findLast(L))==null?void 0:s.timestamp;return e?new Date(e):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!q.isEmpty(t)||q.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(e=>e!==t)}}_notifyBeforeChangeConversation(t,e){let s=e;for(const i of this._onBeforeChangeConversationListeners){const r=i(t,s);r!==void 0&&(s=r)}return s}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return q.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??Pt.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return q.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return q.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){if(this.flags.enableModelRegistry&&wt(this._state))return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}get flags(){return this._chatFlagModel}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var s;const t=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",e=this.hasImagesInDraft();return t||e}hasImagesInDraft(){var s;const t=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!t)return!1;const e=i=>Array.isArray(i)?i.some(e):!!i&&(i.type==="file"||!(!i.content||!Array.isArray(i.content))&&i.content.some(e));return e(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){return this.chatHistory.find(L)??null}get lastExchange(){return this.chatHistory.findLast(L)??null}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>L(t)&&t.status===M.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>Lt(t)||Gt(t)||qt(t))}get totalCharactersStore(){return this._totalCharactersStore}convertHistoryToExchanges(t){if(t.length===0)return[];t=this._chatHistorySummarizationModel.preprocessChatHistory(t);const e=[];for(const s of t)if(Lt(s))e.push(Ye(s));else if(qt(s))e.push(Ye(s));else if(Gt(s)&&s.fromTimestamp!==void 0&&s.toTimestamp!==void 0&&s.revertTarget){const i=ui(s,1),r={request_message:"",response_text:"",request_id:s.request_id||crypto.randomUUID(),request_nodes:[i],response_nodes:[]};e.push(r)}return e}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===M.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const e=crypto.randomUUID();let s,i="";const r=await this._addIdeStateNode(ts({...t,request_id:e,status:M.sent,timestamp:new Date().toISOString()}));for await(const o of this.sendUserMessage(e,r,!0))o.response_text&&(i+=o.response_text),o.request_id&&(s=o.request_id);return{responseText:i,requestId:s}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t.request_id,t.lastChunkId,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,e,s){return[]}_resolveUnresolvedToolUses(t,e,s){var u,m,C;if(t.length===0)return[t,e];const i=t[t.length-1],r=((u=i.response_nodes)==null?void 0:u.filter(g=>g.type===F.TOOL_USE))??[];if(r.length===0)return[t,e];const o=new Set;(m=e.structured_request_nodes)==null||m.forEach(g=>{var v;g.type===G.TOOL_RESULT&&((v=g.tool_result_node)!=null&&v.tool_use_id)&&o.add(g.tool_result_node.tool_use_id)});const l=r.filter(g=>{var b;const v=(b=g.tool_use)==null?void 0:b.tool_use_id;return v&&!o.has(v)});if(l.length===0)return[t,e];const c=l.map((g,v)=>{const b=g.tool_use.tool_use_id;return function(w,f,I,Y){const W=ei(f,w,Y);let P;if(W!==void 0)P=W;else{let U;switch(f.phase){case A.runnable:U="Tool was cancelled before running.";break;case A.new:U="Cancelled by user.";break;case A.checkingSafety:U="Tool was cancelled during safety check.";break;case A.running:U="Tool was cancelled while running.";break;case A.cancelling:U="Tool cancellation was interrupted.";break;case A.cancelled:U="Cancelled by user.";break;case A.error:U="Tool execution failed.";break;case A.completed:U="Tool completed but result was unavailable.";break;case A.unknown:default:U="Cancelled by user.",f.phase!==A.unknown&&console.error(`Unexpected tool state phase: ${f.phase}`)}P={tool_use_id:w,content:U,is_error:!0}}return{id:I,type:G.TOOL_RESULT,tool_result_node:P}}(b,this.getToolUseState(i.request_id,b),Ae(e.structured_request_nodes??[])+v+1,this._chatFlagModel.enableDebugFeatures)});if((C=e.structured_request_nodes)==null?void 0:C.some(g=>g.type===G.TOOL_RESULT))return[t,{...e,structured_request_nodes:[...e.structured_request_nodes??[],...c]}];{const g={request_message:"",response_text:"OK.",request_id:crypto.randomUUID(),structured_request_nodes:c,structured_output_nodes:[],status:M.success,hidden:!0};return s||this.addExchangeBeforeLast(g),[t.concat(this.convertHistoryToExchanges([g])),e]}}async*sendUserMessage(t,e,s,i){const r=this._chatFlagModel.enableParallelTools,o=await(r?((l,c,d)=>{const u=ve.get(l)??Ys();return ve.has(l)||ve.set(l,u),u.start(c,d)})("sendMessage",t):Promise.resolve({end:()=>{}}));try{for await(const l of this._sendUserMessage(t,e,s,i))yield l}finally{o.end(t)}}async*_sendUserMessage(t,e,s,i){var C;const r=this._specialContextInputModel.chatActiveContext;let o;if(e.chatHistory!==void 0)o=e.chatHistory;else{let g=this.successfulMessages;if(e.chatItemType===Tt.summaryTitle){const v=g.findIndex(b=>b.chatItemType!==Tt.agentOnboarding&&Te(b));v!==-1&&(g=g.slice(v))}o=this.convertHistoryToExchanges(g)}this._chatFlagModel.enableParallelTools&&([o,e]=this._resolveUnresolvedToolUses(o,e,s));let l=this.personaType;if(e.structured_request_nodes){const g=e.structured_request_nodes.find(v=>v.type===G.CHANGE_PERSONALITY);g&&g.change_personality_node&&(l=g.change_personality_node.personality_type)}let c=[];if(this._chatFlagModel.enableRules&&this._rulesModel){this._rulesModel.requestRules();const g=se(this._rulesModel.getCachedRules());c=Re.filterRulesByContext(g,r.ruleFiles||[])}const d={text:e.request_message,chatHistory:o,silent:s,modelId:e.model_id,context:r,userSpecifiedFiles:r.userSpecifiedFiles,externalSourceIds:(C=r.externalSources)==null?void 0:C.map(g=>g.id),disableRetrieval:e.disableRetrieval??!1,disableSelectedCodeDetails:e.disableSelectedCodeDetails??!1,nodes:e.structured_request_nodes,memoriesInfo:e.memoriesInfo,personaType:l,conversationId:this.id,createdTimestamp:Date.now(),requestIdOverride:i,rules:c},u=this._createStreamStateHandlers(t,d,{flags:this._chatFlagModel}),m=this._extensionClient.startChatStreamWithRetry(t,d,{flags:this._chatFlagModel});for await(const g of m){let v=g;t=g.request_id||t;for(const b of u)v=b.handleChunk(v)??v;yield v}for(const g of u)yield*g.handleComplete();this.updateExchangeById({structured_request_nodes:e.structured_request_nodes},t)}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(e=>e!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(e=>e!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(e=>e!==t)}}updateChatItem(t,e){return this.chatHistory.find(s=>s.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t?{...s,...e}:s)}),!0)}async _addIdeStateNode(t){let e,s=(t.structured_request_nodes??[]).filter(i=>i.type!==G.IDE_STATE);try{e=await this._extensionClient.getChatRequestIdeState()}catch(i){console.error("Failed to add IDE state to exchange:",i)}return e?(s=[...s,{id:Ae(s)+1,type:G.IDE_STATE,ide_state_node:e}],{...t,structured_request_nodes:s}):t}}function ui(h,t){const e=(Gt(h),h.fromTimestamp),s=(Gt(h),h.toTimestamp),i=Gt(h)&&h.revertTarget!==void 0;return{id:t,type:G.CHECKPOINT_REF,checkpoint_ref_node:{request_id:h.request_id||"",from_timestamp:e,to_timestamp:s,source:i?Zs.CHECKPOINT_REVERT:void 0}}}function Ye(h){const t=(h.structured_output_nodes??[]).filter(e=>e.type===F.RAW_RESPONSE||e.type===F.TOOL_USE||e.type===F.TOOL_USE_START||e.type===F.THINKING).map(e=>e.type===F.TOOL_USE_START?{...e,tool_use:{...e.tool_use,input_json:"{}"},type:F.TOOL_USE}:e);return{request_message:h.request_message,response_text:h.response_text??"",request_id:h.request_id||"",request_nodes:h.structured_request_nodes??[],response_nodes:t}}function Ae(h){return h.length>0?Math.max(...h.map(t=>t.id)):0}function ts(h){var t;if(h.request_message.length>0&&!((t=h.structured_request_nodes)!=null&&t.some(e=>e.type===G.TEXT))){let e=h.structured_request_nodes??[];return e=[...e,{id:Ae(e)+1,type:G.TEXT,text_node:{content:h.request_message}}],{...h,structured_request_nodes:e}}return h}class mi{constructor(t){a(this,"trackedExperiments",new Set);this.dependencies=t}_getChatMessagePayload(){const t=se(this.dependencies.chatModeType),e=se(this.dependencies.agentExecutionMode),s={chatMode:t,agentExecutionMode:t==="localAgent"?e:void 0};return t==="localAgent"&&(s.sendMode=se(this.dependencies.currentSendMode)),s}trackEvent(t,e){const s={...this._getChatMessagePayload(),...e};this.trackSimpleEvent(t,s)}trackSimpleEvent(t,e){this.dependencies.extensionClient.trackEventWithTypes(t,e)}trackExperimentViewed(t,e,s){if(e===Ls.OFF)return;const i=`${t}:${e}`;this.trackedExperiments.has(i)||(this.trackedExperiments.add(i),this.dependencies.extensionClient.trackExperimentViewed(t,e,s))}}class _i{constructor(t=!0,e=setTimeout){a(this,"_notify",new Set);a(this,"_clearTimeout",t=>{t.timeoutId&&clearTimeout(t.timeoutId)});a(this,"_schedule",t=>{if(!this._started||t.date&&(t.timeout=t.date.getTime()-Date.now(),t.timeout<0))return;const e=this._setTimeout;t.timeoutId=e(this._handle,t.timeout,t)});a(this,"_handle",t=>{t.notify(),t.date?this._notify.delete(t):t.once||this._schedule(t)});a(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=t,this._setTimeout=e}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(t){t?this.start():this.stop()}once(t,e){return this._register(t,e,!0)}interval(t,e){return this._register(t,e,!1)}at(t,e){return this._register(0,e,!1,typeof t=="number"?new Date(Date.now()+t):t)}reschedule(){this._notify.forEach(t=>{this._clearTimeout(t),this._schedule(t)})}_register(t,e,s,i){if(!t&&!i)return()=>{};const r={timeout:t,notify:e,once:s,date:i};return this._notify.add(r),this._schedule(r),()=>{this._clearTimeout(r),this._notify.delete(r)}}}class gi{constructor(t=0,e=0,s=new _i,i=lt("busy"),r=lt(!1)){a(this,"unsubNotify");a(this,"unsubMessage");a(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});a(this,"focus",t=>{this.focusAfterIdle.set(t)});this._idleNotifyTimeout=t,this._idleMessageTimeout=e,this.idleScheduler=s,this.idleStatus=i,this.focusAfterIdle=r,this.idleNotifyTimeout=t,this.idleMessageTimeout=e}set idleMessageTimeout(t){var e;this._idleMessageTimeout!==t&&(this._idleMessageTimeout=t,(e=this.unsubMessage)==null||e.call(this),this.unsubMessage=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(t){var e;this._idleNotifyTimeout!==t&&(this._idleNotifyTimeout=t,(e=this.unsubNotify)==null||e.call(this),this.unsubNotify=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var t,e;(t=this.unsubNotify)==null||t.call(this),(e=this.unsubMessage)==null||e.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}var pi=Vt("<svg><!></svg>"),fi=Vt("<svg><!></svg>"),ie=(h=>(h.send="send",h.addTask="addTask",h))(ie||{});const yi={id:"send",label:"Send to Agent",icon:function(h,t){const e=us(t,["children","$$slots","$$events","$$legacy"]);var s=pi();as(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...e}));var i=hs(s);ds(i,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M57.6 35.8C23.1 20.6-11.3 57.4 6.1 90.9l63 121.2c4.4 8.4 12.6 14.1 22 15.3L266 249.3c3.4.4 6 3.3 6 6.7s-2.6 6.3-6 6.7L91.1 284.6c-9.4 1.2-17.6 6.9-22 15.3l-63 121.2c-17.4 33.5 17 70.2 51.6 55.1l435.2-190.9c25.5-11.2 25.5-47.4 0-58.6z"/>',!0),Bt(h,s)},description:"Send message to agent"},vi={id:"addTask",label:"Add Task",icon:function(h,t){const e=us(t,["children","$$slots","$$events","$$legacy"]);var s=fi();as(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...e}));var i=hs(s);ds(i,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M256 512a256 256 0 1 0 0-512 256 256 0 1 0 0 512m-24-168v-64h-64c-13.3 0-24-10.7-24-24s10.7-24 24-24h64v-64c0-13.3 10.7-24 24-24s24 10.7 24 24v64h64c13.3 0 24 10.7 24 24s-10.7 24-24 24h-64v64c0 13.3-10.7 24-24 24s-24-10.7-24-24"/>',!0),Bt(h,s)},description:"Add task with the message content"},Zi=[yi,vi];class Ci{constructor(){a(this,"_mode",lt(ie.send));a(this,"_currentMode",ie.send);this._mode.subscribe(t=>{this._currentMode=t})}get mode(){return this._mode}setMode(t){this._mode.set(t)}getCurrentMode(){return this._currentMode}initializeFromState(t){t&&Object.values(ie).includes(t)&&this._mode.set(t)}}class Si{constructor(t=3e5){a(this,"_cleanItems",new Set);a(this,"_lastProcessedTime",new Map);this.cooldownMs=t}markClean(t){this._cleanItems.add(t),this._lastProcessedTime.set(t,Date.now())}markDirty(t){this._cleanItems.delete(t),this._lastProcessedTime.delete(t)}isClean(t){return this._cleanItems.has(t)}isWithinCooldown(t){const e=this._lastProcessedTime.get(t);return!!e&&Date.now()-e<this.cooldownMs}getLastProcessedTime(t){return this._lastProcessedTime.get(t)||0}cleanup(t){const e=Array.isArray(t)?t:Array.from(t);for(const s of e)this.markDirty(s)}clear(){this._cleanItems.clear(),this._lastProcessedTime.clear()}getStats(){return{cleanCount:this._cleanItems.size,trackedCount:this._lastProcessedTime.size}}}const $=[];for(let h=0;h<256;++h)$.push((h+256).toString(16).slice(1));let be;const bi=new Uint8Array(16),es={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function xi(h,t,e){var i;if(es.randomUUID&&!h)return es.randomUUID();const s=(h=h||{}).random??((i=h.rng)==null?void 0:i.call(h))??function(){if(!be){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");be=crypto.getRandomValues.bind(crypto)}return be(bi)}();if(s.length<16)throw new Error("Random bytes length must be >= 16");return s[6]=15&s[6]|64,s[8]=63&s[8]|128,function(r,o=0){return($[r[o+0]]+$[r[o+1]]+$[r[o+2]]+$[r[o+3]]+"-"+$[r[o+4]]+$[r[o+5]]+"-"+$[r[o+6]]+$[r[o+7]]+"-"+$[r[o+8]]+$[r[o+9]]+"-"+$[r[o+10]]+$[r[o+11]]+$[r[o+12]]+$[r[o+13]]+$[r[o+14]]+$[r[o+15]]).toLowerCase()}(s)}class wi{constructor(t,e,s=new Si(3e5)){a(this,"_maxItemsPerIteration",5);this._extensionClient=t,this._flags=e,this._cache=s}async hydrateConversation(t){let e=t;return e=await this._hydrateExchanges(e),e=await this._hydrateToolUseStates(e),this._cache.markDirty(t.id),e}async dehydrateConversation(t){let e=t;return this._flags.enableExchangeStorage&&(e=await this._dehydrateExchanges(e)),this._flags.enableToolUseStateStorage&&(e=await this._dehydrateToolUseStates(e)),e}async dehydrateConversationsIncremental(t,e){if(!this._flags.enableExchangeStorage&&!this._flags.enableToolUseStateStorage)return t;const s=Object.entries(t),i=this._selectConversationsForDehydration(s,e),r={};for(const[o,l]of s)if(i.includes(o))try{const c=await this.dehydrateConversation(l);this._cache.markClean(o),r[o]=o===e?l:c}catch(c){console.warn(`Failed to dehydrate conversation ${o}:`,c),r[o]=l}else r[o]=l;return r}async hydrateCurrentConversation(t,e){if(!e||!t[e])return t;try{const s=await this.hydrateConversation(t[e]);return{...t,[e]:s}}catch(s){return console.warn(`Failed to hydrate conversation ${e}:`,s),t}}markNeedsDehydration(t){this._cache.markDirty(t)}markDehydrated(t){this._cache.markClean(t)}cleanupDeletedConversations(t){this._cache.cleanup(t)}_selectConversationsForDehydration(t,e){var r;const s=[];if(e){const o=(r=t.find(([l])=>l===e))==null?void 0:r[1];o&&!this._cache.isClean(e)&&(o.chatHistory.some(L)?s.push(e):this._cache.markClean(e))}const i=t.filter(([o,l])=>o===e||this._cache.isClean(o)?!1:l.chatHistory.some(L)?!this._cache.isWithinCooldown(o):(this._cache.markClean(o),!1)).map(([o])=>o).sort((o,l)=>this._cache.getLastProcessedTime(o)-this._cache.getLastProcessedTime(l));return[...s,...i].slice(0,this._maxItemsPerIteration)}async _dehydrateExchanges(t){var i;const e=[],s=[];for(const r of t.chatHistory)if(L(r)){const o=r,l=o.request_id||((i=crypto==null?void 0:crypto.randomUUID)==null?void 0:i.call(crypto))||xi(),c={request_message:o.request_message,response_text:o.response_text||"",request_id:l,request_nodes:o.structured_request_nodes,response_nodes:o.structured_output_nodes,uuid:l,conversationId:t.id,status:o.status===M.success?"success":o.status===M.failed?"failed":"sent",timestamp:o.timestamp||new Date().toISOString(),seen_state:o.seen_state===mt.seen?"seen":"unseen"};e.push(c);const d={chatItemType:Tt.exchangePointer,exchangeUuid:l,timestamp:o.timestamp,request_message:o.request_message,status:o.status,hasResponse:!!o.response_text,isStreaming:o.status===M.sent,seen_state:o.seen_state};s.push(d)}else s.push(r);if(e.length>0)try{await this._extensionClient.saveExchanges(t.id,e)}catch{return t}return{...t,chatHistory:s}}async _dehydrateToolUseStates(t){if(!this._flags.enableToolUseStateStorage||!t.toolUseStates||Object.keys(t.toolUseStates).length===0)return t;try{return await this._extensionClient.saveToolUseStates(t.id,t.toolUseStates),{...t,toolUseStates:{}}}catch(e){return console.warn(`Failed to store tool use states for conversation ${t.id}:`,e),t}}async _hydrateExchanges(t){const e=t.chatHistory.filter(Ie);if(e.length===0)return t;try{const s=e.map(l=>l.exchangeUuid),i=await this._extensionClient.loadExchanges(t.id,s),r=new Map(i.map(l=>[l.uuid,l])),o=t.chatHistory.map(l=>{if(Ie(l)){const c=r.get(l.exchangeUuid);if(c)return{request_message:c.request_message,response_text:c.response_text,request_id:c.request_id,structured_request_nodes:c.request_nodes,structured_output_nodes:c.response_nodes,timestamp:c.timestamp,status:c.status==="success"?M.success:c.status==="failed"?M.failed:M.sent,seen_state:c.seen_state==="seen"?mt.seen:mt.unseen}}return l});return{...t,chatHistory:o}}catch(s){return console.warn(`Failed to restore exchanges for conversation ${t.id}:`,s),t}}async _hydrateToolUseStates(t){try{const e=await this._extensionClient.loadConversationToolUseStates(t.id);return{...t,toolUseStates:{...t.toolUseStates,...e}}}catch(e){return console.warn(`Failed to restore tool use states for conversation ${t.id}:`,e),t}}}const ee=lt("idle");var Ii=(h=>(h.manual="manual",h.auto="auto",h))(Ii||{});class Ti{constructor(t,e,s,i={}){a(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isPanelCollapsed:!0,displayedAnnouncements:[]});a(this,"extensionClient");a(this,"_chatFlagsModel");a(this,"_currConversationModel");a(this,"_chatModeModel");a(this,"_sendModeModel");a(this,"_flagsLoaded",lt(!1));a(this,"_eventTracker");a(this,"_rulesModel");a(this,"_persistenceController");a(this,"_messageBroker");a(this,"_disposers",[]);a(this,"subscribers",new Set);a(this,"idleMessageModel",new gi);a(this,"isPanelCollapsed");a(this,"agentExecutionMode");a(this,"sortConversationsBy");a(this,"displayedAnnouncements");a(this,"onLoaded",async()=>{var e,s;const t=await this.extensionClient.getChatInitData();this._chatFlagsModel.update({enableEditableHistory:t.enableEditableHistory??!1,enablePreferenceCollection:t.enablePreferenceCollection??!1,enableRetrievalDataCollection:t.enableRetrievalDataCollection??!1,enableDebugFeatures:t.enableDebugFeatures??!1,enableRichTextHistory:t.useRichTextHistory??!0,enableAgentSwarmMode:t.enableAgentSwarmMode??!1,modelDisplayNameToId:t.modelDisplayNameToId??{},fullFeatured:t.fullFeatured??!0,smallSyncThreshold:t.smallSyncThreshold??Ns,bigSyncThreshold:t.bigSyncThreshold??Os,enableExternalSourcesInChat:t.enableExternalSourcesInChat??!1,enableSmartPaste:t.enableSmartPaste??!1,enableDirectApply:t.enableDirectApply??!1,summaryTitles:t.summaryTitles??!1,suggestedEditsAvailable:t.suggestedEditsAvailable??!1,enableShareService:t.enableShareService??!1,maxTrackableFileCount:t.maxTrackableFileCount??Rs,enableDesignSystemRichTextEditor:t.enableDesignSystemRichTextEditor??!1,enableSources:t.enableSources??!1,enableChatMermaidDiagrams:t.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:t.smartPastePrecomputeMode??xs.visibleHover,useNewThreadsMenu:t.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:t.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:t.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:t.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:t.enableChatMultimodal??!1,enableAgentMode:t.enableAgentMode??!1,agentMemoriesFilePathName:t.agentMemoriesFilePathName,enableRichCheckpointInfo:t.enableRichCheckpointInfo??!1,userTier:t.userTier??"unknown",truncateChatHistory:t.truncateChatHistory??!1,enableBackgroundAgents:t.enableBackgroundAgents??!1,enableNewThreadsList:t.enableNewThreadsList??!1,enableVirtualizedMessageList:t.enableVirtualizedMessageList??!1,customPersonalityPrompts:t.customPersonalityPrompts??{},enablePersonalities:t.enablePersonalities??!1,enableRules:t.enableRules??!1,memoryClassificationOnFirstToken:t.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:t.enableGenerateCommitMessage??!1,enablePromptEnhancer:t.enablePromptEnhancer??!1,modelRegistry:t.modelRegistry??{},agentChatModel:t.agentChatModel??"",enableModelRegistry:t.enableModelRegistry??!1,enableTaskList:t.enableTaskList??!1,enableAgentAutoMode:t.enableAgentAutoMode??!1,enableExchangeStorage:t.enableExchangeStorage??!1,enableToolUseStateStorage:t.enableToolUseStateStorage??!1,clientAnnouncement:t.clientAnnouncement??"",useHistorySummary:t.useHistorySummary??!1,historySummaryParams:t.historySummaryParams??"",conversationHistorySizeThresholdBytes:t.conversationHistorySizeThresholdBytes??0,retryChatStreamTimeouts:t.retryChatStreamTimeouts??!1,enableCommitIndexing:t.enableCommitIndexing??!1,enableMemoryRetrieval:t.enableMemoryRetrieval??!1,isVscodeVersionOutdated:t.isVscodeVersionOutdated??!1,vscodeMinVersion:t.vscodeMinVersion??"",enableAgentTabs:t.enableAgentTabs??!1,enableAgentGitTracker:t.enableAgentGitTracker??!1,remoteAgentsResumeHintAvailableTtlDays:t.remoteAgentsResumeHintAvailableTtlDays??0,enableParallelTools:t.enableParallelTools??!1,memoriesParams:t.memoriesParams??{},modelInfoRegistry:t.modelInfoRegistry??{},subscriptionBannerDismissibility:t.subscriptionBannerDismissibility??Ds.OFF,showThinkingSummary:t.showThinkingSummary??!1}),this._chatFlagsModel.enableAgentAutoMode||this.agentExecutionMode.set("manual"),this._flagsLoaded.set(!0),await this.initializeAsync(this.options.initialConversation),(s=(e=this.options).onLoaded)==null||s.call(e),this.notifySubscribers()});a(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));a(this,"initializeSync",t=>{if(this._state={...this._state,...this._host.getState()},t&&(this._state.conversations={...this._state.conversations,[t.id]:t}),this._chatFlagsModel.fullFeatured&&((t==null?void 0:t.id)!==fe&&this.currentConversationId!==fe||(delete this._state.conversations[fe],this.setCurrentConversationToWelcome())),this._disposers.push(this._chatFlagsModel.subscribe(e=>{this.idleMessageModel.idleNotifyTimeout=e.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=e.idleNewSessionMessageTimeoutMs})),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([e,s])=>e===gt||q.isValid(s))),this.currentConversationId&&this.currentConversationId!==this.currentConversationModel.id){const e=this.conversations[this.currentConversationId];e&&this.currentConversationModel.setConversation(e)}this.initializeIsShareableState(),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});a(this,"_subscribeToChatNotifications",()=>{const t=ae.getInstance(),{stream:e,cancel:s}=t.getStream("conversation_id_changed");this._disposers.push(()=>{s()}),(async()=>{for await(const i of e){const r=i.data.conversationId;this._state.conversations[r]!==void 0||(this._state.conversations[r]={...q.create({id:r})}),await this.setCurrentConversation(r)}})()});a(this,"initializeIsShareableState",()=>{const t={...this._state.conversations};for(const[e,s]of Object.entries(t)){if(s.isShareable)continue;const i=s.chatHistory.some(r=>Lt(r));t[e]={...s,isShareable:i}}this._state.conversations=t});a(this,"updateChatState",t=>{this._state={...this._state,...t};const e=this._state.conversations,s=new Set;for(const[i,r]of Object.entries(e))r.isPinned&&s.add(i);this.setState(this._state),this.notifySubscribers()});a(this,"save",()=>{this.setState(this._state)});a(this,"saveImmediate",()=>{this.setState(this._state),this.setState.flush()});a(this,"setState");a(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});a(this,"withWebviewClientEvent",(t,e)=>(...s)=>(this.extensionClient.reportWebviewClientEvent(t),e(...s)));a(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:Tt.educateFeatures,request_id:crypto.randomUUID(),seen_state:mt.seen})});a(this,"popCurrentConversation",async()=>{var e,s;const t=this.currentConversationId;t&&await this.deleteConversation(t,((e=this.nextConversation)==null?void 0:e.id)??((s=this.previousConversation)==null?void 0:s.id))});a(this,"setCurrentConversation",async(t,e=!0,s)=>{if(t===this.currentConversationId&&(s!=null&&s.noopIfSameConversation))return;let i;t===void 0&&(t=gt);const r=this._state.conversations[t];i=r?await this._persistenceController.hydrateConversation(r):q.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid}),t===gt&&(i.id=gt),s!=null&&s.newTaskUuid&&(i.rootTaskUuid=s.newTaskUuid);const o=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(i,!o,e),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});a(this,"saveConversation",async(t,e)=>{this._persistenceController.markNeedsDehydration(t.id),this.updateChatState({...this._state,currentConversationId:t.id,conversations:{...this._state.conversations,[t.id]:t}}),e&&delete this._state.conversations[gt]});a(this,"isConversationShareable",t=>{var e;return((e=this._state.conversations[t])==null?void 0:e.isShareable)??!0});a(this,"setSortConversationsBy",t=>{this.sortConversationsBy.set(t),this.updateChatState({})});a(this,"getConversationUrl",async t=>{const e=this._state.conversations[t];if(e.lastUrl)return e.lastUrl;ee.set("copying");const s=e==null?void 0:e.chatHistory,i=s.reduce((l,c)=>(Lt(c)&&l.push({request_id:c.request_id||"",request_message:c.request_message,response_text:c.response_text||""}),l),[]);if(i.length===0)throw new Error("No chat history to share");const r=q.getDisplayName(e),o=await this.extensionClient.saveChat(t,i,r);if(o.data){let l=o.data.url;return this.updateChatState({conversations:{...this._state.conversations,[t]:{...e,lastUrl:l}}}),l}throw new Error("Failed to create URL")});a(this,"shareConversation",async t=>{if(t!==void 0)try{const e=await this.getConversationUrl(t);if(!e)return void ee.set("idle");navigator.clipboard.writeText(e),ee.set("copied")}catch{ee.set("failed")}});a(this,"deleteConversations",async(t,e=void 0,s=[],i)=>{const r=t.length+s.length;if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${r>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){if(t.length>0){const o=new Set(t);await this.deleteConversationIds(o)}if(s.length>0&&i)for(const o of s)try{await i.deleteAgent(o,!0)}catch(l){console.error(`Failed to delete remote agent ${o}:`,l)}this.currentConversationId&&t.includes(this.currentConversationId)&&this.setCurrentConversation(e)}});a(this,"deleteConversation",async(t,e=void 0)=>{await this.deleteConversations([t],e)});a(this,"deleteConversationIds",async t=>{var i,r,o;const e=[],s=[];for(const l of t){const c=((i=this._state.conversations[l])==null?void 0:i.requestIds)??[];e.push(...c);const d=((r=this._state.conversations[l])==null?void 0:r.toolUseStates)??{};for(const m of Object.keys(d)){const{toolUseId:C}=d[m];C&&s.push(C)}const u=this._state.conversations[l];if(u){for(const m of u.chatHistory)if(L(m)&&m.structured_output_nodes)for(const C of m.structured_output_nodes)C.type===F.TOOL_USE&&((o=C.tool_use)!=null&&o.tool_use_id)&&s.push(C.tool_use.tool_use_id)}}for(const l of Object.values(this._state.conversations))if(t.has(l.id)){for(const d of l.chatHistory)L(d)&&this.deleteImagesInExchange(d);const c=l.draftExchange;c&&this.deleteImagesInExchange(c)}for(const l of t){try{await this.extensionClient.deleteConversationExchanges(l)}catch(c){console.error(`Failed to delete exchanges for conversation ${l}:`,c)}if(this.flags.enableToolUseStateStorage)try{await this.extensionClient.deleteConversationToolUseStates(l)}catch(c){console.error(`Failed to delete tool use states for conversation ${l}:`,c)}}this._persistenceController.cleanupDeletedConversations(t),this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([l])=>!t.has(l)))}),this.extensionClient.clearMetadataFor({requestIds:e,conversationIds:Array.from(t),toolUseIds:s})});a(this,"deleteImagesInExchange",t=>{const e=new Set([...t.rich_text_json_repr?this.findImagesInJson(t.rich_text_json_repr):[],...t.structured_request_nodes?this.findImagesInStructuredRequest(t.structured_request_nodes):[]]);for(const s of e)this.deleteImage(s)});a(this,"findImagesInJson",t=>{const e=[],s=i=>{var r,o;if(i.type==="file"&&((r=i.attrs)!=null&&r.src)){const l=(o=i.attrs)==null?void 0:o.src;cs(l)&&e.push(i.attrs.src)}else if((i.type==="doc"||i.type==="paragraph")&&i.content)for(const l of i.content)s(l)};return s(t),e});a(this,"findImagesInStructuredRequest",t=>t.reduce((e,s)=>(s.type===G.IMAGE_ID&&s.image_id_node&&e.push(s.image_id_node.image_id),e),[]));a(this,"toggleConversationPinned",t=>{const e=this._state.conversations[t],s={...e,isPinned:!e.isPinned};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});a(this,"renameConversation",(t,e)=>{const s={...this._state.conversations[t],name:e};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.setName(e)});a(this,"smartPaste",(t,e,s,i)=>{const r=this._currConversationModel.historyTo(t,!0).filter(o=>Lt(o)).map(o=>({request_message:o.request_message,response_text:o.response_text||"",request_id:o.request_id||""}));this.extensionClient.smartPaste({generatedCode:e,chatHistory:r,targetFile:s??void 0,options:i})});a(this,"saveImage",async t=>await this.extensionClient.saveImage(t));a(this,"saveAttachment",async t=>await this.extensionClient.saveAttachment(t));a(this,"deleteImage",async t=>await this.extensionClient.deleteImage(t));a(this,"renderImage",async t=>await this.extensionClient.loadImage(t));a(this,"dispose",()=>{this._disposers.forEach(t=>t()),this._disposers=[]});var c,d;this._asyncMsgSender=t,this._host=e,this._specialContextInputModel=s,this.options=i,this._chatFlagsModel=new ks(i.initialFlags),this._messageBroker=new bs(this._host),this._rulesModel=new Hs(this._messageBroker,!1),this.extensionClient=new Us(this._host,this._asyncMsgSender,this._chatFlagsModel),this._messageBroker.registerConsumer(this._rulesModel),this._currConversationModel=new q(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation,this._rulesModel,{forceAgentConversation:i.forceAgentConversation}),this._disposers.push(this._currConversationModel.dispose),this._sendModeModel=new Ci,this._persistenceController=new wi(this.extensionClient,this._chatFlagsModel);const r=((c=i.debounceConfig)==null?void 0:c.wait)??5e3,o=((d=i.debounceConfig)==null?void 0:d.maxWait)??3e4;this.setState=ws(u=>{this._setStateWithPersistence(u)},r,{maxWait:o}),this.initializeSync(i.initialConversation);const l=this._state.isPanelCollapsed??this._state.isAgentEditsCollapsed??this._state.isTaskListCollapsed??!0;this.isPanelCollapsed=lt(l),this.agentExecutionMode=lt(this._state.agentExecutionMode??"manual"),this.sortConversationsBy=lt(this._state.sortConversationsBy??"lastMessageTimestamp"),this.displayedAnnouncements=lt(this._state.displayedAnnouncements??[]),this._sendModeModel.initializeFromState(this._state.sendMode),this.onLoaded()}setChatModeModel(t){this._chatModeModel=t;const e={extensionClient:this.extensionClient,chatModeType:t.chatModeType,currentSendMode:this._sendModeModel.mode,agentExecutionMode:this.agentExecutionMode};this._eventTracker=new mi(e),this._currConversationModel.setEventTracker(this._eventTracker)}get flagsLoaded(){return this._flagsLoaded}get eventTracker(){return this._eventTracker}get rulesModel(){return this._rulesModel}async initializeAsync(t){const e=(t==null?void 0:t.id)||this.currentConversationId;this._state.conversations=await this._persistenceController.hydrateCurrentConversation(this._state.conversations,e),this._subscribeToChatNotifications(),t?await this.setCurrentConversation(t.id):await this.setCurrentConversation(this.currentConversationId)}async _setStateWithPersistence(t){const e=await this._persistenceController.dehydrateConversationsIncremental(t.conversations??this._state.conversations,this.currentConversationId);this._host.setState({...t,conversations:e})}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}get sendModeModel(){return this._sendModeModel}get chatModeModel(){return this._chatModeModel}orderedConversations(t,e="desc",s){const i=t||this._state.sortConversationsBy||"lastMessageTimestamp";let r=Object.values(this._state.conversations);return s&&(r=r.filter(s)),r.sort((o,l)=>{const c=q.getTime(o,i).getTime(),d=q.getTime(l,i).getTime();return e==="asc"?c-d:d-c})}get nextConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return t.length>e+1?t[e+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return e>0?t[e-1]:void 0}get host(){return this._host}deleteInvalidConversations(t="all"){const e=Object.keys(this.conversations).filter(s=>{if(s===gt)return!1;const i=!q.isValid(this.conversations[s]),r=wt(this.conversations[s]);return i&&(t==="agent"&&r||t==="chat"&&!r||t==="all")});e.length&&this.deleteConversationIds(new Set(e))}get lastMessageTimestamp(){const t=this.currentConversationModel.lastExchange;return t==null?void 0:t.timestamp}handleMessageFromExtension(t){const e=t.data;if(e.type===xt.newThread){if("data"in e&&e.data){const s=e.data.mode;(async()=>(await this.setCurrentConversation(),s&&this._chatModeModel?s.toLowerCase()==="agent"?await this._chatModeModel.handleSetToThreadType("localAgent","manual"):s.toLowerCase()==="chat"?await this._chatModeModel.handleSetToThreadType("chat"):console.warn("Unknown chat mode:",s):s&&console.warn("ChatModeModel not available, cannot set mode:",s)))()}else this.setCurrentConversation();return!0}return!1}}a(Ti,"NEW_AGENT_KEY",gt);const Ft=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,ss=new Set,Fe=typeof process=="object"&&process?process:{},gs=(h,t,e,s)=>{typeof Fe.emitWarning=="function"?Fe.emitWarning(h,t,e,s):console.error(`[${e}] ${t}: ${h}`)};let he=globalThis.AbortController,is=globalThis.AbortSignal;var rs;if(he===void 0){is=class{constructor(){a(this,"onabort");a(this,"_onabort",[]);a(this,"reason");a(this,"aborted",!1)}addEventListener(e,s){this._onabort.push(s)}},he=class{constructor(){a(this,"signal",new is);t()}abort(e){var s,i;if(!this.signal.aborted){this.signal.reason=e,this.signal.aborted=!0;for(const r of this.signal._onabort)r(e);(i=(s=this.signal).onabort)==null||i.call(s,e)}}};let h=((rs=Fe.env)==null?void 0:rs.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{h&&(h=!1,gs("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const pt=h=>h&&h===Math.floor(h)&&h>0&&isFinite(h),ps=h=>pt(h)?h<=Math.pow(2,8)?Uint8Array:h<=Math.pow(2,16)?Uint16Array:h<=Math.pow(2,32)?Uint32Array:h<=Number.MAX_SAFE_INTEGER?re:null:null;class re extends Array{constructor(t){super(t),this.fill(0)}}var kt;const It=class It{constructor(t,e){a(this,"heap");a(this,"length");if(!n(It,kt))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}static create(t){const e=ps(t);if(!e)return[];y(It,kt,!0);const s=new It(t,e);return y(It,kt,!1),s}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};kt=new WeakMap,x(It,kt,!1);let qe=It;var os,ns,et,Q,st,it,Ht,Ut,D,rt,H,E,S,V,K,j,O,ot,N,nt,at,J,ht,St,B,_,ke,Et,ut,Wt,X,fs,Mt,Dt,jt,ft,yt,He,oe,ne,T,Ue,$t,vt,De;const Oe=class Oe{constructor(t){x(this,_);x(this,et);x(this,Q);x(this,st);x(this,it);x(this,Ht);x(this,Ut);a(this,"ttl");a(this,"ttlResolution");a(this,"ttlAutopurge");a(this,"updateAgeOnGet");a(this,"updateAgeOnHas");a(this,"allowStale");a(this,"noDisposeOnSet");a(this,"noUpdateTTL");a(this,"maxEntrySize");a(this,"sizeCalculation");a(this,"noDeleteOnFetchRejection");a(this,"noDeleteOnStaleGet");a(this,"allowStaleOnFetchAbort");a(this,"allowStaleOnFetchRejection");a(this,"ignoreFetchAbort");x(this,D);x(this,rt);x(this,H);x(this,E);x(this,S);x(this,V);x(this,K);x(this,j);x(this,O);x(this,ot);x(this,N);x(this,nt);x(this,at);x(this,J);x(this,ht);x(this,St);x(this,B);x(this,Et,()=>{});x(this,ut,()=>{});x(this,Wt,()=>{});x(this,X,()=>!1);x(this,Mt,t=>{});x(this,Dt,(t,e,s)=>{});x(this,jt,(t,e,s,i)=>{if(s||i)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});a(this,os,"LRUCache");const{max:e=0,ttl:s,ttlResolution:i=1,ttlAutopurge:r,updateAgeOnGet:o,updateAgeOnHas:l,allowStale:c,dispose:d,disposeAfter:u,noDisposeOnSet:m,noUpdateTTL:C,maxSize:g=0,maxEntrySize:v=0,sizeCalculation:b,fetchMethod:w,memoMethod:f,noDeleteOnFetchRejection:I,noDeleteOnStaleGet:Y,allowStaleOnFetchRejection:W,allowStaleOnFetchAbort:P,ignoreFetchAbort:U}=t;if(e!==0&&!pt(e))throw new TypeError("max option must be a nonnegative integer");const tt=e?ps(e):Array;if(!tt)throw new Error("invalid max value: "+e);if(y(this,et,e),y(this,Q,g),this.maxEntrySize=v||n(this,Q),this.sizeCalculation=b,this.sizeCalculation){if(!n(this,Q)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(f!==void 0&&typeof f!="function")throw new TypeError("memoMethod must be a function if defined");if(y(this,Ut,f),w!==void 0&&typeof w!="function")throw new TypeError("fetchMethod must be a function if specified");if(y(this,Ht,w),y(this,St,!!w),y(this,H,new Map),y(this,E,new Array(e).fill(void 0)),y(this,S,new Array(e).fill(void 0)),y(this,V,new tt(e)),y(this,K,new tt(e)),y(this,j,0),y(this,O,0),y(this,ot,qe.create(e)),y(this,D,0),y(this,rt,0),typeof d=="function"&&y(this,st,d),typeof u=="function"?(y(this,it,u),y(this,N,[])):(y(this,it,void 0),y(this,N,void 0)),y(this,ht,!!n(this,st)),y(this,B,!!n(this,it)),this.noDisposeOnSet=!!m,this.noUpdateTTL=!!C,this.noDeleteOnFetchRejection=!!I,this.allowStaleOnFetchRejection=!!W,this.allowStaleOnFetchAbort=!!P,this.ignoreFetchAbort=!!U,this.maxEntrySize!==0){if(n(this,Q)!==0&&!pt(n(this,Q)))throw new TypeError("maxSize must be a positive integer if specified");if(!pt(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");p(this,_,fs).call(this)}if(this.allowStale=!!c,this.noDeleteOnStaleGet=!!Y,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!l,this.ttlResolution=pt(i)||i===0?i:1,this.ttlAutopurge=!!r,this.ttl=s||0,this.ttl){if(!pt(this.ttl))throw new TypeError("ttl must be a positive integer if specified");p(this,_,ke).call(this)}if(n(this,et)===0&&this.ttl===0&&n(this,Q)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!n(this,et)&&!n(this,Q)){const ct="LRU_CACHE_UNBOUNDED";(bt=>!ss.has(bt))(ct)&&(ss.add(ct),gs("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",ct,Oe))}}static unsafeExposeInternals(t){return{starts:n(t,at),ttls:n(t,J),sizes:n(t,nt),keyMap:n(t,H),keyList:n(t,E),valList:n(t,S),next:n(t,V),prev:n(t,K),get head(){return n(t,j)},get tail(){return n(t,O)},free:n(t,ot),isBackgroundFetch:e=>{var s;return p(s=t,_,T).call(s,e)},backgroundFetch:(e,s,i,r)=>{var o;return p(o=t,_,ne).call(o,e,s,i,r)},moveToTail:e=>{var s;return p(s=t,_,$t).call(s,e)},indexes:e=>{var s;return p(s=t,_,ft).call(s,e)},rindexes:e=>{var s;return p(s=t,_,yt).call(s,e)},isStale:e=>{var s;return n(s=t,X).call(s,e)}}}get max(){return n(this,et)}get maxSize(){return n(this,Q)}get calculatedSize(){return n(this,rt)}get size(){return n(this,D)}get fetchMethod(){return n(this,Ht)}get memoMethod(){return n(this,Ut)}get dispose(){return n(this,st)}get disposeAfter(){return n(this,it)}getRemainingTTL(t){return n(this,H).has(t)?1/0:0}*entries(){for(const t of p(this,_,ft).call(this))n(this,S)[t]===void 0||n(this,E)[t]===void 0||p(this,_,T).call(this,n(this,S)[t])||(yield[n(this,E)[t],n(this,S)[t]])}*rentries(){for(const t of p(this,_,yt).call(this))n(this,S)[t]===void 0||n(this,E)[t]===void 0||p(this,_,T).call(this,n(this,S)[t])||(yield[n(this,E)[t],n(this,S)[t]])}*keys(){for(const t of p(this,_,ft).call(this)){const e=n(this,E)[t];e===void 0||p(this,_,T).call(this,n(this,S)[t])||(yield e)}}*rkeys(){for(const t of p(this,_,yt).call(this)){const e=n(this,E)[t];e===void 0||p(this,_,T).call(this,n(this,S)[t])||(yield e)}}*values(){for(const t of p(this,_,ft).call(this))n(this,S)[t]===void 0||p(this,_,T).call(this,n(this,S)[t])||(yield n(this,S)[t])}*rvalues(){for(const t of p(this,_,yt).call(this))n(this,S)[t]===void 0||p(this,_,T).call(this,n(this,S)[t])||(yield n(this,S)[t])}[(ns=Symbol.iterator,os=Symbol.toStringTag,ns)](){return this.entries()}find(t,e={}){for(const s of p(this,_,ft).call(this)){const i=n(this,S)[s],r=p(this,_,T).call(this,i)?i.__staleWhileFetching:i;if(r!==void 0&&t(r,n(this,E)[s],this))return this.get(n(this,E)[s],e)}}forEach(t,e=this){for(const s of p(this,_,ft).call(this)){const i=n(this,S)[s],r=p(this,_,T).call(this,i)?i.__staleWhileFetching:i;r!==void 0&&t.call(e,r,n(this,E)[s],this)}}rforEach(t,e=this){for(const s of p(this,_,yt).call(this)){const i=n(this,S)[s],r=p(this,_,T).call(this,i)?i.__staleWhileFetching:i;r!==void 0&&t.call(e,r,n(this,E)[s],this)}}purgeStale(){let t=!1;for(const e of p(this,_,yt).call(this,{allowStale:!0}))n(this,X).call(this,e)&&(p(this,_,vt).call(this,n(this,E)[e],"expire"),t=!0);return t}info(t){const e=n(this,H).get(t);if(e===void 0)return;const s=n(this,S)[e],i=p(this,_,T).call(this,s)?s.__staleWhileFetching:s;if(i===void 0)return;const r={value:i};if(n(this,J)&&n(this,at)){const o=n(this,J)[e],l=n(this,at)[e];if(o&&l){const c=o-(Ft.now()-l);r.ttl=c,r.start=Date.now()}}return n(this,nt)&&(r.size=n(this,nt)[e]),r}dump(){const t=[];for(const e of p(this,_,ft).call(this,{allowStale:!0})){const s=n(this,E)[e],i=n(this,S)[e],r=p(this,_,T).call(this,i)?i.__staleWhileFetching:i;if(r===void 0||s===void 0)continue;const o={value:r};if(n(this,J)&&n(this,at)){o.ttl=n(this,J)[e];const l=Ft.now()-n(this,at)[e];o.start=Math.floor(Date.now()-l)}n(this,nt)&&(o.size=n(this,nt)[e]),t.unshift([s,o])}return t}load(t){this.clear();for(const[e,s]of t){if(s.start){const i=Date.now()-s.start;s.start=Ft.now()-i}this.set(e,s.value,s)}}set(t,e,s={}){var C,g,v,b,w;if(e===void 0)return this.delete(t),this;const{ttl:i=this.ttl,start:r,noDisposeOnSet:o=this.noDisposeOnSet,sizeCalculation:l=this.sizeCalculation,status:c}=s;let{noUpdateTTL:d=this.noUpdateTTL}=s;const u=n(this,jt).call(this,t,e,s.size||0,l);if(this.maxEntrySize&&u>this.maxEntrySize)return c&&(c.set="miss",c.maxEntrySizeExceeded=!0),p(this,_,vt).call(this,t,"set"),this;let m=n(this,D)===0?void 0:n(this,H).get(t);if(m===void 0)m=n(this,D)===0?n(this,O):n(this,ot).length!==0?n(this,ot).pop():n(this,D)===n(this,et)?p(this,_,oe).call(this,!1):n(this,D),n(this,E)[m]=t,n(this,S)[m]=e,n(this,H).set(t,m),n(this,V)[n(this,O)]=m,n(this,K)[m]=n(this,O),y(this,O,m),Kt(this,D)._++,n(this,Dt).call(this,m,u,c),c&&(c.set="add"),d=!1;else{p(this,_,$t).call(this,m);const f=n(this,S)[m];if(e!==f){if(n(this,St)&&p(this,_,T).call(this,f)){f.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:I}=f;I===void 0||o||(n(this,ht)&&((C=n(this,st))==null||C.call(this,I,t,"set")),n(this,B)&&((g=n(this,N))==null||g.push([I,t,"set"])))}else o||(n(this,ht)&&((v=n(this,st))==null||v.call(this,f,t,"set")),n(this,B)&&((b=n(this,N))==null||b.push([f,t,"set"])));if(n(this,Mt).call(this,m),n(this,Dt).call(this,m,u,c),n(this,S)[m]=e,c){c.set="replace";const I=f&&p(this,_,T).call(this,f)?f.__staleWhileFetching:f;I!==void 0&&(c.oldValue=I)}}else c&&(c.set="update")}if(i===0||n(this,J)||p(this,_,ke).call(this),n(this,J)&&(d||n(this,Wt).call(this,m,i,r),c&&n(this,ut).call(this,c,m)),!o&&n(this,B)&&n(this,N)){const f=n(this,N);let I;for(;I=f==null?void 0:f.shift();)(w=n(this,it))==null||w.call(this,...I)}return this}pop(){var t;try{for(;n(this,D);){const e=n(this,S)[n(this,j)];if(p(this,_,oe).call(this,!0),p(this,_,T).call(this,e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(n(this,B)&&n(this,N)){const e=n(this,N);let s;for(;s=e==null?void 0:e.shift();)(t=n(this,it))==null||t.call(this,...s)}}}has(t,e={}){const{updateAgeOnHas:s=this.updateAgeOnHas,status:i}=e,r=n(this,H).get(t);if(r!==void 0){const o=n(this,S)[r];if(p(this,_,T).call(this,o)&&o.__staleWhileFetching===void 0)return!1;if(!n(this,X).call(this,r))return s&&n(this,Et).call(this,r),i&&(i.has="hit",n(this,ut).call(this,i,r)),!0;i&&(i.has="stale",n(this,ut).call(this,i,r))}else i&&(i.has="miss");return!1}peek(t,e={}){const{allowStale:s=this.allowStale}=e,i=n(this,H).get(t);if(i===void 0||!s&&n(this,X).call(this,i))return;const r=n(this,S)[i];return p(this,_,T).call(this,r)?r.__staleWhileFetching:r}async fetch(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:r=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:l=this.noDisposeOnSet,size:c=0,sizeCalculation:d=this.sizeCalculation,noUpdateTTL:u=this.noUpdateTTL,noDeleteOnFetchRejection:m=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:C=this.allowStaleOnFetchRejection,ignoreFetchAbort:g=this.ignoreFetchAbort,allowStaleOnFetchAbort:v=this.allowStaleOnFetchAbort,context:b,forceRefresh:w=!1,status:f,signal:I}=e;if(!n(this,St))return f&&(f.fetch="get"),this.get(t,{allowStale:s,updateAgeOnGet:i,noDeleteOnStaleGet:r,status:f});const Y={allowStale:s,updateAgeOnGet:i,noDeleteOnStaleGet:r,ttl:o,noDisposeOnSet:l,size:c,sizeCalculation:d,noUpdateTTL:u,noDeleteOnFetchRejection:m,allowStaleOnFetchRejection:C,allowStaleOnFetchAbort:v,ignoreFetchAbort:g,status:f,signal:I};let W=n(this,H).get(t);if(W===void 0){f&&(f.fetch="miss");const P=p(this,_,ne).call(this,t,W,Y,b);return P.__returned=P}{const P=n(this,S)[W];if(p(this,_,T).call(this,P)){const bt=s&&P.__staleWhileFetching!==void 0;return f&&(f.fetch="inflight",bt&&(f.returnedStale=!0)),bt?P.__staleWhileFetching:P.__returned=P}const U=n(this,X).call(this,W);if(!w&&!U)return f&&(f.fetch="hit"),p(this,_,$t).call(this,W),i&&n(this,Et).call(this,W),f&&n(this,ut).call(this,f,W),P;const tt=p(this,_,ne).call(this,t,W,Y,b),ct=tt.__staleWhileFetching!==void 0&&s;return f&&(f.fetch=U?"stale":"refresh",ct&&U&&(f.returnedStale=!0)),ct?tt.__staleWhileFetching:tt.__returned=tt}}async forceFetch(t,e={}){const s=await this.fetch(t,e);if(s===void 0)throw new Error("fetch() returned undefined");return s}memo(t,e={}){const s=n(this,Ut);if(!s)throw new Error("no memoMethod provided to constructor");const{context:i,forceRefresh:r,...o}=e,l=this.get(t,o);if(!r&&l!==void 0)return l;const c=s(t,l,{options:o,context:i});return this.set(t,c,o),c}get(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:r=this.noDeleteOnStaleGet,status:o}=e,l=n(this,H).get(t);if(l!==void 0){const c=n(this,S)[l],d=p(this,_,T).call(this,c);return o&&n(this,ut).call(this,o,l),n(this,X).call(this,l)?(o&&(o.get="stale"),d?(o&&s&&c.__staleWhileFetching!==void 0&&(o.returnedStale=!0),s?c.__staleWhileFetching:void 0):(r||p(this,_,vt).call(this,t,"expire"),o&&s&&(o.returnedStale=!0),s?c:void 0)):(o&&(o.get="hit"),d?c.__staleWhileFetching:(p(this,_,$t).call(this,l),i&&n(this,Et).call(this,l),c))}o&&(o.get="miss")}delete(t){return p(this,_,vt).call(this,t,"delete")}clear(){return p(this,_,De).call(this,"delete")}};et=new WeakMap,Q=new WeakMap,st=new WeakMap,it=new WeakMap,Ht=new WeakMap,Ut=new WeakMap,D=new WeakMap,rt=new WeakMap,H=new WeakMap,E=new WeakMap,S=new WeakMap,V=new WeakMap,K=new WeakMap,j=new WeakMap,O=new WeakMap,ot=new WeakMap,N=new WeakMap,nt=new WeakMap,at=new WeakMap,J=new WeakMap,ht=new WeakMap,St=new WeakMap,B=new WeakMap,_=new WeakSet,ke=function(){const t=new re(n(this,et)),e=new re(n(this,et));y(this,J,t),y(this,at,e),y(this,Wt,(r,o,l=Ft.now())=>{if(e[r]=o!==0?l:0,t[r]=o,o!==0&&this.ttlAutopurge){const c=setTimeout(()=>{n(this,X).call(this,r)&&p(this,_,vt).call(this,n(this,E)[r],"expire")},o+1);c.unref&&c.unref()}}),y(this,Et,r=>{e[r]=t[r]!==0?Ft.now():0}),y(this,ut,(r,o)=>{if(t[o]){const l=t[o],c=e[o];if(!l||!c)return;r.ttl=l,r.start=c,r.now=s||i();const d=r.now-c;r.remainingTTL=l-d}});let s=0;const i=()=>{const r=Ft.now();if(this.ttlResolution>0){s=r;const o=setTimeout(()=>s=0,this.ttlResolution);o.unref&&o.unref()}return r};this.getRemainingTTL=r=>{const o=n(this,H).get(r);if(o===void 0)return 0;const l=t[o],c=e[o];return!l||!c?1/0:l-((s||i())-c)},y(this,X,r=>{const o=e[r],l=t[r];return!!l&&!!o&&(s||i())-o>l})},Et=new WeakMap,ut=new WeakMap,Wt=new WeakMap,X=new WeakMap,fs=function(){const t=new re(n(this,et));y(this,rt,0),y(this,nt,t),y(this,Mt,e=>{y(this,rt,n(this,rt)-t[e]),t[e]=0}),y(this,jt,(e,s,i,r)=>{if(p(this,_,T).call(this,s))return 0;if(!pt(i)){if(!r)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof r!="function")throw new TypeError("sizeCalculation must be a function");if(i=r(s,e),!pt(i))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return i}),y(this,Dt,(e,s,i)=>{if(t[e]=s,n(this,Q)){const r=n(this,Q)-t[e];for(;n(this,rt)>r;)p(this,_,oe).call(this,!0)}y(this,rt,n(this,rt)+t[e]),i&&(i.entrySize=s,i.totalCalculatedSize=n(this,rt))})},Mt=new WeakMap,Dt=new WeakMap,jt=new WeakMap,ft=function*({allowStale:t=this.allowStale}={}){if(n(this,D))for(let e=n(this,O);p(this,_,He).call(this,e)&&(!t&&n(this,X).call(this,e)||(yield e),e!==n(this,j));)e=n(this,K)[e]},yt=function*({allowStale:t=this.allowStale}={}){if(n(this,D))for(let e=n(this,j);p(this,_,He).call(this,e)&&(!t&&n(this,X).call(this,e)||(yield e),e!==n(this,O));)e=n(this,V)[e]},He=function(t){return t!==void 0&&n(this,H).get(n(this,E)[t])===t},oe=function(t){var r,o;const e=n(this,j),s=n(this,E)[e],i=n(this,S)[e];return n(this,St)&&p(this,_,T).call(this,i)?i.__abortController.abort(new Error("evicted")):(n(this,ht)||n(this,B))&&(n(this,ht)&&((r=n(this,st))==null||r.call(this,i,s,"evict")),n(this,B)&&((o=n(this,N))==null||o.push([i,s,"evict"]))),n(this,Mt).call(this,e),t&&(n(this,E)[e]=void 0,n(this,S)[e]=void 0,n(this,ot).push(e)),n(this,D)===1?(y(this,j,y(this,O,0)),n(this,ot).length=0):y(this,j,n(this,V)[e]),n(this,H).delete(s),Kt(this,D)._--,e},ne=function(t,e,s,i){const r=e===void 0?void 0:n(this,S)[e];if(p(this,_,T).call(this,r))return r;const o=new he,{signal:l}=s;l==null||l.addEventListener("abort",()=>o.abort(l.reason),{signal:o.signal});const c={signal:o.signal,options:s,context:i},d=(g,v=!1)=>{const{aborted:b}=o.signal,w=s.ignoreFetchAbort&&g!==void 0;if(s.status&&(b&&!v?(s.status.fetchAborted=!0,s.status.fetchError=o.signal.reason,w&&(s.status.fetchAbortIgnored=!0)):s.status.fetchResolved=!0),b&&!w&&!v)return u(o.signal.reason);const f=m;return n(this,S)[e]===m&&(g===void 0?f.__staleWhileFetching?n(this,S)[e]=f.__staleWhileFetching:p(this,_,vt).call(this,t,"fetch"):(s.status&&(s.status.fetchUpdated=!0),this.set(t,g,c.options))),g},u=g=>{const{aborted:v}=o.signal,b=v&&s.allowStaleOnFetchAbort,w=b||s.allowStaleOnFetchRejection,f=w||s.noDeleteOnFetchRejection,I=m;if(n(this,S)[e]===m&&(!f||I.__staleWhileFetching===void 0?p(this,_,vt).call(this,t,"fetch"):b||(n(this,S)[e]=I.__staleWhileFetching)),w)return s.status&&I.__staleWhileFetching!==void 0&&(s.status.returnedStale=!0),I.__staleWhileFetching;if(I.__returned===I)throw g};s.status&&(s.status.fetchDispatched=!0);const m=new Promise((g,v)=>{var w;const b=(w=n(this,Ht))==null?void 0:w.call(this,t,r,c);b&&b instanceof Promise&&b.then(f=>g(f===void 0?void 0:f),v),o.signal.addEventListener("abort",()=>{s.ignoreFetchAbort&&!s.allowStaleOnFetchAbort||(g(void 0),s.allowStaleOnFetchAbort&&(g=f=>d(f,!0)))})}).then(d,g=>(s.status&&(s.status.fetchRejected=!0,s.status.fetchError=g),u(g))),C=Object.assign(m,{__abortController:o,__staleWhileFetching:r,__returned:void 0});return e===void 0?(this.set(t,C,{...c.options,status:void 0}),e=n(this,H).get(t)):n(this,S)[e]=C,C},T=function(t){if(!n(this,St))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof he},Ue=function(t,e){n(this,K)[e]=t,n(this,V)[t]=e},$t=function(t){t!==n(this,O)&&(t===n(this,j)?y(this,j,n(this,V)[t]):p(this,_,Ue).call(this,n(this,K)[t],n(this,V)[t]),p(this,_,Ue).call(this,n(this,O),t),y(this,O,t))},vt=function(t,e){var i,r,o,l;let s=!1;if(n(this,D)!==0){const c=n(this,H).get(t);if(c!==void 0)if(s=!0,n(this,D)===1)p(this,_,De).call(this,e);else{n(this,Mt).call(this,c);const d=n(this,S)[c];if(p(this,_,T).call(this,d)?d.__abortController.abort(new Error("deleted")):(n(this,ht)||n(this,B))&&(n(this,ht)&&((i=n(this,st))==null||i.call(this,d,t,e)),n(this,B)&&((r=n(this,N))==null||r.push([d,t,e]))),n(this,H).delete(t),n(this,E)[c]=void 0,n(this,S)[c]=void 0,c===n(this,O))y(this,O,n(this,K)[c]);else if(c===n(this,j))y(this,j,n(this,V)[c]);else{const u=n(this,K)[c];n(this,V)[u]=n(this,V)[c];const m=n(this,V)[c];n(this,K)[m]=n(this,K)[c]}Kt(this,D)._--,n(this,ot).push(c)}}if(n(this,B)&&((o=n(this,N))!=null&&o.length)){const c=n(this,N);let d;for(;d=c==null?void 0:c.shift();)(l=n(this,it))==null||l.call(this,...d)}return s},De=function(t){var e,s,i;for(const r of p(this,_,yt).call(this,{allowStale:!0})){const o=n(this,S)[r];if(p(this,_,T).call(this,o))o.__abortController.abort(new Error("deleted"));else{const l=n(this,E)[r];n(this,ht)&&((e=n(this,st))==null||e.call(this,o,l,t)),n(this,B)&&((s=n(this,N))==null||s.push([o,l,t]))}}if(n(this,H).clear(),n(this,S).fill(void 0),n(this,E).fill(void 0),n(this,J)&&n(this,at)&&(n(this,J).fill(0),n(this,at).fill(0)),n(this,nt)&&n(this,nt).fill(0),y(this,j,0),y(this,O,0),n(this,ot).length=0,y(this,rt,0),y(this,D,0),n(this,B)&&n(this,N)){const r=n(this,N);let o;for(;o=r==null?void 0:r.shift();)(i=n(this,it))==null||i.call(this,...o)}};let Le=Oe;class Qi{constructor(){a(this,"_syncStatus",{status:Ps.done,foldersProgress:[]});a(this,"_syncEnabledState",Ge.initializing);a(this,"_workspaceGuidelines",[]);a(this,"_openUserGuidelinesInput",!1);a(this,"_userGuidelines");a(this,"_contextStore",new Ei);a(this,"_prevOpenFiles",[]);a(this,"_disableContext",!1);a(this,"_enableAgentMemories",!1);a(this,"subscribers",new Set);a(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));a(this,"handleMessageFromExtension",t=>{const e=t.data;switch(e.type){case xt.sourceFoldersUpdated:this.onSourceFoldersUpdated(e.data.sourceFolders);break;case xt.sourceFoldersSyncStatus:this.onSyncStatusUpdated(e.data);break;case xt.fileRangesSelected:this.updateSelections(e.data);break;case xt.currentlyOpenFiles:this.setCurrentlyOpenFiles(e.data);break;case xt.syncEnabledState:this.onSyncEnabledStateUpdate(e.data);break;case xt.updateGuidelinesState:this.onGuidelinesStateUpdate(e.data);break;default:return!1}return!0});a(this,"onSourceFoldersUpdated",t=>{const e=this.sourceFolders;t=this.updateSourceFoldersWithGuidelines(t),this._contextStore.update(t.map(s=>({sourceFolder:s,status:R.active,label:s.folderRoot,showWarning:s.guidelinesOverLimit,id:s.folderRoot+String(s.guidelinesOverLimit)})),e,s=>s.id),this.notifySubscribers()});a(this,"onSyncStatusUpdated",t=>{this._syncStatus=t,this.notifySubscribers()});a(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});a(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});a(this,"addFile",t=>{this.addFiles([t])});a(this,"addFiles",t=>{this.updateFiles(t,[])});a(this,"removeFile",t=>{this.removeFiles([t])});a(this,"removeFiles",t=>{this.updateFiles([],t)});a(this,"updateItems",(t,e)=>{this.updateItemsInplace(t,e),this.notifySubscribers()});a(this,"updateItemsInplace",(t,e)=>{this._contextStore.update(t,e,s=>s.id)});a(this,"updateFiles",(t,e)=>{const s=o=>({file:o,...ge(o)}),i=t.map(s),r=e.map(s);this._contextStore.update(i,r,o=>o.id),this.notifySubscribers()});a(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});a(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});a(this,"setCurrentlyOpenFiles",t=>{const e=t.map(i=>({recentFile:i,...ge(i)})),s=this._prevOpenFiles;this._prevOpenFiles=e,this._contextStore.update(e,s,i=>i.id),s.forEach(i=>{const r=this._contextStore.peekKey(i.id);r!=null&&r.recentFile&&(r.file=r.recentFile,delete r.recentFile)}),e.forEach(i=>{const r=this._contextStore.peekKey(i.id);r!=null&&r.file&&(r.recentFile=r.file,delete r.file)}),this.notifySubscribers()});a(this,"onSyncEnabledStateUpdate",t=>{this._syncEnabledState=t,this.notifySubscribers()});a(this,"updateUserGuidelines",(t,e)=>{const s=this.userGuidelines,i=t.overLimit||((e==null?void 0:e.overLimit)??!1),r={userGuidelines:t,label:"User Guidelines",id:"userGuidelines",status:R.active,referenceCount:1,showWarning:i,rulesAndGuidelinesState:e};this._contextStore.update([r],s,o=>{var l;return o.id+String((l=o.userGuidelines)==null?void 0:l.overLimit)}),this.notifySubscribers()});a(this,"onGuidelinesStateUpdate",t=>{var i;this._userGuidelines=t.userGuidelines,this._workspaceGuidelines=t.workspaceGuidelines??[];const e=t.userGuidelines,s=this.userGuidelines;if(e||t.rulesAndGuidelines||s.length>0){const r=e||{overLimit:!1,contents:"",lengthLimit:((i=t.rulesAndGuidelines)==null?void 0:i.lengthLimit)??2e3};this.updateUserGuidelines(r,t.rulesAndGuidelines)}this.onSourceFoldersUpdated(this.sourceFolders.map(r=>r.sourceFolder))});a(this,"updateSourceFoldersWithGuidelines",t=>t.map(e=>{const s=this._workspaceGuidelines.find(i=>i.workspaceFolder===e.folderRoot);return{...e,guidelinesOverLimit:(s==null?void 0:s.overLimit)??!1,guidelinesLengthLimit:(s==null?void 0:s.lengthLimit)??2e3}}));a(this,"toggleStatus",t=>{this._contextStore.toggleStatus(t.id),this.notifySubscribers()});a(this,"updateExternalSources",(t,e)=>{this._contextStore.update(t,e,s=>s.id),this.notifySubscribers()});a(this,"clearFiles",()=>{this._contextStore.update([],this.files,t=>t.id),this.notifySubscribers()});a(this,"updateSelections",t=>{const e=this._contextStore.values.filter(je),s=t.map(i=>({selection:i,...ge(i)}));this._contextStore.update([],e,i=>i.id),this._contextStore.update(s,[],i=>i.id),this.notifySubscribers()});a(this,"maybeHandleDelete",({editor:t})=>{if(t.state.selection.empty&&t.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const e=this.recentActiveItems[0];return this.markInactive(e),!0}return!1});a(this,"markInactive",t=>{this.markItemsInactive([t])});a(this,"markItemsInactive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,R.inactive)}),this.notifySubscribers()});a(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});a(this,"markActive",t=>{this.markItemsActive([t])});a(this,"markItemsActive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,R.active)}),this.notifySubscribers()});a(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});a(this,"unpin",t=>{this._contextStore.unpin(t.id),this.notifySubscribers()});a(this,"togglePinned",t=>{this._contextStore.togglePinned(t.id),this.notifySubscribers()});a(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(t=>zs(t)&&!We(t))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(We)}get userGuidelinesText(){var t;return((t=this._userGuidelines)==null?void 0:t.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(je)}get folders(){return this._disableContext?[]:this._contextStore.values.filter($s)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(Ve)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(Gs)}get userGuidelines(){return this._contextStore.values.filter(Be)}get workspaceGuidelines(){return this._workspaceGuidelines}get agentMemories(){return[{...Ws,status:this._enableAgentMemories?R.active:R.inactive,referenceCount:1}]}get rules(){return this._contextStore.values.filter(t=>Ze(t))}get activeFiles(){return this._disableContext?[]:this.files.filter(t=>t.status===R.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(t=>t.status===R.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(t=>t.status===R.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(t=>t.status===R.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(t=>t.status===R.active)}get activeRules(){return this._disableContext?[]:this.rules.filter(t=>t.status===R.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var c;if(this.syncEnabledState===Ge.disabled||!this._syncStatus.foldersProgress)return;const t=this._syncStatus.foldersProgress.filter(d=>d.progress!==void 0);if(t.length===0)return;const e=t.reduce((d,u)=>{var m;return d+(((m=u==null?void 0:u.progress)==null?void 0:m.trackedFiles)??0)},0),s=t.reduce((d,u)=>{var m;return d+(((m=u==null?void 0:u.progress)==null?void 0:m.backlogSize)??0)},0),i=Math.max(e,0),r=Math.min(Math.max(s,0),i),o=i-r,l=[];for(const d of t)(c=d==null?void 0:d.progress)!=null&&c.newlyTracked&&l.push(d.folderRoot);return{status:this._syncStatus.status,totalFiles:i,syncedCount:o,backlogSize:r,newlyTrackedFolders:l}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:[...this.activeFiles.map(t=>({rootPath:t.file.repoRoot,relPath:t.file.pathName}))],ruleFiles:this.activeRules.map(t=>t.rule),recentFiles:this.activeRecentFiles.map(t=>({rootPath:t.recentFile.repoRoot,relPath:t.recentFile.pathName})),externalSources:this.activeExternalSources.map(t=>t.externalSource),selections:this.activeSelections.map(t=>t.selection),sourceFolders:this.activeSourceFolders.map(t=>({rootPath:t.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(t=>!(Ve(t)||Be(t)||ls(t)||Ze(t))),...this.sourceFolders,...this.rules,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(t=>t.status===R.active)}get recentInactiveItems(){return this.recentItems.filter(t=>t.status===R.inactive)}get isContextDisabled(){return this._disableContext}}class Ei{constructor(){a(this,"_cache",new Le({max:1e3}));a(this,"peekKey",t=>this._cache.get(t,{updateAgeOnGet:!1}));a(this,"clear",()=>{this._cache.clear()});a(this,"update",(t,e,s)=>{t.forEach(i=>this.addInPlace(i,s)),e.forEach(i=>this.removeInPlace(i,s))});a(this,"removeFromStore",(t,e)=>{const s=e(t);this._cache.delete(s)});a(this,"addInPlace",(t,e)=>{const s=e(t),i=t.referenceCount??1,r=this._cache.get(s),o=t.status??(r==null?void 0:r.status)??R.active;r?(r.referenceCount+=i,r.status=o,r.pinned=t.pinned??r.pinned,r.showWarning=t.showWarning??r.showWarning,"userGuidelines"in t&&t.userGuidelines&&"userGuidelines"in r&&(r.userGuidelines=t.userGuidelines),"rulesAndGuidelinesState"in t&&t.rulesAndGuidelinesState&&"rulesAndGuidelinesState"in r&&(r.rulesAndGuidelinesState=t.rulesAndGuidelinesState)):this._cache.set(s,{...t,pinned:void 0,referenceCount:i,status:o})});a(this,"removeInPlace",(t,e)=>{const s=e(t),i=this._cache.get(s);i&&(i.referenceCount-=1,i.referenceCount===0&&this._cache.delete(s))});a(this,"setStatus",(t,e)=>{const s=this._cache.get(t);s&&(s.status=e)});a(this,"togglePinned",t=>{const e=this._cache.peek(t);e&&(e.pinned?this.unpin(t):this.pin(t))});a(this,"pin",t=>{const e=this._cache.peek(t);e&&!e.pinned&&(e.pinned=!0,e.referenceCount+=1)});a(this,"unpin",t=>{const e=this._cache.peek(t);e&&e.pinned&&(e.pinned=!1,e.referenceCount-=1,e.referenceCount===0&&this._cache.delete(t))});a(this,"toggleStatus",t=>{const e=this._cache.get(t);e&&(e.status=e.status===R.active?R.inactive:R.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}var Mi=Vt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M5 2V1H10V2H5ZM4.75 0C4.33579 0 4 0.335786 4 0.75V1H3.5C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H7V13H3.5C3.22386 13 3 12.7761 3 12.5V2.5C3 2.22386 3.22386 2 3.5 2H4V2.25C4 2.66421 4.33579 3 4.75 3H10.25C10.6642 3 11 2.66421 11 2.25V2H11.5C11.7761 2 12 2.22386 12 2.5V7H13V2.5C13 1.67157 12.3284 1 11.5 1H11V0.75C11 0.335786 10.6642 0 10.25 0H4.75ZM9 8.5C9 8.77614 8.77614 9 8.5 9C8.22386 9 8 8.77614 8 8.5C8 8.22386 8.22386 8 8.5 8C8.77614 8 9 8.22386 9 8.5ZM10.5 9C10.7761 9 11 8.77614 11 8.5C11 8.22386 10.7761 8 10.5 8C10.2239 8 10 8.22386 10 8.5C10 8.77614 10.2239 9 10.5 9ZM13 8.5C13 8.77614 12.7761 9 12.5 9C12.2239 9 12 8.77614 12 8.5C12 8.22386 12.2239 8 12.5 8C12.7761 8 13 8.22386 13 8.5ZM14.5 9C14.7761 9 15 8.77614 15 8.5C15 8.22386 14.7761 8 14.5 8C14.2239 8 14 8.22386 14 8.5C14 8.77614 14.2239 9 14.5 9ZM15 10.5C15 10.7761 14.7761 11 14.5 11C14.2239 11 14 10.7761 14 10.5C14 10.2239 14.2239 10 14.5 10C14.7761 10 15 10.2239 15 10.5ZM14.5 13C14.7761 13 15 12.7761 15 12.5C15 12.2239 14.7761 12 14.5 12C14.2239 12 14 12.2239 14 12.5C14 12.7761 14.2239 13 14.5 13ZM14.5 15C14.7761 15 15 14.7761 15 14.5C15 14.2239 14.7761 14 14.5 14C14.2239 14 14 14.2239 14 14.5C14 14.7761 14.2239 15 14.5 15ZM8.5 11C8.77614 11 9 10.7761 9 10.5C9 10.2239 8.77614 10 8.5 10C8.22386 10 8 10.2239 8 10.5C8 10.7761 8.22386 11 8.5 11ZM9 12.5C9 12.7761 8.77614 13 8.5 13C8.22386 13 8 12.7761 8 12.5C8 12.2239 8.22386 12 8.5 12C8.77614 12 9 12.2239 9 12.5ZM8.5 15C8.77614 15 9 14.7761 9 14.5C9 14.2239 8.77614 14 8.5 14C8.22386 14 8 14.2239 8 14.5C8 14.7761 8.22386 15 8.5 15ZM11 14.5C11 14.7761 10.7761 15 10.5 15C10.2239 15 10 14.7761 10 14.5C10 14.2239 10.2239 14 10.5 14C10.7761 14 11 14.2239 11 14.5ZM12.5 15C12.7761 15 13 14.7761 13 14.5C13 14.2239 12.7761 14 12.5 14C12.2239 14 12 14.2239 12 14.5C12 14.7761 12.2239 15 12.5 15Z" fill="currentColor"></path></svg>');function Ki(h){var t=Mi();Bt(h,t)}var Ai=Vt('<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.71 4.29L10.71 1.29L10 1H4L3 2V14L4 15H13L14 14V5L13.71 4.29ZM13 14H4V2H9V6H13V14ZM10 5V2L13 5H10Z" fill="currentColor"></path></svg>');function Ji(h){var t=Ai();Bt(h,t)}var Fi=Vt('<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1.5 14H12.5L12.98 13.63L15.61 6.63L15.13 6H14V3.5L13.5 3H7.70996L6.84998 2.15002L6.5 2H1.5L1 2.5V13.5L1.5 14ZM2 3H6.29004L7.15002 3.84998L7.5 4H13V6H8.5L8.15002 6.15002L7.29004 7H3.5L3.03003 7.33997L2.03003 10.42L2 3ZM12.13 13H2.18994L3.85999 8H7.5L7.84998 7.84998L8.70996 7H14.5L12.13 13Z" fill="currentColor"></path></svg>');function Xi(h){var t=Fi();Bt(h,t)}export{Ii as A,Ti as C,Qs as F,gt as N,Qi as S,Se as T,Ki as a,Ji as b,Xi as c,ji as d,q as e,Ye as f,ri as g,ei as h,wt as i,Gi as j,Bi as k,ie as l,R as m,yi as n,vi as o,Zi as p,ae as q,Wi as r,Vi as s,si as t,ii as u,ms as v};
