import{ac as Ce,c as be,z as re,R as xe,r as le,l as me,p as ye,aw as de,ax as ce,ay as Re,az as J,q as ke,ad as ge,aA as Pe,m as Fe,s as pe,aB as Q,aC as X,e as ue,aD as Ee,aE as Ie,aF as Ge,ae as De,aG as Me,aH as Le,a0 as L,aI as T,aJ as Te,aK as He,aL as Oe}from"./legacy-AoIeRrIA.js";let Ue=!1;function nt(e,s){return s}function ot(e,s,t,n,o,l=null){var h=e,H={flags:s,items:new Map,first:null};!(s&de)||(h=e.appendChild(Ce()));var p=null,y=!1,S=xe(()=>{var u=t();return Re(u)?u:u==null?[]:ce(u)});be(()=>{var u=re(S),g=u.length;y&&g===0||(y=g===0,function(O,c,k,ve,A,Z,we){var te,se,ne,oe;var m,P,F,q,i,r,E=!!(A&Pe),qe=!!(A&(Q|X)),U=O.length,I=c.items,Se=c.first,a=Se,d=null,f=[],R=[];if(E)for(r=0;r<U;r+=1)q=Z(F=O[r],r),(i=I.get(q))!==void 0&&((te=i.a)==null||te.measure(),(P??(P=new Set)).add(i));for(r=0;r<U;r+=1)if(q=Z(F=O[r],r),(i=I.get(q))!==void 0){if(qe&&Ve(i,F,r,A),i.e.f&J&&(le(i.e),E&&((se=i.a)==null||se.unfix(),(P??(P=new Set)).delete(i))),i!==a){if(m!==void 0&&m.has(i)){if(f.length<R.length){var v,G=R[0];d=G.prev;var ee=f[0],V=f[f.length-1];for(v=0;v<f.length;v+=1)fe(f[v],G,k);for(v=0;v<R.length;v+=1)m.delete(R[v]);w(c,ee.prev,V.next),w(c,d,ee),w(c,V,G),a=G,d=V,r-=1,f=[],R=[]}else m.delete(i),fe(i,a,k),w(c,i.prev,i.next),w(c,i,d===null?c.first:d.next),w(c,d,i),d=i;continue}for(f=[],R=[];a!==null&&a.k!==q;)a.e.f&J||(m??(m=new Set)).add(a),R.push(a),a=a.next;if(a===null)continue;i=a}f.push(i),d=i,a=i.next}else d=ze(a?a.e.nodes_start:k,c,d,d===null?c.first:d.next,F,q,r,ve,A,we),I.set(q,d),f=[],R=[],a=d.next;if(a!==null||m!==void 0){for(var C=m===void 0?[]:ce(m);a!==null;)a.e.f&J||C.push(a),a=a.next;var z=C.length;if(z>0){var Ae=A&de&&U===0?k:null;if(E){for(r=0;r<z;r+=1)(ne=C[r].a)==null||ne.measure();for(r=0;r<z;r+=1)(oe=C[r].a)==null||oe.fix()}(function(b,x,W,ie){for(var B=[],D=x.length,N=0;N<D;N++)Ee(x[N].e,B,!0);var j=D>0&&B.length===0&&W!==null;if(j){var ae=W.parentNode;Ie(ae),ae.append(W),ie.clear(),w(b,x[0].prev,x[D-1].next)}Ge(B,()=>{for(var _=0;_<D;_++){var M=x[_];j||(ie.delete(M.k),w(b,M.prev,M.next)),De(M.e,!j)}})})(c,C,Ae,I)}}E&&ke(()=>{var b;if(P!==void 0)for(i of P)(b=i.a)==null||b.apply()}),ge.first=c.first&&c.first.e,ge.last=d&&d.e}(u,H,h,o,s,n,t),l!==null&&(g===0?p?le(p):p=me(()=>l(h)):p!==null&&ye(p,()=>{p=null})),re(S))})}function Ve(e,s,t,n){n&Q&&ue(e.v,s),n&X?ue(e.i,t):e.i=t}function ze(e,s,t,n,o,l,h,H,p,y){var S=p&Q?p&Me?pe(o):Fe(o,!1,!1):o,u=p&X?pe(h):h,g={i:u,v:S,k:l,a:null,e:null,prev:t,next:n};try{return g.e=me(()=>H(e,S,u,y),Ue),g.e.prev=t&&t.e,g.e.next=n&&n.e,t===null?s.first=g:(t.next=g,t.e.next=g.e),n!==null&&(n.prev=g,n.e.prev=g.e),g}finally{}}function fe(e,s,t){for(var n=e.next?e.next.e.nodes_start:t,o=s?s.e.nodes_start:t,l=e.e.nodes_start;l!==n;){var h=Le(l);o.before(l),l=h}}function w(e,s,t){s===null?e.first=t:(s.next=t,s.e.next=t&&t.e),t!==null&&(t.prev=s,t.e.prev=s&&s.e)}function it(e,s){var l;var t=(l=e.$$events)==null?void 0:l[s.type],n=Re(t)?t.slice():t==null?[]:[t];for(var o of n)o.call(this,s)}var We=(e=>(e.asyncWrapper="async-wrapper",e.historyLoaded="history-loaded",e.historyInitialize="history-initialize",e.completionRating="completion-rating",e.completionRatingDone="completion-rating-done",e.nextEditRating="next-edit-rating",e.nextEditRatingDone="next-edit-rating-done",e.completions="completions",e.historyConfig="history-config",e.copyRequestID="copy-request-id-to-clipboard",e.openFile="open-file",e.openDiffInBuffer="open-diff-in-buffer",e.saveFile="save-file",e.loadFile="load-file",e.importFileRequest="import-file-request",e.importDirectoryRequest="import-directory-request",e.triggerImportDialogRequest="trigger-import-dialog-request",e.triggerImportDialogResponse="trigger-import-dialog-response",e.openMemoriesFile="open-memories-file",e.openAndEditFile="open-and-edit-file",e.diffViewNotifyReinit="diff-view-notify-reinit",e.diffViewLoaded="diff-view-loaded",e.diffViewInitialize="diff-view-initialize",e.diffViewResolveChunk="diff-view-resolve-chunk",e.diffViewFetchPendingStream="diff-view-fetch-pending-stream",e.diffViewDiffStreamStarted="diff-view-diff-stream-started",e.diffViewDiffStreamChunk="diff-view-diff-stream-chunk",e.diffViewDiffStreamEnded="diff-view-diff-stream-ended",e.diffViewAcceptAllChunks="diff-view-accept-all-chunks",e.diffViewAcceptFocusedChunk="diff-view-accept-selected-chunk",e.diffViewRejectFocusedChunk="diff-view-reject-focused-chunk",e.diffViewFocusPrevChunk="diff-view-focus-prev-chunk",e.diffViewFocusNextChunk="diff-view-focus-next-chunk",e.diffViewWindowFocusChange="diff-view-window-focus-change",e.diffViewFileFocus="diff-view-file-focus",e.disposeDiffView="dispose-diff-view",e.reportWebviewClientMetric="report-webview-client-metric",e.reportError="report-error",e.showNotification="show-notification",e.showBannerNotification="show-banner-notification",e.dismissBannerNotification="dismiss-banner-notification",e.openConfirmationModal="open-confirmation-modal",e.confirmationModalResponse="confirmation-modal-response",e.clientTools="client-tools",e.currentlyOpenFiles="currently-open-files",e.findFileRequest="find-file-request",e.resolveFileRequest="resolve-file-request",e.findFileResponse="find-file-response",e.resolveFileResponse="resolve-file-response",e.findRecentlyOpenedFilesRequest="find-recently-opened-files",e.findRecentlyOpenedFilesResponse="find-recently-opened-files-response",e.findFolderRequest="find-folder-request",e.findFolderResponse="find-folder-response",e.findExternalSourcesRequest="find-external-sources-request",e.findExternalSourcesResponse="find-external-sources-response",e.findSymbolRequest="find-symbol-request",e.findSymbolRegexRequest="find-symbol-regex-request",e.findSymbolResponse="find-symbol-response",e.fileRangesSelected="file-ranges-selected",e.getDiagnosticsRequest="get-diagnostics-request",e.getDiagnosticsResponse="get-diagnostics-response",e.resolveWorkspaceFileChunkRequest="resolve-workspace-file-chunk",e.resolveWorkspaceFileChunkResponse="resolve-workspace-file-chunk-response",e.sourceFoldersUpdated="source-folders-updated",e.sourceFoldersSyncStatus="source-folders-sync-status",e.syncEnabledState="sync-enabled-state",e.shouldShowSummary="should-show-summary",e.showAugmentPanel="show-augment-panel",e.updateGuidelinesState="update-guidelines-state",e.openGuidelines="open-guidelines",e.updateWorkspaceGuidelines="update-workspace-guidelines",e.updateUserGuidelines="update-user-guidelines",e.chatAgentEditListHasUpdates="chat-agent-edit-list-has-updates",e.chatMemoryHasUpdates="chat-memory-has-updates",e.getAgentEditContentsByRequestId="getAgentEditContentsByRequestId",e.chatModeChanged="chat-mode-changed",e.chatClearMetadata="chat-clear-metadata",e.chatLoaded="chat-loaded",e.chatInitialize="chat-initialize",e.chatGetStreamRequest="chat-get-stream-request",e.chatUserMessage="chat-user-message",e.generateCommitMessage="generate-commit-message",e.chatUserCancel="chat-user-cancel",e.chatModelReply="chat-model-reply",e.chatInstructionMessage="chat-instruction-message",e.chatInstructionModelReply="chat-instruction-model-reply",e.chatCreateFile="chat-create-file",e.chatSmartPaste="chat-smart-paste",e.chatRating="chat-rating",e.chatRatingDone="chat-rating-done",e.chatStreamDone="chat-stream-done",e.runSlashCommand="run-slash-command",e.callTool="call-tool",e.callToolResponse="call-tool-response",e.cancelToolRun="cancel-tool-run",e.cancelToolRunResponse="cancel-tool-run-response",e.toolCheckSafe="check-safe",e.toolCheckSafeResponse="check-safe-response",e.checkToolExists="checkToolExists",e.checkToolExistsResponse="checkToolExistsResponse",e.startRemoteMCPAuth="start-remote-mcp-auth",e.getToolCallCheckpoint="get-tool-call-checkpoint",e.getToolCallCheckpointResponse="get-tool-call-checkpoint-response",e.updateAditionalChatModels="update-additional-chat-models",e.saveChat="save-chat",e.saveChatDone="save-chat-done",e.newThread="new-thread",e.chatSaveImageRequest="chat-save-image-request",e.chatSaveImageResponse="chat-save-image-response",e.chatLoadImageRequest="chat-load-image-request",e.chatLoadImageResponse="chat-load-image-response",e.chatDeleteImageRequest="chat-delete-image-request",e.chatDeleteImageResponse="chat-delete-image-response",e.chatSaveAttachmentRequest="chat-save-attachment-request",e.chatSaveAttachmentResponse="chat-save-attachment-response",e.chatNotification="chat-notification",e.instructions="instructions",e.nextEditDismiss="next-edit-dismiss",e.nextEditLoaded="next-edit-loaded",e.nextEditSuggestions="next-edit-suggestions",e.nextEditSuggestionsAction="next-edit-suggestions-action",e.nextEditRefreshStarted="next-edit-refresh-started",e.nextEditRefreshFinished="next-edit-refresh-finished",e.nextEditCancel="next-edit-cancel",e.nextEditPreviewActive="next-edit-preview-active",e.nextEditSuggestionsChanged="next-edit-suggestions-changed",e.nextEditNextSuggestionChanged="next-edit-next-suggestion-changed",e.nextEditOpenSuggestion="next-edit-open-suggestion",e.nextEditToggleSuggestionTree="next-edit-toggle-suggestion-tree",e.nextEditActiveSuggestionChanged="next-edit-active-suggestion",e.nextEditPanelFocus="next-edit-panel-focus",e.onboardingLoaded="onboarding-loaded",e.onboardingUpdateState="onboarding-update-state",e.usedChat="used-chat",e.preferencePanelLoaded="preference-panel-loaded",e.preferenceInit="preference-init",e.preferenceResultMessage="preference-result-message",e.preferenceNotify="preference-notify",e.openSettingsPage="open-settings-page",e.settingsPanelLoaded="settings-panel-loaded",e.navigateToSettingsSection="navigate-to-settings-section",e.mainPanelDisplayApp="main-panel-display-app",e.mainPanelLoaded="main-panel-loaded",e.mainPanelActions="main-panel-actions",e.mainPanelPerformAction="main-panel-perform-action",e.mainPanelCreateProject="main-panel-create-project",e.usedSlashAction="used-slash-action",e.signInLoaded="sign-in-loaded",e.signInLoadedResponse="sign-in-loaded-response",e.signOut="sign-out",e.awaitingSyncingPermissionLoaded="awaiting-syncing-permission-loaded",e.awaitingSyncingPermissionInitialize="awaiting-syncing-permission-initialize",e.readFileRequest="read-file-request",e.readFileResponse="read-file-response",e.wsContextGetChildrenRequest="ws-context-get-children-request",e.wsContextGetChildrenResponse="ws-context-get-children-response",e.wsContextGetSourceFoldersRequest="ws-context-get-source-folders-request",e.wsContextGetSourceFoldersResponse="ws-context-get-source-folders-response",e.wsContextAddMoreSourceFolders="ws-context-add-more-source-folders",e.wsContextRemoveSourceFolder="ws-context-remove-source-folder",e.wsContextSourceFoldersChanged="ws-context-source-folders-changed",e.wsContextFolderContentsChanged="ws-context-folder-contents-changed",e.wsContextUserRequestedRefresh="ws-context-user-requested-refresh",e.augmentLink="augment-link",e.resetAgentOnboarding="reset-agent-onboarding",e.empty="empty",e.chatGetAgentOnboardingPromptRequest="chat-get-agent-onboarding-prompt-request",e.chatGetAgentOnboardingPromptResponse="chat-get-agent-onboarding-prompt-response",e.getWorkspaceInfoRequest="get-workspace-info-request",e.getWorkspaceInfoResponse="get-workspace-info-response",e.getRemoteAgentOverviewsRequest="get-remote-agent-overviews-request",e.getRemoteAgentOverviewsResponse="get-remote-agent-overviews-response",e.remoteAgentOverviewsStreamRequest="remote-agent-overviews-stream-request",e.remoteAgentOverviewsStreamResponse="remote-agent-overviews-stream-response",e.getRemoteAgentChatHistoryRequest="get-remote-agent-chat-history-request",e.getRemoteAgentChatHistoryResponse="get-remote-agent-chat-history-response",e.remoteAgentHistoryStreamRequest="remote-agent-history-stream-request",e.remoteAgentHistoryStreamResponse="remote-agent-history-stream-response",e.cancelRemoteAgentsStreamRequest="cancel-remote-agents-stream-request",e.createRemoteAgentRequest="create-remote-agent-request",e.createRemoteAgentResponse="create-remote-agent-response",e.deleteRemoteAgentRequest="delete-remote-agent-request",e.deleteRemoteAgentResponse="delete-remote-agent-response",e.remoteAgentChatRequest="remote-agent-chat-request",e.remoteAgentChatResponse="remote-agent-chat-response",e.remoteAgentInterruptRequest="remote-agent-interrupt-request",e.remoteAgentInterruptResponse="remote-agent-interrupt-response",e.listSetupScriptsRequest="list-setup-scripts-request",e.listSetupScriptsResponse="list-setup-scripts-response",e.saveSetupScriptRequest="save-setup-script-request",e.saveSetupScriptResponse="save-setup-script-response",e.deleteSetupScriptRequest="delete-setup-script-request",e.deleteSetupScriptResponse="delete-setup-script-response",e.renameSetupScriptRequest="rename-setup-script-request",e.renameSetupScriptResponse="rename-setup-script-response",e.remoteAgentSshRequest="remote-agent-ssh-request",e.remoteAgentSshResponse="remote-agent-ssh-response",e.setRemoteAgentNotificationEnabled="set-remote-agent-notification-enabled",e.getRemoteAgentNotificationEnabledRequest="get-remote-agent-notification-enabled-request",e.getRemoteAgentNotificationEnabledResponse="get-remote-agent-notification-enabled-response",e.deleteRemoteAgentNotificationEnabled="delete-remote-agent-notification-enabled",e.setRemoteAgentPinnedStatus="set-remote-agent-pinned-status",e.getRemoteAgentPinnedStatusRequest="get-remote-agent-pinned-status-request",e.getRemoteAgentPinnedStatusResponse="get-remote-agent-pinned-status-response",e.deleteRemoteAgentPinnedStatus="delete-remote-agent-pinned-status",e.remoteAgentNotifyReady="remote-agent-notify-ready",e.remoteAgentSelectAgentId="remote-agent-select-agent-id",e.remoteAgentWorkspaceLogsRequest="remote-agent-workspace-logs-request",e.remoteAgentWorkspaceLogsResponse="remote-agent-workspace-logs-response",e.remoteAgentPauseRequest="remote-agent-pause-request",e.remoteAgentResumeRequest="remote-agent-resume-request",e.remoteAgentResumeHintRequest="remote-agent-resume-hint-request",e.updateRemoteAgentRequest="update-remote-agent-request",e.updateRemoteAgentResponse="update-remote-agent-response",e.updateSharedWebviewState="update-shared-webview-state",e.getSharedWebviewState="get-shared-webview-state",e.getSharedWebviewStateResponse="get-shared-webview-state-response",e.getGitBranchesRequest="get-git-branches-request",e.getGitBranchesResponse="get-git-branches-response",e.gitFetchRequest="git-fetch-request",e.gitFetchResponse="git-fetch-response",e.gitUpdateEvent="git-update-event",e.isGitRepositoryRequest="is-git-repository-request",e.isGitRepositoryResponse="is-git-repository-response",e.getWorkspaceDiffRequest="get-workspace-diff-request",e.getWorkspaceDiffResponse="get-workspace-diff-response",e.getRemoteUrlRequest="get-remote-url-request",e.getRemoteUrlResponse="get-remote-url-response",e.diffExplanationRequest="get-diff-explanation-request",e.diffExplanationResponse="get-diff-explanation-response",e.diffGroupChangesRequest="get-diff-group-changes-request",e.diffGroupChangesResponse="get-diff-group-changes-response",e.diffDescriptionsRequest="get-diff-descriptions-request",e.diffDescriptionsResponse="get-diff-descriptions-response",e.canApplyChangesRequest="can-apply-changes-request",e.canApplyChangesResponse="can-apply-changes-response",e.applyChangesRequest="apply-changes-request",e.applyChangesResponse="apply-changes-response",e.previewApplyChangesRequest="preview-apply-changes-request",e.previewApplyChangesResponse="preview-apply-changes-response",e.openFileRequest="open-file-request",e.openFileResponse="open-file-response",e.stashUnstagedChangesRequest="stash-unstaged-changes-request",e.stashUnstagedChangesResponse="stash-unstaged-changes-response",e.isGithubAuthenticatedRequest="is-github-authenticated-request",e.isGithubAuthenticatedResponse="is-github-authenticated-response",e.authenticateGithubRequest="authenticate-github-request",e.authenticateGithubResponse="authenticate-github-response",e.revokeGithubAccessRequest="revoke-github-access-request",e.revokeGithubAccessResponse="revoke-github-access-response",e.listGithubReposForAuthenticatedUserRequest="list-github-repos-for-authenticated-user-request",e.listGithubReposForAuthenticatedUserResponse="list-github-repos-for-authenticated-user-response",e.listGithubRepoBranchesRequest="list-github-repo-branches-request",e.listGithubRepoBranchesResponse="list-github-repo-branches-response",e.getGithubRepoRequest="get-github-repo-request",e.getGithubRepoResponse="get-github-repo-response",e.getCurrentLocalBranchRequest="get-current-local-branch-request",e.getCurrentLocalBranchResponse="get-current-local-branch-response",e.remoteAgentDiffPanelLoaded="remote-agent-diff-panel-loaded",e.remoteAgentDiffPanelSetOpts="remote-agent-diff-panel-set-opts",e.showRemoteAgentDiffPanel="show-remote-agent-diff-panel",e.closeRemoteAgentDiffPanel="close-remote-agent-diff-panel",e.remoteAgentHomePanelLoaded="remote-agent-home-panel-loaded",e.showRemoteAgentHomePanel="show-remote-agent-home-panel",e.closeRemoteAgentHomePanel="close-remote-agent-home-panel",e.secretsHomePanelLoaded="secrets-home-panel-loaded",e.showSecretsHomePanel="show-secrets-home-panel",e.closeSecretsHomePanel="close-secrets-home-panel",e.listSecretsRequest="list-secrets-request",e.listSecretsResponse="list-secrets-response",e.updateSecretRequest="update-secret-request",e.updateSecretResponse="update-secret-response",e.deleteSecretRequest="delete-secret-request",e.deleteSecretResponse="delete-secret-response",e.createSecretRequest="create-secret-request",e.createSecretResponse="create-secret-response",e.triggerInitialOrientation="trigger-initial-orientation",e.executeInitialOrientation="execute-initial-orientation",e.orientationStatusUpdate="orientation-status-update",e.getOrientationStatus="get-orientation-status",e.checkAgentAutoModeApproval="check-agent-auto-mode-approval",e.checkAgentAutoModeApprovalResponse="check-agent-auto-mode-approval-response",e.setAgentAutoModeApproved="set-agent-auto-mode-approved",e.toolConfigLoaded="tool-config-loaded",e.toolConfigInitialize="tool-config-initialize",e.toolConfigSave="tool-config-save",e.toolConfigGetDefinitions="tool-config-get-definitions",e.toolConfigDefinitionsResponse="tool-config-definitions-response",e.toolConfigStartOAuth="tool-config-start-oauth",e.toolConfigStartOAuthResponse="tool-config-start-oauth-response",e.toolConfigRevokeAccess="tool-config-revoke-access",e.toolApprovalConfigSetRequest="tool-approval-config-set-request",e.toolApprovalConfigGetRequest="tool-approval-config-get-request",e.toolApprovalConfigGetResponse="tool-approval-config-get-response",e.getStoredMCPServers="get-stored-mcp-servers",e.setStoredMCPServers="set-stored-mcp-servers",e.getStoredMCPServersResponse="get-stored-mcp-servers-response",e.deleteOAuthSession="delete-oauth-session",e.getChatRequestIdeStateRequest="get-ide-state-node-request",e.getChatRequestIdeStateResponse="get-ide-state-node-response",e.executeCommand="execute-command",e.toggleCollapseUnchangedRegions="toggle-collapse-unchanged-regions",e.openScratchFileRequest="open-scratch-file-request",e.getTerminalSettings="get-terminal-settings",e.terminalSettingsResponse="terminal-settings-response",e.updateTerminalSettings="update-terminal-settings",e.canShowTerminal="can-show-terminal",e.canShowTerminalResponse="can-show-terminal-response",e.showTerminal="show-terminal",e.showTerminalResponse="show-terminal-response",e.getRemoteAgentStatus="get-remote-agent-status",e.remoteAgentStatusResponse="remote-agent-status-response",e.remoteAgentStatusChanged="remote-agent-status-changed",e.saveLastRemoteAgentSetupRequest="save-last-remote-agent-setup-request",e.getLastRemoteAgentSetupRequest="get-last-remote-agent-setup-request",e.getLastRemoteAgentSetupResponse="get-last-remote-agent-setup-response",e.rulesLoaded="rules-loaded",e.memoriesLoaded="memories-loaded",e.getRulesListResponse="get-rules-list-response",e.getSubscriptionInfo="get-subscription-info",e.getSubscriptionInfoResponse="get-subscription-info-response",e.reportRemoteAgentEvent="report-remote-agent-event",e.reportAgentChangesApplied="report-agent-changes-applied",e.setPermissionToWriteToSSHConfig="set-permission-to-write-to-ssh-config",e.getShouldShowSSHConfigPermissionPromptRequest="get-should-show-ssh-config-permission-prompt-request",e.getShouldShowSSHConfigPermissionPromptResponse="get-should-show-ssh-config-permission-prompt-response",e))(We||{}),Be=(e=>(e.off="off",e.visibleHover="visible-hover",e.visible="visible",e.on="on",e))(Be||{}),Ne=(e=>(e.accept="accept",e.reject="reject",e))(Ne||{}),je=(e=>(e.loading="loading",e.signIn="sign-in",e.chat="chat",e.workspaceContext="workspace-context",e.awaitingSyncingPermission="awaiting-syncing-permission",e.folderSelection="folder-selection",e))(je||{}),_e=(e=>(e.idle="idle",e.inProgress="in-progress",e.succeeded="succeeded",e.failed="failed",e.aborted="aborted",e))(_e||{}),Je=(e=>(e.included="included",e.excluded="excluded",e.partial="partial",e))(Je||{}),$e=(e=>(e[e.unspecified=0]="unspecified",e[e.typingMessage=1]="typingMessage",e[e.viewingAgent=2]="viewingAgent",e))($e||{}),Y=(e=>(e.vscode="vscode",e.jetbrains="jetbrains",e.web="web",e))(Y||{});const K="data-vscode-theme-kind";function Ke(){return self.acquireVsCodeApi!==void 0}function he(){Te(function(){const e=document.body.getAttribute(K);if(e)return Xe[e]}()),He(function(){const e=document.body.getAttribute(K);if(e)return Ye[e]}())}function Qe(){if(self.acquireVsCodeApi===void 0)throw new Error("acquireVsCodeAPI not available");return function(){new MutationObserver(he).observe(document.body,{attributeFilter:[K],attributes:!0}),he()}(),{...self.acquireVsCodeApi(),clientType:Y.vscode}}const Xe={"vscode-dark":L.dark,"vscode-high-contrast":L.dark,"vscode-light":L.light,"vscode-high-contrast-light":L.light},Ye={"vscode-dark":T.regular,"vscode-light":T.regular,"vscode-high-contrast":T.highContrast,"vscode-high-contrast-light":T.highContrast};function Ze(){return window.augment_intellij!==void 0}function $(e,s,t={},n){return Oe({name:e,op:s,attributes:t},o=>{try{const l=n(o);return o==null||o.setStatus({code:1}),l}catch(l){throw o==null||o.setStatus({code:2,message:l instanceof Error?l.message:"Unknown error"}),l}})}function et(){var e;if(Ke())return Qe();if(Ze())return function(){const s=window.augment_intellij;if(s===void 0||s.setState===void 0||s.getState===void 0||s.postMessage===void 0)throw new Error("Augment IntelliJ host not available");window.augment=window.augment||{};let t=!1;return window.augment.host={clientType:Y.jetbrains,setState:n=>$("hostSetState","hostOperation",{hostClientType:"jetbrains",hostInitialized:t},o=>{t||(console.error("Host not initialized"),o==null||o.setStatus({code:2,message:"Host not initialized"})),s.setState(n)}),getState:()=>$("hostGetState","hostOperation",{hostClientType:"jetbrains",hostInitialized:t},n=>(t||(console.error("Host not initialized"),n==null||n.setStatus({code:2,message:"Host not initialized"})),s.getState())),postMessage:n=>$("hostPostMessage","hostOperation",{hostClientType:"jetbrains",hostInitialized:t,messageType:(n==null?void 0:n.type)??"unknown"},o=>{t||(console.error("Host not initialized"),o==null||o.setStatus({code:2,message:"Host not initialized"})),s.postMessage(n)}),initialize:async()=>{await s.initializationPromise,t=!0}},window.augment.host}();if(!((e=window.augment)!=null&&e.host))throw new Error("Augment host not available");return window.augment.host}function tt(){var e;return(e=window.augment)!=null&&e.host||(window.augment=window.augment||{},window.augment.host=et()),window.augment.host}const at=tt();export{Ne as D,Y as H,je as M,_e as O,$e as R,Be as S,We as W,Je as a,it as b,Ke as c,Ze as d,ot as e,tt as g,at as h,nt as i};
