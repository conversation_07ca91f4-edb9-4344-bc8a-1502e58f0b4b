var lr=Object.defineProperty;var cr=(i,e,s)=>e in i?lr(i,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[e]=s;var Ie=(i,e,s)=>cr(i,typeof e!="symbol"?e+"":e,s);import{ay as dr,aY as ur,aZ as vr,f as lt,b as r,Z as Qe,w as xs,a_ as gr,aO as gs,a4 as qs,x as Je,y as h,o,E as d,z as t,A as it,G as Be,D as V,N as oe,a1 as ut,O as ke,M as I,a3 as St,a2 as hr,I as me,J as nt,a as Pt,C as pr,m as H,u as k,a8 as mt,B as ge,L as Xe,P as v,H as mr,Q as x,K as tt,ag as Ss,R as _e,as as As,Y as Kt,X as Et,a$ as zs,aq as ns,W as hs,F as os,S as Ls,a6 as Hs,a5 as Gs,ar as fr,au as Cr}from"./legacy-AoIeRrIA.js";import{p as S,T as se,a as gt,b as je,h as $r,l as zt,g as dt,f as Js,S as rs,e as Jt,s as is,r as wr,o as Rs}from"./SpinnerAugment-mywmfXFR.js";import"./design-system-init-Creeq9bS.js";import{W as qe,a as pt,e as et,b as yr,h as Ye,i as bt,c as Is,g as Sr,H as ls}from"./host-qgbK079d.js";import{M as br}from"./message-broker-EhzME3pO.js";import{G as Mr,S as kr,a as xr,N as Ar,L as zr,b as rt,M as It,D as Lr,F as Rr,f as Bs,R as Ir,c as _s,d as _r,C as Nr,P as cs,e as Er,g as Tr,h as Fr,A as Pr,T as Ur,i as Or}from"./partner-mcp-utils-BH31APX7.js";import{h as ds,a1 as Ct,L as De,a2 as Ue,f as us,z as ps,T as At,D as Ze,a3 as Vr,C as js,E as Zs,a4 as Yt,b as vs,A as Ns,c as Dr,d as qr,R as Hr}from"./index-BLDiLrXG.js";import{G as Gr,B as ms}from"./github-BdMD2Kls.js";import{D as Qt,P as Tt,C as Jr,g as Ws,T as Ks,a as fs,b as Ys,S as Cs,c as Br,s as jr}from"./extension-client-context-CnKF1ct_.js";import{o as es}from"./keypress-DD1aQVr0.js";import{V as Qs}from"./VSCodeCodicon-Bh752rpS.js";import{I as Lt,a as Zr,h as Zt}from"./IconButtonAugment-DZyIKjh7.js";import{A as Wr}from"./async-messaging-Dmg2N9Pf.js";import{c as $s,M as Kr}from"./index-BdF7sLLk.js";import{k as Yr,C as Xs,a as Qr,T as ts}from"./CollapseButtonAugment-BXWAqMp6.js";import{D as Xr}from"./Drawer-KXH87plr.js";import{b as er,T as $t,a as Nt}from"./CardAugment-DwIptXof.js";import{B as Ge}from"./ButtonAugment-D7YBjBq5.js";import{p as ea}from"./event-modifiers-Bz4QCcZc.js";import{C as Bt}from"./CalloutAugment-Db5scVK5.js";import{E as ta}from"./ellipsis-5yZhsJie.js";import{P as sa}from"./pen-to-square-DPoLDlf4.js";import{C as ra,T as bs}from"./TextAreaAugment-B1LKPxPr.js";import{C as Ms}from"./chevron-down-CUZr9PGN.js";import{S as aa}from"./SuccessfulButton-xgZ5Aax4.js";import"./BaseTextInput-D2MYbf3a.js";import{R as na}from"./RulesModeSelector-DckNSDsy.js";import{M as tr}from"./ModalAugment-CfGbMl4M.js";import{R as Es}from"./chat-types-BfwvR7Kn.js";import"./focusTrapStack-CaEmYw0i.js";import"./isObjectLike-CnrzNkq5.js";import"./input-DCBQtNgo.js";import"./BadgeRoot-CMDpgWKP.js";import"./index-Dtf_gCqL.js";const oa=[];function Ts(i,e=!1){return ss(i,new Map,"",oa)}function ss(i,e,s,a,n=null){if(typeof i=="object"&&i!==null){var c=e.get(i);if(c!==void 0)return c;if(i instanceof Map)return new Map(i);if(i instanceof Set)return new Set(i);if(dr(i)){var u=Array(i.length);e.set(i,u),n!==null&&e.set(n,u);for(var b=0;b<i.length;b+=1){var A=i[b];b in i&&(u[b]=ss(A,e,s,a))}return u}if(ur(i)===vr){for(var m in u={},e.set(i,u),n!==null&&e.set(n,u),i)u[m]=ss(i[m],e,s,a);return u}if(i instanceof Date)return structuredClone(i);if(typeof i.toJSON=="function")return ss(i.toJSON(),e,s,a,i)}if(i instanceof EventTarget)return i;try{return structuredClone(i)}catch{return i}}const qt={maxMS:9e5,initialMS:6e4,mult:2,maxSteps:4};class ia{constructor(e,s=qt){Ie(this,"timerId",null);Ie(this,"currentMS");Ie(this,"step",0);Ie(this,"params");this.callback=e;const a={...s};a.maxMS<0&&(console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes"),a.maxMS=qt.maxMS),a.initialMS<=0&&(console.warn("PollingManager: Negative or zero initialMS detected, using default value of 1 minute"),a.initialMS=qt.initialMS),a.mult<=0&&(console.warn("PollingManager: Negative or zero multiplier detected, using default value of 2"),a.mult=qt.mult),a.maxSteps!==void 0&&a.maxSteps<0&&(console.warn("PollingManager: Negative maxSteps detected, using default value of 4"),a.maxSteps=qt.maxSteps),this.params=a,this.currentMS=this.params.maxMS}startPolling(){this.stopPolling(),this.currentMS=this.params.initialMS,this.step=0,this.safeExecute(),this.scheduleNext()}stopPolling(){this.timerId!==null&&(window.clearTimeout(this.timerId),this.timerId=null)}dispose(){this.stopPolling()}scheduleNext(){this.timerId=window.setTimeout(()=>{if(this.safeExecute(),this.params.maxMS===0){if(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps)return void this.stopPolling()}else this.currentMS<this.params.maxMS&&(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps?(this.currentMS=this.params.maxMS,this.step=0):this.currentMS=Math.min(this.currentMS*this.params.mult,this.params.maxMS));this.scheduleNext()},this.currentMS)}safeExecute(){try{const e=this.callback();e instanceof Promise&&e.catch(s=>console.error("Error in polling callback:",s))}catch(e){console.error("Error in polling callback:",e)}}}var la=lt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.43703 10.7785C2.30998 10.978 2.16478 11.2137 2.05588 11.3951C1.94698 11.5764 2.00143 11.8121 2.18293 11.921L4.66948 13.4442C4.85098 13.553 5.08695 13.4986 5.19585 13.3173C5.2866 13.1541 5.41365 12.9365 5.55885 12.7007C6.53895 11.0868 7.5372 11.2681 9.3159 12.1204L11.7843 13.281C11.9839 13.3717 12.2017 13.281 12.2925 13.0997L13.4722 10.4339C13.563 10.2526 13.4722 10.0169 13.2907 9.92619C12.7644 9.69044 11.7298 9.20084 10.8223 8.74749C7.44645 7.13354 4.59689 7.24234 2.43703 10.7785Z" fill="currentColor"></path><path d="M13.563 4.72157C13.69 4.52209 13.8352 4.28635 13.9441 4.105C14.053 3.92366 13.9985 3.68791 13.817 3.57911L11.3305 2.05583C11.149 1.94702 10.913 2.00143 10.8041 2.18277C10.7134 2.34598 10.5863 2.56359 10.4411 2.79934C9.461 4.41329 8.46275 4.23194 6.68405 3.37963L4.21563 2.21904C4.01598 2.12837 3.79818 2.21904 3.70743 2.40038L2.52767 5.0661C2.43692 5.24745 2.52767 5.4832 2.70917 5.5739C3.23552 5.80965 4.27007 6.29925 5.1776 6.7526C8.53535 8.34845 11.3849 8.25775 13.563 4.72157Z" fill="currentColor"></path></svg>');function ca(i){var e=la();r(i,e)}var da=lt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5028 2H7.7257C7.7257 3.44 8.8914 4.60571 10.3314 4.60571H11.3942V5.6343C11.3942 7.0743 12.5599 8.24 14 8.24V2.49714C14 2.22285 13.7771 2 13.5028 2ZM10.6399 4.88H4.86279C4.86279 6.32 6.0285 7.4857 7.4685 7.4857H8.53135V8.5143C8.53135 9.9543 9.69705 11.12 11.137 11.12V5.37715C11.137 5.10285 10.9142 4.88 10.6399 4.88ZM2 7.75995H7.7771C8.0514 7.75995 8.27425 7.9828 8.27425 8.2571V13.9999C6.83425 13.9999 5.66855 12.8342 5.66855 11.3942V10.3656H4.6057C3.16571 10.3656 2 9.19995 2 7.75995Z" fill="currentColor"></path></svg>');function ua(i){var e=da();r(i,e)}var va=lt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 2.5C3 2.22386 3.22386 2 3.5 2H9.08579C9.21839 2 9.34557 2.05268 9.43934 2.14645L11.8536 4.56066C11.9473 4.65443 12 4.78161 12 4.91421V12.5C12 12.7761 11.7761 13 11.5 13H3.5C3.22386 13 3 12.7761 3 12.5V2.5ZM3.5 1C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H11.5C12.3284 14 13 13.3284 13 12.5V4.91421C13 4.51639 12.842 4.13486 12.5607 3.85355L10.1464 1.43934C9.86514 1.15804 9.48361 1 9.08579 1H3.5ZM4.5 4C4.22386 4 4 4.22386 4 4.5C4 4.77614 4.22386 5 4.5 5H7.5C7.77614 5 8 4.77614 8 4.5C8 4.22386 7.77614 4 7.5 4H4.5ZM4.5 7C4.22386 7 4 7.22386 4 7.5C4 7.77614 4.22386 8 4.5 8H10.5C10.7761 8 11 7.77614 11 7.5C11 7.22386 10.7761 7 10.5 7H4.5ZM4.5 10C4.22386 10 4 10.2239 4 10.5C4 10.7761 4.22386 11 4.5 11H10.5C10.7761 11 11 10.7761 11 10.5C11 10.2239 10.7761 10 10.5 10H4.5Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg>');function yt(i){var e=va();r(i,e)}var ga=lt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.5 2C3.22386 2 3 2.22386 3 2.5V12.5C3 12.7761 3.22386 13 3.5 13H11.5C11.7761 13 12 12.7761 12 12.5V4.70711L9.29289 2H3.5ZM2 2.5C2 1.67157 2.67157 1 3.5 1H9.5C9.63261 1 9.75979 1.05268 9.85355 1.14645L12.7803 4.07322C12.921 4.21388 13 4.40464 13 4.60355V12.5C13 13.3284 12.3284 14 11.5 14H3.5C2.67157 14 2 13.3284 2 12.5V2.5ZM4.75 7.5C4.75 7.22386 4.97386 7 5.25 7H9.75C10.0261 7 10.25 7.22386 10.25 7.5C10.25 7.77614 10.0261 8 9.75 8H5.25C4.97386 8 4.75 7.77614 4.75 7.5Z" fill="currentColor"></path></svg>');function ha(i){var e=ga();r(i,e)}class Ft{constructor(e){Ie(this,"configs",Qe([]));Ie(this,"pollingManager");Ie(this,"_enableDebugFeatures",Qe(!1));Ie(this,"_settingsComponentSupported",Qe({workspaceContext:!1,mcpServerList:!1,mcpServerImport:!1,orientation:!1,remoteTools:!1,userGuidelines:!1,terminal:!1,rules:!1}));Ie(this,"_enableAgentMode",Qe(!1));Ie(this,"_enableAgentSwarmMode",Qe(!1));Ie(this,"_enableNativeRemoteMcp",Qe(!0));Ie(this,"_hasEverUsedRemoteAgent",Qe(!1));Ie(this,"_enableInitialOrientation",Qe(!1));Ie(this,"_userTier",Qe("unknown"));Ie(this,"_userEmail",Qe(void 0));Ie(this,"_guidelines",Qe({}));this._host=e,this.pollingManager=new ia(()=>this.requestToolStatus(!1),{maxMS:0,initialMS:2e3,mult:1,maxSteps:150}),this.requestToolStatus(!1)}transformToolDisplay(e){const s=!e.isConfigured,a=e.oauthUrl;if(e.identifier.hostName===ds.remoteToolHost){let n=e.identifier.toolId;switch(typeof n=="string"&&/^\d+$/.test(n)&&(n=Number(n)),n){case Ct.GitHubApi:return{displayName:"GitHub",description:"Configure GitHub API access for repository operations",icon:Gr,requiresAuthentication:s,authUrl:a};case Ct.Linear:return{displayName:"Linear",description:"Configure Linear API access for issue tracking",icon:zr,requiresAuthentication:s,authUrl:a};case Ct.Jira:return{displayName:"Jira",description:"Configure Jira API access for issue tracking",icon:ua,requiresAuthentication:s,authUrl:a};case Ct.Notion:return{displayName:"Notion",description:"Configure Notion API access",icon:Ar,requiresAuthentication:s,authUrl:a};case Ct.Confluence:return{displayName:"Confluence",description:"Configure Confluence API access",icon:ca,requiresAuthentication:s,authUrl:a};case Ct.WebSearch:return{displayName:"Web Search",description:"Configure web search capabilities",icon:xr,requiresAuthentication:s,authUrl:a};case Ct.Supabase:return{displayName:"Supabase",description:"Configure Supabase API access",icon:kr,requiresAuthentication:s,authUrl:a};case Ct.Glean:return{displayName:"Glean",description:"Configure Glean API access",icon:Mr,requiresAuthentication:s,authUrl:a};case Ct.Unknown:return{displayName:"Unknown",description:"Unknown tool",requiresAuthentication:s,authUrl:a};default:throw new Error(`Unhandled RemoteToolId: ${n}`)}}else if(e.identifier.hostName===ds.localToolHost){const n=e.identifier.toolId;switch(n){case De.readFile:case De.editFile:case De.saveFile:case De.launchProcess:case De.killProcess:case De.readProcess:case De.writeProcess:case De.listProcesses:case De.waitProcess:case De.openBrowser:case De.clarify:case De.onboardingSubAgent:case De.strReplaceEditor:case De.remember:case De.diagnostics:case De.webFetch:case De.setupScript:case De.readTerminal:case De.gitCommitRetrieval:case De.memoryRetrieval:case De.startWorkerAgent:case De.readWorkerState:case De.waitForWorkerAgent:case De.sendInstructionToWorkerAgent:case De.stopWorkerAgent:case De.deleteWorkerAgent:case De.readWorkerAgentEdits:case De.applyWorkerAgentEdits:case De.LocalSubAgent:return{displayName:e.definition.name.toString(),description:"Local tool",icon:yt,requiresAuthentication:s,authUrl:a};default:throw new Error(`Unhandled LocalToolType: ${n}`)}}else if(e.identifier.hostName===ds.sidecarToolHost){const n=e.identifier.toolId;switch(n){case rt.codebaseRetrieval:return{displayName:"Code Search",description:"Configure codebase search capabilities",icon:It,requiresAuthentication:s,authUrl:a};case rt.shell:return{displayName:"Shell",description:"Shell",icon:It,requiresAuthentication:s,authUrl:a};case rt.strReplaceEditor:return{displayName:"File Edit",description:"File Editor",icon:It,requiresAuthentication:s,authUrl:a};case rt.view:return{displayName:"File View",description:"File Viewer",icon:It,requiresAuthentication:s,authUrl:a};case rt.webFetch:return{displayName:"Web Fetch",description:"Retrieve information from the web",icon:It,requiresAuthentication:s,authUrl:a};case rt.removeFiles:return{displayName:"Remove Files",description:"Remove files from the codebase",icon:ha,requiresAuthentication:s,authUrl:a};case rt.remember:return{displayName:e.definition.name.toString(),description:"Remember",icon:yt,requiresAuthentication:s,authUrl:a};case rt.saveFile:return{displayName:"Save File",description:"Save a new file",icon:Rr,requiresAuthentication:s,authUrl:a};case rt.viewTaskList:return{displayName:"View Task List",description:"View the current task list",icon:yt,requiresAuthentication:s,authUrl:a};case rt.reorganizeTaskList:return{displayName:"Reorganize Task List",description:"Reorganize the task list structure for major restructuring",icon:yt,requiresAuthentication:s,authUrl:a};case rt.viewRangeUntruncated:return{displayName:e.definition.name.toString(),description:"View Range",icon:yt,requiresAuthentication:s,authUrl:a};case rt.updateTasks:return{displayName:"Update Tasks",description:"Update one or more tasks in the task list",icon:yt,requiresAuthentication:s,authUrl:a};case rt.addTasks:return{displayName:"Add Tasks",description:"Add one or more new tasks to the task list",icon:yt,requiresAuthentication:s,authUrl:a};case rt.searchUntruncated:return{displayName:e.definition.name.toString(),description:"Search Untruncated",icon:yt,requiresAuthentication:s,authUrl:a};case rt.renderMermaid:return{displayName:"View Mermaid Diagram",description:"View a mermaid diagram",icon:Lr,requiresAuthentication:s,authUrl:a};case rt.grepSearch:return{displayName:"Grep search",description:"Run grep search",icon:It,requiresAuthentication:s,authUrl:a};default:throw new Error(`Unhandled SidecarToolType: ${n}`)}}return{displayName:e.definition.name.toString(),description:e.definition.description||"",requiresAuthentication:s,authUrl:a}}handleMessageFromExtension(e){const s=e.data;switch(s.type){case qe.toolConfigInitialize:return this.createConfigsFromHostTools(s.data.hostTools,s.data.toolConfigs),s.data&&s.data.enableDebugFeatures!==void 0&&this._enableDebugFeatures.set(s.data.enableDebugFeatures),s.data&&s.data.settingsComponentSupported!==void 0&&this._settingsComponentSupported.set(s.data.settingsComponentSupported),s.data.enableAgentMode!==void 0&&this._enableAgentMode.set(s.data.enableAgentMode),s.data.enableAgentSwarmMode!==void 0&&this._enableAgentSwarmMode.set(s.data.enableAgentSwarmMode),s.data.hasEverUsedRemoteAgent!==void 0&&this._hasEverUsedRemoteAgent.set(s.data.hasEverUsedRemoteAgent),s.data.enableInitialOrientation!==void 0&&this._enableInitialOrientation.set(s.data.enableInitialOrientation),s.data.userTier!==void 0&&this._userTier.set(s.data.userTier),s.data.userEmail!==void 0&&this._userEmail.set(s.data.userEmail),s.data.guidelines!==void 0&&this._guidelines.set(s.data.guidelines),s.data.enableNativeRemoteMcp!==void 0&&this._enableNativeRemoteMcp.set(s.data.enableNativeRemoteMcp),!0;case qe.toolConfigDefinitionsResponse:return this.configs.update(a=>this.createConfigsFromHostTools(s.data.hostTools,[]).map(n=>{const c=a.find(u=>u.name===n.name);return c?{...c,displayName:n.displayName,description:n.description,icon:n.icon,requiresAuthentication:n.requiresAuthentication,authUrl:n.authUrl,isConfigured:n.isConfigured,toolApprovalConfig:n.toolApprovalConfig}:n})),!0}return!1}createConfigsFromHostTools(e,s){return e.map(a=>{const n=this.transformToolDisplay(a),c=s.find(b=>b.name===a.definition.name),u=(c==null?void 0:c.isConfigured)??!n.requiresAuthentication;return{config:(c==null?void 0:c.config)??{},configString:JSON.stringify((c==null?void 0:c.config)??{},null,2),isConfigured:u,name:a.definition.name.toString(),displayName:n.displayName,description:n.description,identifier:a.identifier,icon:n.icon,requiresAuthentication:n.requiresAuthentication,authUrl:n.authUrl,showStatus:!1,statusMessage:"",statusType:"info",toolApprovalConfig:a.toolApprovalConfig}})}getConfigs(){return this.configs}isDisplayableTool(e){return["github","linear","notion","jira","confluence","supabase"].includes(e.displayName.toLowerCase())}getDisplayableTools(){return xs(this.configs,e=>{const s=e.filter(n=>this.isDisplayableTool(n)),a=new Map;for(const n of s)a.set(n.displayName,n);return Array.from(a.values()).sort((n,c)=>{const u={GitHub:1,Linear:2,Notion:3},b=Number.MAX_SAFE_INTEGER,A=u[n.displayName]||b,m=u[c.displayName]||b;return A<b&&m<b||A===b&&m===b?A!==m?A-m:n.displayName.localeCompare(c.displayName):A-m})})}getPretendNativeToolDefs(){return xs(this.configs,e=>this.getEnableNativeRemoteMcp()?Bs(e):[])}saveConfig(e){this.startPolling()}notifyLoaded(){this._host.postMessage({type:qe.toolConfigLoaded})}startPolling(){this.pollingManager.startPolling()}requestToolStatus(e=!0){this._host.postMessage({type:qe.toolConfigGetDefinitions,data:{useCache:e}})}dispose(){this.pollingManager.dispose()}getEnableDebugFeatures(){return this._enableDebugFeatures}getEnableAgentMode(){return this._enableAgentMode}getEnableAgentSwarmMode(){return this._enableAgentSwarmMode}getEnableNativeRemoteMcp(){return this._host.clientType==="vscode"?this._enableNativeRemoteMcp:gr(!1)}getHasEverUsedRemoteAgent(){return this._hasEverUsedRemoteAgent}getEnableInitialOrientation(){return this._enableInitialOrientation}getUserTier(){return this._userTier}getUserEmail(){return this._userEmail}getGuidelines(){return this._guidelines}updateLocalUserGuidelines(e){this._guidelines.update(s=>s.userGuidelines?{...s,userGuidelines:{...s.userGuidelines,contents:e,enabled:e.length>0}}:s)}updateToolApprovalConfig(e,s){this.configs.update(a=>a.map(n=>n.identifier.toolId===e.toolId&&n.identifier.hostName===e.hostName?{...n,toolApprovalConfig:s}:n))}getSettingsComponentSupported(){return this._settingsComponentSupported}}Ie(Ft,"key","toolConfigModel");class at extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,at.prototype)}}const wt=Ue.object({name:Ue.string().optional(),title:Ue.string().optional(),type:Ue.enum(["stdio","http","sse"]).optional(),command:Ue.string().optional(),args:Ue.array(Ue.union([Ue.string(),Ue.number(),Ue.boolean()])).optional(),env:Ue.record(Ue.union([Ue.string(),Ue.number(),Ue.boolean(),Ue.null(),Ue.undefined()])).optional(),url:Ue.string().optional()}).passthrough();function ct(i){return(i==null?void 0:i.type)==="http"||(i==null?void 0:i.type)==="sse"}function _t(i){return(i==null?void 0:i.type)==="stdio"}function Xt(i){return ct(i)?i.url:_t(i)?i.command:""}const pa=Ue.array(wt),ma=Ue.object({servers:Ue.array(wt)}),fa=Ue.object({mcpServers:Ue.array(wt)}),Ca=Ue.object({servers:Ue.record(Ue.unknown())}),$a=Ue.object({mcpServers:Ue.record(Ue.unknown())}),wa=Ue.record(Ue.unknown()),ya=wt.refine(i=>{const e=i.command!==void 0,s=i.url!==void 0;if(!e&&!s)return!1;const a=new Set(["name","title","type","command","args","env","url"]);return Object.keys(i).every(n=>a.has(n))},{message:"Single server object must have valid server properties"});function kt(i){try{const e=wt.transform(s=>{let a;if(s.type)a=s.type;else if(s.url)a="http";else{if(!s.command)throw new Error("Server must have either 'command' (for stdio) or 'url' (for http/sse) property");a="stdio"}if(a==="http"||a==="sse"){if(!s.url)throw new Error(`${a.toUpperCase()} server must have a 'url' property`);return{type:a,name:s.name||s.title||s.url,url:s.url}}{const n=s.command||"",c=s.args?s.args.map(m=>String(m)):[];if(!n)throw new Error("Stdio server must have a 'command' property");const u=c.length>0?`${n} ${c.join(" ")}`:n,b=s.name||s.title||(n?n.split(" ")[0]:""),A=s.env?Object.fromEntries(Object.entries(s.env).filter(([m,l])=>l!=null).map(([m,l])=>[m,String(l)])):void 0;return{type:"stdio",name:b,command:u,arguments:"",useShellInterpolation:!0,env:Object.keys(A||{}).length>0?A:void 0}}}).refine(s=>!!s.name,{message:"Server must have a name",path:["name"]}).refine(s=>s.type==="http"||s.type==="sse"?!!s.url:!!s.command,{message:"Server must have either 'command' (for stdio) or 'url' (for http/sse)",path:["command","url"]}).safeParse(i);if(!e.success)throw new at(e.error.message);return e.data}catch(e){throw e instanceof Error?new at(`Invalid server configuration: ${e.message}`):new at("Invalid server configuration")}}class Wt{constructor(e){Ie(this,"servers",Qe([]));this.host=e,this.loadServersFromStorage()}handleMessageFromExtension(e){const s=e.data;if(s.type===qe.getStoredMCPServersResponse){const a=s.data;return Array.isArray(a)&&this.servers.set(a),!0}return!1}async importServersFromJSON(e){return this.importFromJSON(e)}loadServersFromStorage(){try{this.host.postMessage({type:qe.getStoredMCPServers})}catch(e){console.error("Failed to load MCP servers:",e),this.servers.set([])}}saveServers(e){try{this.host.postMessage({type:qe.setStoredMCPServers,data:e})}catch(s){throw console.error("Failed to save MCP servers:",s),new at("Failed to save MCP servers")}}getServers(){return this.servers}addServer(e){this.checkExistingServerName(e.name),this.servers.update(s=>{const a=[...s,{...e,id:crypto.randomUUID()}];return this.saveServers(a),a})}addServers(e){for(const s of e)this.checkExistingServerName(s.name);this.servers.update(s=>{const a=[...s,...e.map(n=>({...n,id:crypto.randomUUID()}))];return this.saveServers(a),a})}checkExistingServerName(e,s){const a=gs(this.servers).find(n=>n.name===e);if(a&&(a==null?void 0:a.id)!==s)throw new at(`Server name '${e}' already exists`)}updateServer(e){this.checkExistingServerName(e.name,e.id),this.servers.update(s=>{const a=s.map(n=>n.id===e.id?e:n);return this.saveServers(a),a})}deleteServer(e){this.servers.update(s=>{const a=s.filter(n=>n.id!==e.id);return this.saveServers(a),a}),e.type!=="http"&&e.type!=="sse"||this.host.postMessage({type:qe.deleteOAuthSession,data:e.name})}toggleDisabledServer(e){this.servers.update(s=>{const a=s.map(n=>n.id===e?{...n,disabled:!n.disabled}:n);return this.saveServers(a),a})}static convertServerToJSON(e){if(ct(e))return JSON.stringify({mcpServers:{[e.name]:{url:e.url,type:e.type}}},null,2);{const s=e;return JSON.stringify({mcpServers:{[s.name]:{command:s.command.split(" ")[0],args:s.command.split(" ").slice(1),env:s.env}}},null,2)}}static parseServerValidationMessages(e){const s=new Map,a=new Map;e.forEach(c=>{var b,A;const u=(b=c.tools)==null?void 0:b.filter(m=>!m.enabled).map(m=>m.definition.mcp_tool_name);c.disabled?s.set(c.id,"MCP server has been manually disabled"):c.tools&&c.tools.length===0?s.set(c.id,"No tools are available for this MCP server"):u&&u.length===((A=c.tools)==null?void 0:A.length)?s.set(c.id,"All tools for this MCP server have validation errors: "+u.join(", ")):u&&u.length>0&&a.set(c.id,"MCP server has validation errors in the following tools which have been disabled: "+u.join(", "))});const n=this.parseDuplicateServerIds(e);return{errors:new Map([...s,...n]),warnings:a}}static parseDuplicateServerIds(e){const s=new Map;for(const n of e)s.has(n.name)||s.set(n.name,[]),s.get(n.name).push(n.id);const a=new Map;for(const[,n]of s)if(n.length>1)for(let c=1;c<n.length;c++)a.set(n[c],"MCP server is disabled due to duplicate server names");return a}static convertParsedServerToWebview(e){const{tools:s,...a}=e;return{...a,tools:void 0}}static parseServerConfigFromJSON(e){return function(a){try{const n=JSON.parse(a),c=Ue.union([pa.transform(u=>u.map(b=>kt(b))),ma.transform(u=>u.servers.map(b=>kt(b))),fa.transform(u=>u.mcpServers.map(b=>kt(b))),Ca.transform(u=>Object.entries(u.servers).map(([b,A])=>{const m=wt.parse(A);return kt({...m,name:m.name||b})})),$a.transform(u=>Object.entries(u.mcpServers).map(([b,A])=>{const m=wt.parse(A);return kt({...m,name:m.name||b})})),ya.transform(u=>[kt(u)]),wa.transform(u=>{if(!Object.values(u).some(b=>{const A=wt.safeParse(b);return A.success&&(A.data.command!==void 0||A.data.url!==void 0)}))throw new Error("No command or url property found in any server config");return Object.entries(u).map(([b,A])=>{const m=wt.parse(A);return kt({...m,name:m.name||b})})})]).safeParse(n);if(c.success)return c.data;throw new at("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(n){throw n instanceof at?n:new at("Failed to parse MCP servers from JSON. Please check the format.")}}(e).map(a=>this.convertParsedServerToWebview(a))}importFromJSON(e){try{const s=Wt.parseServerConfigFromJSON(e),a=gs(this.servers),n=new Set(a.map(c=>c.name));for(const c of s){if(!c.name)throw new at("All servers must have a name.");if(n.has(c.name))throw new at(`A server with the name '${c.name}' already exists.`);n.add(c.name)}return this.servers.update(c=>{const u=[...c,...s.map(b=>({...b,id:crypto.randomUUID()}))];return this.saveServers(u),u}),s.length}catch(s){throw s instanceof at?s:new at("Failed to import MCP servers from JSON. Please check the format.")}}}class Sa{constructor(e){Ie(this,"_terminalSettings",Qe({supportedShells:[],selectedShell:void 0,startupScript:void 0}));this._host=e,this.requestTerminalSettings()}handleMessageFromExtension(e){const s=e.data;return s.type===qe.terminalSettingsResponse&&(this._terminalSettings.set(s.data),!0)}getTerminalSettings(){return this._terminalSettings}requestTerminalSettings(){this._host.postMessage({type:qe.getTerminalSettings})}updateSelectedShell(e){this._terminalSettings.update(s=>({...s,selectedShell:e})),this._host.postMessage({type:qe.updateTerminalSettings,data:{selectedShell:e}})}updateStartupScript(e){this._terminalSettings.update(s=>({...s,startupScript:e})),this._host.postMessage({type:qe.updateTerminalSettings,data:{startupScript:e}})}}const Gt=class Gt{constructor(e){Ie(this,"_swarmModeSettings",Qe(Qt));Ie(this,"_isLoaded",!1);Ie(this,"_pollInterval",null);Ie(this,"_lastKnownSettingsHash","");Ie(this,"dispose",()=>{this.stopPolling()});this._msgBroker=e,this.initialize(),this.startPolling()}get getCurrentSettings(){return this._swarmModeSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:us.getSwarmModeSettings});e.data&&(this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=JSON.stringify(e.data)),this._isLoaded=!0}catch(e){console.warn("Failed to load swarm mode settings, using defaults:",e),this._swarmModeSettings.set(Qt),this._lastKnownSettingsHash=JSON.stringify(Qt),this._isLoaded=!0}}async updateSettings(e){try{const s=await this._msgBroker.sendToSidecar({type:us.updateSwarmModeSettings,data:e});s.data&&(this._swarmModeSettings.set(s.data),this._lastKnownSettingsHash=JSON.stringify(s.data))}catch(s){throw console.error("Failed to update swarm mode settings:",s),s}}async setEnabled(e){await this.updateSettings({enabled:e})}async resetToDefaults(){await this.updateSettings(Qt)}updateEnabled(e){this.setEnabled(e).catch(s=>{console.error("Failed to update enabled setting:",s)})}startPolling(){this._pollInterval=setInterval(()=>{this.checkForUpdates()},Gt.POLLING_INTERVAL_MS)}stopPolling(){this._pollInterval!==null&&(clearInterval(this._pollInterval),this._pollInterval=null)}async checkForUpdates(){try{const e=await this._msgBroker.sendToSidecar({type:us.getSwarmModeSettings}),s=JSON.stringify(e.data);this._lastKnownSettingsHash&&s!==this._lastKnownSettingsHash&&e.data&&this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=s}catch(e){console.warn("Failed to check for swarm mode settings updates:",e)}}};Ie(Gt,"key","swarmModeModel"),Ie(Gt,"POLLING_INTERVAL_MS",5e3);let jt=Gt;var xt=(i=>(i.file="file",i.folder="folder",i))(xt||{});class Mt{constructor(e,s){Ie(this,"subscribe");Ie(this,"set");Ie(this,"update");Ie(this,"handleMessageFromExtension",async e=>{const s=e.data;switch(s.type){case qe.wsContextSourceFoldersChanged:case qe.wsContextFolderContentsChanged:this.updateSourceFolders(await this.getSourceFolders());break;case qe.sourceFoldersSyncStatus:this.update(a=>({...a,syncStatus:s.data.status}))}});Ie(this,"getSourceFolders",async()=>(await this.asyncMsgSender.send({type:qe.wsContextGetSourceFoldersRequest},1e4)).data.workspaceFolders);Ie(this,"getChildren",async e=>(await this.asyncMsgSender.send({type:qe.wsContextGetChildrenRequest,data:{fileId:e}},1e4)).data.children.map(s=>s.type==="folder"?{...s,children:[],expanded:!1}:{...s}).sort((s,a)=>s.type===a.type?s.name.localeCompare(a.name):s.type==="folder"?-1:1));this.host=e,this.asyncMsgSender=s;const{subscribe:a,set:n,update:c}=Qe({sourceFolders:[],sourceTree:[],syncStatus:ps.done});this.subscribe=a,this.set=n,this.update=c,this.getSourceFolders().then(u=>{this.update(b=>({...b,sourceFolders:u,sourceTree:Mt.sourceFoldersToSourceNodes(u)}))})}async expandNode(e){e.children=await this.getChildren(e.fileId),e.expanded=!0,this.update(s=>s)}collapseNode(e){this.update(s=>(e.children=[],e.expanded=!1,s))}toggleNode(e){e.type==="folder"&&e.inclusionState!==pt.excluded&&(e.expanded?this.collapseNode(e):this.expandNode(e))}addMoreSourceFolders(){this.host.postMessage({type:qe.wsContextAddMoreSourceFolders})}removeSourceFolder(e){this.host.postMessage({type:qe.wsContextRemoveSourceFolder,data:e})}requestRefresh(){this.host.postMessage({type:qe.wsContextUserRequestedRefresh})}async updateSourceFolders(e){let s=gs(this);const a=await this.getRefreshedSourceTree(s.sourceTree,e);this.update(n=>({...n,sourceFolders:e,sourceTree:a}))}async getRefreshedSourceTree(e,s){const a=Mt.sourceFoldersToSourceNodes(s);return this.getRefreshedSourceTreeRecurse(e,a)}async getRefreshedSourceTreeRecurse(e,s){const a=new Map(e.map(n=>[JSON.stringify([n.fileId.folderRoot,n.fileId.relPath]),n]));for(let n of s){const c=Mt.fileIdToString(n.fileId);if(n.type==="folder"){const u=a.get(c);u&&(n.expanded=u.type==="folder"&&u.expanded,n.expanded&&(n.children=await this.getChildren(n.fileId),n.children=await this.getRefreshedSourceTreeRecurse(u.children,n.children)))}}return s}static fileIdToString(e){return JSON.stringify([e.folderRoot,e.relPath])}static sourceFoldersToSourceNodes(e){return e.filter(s=>!s.isNestedFolder&&!s.isPending).sort((s,a)=>s.name.localeCompare(a.name)).map(s=>({name:s.name,fileId:s.fileId,children:[],expanded:!1,type:"folder",inclusionState:s.inclusionState,reason:"",trackedFileCount:s.trackedFileCount}))}}var ba=h('<div><!> <!> <span class="name svelte-1skknri"> <span class="folderRoot svelte-1skknri"> </span></span> <!></div>'),Ma=h('<div class="source-folder svelte-1skknri"><!> <div role="button" tabindex="0" class="add-more svelte-1skknri"><!> Add more...</div></div>');qs(["keyup","click"]);const ka="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM5.86301%208.67273L9.44256%203.9L8.24256%203L5.12729%207.15368L3.17471%205.59162L2.23767%206.76292L4.79449%208.80838L5.86301%208.67273Z'%20fill='%23388A34'/%3e%3c/svg%3e",xa="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01119C3.65328%200.351896%204.81332%200%206%200C7.5907%200.00195419%209.11569%200.634726%2010.2405%201.75953C11.3653%202.88433%2011.998%204.40933%2012%206.00003C12%207.18673%2011.6481%208.34677%2010.9888%209.33347C10.3295%2010.3202%209.39246%2011.0892%208.2961%2011.5433C7.19975%2011.9975%205.99335%2012.1163%204.82946%2011.8848C3.66558%2011.6533%202.59648%2011.0818%201.75736%2010.2427C0.918247%209.40358%200.346802%208.33447%200.115291%207.17058C-0.11622%206.00669%200.00259969%204.80028%200.456726%203.70392C0.910851%202.60756%201.67989%201.67048%202.66658%201.01119ZM6.00007%207.07359L8.1213%209.19482L9.18196%208.13416L7.06073%206.01292L9.18198%203.89166L8.12132%202.83099L6.00007%204.95225L3.87866%202.83083L2.818%203.89149L4.93941%206.01292L2.81802%208.13432L3.87868%209.19499L6.00007%207.07359Z'%20fill='%23E51400'/%3e%3c/svg%3e",Aa="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM3.66667%205.83333C3.66667%205.99815%203.61779%206.15927%203.52623%206.29631C3.43466%206.43335%203.30451%206.54016%203.15224%206.60323C2.99997%206.66631%202.83241%206.68281%202.67076%206.65065C2.50911%206.6185%202.36062%206.53913%202.24408%206.42259C2.12753%206.30605%202.04817%206.15756%202.01601%205.99591C1.98386%205.83426%202.00036%205.6667%202.06343%205.51443C2.12651%205.36216%202.23332%205.23201%202.37036%205.14044C2.5074%205.04887%202.66852%205%202.83333%205C3.05435%205%203.26631%205.0878%203.42259%205.24408C3.57887%205.40036%203.66667%205.61232%203.66667%205.83333ZM6.83333%205.83333C6.83333%205.99815%206.78446%206.15927%206.69289%206.29631C6.60132%206.43335%206.47117%206.54016%206.3189%206.60323C6.16663%206.66631%205.99908%206.68281%205.83742%206.65065C5.67577%206.6185%205.52729%206.53913%205.41074%206.42259C5.2942%206.30605%205.21483%206.15756%205.18268%205.99591C5.15052%205.83426%205.16703%205.6667%205.2301%205.51443C5.29317%205.36216%205.39998%205.23201%205.53702%205.14044C5.67407%205.04887%205.83518%205%206%205C6.22101%205%206.43297%205.0878%206.58926%205.24408C6.74554%205.40036%206.83333%205.61232%206.83333%205.83333ZM9.85956%206.29631C9.95113%206.15927%2010%205.99815%2010%205.83333C10%205.61232%209.9122%205.40036%209.75592%205.24408C9.59964%205.0878%209.38768%205%209.16667%205C9.00185%205%208.84073%205.04887%208.70369%205.14044C8.56665%205.23201%208.45984%205.36216%208.39677%205.51443C8.33369%205.6667%208.31719%205.83426%208.34935%205.99591C8.3815%206.15756%208.46087%206.30605%208.57741%206.42259C8.69395%206.53913%208.84244%206.6185%209.00409%206.65065C9.16574%206.68281%209.3333%206.66631%209.48557%206.60323C9.63784%206.54016%209.76799%206.43335%209.85956%206.29631Z'%20fill='%23388A34'/%3e%3c/svg%3e";var za=h('<div class="children-container"></div>'),La=h('<div><div role="treeitem" aria-selected="false" tabindex="0"><!> <span class="name svelte-sympus"> </span> <!> <img/></div> <!></div>');function sr(i,e){Je(e,!0);const s=()=>{e.wsContextModel.toggleNode(e.data)},a={[pt.included]:ka,[pt.excluded]:xa,[pt.partial]:Aa},n={[pt.included]:"included",[pt.excluded]:"excluded",[pt.partial]:"partially included"};let c=it(()=>e.data.type===xt.folder&&e.data.inclusionState!==pt.excluded),u=it(()=>{return(p=e.data).type===xt.folder&&p.inclusionState!==pt.excluded?p.expanded?"chevron-down":"chevron-right":p.type===xt.folder?"folder":"file";var p}),b=it(()=>e.data.type===xt.folder&&e.data.expanded&&e.data.children&&e.data.children.length>0?e.data:null);var A=La(),m=o(A);let l;m.__click=s;var Y=it(()=>es("Enter",s));m.__keyup=function(...p){var g;(g=t(Y))==null||g.apply(this,p)};var P=o(m);Qs(P,{get icon(){return t(u)}});var ee=d(P,2),ie=o(ee),U=d(ee,2),z=p=>{se(p,{size:1,class:"file-count",children:(g,N)=>{var Z=I();oe(C=>ke(Z,C),[()=>e.data.trackedFileCount.toLocaleString()]),r(g,Z)},$$slots:{default:!0}})};V(U,p=>{e.data.type===xt.folder&&e.data.inclusionState!==pt.excluded&&typeof e.data.trackedFileCount=="number"&&p(z)});var L=d(U,2),D=d(m,2),j=p=>{var g=za();et(g,21,()=>t(b).children,N=>Mt.fileIdToString(N.fileId),(N,Z)=>{const C=it(()=>e.indentLevel+1);sr(N,{get data(){return t(Z)},get wsContextModel(){return e.wsContextModel},get indentLevel(){return t(C)}})}),r(p,g)};V(D,p=>{t(b)&&p(j)}),oe(p=>{l=ut(m,1,"tree-item svelte-sympus",null,l,p),St(m,"title",e.data.reason),St(m,"aria-expanded",e.data.type===xt.folder&&e.data.expanded),St(m,"aria-level",e.indentLevel),hr(m,`padding-left: ${10*e.indentLevel+20}px;`),ke(ie,e.data.name),St(L,"src",a[e.data.inclusionState]),St(L,"alt",n[e.data.inclusionState])},[()=>({"included-folder":t(c)})]),r(i,A),Be()}qs(["click","keyup"]);var Ra=h('<div class="files-container svelte-8hfqhl"></div>');function Ia(i,e){Je(e,!0);const[s,a]=gt();let n=it(()=>je(e.wsContextModel,"$wsContextModel",s).sourceTree);var c=Ra();et(c,21,()=>t(n),u=>Mt.fileIdToString(u.fileId),(u,b)=>{sr(u,{get wsContextModel(){return e.wsContextModel},get data(){return t(b)},indentLevel:0})}),r(i,c),Be(),a()}var _a=lt('<svg width="15" height="15" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="16" height="16" transform="matrix(-1 0 0 -1 16 16)" fill="currentColor" fill-opacity="0.01"></rect><path fill-rule="evenodd" clip-rule="evenodd" d="M13.7075 11.7333C13.7075 12.8236 12.8236 13.7075 11.7333 13.7075C10.643 13.7075 9.75909 12.8236 9.75909 11.7333C9.75909 10.643 10.643 9.75909 11.7333 9.75909C12.8236 9.75909 13.7075 10.643 13.7075 11.7333ZM11.7333 14.6675C13.3538 14.6675 14.6675 13.3538 14.6675 11.7333C14.6675 10.1128 13.3538 8.79909 11.7333 8.79909C10.1128 8.79909 8.79909 10.1128 8.79909 11.7333C8.79909 13.3538 10.1128 14.6675 11.7333 14.6675ZM9.79161 4.26647L13.3333 2.30721V6.22571L9.79161 4.26647ZM13.1852 7.24088C13.6829 7.51617 14.2933 7.15625 14.2933 6.58752V1.9454C14.2933 1.37665 13.6829 1.01676 13.1852 1.29207L8.98946 3.61313C8.47582 3.89729 8.47582 4.63564 8.98946 4.9198L13.1852 7.24088ZM7.14663 6.39988C7.14663 6.81225 6.81233 7.14654 6.39996 7.14654H2.1333C1.72093 7.14654 1.38664 6.81225 1.38664 6.39988V2.13324C1.38664 1.72087 1.72093 1.38657 2.1333 1.38657H6.39996C6.81233 1.38657 7.14663 1.72087 7.14663 2.13324V6.39988ZM6.18663 6.18654V2.34657H2.34664V6.18654H6.18663ZM1.66056 13.6606C1.47314 13.848 1.47314 14.152 1.66056 14.3394C1.84797 14.5269 2.15186 14.5269 2.33938 14.3394L4.26664 12.4121L6.19388 14.3394C6.38133 14.5268 6.68525 14.5268 6.8727 14.3394C7.06015 14.1519 7.06015 13.848 6.8727 13.6606L4.94546 11.7333L6.8727 9.80608C7.06015 9.61863 7.06015 9.31471 6.8727 9.12726C6.68525 8.9398 6.38133 8.9398 6.19388 9.12726L4.26664 11.0545L2.33938 9.12722C2.15186 8.93978 1.84797 8.93978 1.66056 9.12722C1.47314 9.31468 1.47314 9.61861 1.66056 9.80605L3.58781 11.7333L1.66056 13.6606Z" fill="currentColor"></path></svg>');function Na(i){var e=_a();r(i,e)}var Ea=h('<div class="icon-wrapper svelte-13uht7n"><!></div>'),Ta=h("<!> <!>",1),Fa=h('<div class="settings-card-body"><!></div>'),Pa=h('<div><div class="settings-card-content svelte-13uht7n"><div class="settings-card-left svelte-13uht7n"><!></div> <div class="settings-card-right svelte-13uht7n"><!></div></div> <!></div>');function ft(i,e){const s=$r(e),a=zt(e,["children","$$slots","$$events","$$legacy"]),n=zt(a,["class","icon","title","isClickable"]);Je(e,!1);const c=H(),u=H(),b=H();let A=S(e,"class",8,""),m=S(e,"icon",24,()=>{}),l=S(e,"title",24,()=>{}),Y=S(e,"isClickable",8,!1);me(()=>(t(c),t(u),x(n)),()=>{v(c,n.class),v(u,mr(n,["class"]))}),me(()=>(x(A()),t(c)),()=>{v(b,`settings-card ${A()} ${t(c)||""}`)}),nt();var P=Pa();Pt(P,N=>({role:"button",class:t(b),...t(u),[pr]:N}),[()=>({clickable:Y()})],"svelte-13uht7n");var ee=o(P),ie=o(ee),U=o(ie),z=N=>{var Z=Ta(),C=ge(Z),M=$=>{var _=Ea(),te=o(_);$s(te,m,(R,F)=>{F(R,{})}),r($,_)};V(C,$=>{m()&&$(M)});var G=d(C,2),y=$=>{se($,{color:"neutral",size:1,weight:"light",class:"card-title",children:(_,te)=>{var R=I();oe(()=>ke(R,l())),r(_,R)},$$slots:{default:!0}})};V(G,$=>{l()&&$(y)}),r(N,Z)},L=N=>{var Z=Xe(),C=ge(Z);dt(C,e,"header-left",{},null),r(N,Z)};V(U,N=>{m()||l()?N(z):N(L,!1)});var D=d(ie,2),j=o(D);dt(j,e,"header-right",{},null);var p=d(ee,2),g=N=>{var Z=Fa(),C=o(Z);dt(C,e,"default",{},null),r(N,Z)};V(p,N=>{k(()=>s.default)&&N(g)}),mt("click",P,function(N){yr.call(this,e,N)}),r(i,P),Be()}var Ua=h('<div class="context-list svelte-qsnirf"><div><!> <!></div> <div><div class="files-header svelte-qsnirf"><!> <!></div> <!></div></div>'),Oa=h('<div slot="header-right"><!></div>');function Va(i,e){Je(e,!1);const[s,a]=gt(),n=()=>je(u,"$wsContextModel",s),c=H();let u=new Mt(Ye,new Wr(Ye.postMessage)),b=H(),A=H();me(()=>n(),()=>{v(b,n().sourceFolders.sort((m,l)=>m.isWorkspaceFolder!==l.isWorkspaceFolder?m.isWorkspaceFolder?-1:1:m.fileId.folderRoot.localeCompare(l.fileId.folderRoot)))}),me(()=>n(),()=>{v(A,n().syncStatus)}),me(()=>t(b),()=>{v(c,t(b).reduce((m,l)=>m+(l.trackedFileCount??0),0))}),nt(),tt(),mt("message",Ss,function(...m){var l;(l=u.handleMessageFromExtension)==null||l.apply(this,m)}),ft(i,{get icon(){return Na},title:"Context",$$events:{contextmenu:m=>m.preventDefault()},children:(m,l)=>{var Y=Ua(),P=o(Y),ee=o(P);se(ee,{size:1,weight:"medium",class:"context-section-header",children:(D,j)=>{var p=I("SOURCE FOLDERS");r(D,p)},$$slots:{default:!0}}),function(D,j){Je(j,!0);let p=S(j,"folders",19,()=>[]);var g=Ma(),N=o(g);et(N,17,p,G=>Mt.fileIdToString(G.fileId),(G,y)=>{var $=ba();let _;var te=o($),R=K=>{const Re=it(()=>es("Enter",()=>j.onRemove(t(y).fileId.folderRoot)));Lt(K,{title:"Remove source folder from Augment context",onclick:()=>j.onRemove(t(y).fileId.folderRoot),get onkeyup(){return t(Re)},variant:"ghost",color:"neutral",size:1,class:"source-folder-v-adjust",children:(Ve,$e)=>{Jr(Ve)},$$slots:{default:!0}})};V(te,K=>{t(y).isWorkspaceFolder||K(R)});var F=d(te,2);const E=it(()=>(K=>K.isWorkspaceFolder?"root-folder":"folder")(t(y)));Qs(F,{class:"source-folder-v-adjust",get icon(){return t(E)}});var J=d(F,2),B=o(J),we=d(B),ue=o(we),ae=d(J,2),Le=K=>{se(K,{size:1,class:"file-count",children:(Re,Ve)=>{var $e=I();oe(f=>ke($e,f),[()=>t(y).trackedFileCount.toLocaleString()]),r(Re,$e)},$$slots:{default:!0}})};V(ae,K=>{t(y).trackedFileCount&&K(Le)}),oe(K=>{_=ut($,1,"item svelte-1skknri",null,_,K),ke(B,`${t(y).name??""} `),ke(ue,t(y).isPending?"(pending)":t(y).fileId.folderRoot)},[()=>({"workspace-folder":t(y).isWorkspaceFolder})]),r(G,$)});var Z=d(N,2),C=it(()=>es("Enter",j.onAddMore));Z.__keyup=function(...G){var y;(y=t(C))==null||y.apply(this,G)},Z.__click=function(...G){var y;(y=j.onAddMore)==null||y.apply(this,G)};var M=o(Z);Tt(M,{}),r(D,g),Be()}(d(ee,2),{get folders(){return t(b)},onRemove:D=>u.removeSourceFolder(D),onAddMore:()=>u.addMoreSourceFolders()});var ie=d(P,2),U=o(ie),z=o(U);se(z,{size:1,weight:"medium",class:"context-section-header",children:(D,j)=>{var p=I("FILES");r(D,p)},$$slots:{default:!0}});var L=d(z,2);se(L,{size:1,class:"file-count",children:(D,j)=>{var p=I();oe(g=>ke(p,g),[()=>(t(c),k(()=>t(c).toLocaleString()))],_e),r(D,p)},$$slots:{default:!0}}),Ia(d(U,2),{get wsContextModel(){return u}}),r(m,Y)},$$slots:{default:!0,"header-right":(m,l)=>{var Y=Oa(),P=o(Y),ee=ie=>{var U=it(()=>es("Enter",()=>u.requestRefresh()));Lt(ie,{title:"Refresh",variant:"ghost-block",color:"neutral",size:1,$$events:{click:()=>u.requestRefresh(),keyup(...z){var L;(L=t(U))==null||L.apply(this,z)}},children:(z,L)=>{Ir(z)},$$slots:{default:!0}})};V(P,ie=>{t(A),x(ps),k(()=>t(A)===ps.done)&&ie(ee)}),r(m,Y)}}}),Be(),a()}function ws(i){return function(e){switch(typeof e){case"object":return e!=null;case"function":return!0;default:return!1}}(i)&&"name"in i}function Fs(i){return ws(i)&&"component"in i}var Da=lt('<svg width="16" height="15" viewBox="0 0 16 15" xmlns="http://www.w3.org/2000/svg"><path d="M5.5 1.75V3H10.5V1.75C10.5 1.625 10.375 1.5 10.25 1.5H5.75C5.59375 1.5 5.5 1.625 5.5 1.75ZM4 3V1.75C4 0.8125 4.78125 0 5.75 0H10.25C11.1875 0 12 0.8125 12 1.75V3H14C15.0938 3 16 3.90625 16 5V8.75V13C16 14.125 15.0938 15 14 15H2C0.875 15 0 14.125 0 13V8.75V5C0 3.90625 0.875 3 2 3H4ZM1.5 9.5V13C1.5 13.2812 1.71875 13.5 2 13.5H14C14.25 13.5 14.5 13.2812 14.5 13V9.5H10V10C10 10.5625 9.53125 11 9 11H7C6.4375 11 6 10.5625 6 10V9.5H1.5ZM6 8H10H14.5V5C14.5 4.75 14.25 4.5 14 4.5H11.25H4.75H2C1.71875 4.5 1.5 4.75 1.5 5V8H6Z" fill="currentColor"></path></svg>');function Ps(i){var e=Da();r(i,e)}var qa=h('<div class="c-navigation__content-header svelte-z0ijuz"> </div>'),Ha=h('<div class="c-navigation__content-description svelte-z0ijuz"> </div>'),Ga=h('<!> <!> <div class="c-navigation__content-container svelte-z0ijuz"><!></div>',1),Ja=h('<div class="c-navigation__content svelte-z0ijuz"><!> <div><!></div></div>');function Us(i,e){Je(e,!1);let s=S(e,"item",8);tt();var a=Ja(),n=o(a);dt(n,e,"header",{},null);var c=d(n,2),u=o(c),b=A=>{var m=Ga(),l=ge(m);se(l,{size:4,weight:"medium",color:"neutral",children:(U,z)=>{var L=qa(),D=o(L);oe(()=>ke(D,(x(s()),k(()=>{var j;return(j=s())==null?void 0:j.name})))),r(U,L)},$$slots:{default:!0}});var Y=d(l,2),P=U=>{se(U,{color:"secondary",size:1,weight:"light",children:(z,L)=>{var D=Ha(),j=o(D);oe(()=>ke(j,(x(s()),k(()=>{var p;return(p=s())==null?void 0:p.description})))),r(z,D)},$$slots:{default:!0}})};V(Y,U=>{x(s()),k(()=>{var z;return(z=s())==null?void 0:z.description})&&U(P)});var ee=d(Y,2),ie=o(ee);dt(ie,e,"content",{get item(){return s()}},null),r(A,m)};V(u,A=>{s()!=null&&A(b)}),oe(()=>St(a,"id",(x(s()),k(()=>{var A;return(A=s())==null?void 0:A.id})))),r(i,a),Be()}function Ht(i,e,s,a,n,c){return n!==void 0?{name:i,description:e,icon:s,id:a,component:n,props:c}:{name:i,description:e,icon:s,id:a}}var Ba=h('<div class="c-navigation__head svelte-n5ccbo"><!> <!></div>'),ja=h('<span class="c-navigation__head-icon"><!></span> ',1),Za=h("<button><!></button>"),Wa=h('<div class="c-navigation__group"><!> <div class="c-navigation__items svelte-n5ccbo"></div></div>'),Ka=h('<nav class="c-navigation__nav svelte-n5ccbo" slot="left"><!></nav>'),Ya=h('<span class="c-navigation__head-icon"><!></span> <span> </span>',1),Qa=h("<span><!></span>"),Xa=h('<div class="c-navigation__head svelte-n5ccbo"><!></div> <!>',1),en=h('<div class="c-navigation__flat svelte-n5ccbo"><!> <!></div>'),tn=h("<div><!></div>");function sn(i,e){Je(e,!1);let s=S(e,"group",8,"Workspace Settings"),a=S(e,"items",24,()=>[]),n=S(e,"item",28,()=>{}),c=S(e,"mode",8,"tree"),u=S(e,"selectedId",28,()=>{}),b=S(e,"onNavigationChangeItem",8,U=>{}),A=S(e,"showButton",8,!0),m=S(e,"class",8,""),l=H(new Map);me(()=>(x(u()),x(n()),x(a())),()=>{var U;u()?n(a().find(z=>(z==null?void 0:z.id)===u())):u((U=n())==null?void 0:U.id)}),me(()=>(x(a()),x(s())),()=>{v(l,a().reduce((U,z)=>{if(!z)return U;const L=z.group??s(),D=U.get(L)??[];return D.push(z),U.set(L,D),U},new Map))}),me(()=>(x(n()),x(a())),()=>{n()||n(a()[0])}),me(()=>(x(b()),x(u())),()=>{b()(u())}),nt(),tt();var Y=tn(),P=o(Y),ee=U=>{Xr(U,{initialWidth:200,expandedMinWidth:150,columnLayoutThreshold:0,get showButton(){return A()},minimized:!1,$$slots:{left:(z,L)=>{var D=Ka(),j=o(D);Yr(j,u,p=>{var g=Xe(),N=ge(g);et(N,1,()=>t(l),bt,(Z,C)=>{var M=it(()=>As(t(C),2));let G=()=>t(M)[0];var y=Wa(),$=o(y);dt($,e,"group",{get label(){return G()},get mode(){return c()}},te=>{var R=Ba(),F=o(R);Ps(F);var E=d(F,2);se(E,{size:2,color:"primary",children:(J,B)=>{var we=I();oe(()=>ke(we,G())),r(J,we)},$$slots:{default:!0}}),r(te,R)});var _=d($,2);et(_,5,()=>t(M)[1],bt,(te,R)=>{var F=Za();let E;var J=o(F);se(J,{size:2,weight:"regular",color:"primary",children:(B,we)=>{var ue=ja(),ae=ge(ue),Le=o(ae);$s(Le,()=>t(R).icon,(Re,Ve)=>{Ve(Re,{})});var K=d(ae);oe(()=>ke(K,` ${t(R),k(()=>t(R).name)??""}`)),r(B,ue)},$$slots:{default:!0}}),oe(B=>E=ut(F,1,"c-navigation__item svelte-n5ccbo",null,E,B),[()=>({"is-active":t(R).id===u()})],_e),mt("click",F,()=>{return B=t(R),n(B),void u(B==null?void 0:B.id);var B}),r(te,F)}),r(Z,y)}),r(p,g)}),r(z,D)},right:(z,L)=>{Us(z,{get item(){return n()},slot:"right",$$slots:{header:(D,j)=>{var p=Xe(),g=ge(p);dt(g,e,"header",{get item(){return n()},get selectedId(){return u()}},null),r(D,p)},content:(D,j)=>{var p=Xe(),g=ge(p);dt(g,e,"content",{get item(){return n()},get isSelected(){return x(n()),x(u()),k(()=>{var N;return((N=n())==null?void 0:N.id)===u()})}},N=>{var Z=Xe(),C=ge(Z),M=G=>{var y=Xe(),$=ge(y);$s($,()=>n().component,(_,te)=>{te(_,Js(()=>n().props))}),r(G,y)};V(C,G=>{x(Fs),x(n()),x(c()),x(u()),k(()=>{return Fs(n())&&(y=n(),$=c(),_=u(),$!=="tree"||(y==null?void 0:y.id)===_);var y,$,_})&&G(M)}),r(N,Z)}),r(D,p)}}})}}})},ie=U=>{var z=en(),L=o(z);dt(L,e,"header",{get item(){return n()}},null);var D=d(L,2);et(D,1,()=>t(l),bt,(j,p)=>{var g=it(()=>As(t(p),2));let N=()=>t(g)[0];var Z=Xa(),C=ge(Z),M=o(C);dt(M,e,"group",{get label(){return N()},get mode(){return c()}},y=>{se(y,{color:"secondary",size:2,weight:"medium",children:($,_)=>{var te=Ya(),R=ge(te);Ps(o(R));var F=d(R,2),E=o(F);oe(()=>ke(E,N())),r($,te)},$$slots:{default:!0}})});var G=d(C,2);et(G,1,()=>t(g)[1],bt,(y,$)=>{var _=Qa();Us(o(_),{get item(){return t($)},$$slots:{content:(te,R)=>{var F=Xe(),E=ge(F);dt(E,e,"content",{get item(){return t($)}},null),r(te,F)}}}),Zr(_,(te,R)=>function(F,E){let J;function B({scrollTo:we,delay:ue,options:ae}){clearTimeout(J),we&&(J=setTimeout(()=>{F.scrollIntoView(ae)},ue))}return B(E),{update:B,destroy(){clearTimeout(J)}}}(te,R),()=>({scrollTo:c()==="flat"&&t($).id===u(),delay:300,options:{behavior:"smooth"}})),r(y,_)}),r(j,Z)}),r(U,z)};V(P,U=>{c()==="tree"?U(ee):U(ie,!1)}),oe(()=>ut(Y,1,`c-navigation c-navigation--mode__${c()??""} ${m()??""}`,"svelte-n5ccbo")),r(i,Y),Be()}var rn=lt('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M3.13281 0.886719L5.97656 3.07422C6.14062 3.21094 6.25 3.40234 6.25 3.59375V5.07031L9.23047 8.05078C10.0234 7.66797 11.0078 7.80469 11.6641 8.46094L14.7266 11.5234C15.082 11.8516 15.082 12.4258 14.7266 12.7539L12.9766 14.5039C12.6484 14.8594 12.0742 14.8594 11.7461 14.5039L8.68359 11.4414C8.02734 10.7852 7.89062 9.77344 8.30078 8.98047L5.32031 6H3.81641C3.625 6 3.43359 5.91797 3.29688 5.75391L1.10938 2.91016C0.917969 2.63672 0.945312 2.28125 1.19141 2.03516L2.28516 0.941406C2.50391 0.722656 2.88672 0.695312 3.13281 0.886719ZM1.62891 11.0586L5.375 7.3125L6.30469 8.24219L2.55859 11.9883C2.39453 12.1523 2.3125 12.3711 2.3125 12.5898C2.3125 13.0547 2.69531 13.4375 3.16016 13.4375C3.37891 13.4375 3.59766 13.3555 3.76172 13.1914L7.17969 9.77344C7.15234 10.293 7.26172 10.8125 7.50781 11.3047L4.69141 14.1211C4.28125 14.5312 3.73438 14.75 3.16016 14.75C1.95703 14.75 1 13.793 1 12.5898C1 12.0156 1.21875 11.4688 1.62891 11.0586ZM13.6602 5.23438L12.9766 5.94531C12.6484 6.27344 12.2109 6.46484 11.7461 6.46484H11.0625C10.0781 6.46484 9.3125 5.67188 9.3125 4.71484V4.00391C9.3125 3.53906 9.47656 3.10156 9.80469 2.77344L10.5156 2.08984C8.875 2.14453 7.5625 3.48438 7.5625 5.125V5.15234L7.125 4.71484V3.59375C7.125 3.32031 7.04297 3.04688 6.90625 2.82812C7.67188 1.59766 9.03906 0.75 10.625 0.75C11.2812 0.75 11.9375 0.914062 12.5117 1.1875C12.7578 1.32422 12.7852 1.65234 12.5938 1.84375L10.7344 3.70312C10.6523 3.78516 10.625 3.89453 10.625 4.00391V4.6875C10.625 4.93359 10.8164 5.125 11.0625 5.125L11.7461 5.15234C11.8555 5.15234 11.9648 5.09766 12.0469 5.01562L13.9062 3.15625C14.0977 2.96484 14.4258 2.99219 14.5625 3.23828C14.8359 3.8125 15 4.46875 15 5.15234C15 6.60156 14.2617 7.91406 13.1406 8.70703L12.293 7.83203C12.2656 7.80469 12.2383 7.77734 12.2109 7.75C13.0586 7.23047 13.6328 6.30078 13.6602 5.23438Z" fill="currentColor"></path></svg>');function an(i){var e=rn();r(i,e)}var nn=lt('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M6.25 10.8125H12.375C12.5938 10.8125 12.8125 10.6211 12.8125 10.375V4.25H11.5C11.0078 4.25 10.625 3.86719 10.625 3.375V2.0625H6.25C6.00391 2.0625 5.8125 2.28125 5.8125 2.5V10.375C5.8125 10.6211 6.00391 10.8125 6.25 10.8125ZM12.375 12.125H6.25C5.26562 12.125 4.5 11.3594 4.5 10.375V2.5C4.5 1.54297 5.26562 0.75 6.25 0.75H10.7617C11.2266 0.75 11.6641 0.941406 11.9922 1.26953L13.6055 2.88281C13.9336 3.21094 14.125 3.64844 14.125 4.11328V10.375C14.125 11.3594 13.332 12.125 12.375 12.125ZM2.53125 3.375C2.88672 3.375 3.1875 3.67578 3.1875 4.03125V11.0312C3.1875 12.3711 4.25391 13.4375 5.59375 13.4375H10.8438C11.1992 13.4375 11.5 13.7383 11.5 14.0938C11.5 14.4766 11.1992 14.75 10.8438 14.75H5.59375C3.51562 14.75 1.875 13.1094 1.875 11.0312V4.03125C1.875 3.67578 2.14844 3.375 2.53125 3.375Z" fill="currentColor"></path></svg>');function on(i){var e=nn();r(i,e)}var ln=lt("<svg><!></svg>");function Os(i,e){const s=zt(e,["children","$$slots","$$events","$$legacy"]);var a=ln();Pt(a,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 17 16",...s}));var n=o(a);Zt(n,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M3.552 7.158a.57.57 0 0 1 .804 0l.702.702L6.23 6.688c.2-.2.511-.22.734-.06l.07.06a.57.57 0 0 1 0 .804L5.862 8.664l1.626 1.626 1.173-1.172c.2-.2.511-.22.733-.06l.071.06a.57.57 0 0 1 0 .804l-1.173 1.172.703.703c.2.2.22.51.06.733l-.06.07a.57.57 0 0 1-.804 0l-.041-.039-.812.813a3.226 3.226 0 0 1-4.043.421l-.08-.054-.959.96c-.2.2-.511.22-.733.06l-.071-.06a.57.57 0 0 1 0-.804l.96-.96-.054-.079a3.226 3.226 0 0 1 .294-3.91l.127-.133.811-.813-.038-.04a.57.57 0 0 1-.06-.734zm3.759-3.759a.57.57 0 0 1 .804 0l.038.04.815-.813a3.226 3.226 0 0 1 4.043-.421l.078.054.96-.96c.2-.2.511-.22.734-.06l.07.06a.57.57 0 0 1 0 .804l-.96.96.055.079a3.226 3.226 0 0 1-.295 3.91l-.126.133-.814.813.04.04c.201.2.221.511.06.734l-.06.07a.57.57 0 0 1-.804 0L7.31 4.204a.57.57 0 0 1 0-.805m2.39-.04-.884.884 3.093 3.093.884-.884A2.186 2.186 0 1 0 9.7 3.359M4.396 8.664l-.884.884a2.186 2.186 0 1 0 3.092 3.093l.884-.884z"/>',!0),r(i,a)}var cn=h('<div class="connect-button-spinner svelte-js5lik"><!></div> <span>Cancel</span>',1),dn=h("<span>Connect</span>"),un=h('<div class="connect-button-content svelte-js5lik"><!></div>'),vn=h('<div class="status-controls svelte-js5lik"><div class="icon-container svelte-js5lik"><div class="connection-status svelte-js5lik"><div><!></div></div> <!></div></div>'),gn=h('<div slot="header-right"><!></div>'),hn=h("<div> </div>"),pn=h('<div class="config-wrapper" role="group" aria-label="Connection status controls"><!> <!></div>'),mn=h('<div class="loading-container svelte-2bsejd"><!> <!></div>'),fn=h('<div class="category-content"><!></div>'),Cn=h('<div class="category"><div class="category-heading"><!></div> <!></div>');function rr(i,e){let s=S(e,"title",8),a=S(e,"loading",8,!1);var n=Cn(),c=o(n),u=o(c);se(u,{size:1,color:"secondary",weight:"regular",children:(l,Y)=>{var P=I();oe(()=>ke(P,s())),r(l,P)},$$slots:{default:!0}});var b=d(c,2),A=l=>{var Y=mn(),P=o(Y);rs(P,{size:1});var ee=d(P,2);se(ee,{size:1,color:"secondary",children:(ie,U)=>{var z=I("Loading...");r(ie,z)},$$slots:{default:!0}}),r(l,Y)},m=l=>{var Y=fn(),P=o(Y);dt(P,e,"default",{},null),r(l,Y)};V(b,l=>{a()?l(A):l(m,!1)}),r(i,n)}const ar="mcpServerModel";function ys(){const i=Et(ar);if(!i)throw new Error("MCPServerModel context not found. Make sure setMCPServerModelContext() was called in a parent component.");return i}var $n=h('<div class="connect-button-spinner svelte-e3a21z"><!></div> <span>Cancel</span>',1),wn=h("<span>Connect</span>"),yn=h('<div class="connect-button-content svelte-e3a21z"><!></div>'),Sn=h('<div class="status-controls svelte-e3a21z"><div><!></div> <!></div>'),bn=h('<div slot="header-right"><!></div>'),Mn=h("<div> </div>"),kn=h('<div class="config-wrapper" role="group" aria-label="Connection status controls"><!> <!></div>'),xn=h('<div class="tool-category-list svelte-on3wl5"><!> <!></div>'),An=h("<div><!></div>");function zn(i,e){Je(e,!0);const[s,a]=gt(),n=()=>je(m,"$pretendNativeToolDefs",s);let c=S(e,"tools",19,()=>[]),u=S(e,"onToolApprovalConfigChange",3,()=>{});const b=Et(Ft.key),A=ys(),m=b.getPretendNativeToolDefs(),l=A.getServers();let Y=it(()=>b.getEnableNativeRemoteMcp()?Bs(je(l,"$allServers",s)):[]);var P=An(),ee=o(P);const ie=it(()=>c().length===0);rr(ee,{get title(){return e.title},get loading(){return t(ie)},children:(U,z)=>{var L=xn(),D=o(L);et(D,17,c,p=>p.name,(p,g)=>{(function(N,Z){Je(Z,!1);let C=S(Z,"config",8),M=S(Z,"onAuthenticate",8),G=S(Z,"onRevokeAccess",8);const y=()=>{};let $=H(!1),_=H(null),te=H(!1),R=H();function F(){if(t($))v($,!1),t(_)&&(clearTimeout(t(_)),v(_,null));else{v($,!0);const ue=C().authUrl||"";M()({authUrl:ue,toolName:C().displayName.toLowerCase()}),v(_,setTimeout(()=>{v($,!1),v(_,null)},6e4))}}me(()=>x(C()),()=>{v(R,C().name.toLowerCase().replace(/[^a-z0-9]/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,""))}),me(()=>(x(C()),t($),t(_)),()=>{C().isConfigured&&t($)&&(v($,!1),t(_)&&(clearTimeout(t(_)),v(_,null)))}),nt(),tt();var E=pn(),J=o(E);ft(J,{get icon(){return x(C()),k(()=>C().icon)},get title(){return x(C()),k(()=>C().displayName)},$$slots:{"header-right":(ue,ae)=>{var Le=gn(),K=o(Le),Re=$e=>{const f=_e(()=>t($)?"neutral":"accent");Ge($e,{variant:"ghost-block",get color(){return t(f)},size:1,$$events:{click:F},children:(w,X)=>{var q=un(),ce=o(q),W=xe=>{var T=cn(),Q=ge(T),O=o(Q);rs(O,{size:1,useCurrentColor:!0}),r(xe,T)},ye=xe=>{var T=dn();r(xe,T)};V(ce,xe=>{t($)?xe(W):xe(ye,!1)}),r(w,q)},$$slots:{default:!0}})},Ve=($e,f)=>{var w=X=>{var q=vn(),ce=o(q),W=o(ce),ye=o(W);let xe;var T=o(ye);const Q=_e(()=>(x(Nt),k(()=>[Nt.Hover])));$t(T,{get triggerOn(){return t(Q)},content:"Revoke Access",children:(le,ne)=>{Lt(le,{color:"neutral",variant:"ghost",size:1,$$events:{click:()=>G()(C())},children:(ve,fe)=>{Os(ve,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var O=d(W,2);ms.Root(O,{color:"success",size:1,variant:"soft",children:(le,ne)=>{var ve=I("Connected");r(le,ve)},$$slots:{default:!0}}),oe(le=>xe=ut(ye,1,"icon-button-wrapper svelte-js5lik",null,xe,le),[()=>({active:t(te)})],_e),r(X,q)};V($e,X=>{x(C()),k(()=>C().isConfigured)&&X(w)},f)};V(K,$e=>{x(C()),k(()=>!C().isConfigured&&C().authUrl)?$e(Re):$e(Ve,!1)}),r(ue,Le)}}});var B=d(J,2),we=ue=>{var ae=hn(),Le=o(ae);oe(()=>{ut(ae,1,`status-message ${x(C()),k(()=>C().statusType)??""}`,"svelte-js5lik"),ke(Le,(x(C()),k(()=>C().statusMessage)))}),r(ue,ae)};V(B,ue=>{x(C()),k(()=>C().showStatus)&&ue(we)}),oe(()=>St(E,"id",t(R))),mt("mouseenter",E,()=>v(te,!0)),mt("mouseleave",E,()=>v(te,!1)),r(N,E),er(Z,"onToolApprovalConfigChange",y),Be({onToolApprovalConfigChange:y})})(p,{get config(){return t(g)},get onAuthenticate(){return e.onAuthenticate},get onRevokeAccess(){return e.onRevokeAccess},onToolApprovalConfigChange:u()})});var j=d(D,2);et(j,1,n,p=>p.name,(p,g)=>{const N=it(()=>t(Y).find(Z=>Z.name===t(g).name));(function(Z,C){Je(C,!1);let M=S(C,"config",12),G=S(C,"mcpTool",8);const y=ys(),$=Ws();async function _(){if(t(R))return E&&(clearTimeout(E),E=null),void v(R,!1);$.startRemoteMCPAuth(M().name),v(R,!0);const ae=new Promise(Le=>{E=setTimeout(()=>{Le(),E=null},6e4)});await Promise.race([ae]),v(R,!1)}async function te(){await $.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${M().displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&(G()&&y.deleteServer(G()),v(R,!1))}let R=H(!1),F=H(!1),E=null;me(()=>(x(M()),_s),()=>{M(_s(M()))}),me(()=>x(G()),()=>{M(M().isConfigured=!!G(),!0)}),nt(),tt();var J=kn(),B=o(J);ft(B,{get icon(){return x(M()),k(()=>M().icon)},get title(){return x(M()),k(()=>M().displayName)},$$slots:{"header-right":(ae,Le)=>{var K=bn(),Re=o(K),Ve=f=>{const w=_e(()=>t(R)?"neutral":"accent");Ge(f,{variant:"ghost-block",get color(){return t(w)},size:1,$$events:{click:_},children:(X,q)=>{var ce=yn(),W=o(ce),ye=T=>{var Q=$n(),O=ge(Q),le=o(O);rs(le,{size:1,useCurrentColor:!0}),r(T,Q)},xe=T=>{var Q=wn();r(T,Q)};V(W,T=>{t(R)?T(ye):T(xe,!1)}),r(X,ce)},$$slots:{default:!0}})},$e=(f,w)=>{var X=q=>{var ce=Sn(),W=o(ce);let ye;var xe=o(W);const T=_e(()=>(x(Nt),k(()=>[Nt.Hover])));$t(xe,{get triggerOn(){return t(T)},content:"Revoke Access",children:(O,le)=>{Lt(O,{color:"neutral",variant:"ghost",size:1,$$events:{click:te},children:(ne,ve)=>{Os(ne,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var Q=d(W,2);ms.Root(Q,{color:"success",size:1,variant:"soft",children:(O,le)=>{var ne=I("Connected");r(O,ne)},$$slots:{default:!0}}),oe(O=>ye=ut(W,1,"disconnect-button svelte-e3a21z",null,ye,O),[()=>({active:t(F)})],_e),r(q,ce)};V(f,q=>{x(M()),k(()=>M().isConfigured)&&q(X)},w)};V(Re,f=>{x(M()),k(()=>!M().isConfigured)?f(Ve):f($e,!1)}),r(ae,K)}}});var we=d(B,2),ue=ae=>{var Le=Mn(),K=o(Le);oe(()=>{ut(Le,1,`status-message ${x(M()),k(()=>M().statusType)??""}`,"svelte-e3a21z"),ke(K,(x(M()),k(()=>M().statusMessage)))}),r(ae,Le)};V(we,ae=>{x(M()),k(()=>M().showStatus)&&ae(ue)}),mt("mouseenter",J,()=>v(F,!0)),mt("mouseleave",J,()=>v(F,!1)),r(Z,J),Be()})(p,{get mcpTool(){return t(N)},get config(){return t(g)}})}),r(U,L)},$$slots:{default:!0}}),r(i,P),Be(),a()}var Ln=h('<tr class="env-var-row svelte-1mazg1z"><td class="name-cell svelte-1mazg1z"><!></td><td class="value-cell svelte-1mazg1z"><!></td><td class="action-cell svelte-1mazg1z"><!></td></tr>'),Rn=h('<!> <table class="env-vars-table svelte-1mazg1z"><tbody><!></tbody></table> <div class="new-var-button-container svelte-1mazg1z"><!></div>',1);function In(i,e){Je(e,!1);let s=S(e,"handleEnterEditMode",8),a=S(e,"envVarEntries",28,()=>[]);tt();var n=Rn(),c=ge(n);se(c,{size:1,weight:"medium",children:(P,ee)=>{var ie=I("Environment Variables");r(P,ie)},$$slots:{default:!0}});var u=d(c,2),b=o(u),A=o(b),m=P=>{var ee=Xe(),ie=ge(ee);et(ie,1,a,U=>U.id,(U,z,L)=>{var D=Ln(),j=o(D),p=o(j);At(p,{size:1,placeholder:"Name",class:"full-width",get value(){return t(z).key},set value(M){t(z).key=M,zs(()=>a())},$$events:{focus(...M){var G;(G=s())==null||G.apply(this,M)},change:()=>function(M,G){const y=a().findIndex($=>$.id===M);y!==-1&&(a(a()[y].key=G,!0),a(a()))}(t(z).id,t(z).key)},$$legacy:!0});var g=d(j),N=o(g);At(N,{size:1,placeholder:"Value",class:"full-width",get value(){return t(z).value},set value(M){t(z).value=M,zs(()=>a())},$$events:{focus(...M){var G;(G=s())==null||G.apply(this,M)},change:()=>function(M,G){const y=a().findIndex($=>$.id===M);y!==-1&&(a(a()[y].value=G,!0),a(a()))}(t(z).id,t(z).value)},$$legacy:!0});var Z=d(g),C=o(Z);$t(C,{content:"Remove",children:(M,G)=>{Ge(M,{variant:"ghost",color:"neutral",type:"button",size:1,$$events:{focus(...y){var $;($=s())==null||$.apply(this,y)},click:()=>{return y=t(z).id,s()(),void a(a().filter($=>$.id!==y));var y}},$$slots:{iconLeft:(y,$)=>{Ks(y,{slot:"iconLeft"})}}})},$$slots:{default:!0}}),r(U,D)}),r(P,ee)};V(A,P=>{x(a()),k(()=>a().length>0)&&P(m)});var l=d(u,2),Y=o(l);Ge(Y,{size:1,variant:"soft",color:"neutral",type:"button",$$events:{click:function(){s()(),a([...a(),{id:crypto.randomUUID(),key:"",value:""}])}},children:(P,ee)=>{var ie=I("Variable");r(P,ie)},$$slots:{default:!0,iconLeft:(P,ee)=>{Tt(P,{slot:"iconLeft"})}}}),r(i,n),Be()}var _n=h("<div></div>"),Nn=h(" <!>",1),En=h('<div class="server-name svelte-1koxb3z"><!></div>'),Tn=h('<div slot="header-left" class="l-header svelte-1koxb3z"><!> <!> <!> <div class="command-text svelte-1koxb3z"><!></div></div>'),Fn=h('<div class="status-controls-button svelte-1koxb3z"><!> <!></div>'),Pn=h('<div class="status-controls-button svelte-1koxb3z"><!> <!></div>'),Un=h('<div class="status-controls-button svelte-1koxb3z"><!> <!></div>'),On=h("<!> <!> <!>",1),Vn=h("<!> <!>",1),Dn=h('<div class="server-actions svelte-1koxb3z" slot="header-right"><!> <div class="status-controls svelte-1koxb3z"><!> <!></div></div>'),qn=h('<div class="c-tool-item svelte-1koxb3z"><div class="c-tool-info svelte-1koxb3z"><div class="tool-status svelte-1koxb3z"><div></div> <!></div> <div class="c-tool-description svelte-1koxb3z"><!></div></div></div>'),Hn=h('<div slot="footer"></div>'),Gn=h('<div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div> <div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div>',1),Jn=h('<div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!> <div class="connection-type-buttons svelte-1koxb3z"><!> <!></div></div></div>'),Bn=h('<div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div>'),jn=h('<div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div>'),Zn=h('<!> <div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div> <!>',1),Wn=h('<form><div class="server-edit-form svelte-1koxb3z"><div class="server-header svelte-1koxb3z"><div class="server-title svelte-1koxb3z"><div class="server-icon svelte-1koxb3z"><!></div> <!></div></div> <!> <!> <div class="form-actions-row svelte-1koxb3z"><div><!></div> <div class="form-actions svelte-1koxb3z"><!> <!></div></div></div></form>');function Vs(i,e){var $e;Je(e,!1);const s=H(),a=H(),n=H(),c=H(),u=H(),b=H(),A=H(),m=H();let l=S(e,"server",8,null),Y=S(e,"onDelete",8),P=S(e,"onAdd",8),ee=S(e,"onSave",8),ie=S(e,"onEdit",8),U=S(e,"onToggleDisableServer",8),z=S(e,"onJSONImport",8),L=S(e,"onCancel",8),D=S(e,"onAuthenticate",24,()=>{}),j=S(e,"disabledText",24,()=>{}),p=S(e,"warningText",24,()=>{}),g=S(e,"mode",12,"view"),N=S(e,"mcpServerError",12,""),Z=H(0),C=H((($e=l())==null?void 0:$e.name)??""),M=H(ct(l())?"":_t(l())?l().command:""),G=H(ct(l())?l().url:""),y=_t(l())?l().env??{}:{},$=H(""),_=H(ct(l())?l().type:"http"),te=H([]);F();let R=H(!0);function F(){v(te,Object.entries(y).map(([f,w])=>({id:crypto.randomUUID(),key:f,value:w})))}let E=H(()=>{});function J(){l()&&g()==="view"&&(g("edit"),ie()(l()),t(E)())}let B=S(e,"busy",12,!1);function we({key:f,value:w}){return f.trim()&&w.trim()}async function ue(){N(""),B(!0);const f=t(te).filter(we);y=Object.fromEntries(f.map(({key:w,value:X})=>[w.trim(),X.trim()])),F();try{if(g()==="add"){const w={type:"stdio",name:t(C).trim(),command:t(M).trim(),arguments:"",useShellInterpolation:!0,env:Object.keys(y).length>0?y:void 0};await P()(w)}else if(g()==="addRemote"){const w={type:t(_),name:t(C).trim(),url:t(G).trim()};await P()(w)}else if(g()==="addJson"){try{JSON.parse(t($))}catch(w){const X=w instanceof Error?w.message:String(w);throw new at(`Invalid JSON format: ${X}`)}await z()(t($))}else if(g()==="edit"&&l()){if(ct(l())){const w={...l(),type:t(_),name:t(C).trim(),url:t(G).trim()};await ee()(w)}else if(_t(l())){const w={...l(),name:t(C).trim(),command:t(M).trim(),arguments:"",env:Object.keys(y).length>0?y:void 0};await ee()(w)}}}catch(w){N(w instanceof at?w.message:"Failed to save server"),console.warn(w)}finally{B(!1)}}function ae(){var f,w;B(!1),N(""),(f=L())==null||f(),v($,""),v(C,((w=l())==null?void 0:w.name)??""),v(M,ct(l())?"":_t(l())?l().command:""),v(G,ct(l())?l().url:""),y=_t(l())&&l().env?{...l().env}:{},v(_,ct(l())?l().type:"http"),F()}me(()=>x(l()),()=>{var f;v(s,((f=l())==null?void 0:f.tools)??[])}),me(()=>x(l()),()=>{v(a,ct(l()))}),me(()=>t(Z),()=>{v(n,Date.now()-t(Z)<3e3)}),me(()=>(x(l()),t(a),t(s),t(n)),()=>{var f;v(c,!((f=l())!=null&&f.disabled)&&t(a)&&l().authRequired===!0&&t(s).length===0&&!t(n))}),me(()=>(t(C),t(M)),()=>{t(C)&&t(M)&&N("")}),me(()=>(x(g()),t(C),t(M),t(G)),()=>{v(u,!((g()!=="add"||t(C).trim()&&t(M).trim())&&(g()!=="addRemote"||t(C).trim()&&t(G).trim())))}),me(()=>(x(g()),t($)),()=>{v(b,g()==="addJson"&&!t($).trim())}),me(()=>(t(u),x(g()),t(b)),()=>{v(A,t(u)||g()==="view"||t(b))}),me(()=>x(g()),()=>{v(m,(()=>{switch(g()){case"add":return"New MCP Server";case"addRemote":return"New Remote MCP Server";case"addJson":return"Import MCP Server";default:return"Edit MCP Server"}})())}),nt(),tt();var Le=Xe();oe(()=>{console.log({server:k(()=>Ts(l())),serverTools:k(()=>Ts(t(s)))})});var K=ge(Le),Re=f=>{Xs(f,{get collapsed(){return t(R)},set collapsed(w){v(R,w)},$$slots:{header:(w,X)=>{ft(w,{slot:"header",$$slots:{"header-left":(q,ce)=>{var W=Tn(),ye=o(W),xe=fe=>{Lt(fe,{size:1,variant:"ghost",$$events:{click:()=>v(R,!t(R))},children:(Ce,Se)=>{var be=Xe(),Ne=ge(be),he=re=>{Vr(re,{})},de=re=>{Ms(re,{})};V(Ne,re=>{t(R)?re(he):re(de,!1)}),r(Ce,be)},$$slots:{default:!0}})};V(ye,fe=>{t(s),k(()=>t(s).length>0)&&fe(xe)});var T=d(ye,2);const Q=_e(()=>j()||(t(c)?"Authentication required":p()));$t(T,{get content(){return t(Q)},children:(fe,Ce)=>{var Se=_n();let be;oe(Ne=>be=ut(Se,1,"c-dot svelte-1koxb3z",null,be,Ne),[()=>({"c-green":!j()&&!t(c),"c-warning":t(c)||!j()&&!!p(),"c-red":!!j()&&!t(c),"c-disabled":l().disabled})],_e),r(fe,Se)},$$slots:{default:!0}});var O=d(T,2);$t(O,{get content(){return x(l()),k(()=>l().name)},side:"top",align:"start",children:(fe,Ce)=>{var Se=En(),be=o(Se);se(be,{size:1,weight:"medium",children:(Ne,he)=>{var de=Nn(),re=ge(de),pe=d(re),Me=Ee=>{var Ae=I();oe(()=>ke(Ae,`(${t(s),k(()=>t(s).length)??""}) tools`)),r(Ee,Ae)};V(pe,Ee=>{t(s),k(()=>t(s).length>0)&&Ee(Me)}),oe(()=>ke(re,`${x(l()),k(()=>l().name)??""} `)),r(Ne,de)},$$slots:{default:!0}}),r(fe,Se)},$$slots:{default:!0}});var le=d(O,2),ne=o(le);const ve=_e(()=>(x(Xt),x(l()),k(()=>Xt(l()))));$t(ne,{get content(){return t(ve)},side:"top",align:"start",children:(fe,Ce)=>{se(fe,{color:"secondary",size:1,weight:"regular",children:(Se,be)=>{var Ne=I();oe(he=>ke(Ne,he),[()=>(x(Xt),x(l()),k(()=>Xt(l())))],_e),r(Se,Ne)},$$slots:{default:!0}})},$$slots:{default:!0}}),r(q,W)},"header-right":(q,ce)=>{var W=Dn(),ye=o(W),xe=ne=>{Ge(ne,{size:1,variant:"surface",color:"warning",$$events:{click:()=>{var ve;(ve=D())==null||ve(l())}},children:(ve,fe)=>{var Ce=I("Authenticate");r(ve,Ce)},$$slots:{default:!0}})};V(ye,ne=>{t(c)&&ne(xe)});var T=d(ye,2),Q=o(T),O=ne=>{const ve=_e(()=>(x(l()),k(()=>!l().disabled)));fs(ne,{size:1,get checked(){return t(ve)},onchange:()=>{l()&&(v(Z,Date.now()),U()(l().id)),t(E)()}})};V(Q,ne=>{x(Is),k(Is)&&ne(O)});var le=d(Q,2);Ze.Root(le,{get requestClose(){return t(E)},set requestClose(ne){v(E,ne)},children:(ne,ve)=>{var fe=Vn(),Ce=ge(fe);Ze.Trigger(Ce,{children:(be,Ne)=>{Lt(be,{size:1,variant:"ghost-block",color:"neutral",children:(he,de)=>{ta(he,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var Se=d(Ce,2);Ze.Content(Se,{side:"bottom",align:"end",children:(be,Ne)=>{var he=On(),de=ge(he);Ze.Item(de,{onSelect:J,children:(Me,Ee)=>{var Ae=Fn(),ze=o(Ae);sa(ze,{});var Oe=d(ze,2);se(Oe,{size:1,weight:"medium",children:(He,st)=>{var Ke=I("Edit");r(He,Ke)},$$slots:{default:!0}}),r(Me,Ae)},$$slots:{default:!0}});var re=d(de,2);Ze.Item(re,{onSelect:()=>{(function(){if(l()){const Me=Wt.convertServerToJSON(l());navigator.clipboard.writeText(Me)}})(),t(E)()},children:(Me,Ee)=>{var Ae=Pn(),ze=o(Ae);ra(ze,{});var Oe=d(ze,2);se(Oe,{size:1,weight:"medium",children:(He,st)=>{var Ke=I("Copy JSON");r(He,Ke)},$$slots:{default:!0}}),r(Me,Ae)},$$slots:{default:!0}});var pe=d(re,2);Ze.Item(pe,{color:"error",onSelect:()=>{Y()(l()),t(E)()},children:(Me,Ee)=>{var Ae=Un(),ze=o(Ae);Ks(ze,{});var Oe=d(ze,2);se(Oe,{size:1,weight:"medium",children:(He,st)=>{var Ke=I("Delete");r(He,Ke)},$$slots:{default:!0}}),r(Me,Ae)},$$slots:{default:!0}}),r(be,he)},$$slots:{default:!0}}),r(ne,fe)},$$slots:{default:!0},$$legacy:!0}),r(q,W)}}})},footer:(w,X)=>{var q=Hn();et(q,5,()=>t(s),bt,(ce,W)=>{var ye=qn(),xe=o(ye),T=o(xe),Q=o(T);let O;var le=d(Q,2);se(le,{size:1,weight:"medium",children:(fe,Ce)=>{var Se=I();oe(()=>ke(Se,(t(W),k(()=>t(W).definition.mcp_tool_name||t(W).definition.name)))),r(fe,Se)},$$slots:{default:!0}});var ne=d(T,2),ve=o(ne);$t(ve,{get content(){return t(W),k(()=>t(W).definition.description)},align:"start",children:(fe,Ce)=>{var Se=Xe(),be=ge(Se),Ne=he=>{se(he,{size:1,color:"secondary",children:(de,re)=>{var pe=I();oe(()=>ke(pe,(t(W),k(()=>t(W).definition.description)))),r(de,pe)},$$slots:{default:!0}})};V(be,he=>{t(W),k(()=>t(W).definition.description)&&he(Ne)}),r(fe,Se)},$$slots:{default:!0}}),oe(fe=>O=ut(Q,1,"tool-status-dot svelte-1koxb3z",null,O,fe),[()=>({enabled:t(W).enabled,disabled:!t(W).enabled})],_e),r(ce,ye)}),r(w,q)}},$$legacy:!0})},Ve=f=>{var w=Wn(),X=o(w),q=o(X),ce=o(q),W=o(ce),ye=o(W);_r(ye);var xe=d(W,2);se(xe,{color:"secondary",size:1,weight:"medium",children:(de,re)=>{var pe=I();oe(()=>ke(pe,t(m))),r(de,pe)},$$slots:{default:!0}});var T=d(q,2),Q=de=>{var re=Gn(),pe=ge(re),Me=o(pe),Ee=o(Me);se(Ee,{size:1,weight:"medium",children:(He,st)=>{var Ke=I("Code Snippet");r(He,Ke)},$$slots:{default:!0}});var Ae=d(pe,2),ze=o(Ae),Oe=o(ze);bs(Oe,{size:1,placeholder:"Paste JSON here...",get value(){return t($)},set value(He){v($,He)},$$legacy:!0}),r(de,re)},O=(de,re)=>{var pe=Me=>{var Ee=Zn(),Ae=ge(Ee),ze=Te=>{var Pe=Jn(),We=o(Pe),vt=o(We);se(vt,{size:1,weight:"medium",children:(Vt,ks)=>{var Dt=I("Connection Type");r(Vt,Dt)},$$slots:{default:!0}});var ot=d(vt,2),Rt=o(ot);const Ut=_e(()=>t(_)==="http"?"solid":"ghost"),as=_e(()=>t(_)==="http"?"accent":"neutral");Ge(Rt,{size:1,get variant(){return t(Ut)},get color(){return t(as)},type:"button",$$events:{click:()=>v(_,"http")},children:(Vt,ks)=>{var Dt=I("HTTP");r(Vt,Dt)},$$slots:{default:!0}});var Ot=d(Rt,2);const or=_e(()=>t(_)==="sse"?"solid":"ghost"),ir=_e(()=>t(_)==="sse"?"accent":"neutral");Ge(Ot,{size:1,get variant(){return t(or)},get color(){return t(ir)},type:"button",$$events:{click:()=>v(_,"sse")},children:(Vt,ks)=>{var Dt=I("SSE");r(Vt,Dt)},$$slots:{default:!0}}),r(Te,Pe)};V(Ae,Te=>{x(g()),x(l()),k(()=>{var Pe,We;return g()==="addRemote"||g()==="edit"&&(((Pe=l())==null?void 0:Pe.type)==="http"||((We=l())==null?void 0:We.type)==="sse")})&&Te(ze)});var Oe=d(Ae,2),He=o(Oe),st=o(He);At(st,{size:1,placeholder:"Enter a name for your MCP server (e.g., 'Server Memory')",get value(){return t(C)},set value(Te){v(C,Te)},$$events:{focus:J},$$slots:{label:(Te,Pe)=>{se(Te,{slot:"label",size:1,weight:"medium",children:(We,vt)=>{var ot=I("Name");r(We,ot)},$$slots:{default:!0}})}},$$legacy:!0});var Ke=d(Oe,2),ht=Te=>{var Pe=Bn(),We=o(Pe),vt=o(We);At(vt,{size:1,placeholder:"Enter the URL (e.g., 'https://api.example.com/mcp')",get value(){return t(G)},set value(ot){v(G,ot)},$$events:{focus:J},$$slots:{label:(ot,Rt)=>{se(ot,{slot:"label",size:1,weight:"medium",children:(Ut,as)=>{var Ot=I("URL");r(Ut,Ot)},$$slots:{default:!0}})}},$$legacy:!0}),r(Te,Pe)},Fe=Te=>{var Pe=jn(),We=o(Pe),vt=o(We);At(vt,{size:1,placeholder:"Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')",get value(){return t(M)},set value(ot){v(M,ot)},$$events:{focus:J},$$slots:{label:(ot,Rt)=>{se(ot,{slot:"label",size:1,weight:"medium",children:(Ut,as)=>{var Ot=I("Command");r(Ut,Ot)},$$slots:{default:!0}})}},$$legacy:!0}),r(Te,Pe)};V(Ke,Te=>{x(g()),x(l()),k(()=>{var Pe,We;return g()==="addRemote"||((Pe=l())==null?void 0:Pe.type)==="http"||((We=l())==null?void 0:We.type)==="sse"})?Te(ht):Te(Fe,!1)}),r(Me,Ee)};V(de,Me=>{g()!=="add"&&g()!=="addRemote"&&g()!=="edit"||Me(pe)},re)};V(T,de=>{g()==="addJson"?de(Q):de(O,!1)});var le=d(T,2),ne=de=>{In(de,{handleEnterEditMode:J,get envVarEntries(){return t(te)},set envVarEntries(re){v(te,re)},$$legacy:!0})};V(le,de=>{x(g()),x(ct),x(l()),k(()=>(g()==="add"||g()==="edit")&&!ct(l()))&&de(ne)});var ve=d(le,2),fe=o(ve);let Ce;var Se=o(fe);Bt(Se,{variant:"soft",color:"error",size:1,icon:re=>{Nr(re,{})},children:(re,pe)=>{var Me=I();oe(()=>ke(Me,N())),r(re,Me)},$$slots:{icon:!0,default:!0}});var be=d(fe,2),Ne=o(be);Ge(Ne,{size:1,variant:"ghost",color:"neutral",type:"button",$$events:{click:ae},children:(de,re)=>{var pe=I("Cancel");r(de,pe)},$$slots:{default:!0}});var he=d(Ne,2);Ge(he,{size:1,variant:"solid",color:"accent",get loading(){return B()},type:"submit",get disabled(){return t(A)},children:(de,re)=>{var pe=Xe(),Me=ge(pe),Ee=ze=>{var Oe=I("Import");r(ze,Oe)},Ae=(ze,Oe)=>{var He=Ke=>{var ht=I("Add");r(Ke,ht)},st=(Ke,ht)=>{var Fe=Pe=>{var We=I("Add");r(Pe,We)},Te=(Pe,We)=>{var vt=ot=>{var Rt=I("Save");r(ot,Rt)};V(Pe,ot=>{g()==="edit"&&ot(vt)},We)};V(Ke,Pe=>{g()==="addRemote"?Pe(Fe):Pe(Te,!1)},ht)};V(ze,Ke=>{g()==="add"?Ke(He):Ke(st,!1)},Oe)};V(Me,ze=>{g()==="addJson"?ze(Ee):ze(Ae,!1)}),r(de,pe)},$$slots:{default:!0}}),oe(de=>{ut(w,1,"c-mcp-server-card "+(g()==="add"||g()==="addJson"||g()==="addRemote"?"add-server-section":"server-item"),"svelte-1koxb3z"),Ce=ut(fe,1,"error-container svelte-1koxb3z",null,Ce,de)},[()=>({"is-error":!!N()})],_e),mt("submit",w,ea(ue)),r(f,w)};return V(K,f=>{g()==="view"&&l()?f(Re):f(Ve,!1)}),r(i,Le),er(e,"setLocalEnvVarFormState",F),Be({setLocalEnvVarFormState:F})}var Kn=h('<div class="user-input-field svelte-8tbe79"><!> <!> <!></div>'),Yn=h('<div class="user-input-container svelte-8tbe79"><!> <div class="user-input-actions svelte-8tbe79"><!> <!></div></div>'),Qn=h('<div slot="header-left" class="mcp-service-info svelte-8tbe79"><div class="mcp-service-title svelte-8tbe79"><!></div> <!> <!></div>'),Xn=h('<div class="installed-indicator svelte-8tbe79"><!></div>'),eo=h('<div slot="header-right" class="mcp-service-actions svelte-8tbe79"><!></div>'),to=h('<div class="mcp-service-item"><!></div>'),so=h('<div class="mcp-install-content svelte-8tbe79"><div class="mcp-list-container svelte-8tbe79"></div></div>'),ro=h('<div slot="header-left" class="mcp-install-left svelte-8tbe79"><!> <!></div>'),ao=h('<div slot="header" class="mcp-install-header svelte-8tbe79"><!></div>'),no=h('<div class="mcp-install-wrapper svelte-8tbe79"><!></div>');function oo(i,e){Je(e,!1);let s=S(e,"onMCPServerAdd",24,()=>{}),a=S(e,"servers",24,()=>[]);const n=[{label:cs.REDIS,description:"Real-time data platform for building fast apps",command:"uvx",args:["--from","git+https://github.com/redis/mcp-redis.git","redis-mcp-server","--url"],userInput:[{label:"Redis connection URL",description:"Enter your connection URL (redis://localhost:6379/0)",placeholder:"rediss://<USERNAME>:<PASSWORD>@<HOST>:<PORT>?ssl_cert_reqs=required&ssl_ca_certs=<PATH_TO_CERT>",correspondingArg:"--url",type:"argument"}]},{label:cs.MONGODB,description:"Optimize database queries and performance.",command:"npx",args:["-y","mongodb-mcp-server","--connectionString"],userInput:[{label:"MongoDB Connection String",description:"Enter your MongoDB connection string",placeholder:"********************************:port/database",correspondingArg:"--connectionString",type:"argument"}]},{label:cs.CIRCLECI,description:"Debug builds and improve CI/CD pipelines.",command:"npx",args:["-y","@circleci/mcp-server-circleci"],userInput:[{label:"CircleCI Token",description:"Enter your CircleCI token",placeholder:"YOUR_CIRCLE_CI_TOKEN",type:"environmentVariable",envVarName:"CIRCLECI_TOKEN"},{label:"Base URL",description:"Enter the base URL for your CircleCI instance",placeholder:"https://circleci.com",defaultValue:"https://circleci.com",type:"environmentVariable",envVarName:"CIRCLECI_BASE_URL"}]},{label:"Context 7",description:"Package documentation",command:"npx -y @upstash/context7-mcp@latest"},{label:"Playwright",description:"Browser automation",command:"npx -y @playwright/mcp@latest"},{label:"Sequential thinking",description:"Think through complex problems step-by-step.",command:"npx -y @modelcontextprotocol/server-sequential-thinking"}],c="easyMCPInstall.collapsed";let u=H(!1),b=H(!1),A=H(null),m=H({}),l=H({});function Y(U){var j;if(!U.userInput)return;for(let p=0;p<U.userInput.length;p++){const g=U.userInput[p];let N;if(N=g.type==="environmentVariable"&&g.envVarName?g.envVarName:g.correspondingArg?g.correspondingArg:`input_${p}`,!((j=t(m)[N])==null?void 0:j.trim())){const C=t(l)[N];return void(C&&C.focus())}}let z=[U.command],L={};U.args&&z.push(...U.args);for(let p=0;p<U.userInput.length;p++){const g=U.userInput[p];let N;N=g.type==="environmentVariable"&&g.envVarName?g.envVarName:g.correspondingArg?g.correspondingArg:`input_${p}`;const Z=t(m)[N].trim(),C=`"${Z}"`;if(g.type==="environmentVariable"&&g.envVarName)L[g.envVarName]=Z;else if(g.correspondingArg){const M=z.indexOf(g.correspondingArg);M!==-1?z.splice(M+1,0,C):z.push(g.correspondingArg,C)}else z.push(C)}const D={type:"stdio",name:U.label,command:z.join(" "),arguments:"",useShellInterpolation:!0,env:Object.keys(L).length>0?L:void 0};s()&&s()(D),v(A,null),v(m,{})}function P(){v(A,null),v(m,{})}me(()=>{},()=>{const U=localStorage.getItem(c);if(U!==null)try{v(u,JSON.parse(U))}catch{localStorage.removeItem(c)}v(b,!0)}),me(()=>(t(b),t(u)),()=>{typeof window<"u"&&t(b)&&localStorage.setItem(c,JSON.stringify(t(u)))}),nt(),tt();var ee=no(),ie=o(ee);Xs(ie,{get collapsed(){return t(u)},set collapsed(U){v(u,U)},children:(U,z)=>{var L=so(),D=o(L);et(D,5,()=>n,bt,(j,p)=>{var g=to();ft(o(g),{$$slots:{"header-left":(N,Z)=>{var C=Qn(),M=o(C),G=o(M);se(G,{size:1,weight:"medium",children:(R,F)=>{var E=I();oe(()=>ke(E,(t(p),k(()=>t(p).label)))),r(R,E)},$$slots:{default:!0}});var y=d(M,2),$=R=>{se(R,{size:1,color:"secondary",children:(F,E)=>{var J=I();oe(()=>ke(J,(t(p),k(()=>t(p).description)))),r(F,J)},$$slots:{default:!0}})};V(y,R=>{t(p),k(()=>t(p).description)&&R($)});var _=d(y,2),te=R=>{var F=Yn(),E=o(F);et(E,1,()=>(t(p),k(()=>t(p).userInput)),bt,(ue,ae,Le)=>{var K=Kn();const Re=_e(()=>(t(ae),k(()=>t(ae).type==="environmentVariable"&&t(ae).envVarName?t(ae).envVarName:t(ae).correspondingArg||`input_${Le}`)));var Ve=o(K);se(Ve,{size:1,weight:"medium",color:"neutral",children:(q,ce)=>{var W=I();oe(()=>ke(W,(t(ae),k(()=>t(ae).label)))),r(q,W)},$$slots:{default:!0}});var $e=d(Ve,2),f=q=>{se(q,{size:1,color:"secondary",children:(ce,W)=>{var ye=I();oe(()=>ke(ye,(t(ae),k(()=>t(ae).description)))),r(ce,ye)},$$slots:{default:!0}})};V($e,q=>{t(ae),k(()=>t(ae).description)&&q(f)});var w=d($e,2);const X=_e(()=>(t(ae),k(()=>t(ae).placeholder||"")));At(w,{get placeholder(){return t(X)},size:1,variant:"surface",get value(){return t(m)[t(Re)]},set value(q){ns(m,t(m)[t(Re)]=q)},get textInput(){return t(l)[t(Re)]},set textInput(q){ns(l,t(l)[t(Re)]=q)},$$events:{keydown:q=>{q.key==="Enter"?Y(t(p)):q.key==="Escape"&&P()}},$$legacy:!0}),r(ue,K)});var J=d(E,2),B=o(J);Ge(B,{variant:"ghost-block",color:"accent",size:1,$$events:{click:()=>Y(t(p))},children:(ue,ae)=>{var Le=I("Install");r(ue,Le)},$$slots:{default:!0}});var we=d(B,2);Ge(we,{variant:"ghost-block",color:"neutral",size:1,$$events:{click:P},children:(ue,ae)=>{var Le=I("Cancel");r(ue,Le)},$$slots:{default:!0}}),r(R,F)};V(_,R=>{t(A),t(p),k(()=>t(A)===t(p).label&&t(p).userInput)&&R(te)}),r(N,C)},"header-right":(N,Z)=>{var C=eo(),M=o(C),G=$=>{var _=Xn(),te=o(_);ms.Root(te,{color:"success",size:1,variant:"soft",children:(R,F)=>{var E=I("Installed");r(R,E)},$$slots:{default:!0}}),r($,_)},y=($,_)=>{var te=R=>{Lt(R,{variant:"ghost-block",color:"accent",size:1,$$events:{click:()=>function(F){if(a().some(J=>J.name===F.label))return;if(F.userInput&&F.userInput.length>0)return v(m,{}),F.userInput.forEach((J,B)=>{let we;we=J.type==="environmentVariable"&&J.envVarName?J.envVarName:J.correspondingArg?J.correspondingArg:`input_${B}`,ns(m,t(m)[we]=J.defaultValue||"")}),void v(A,F.label);const E={type:"stdio",name:F.label,command:F.command,arguments:"",useShellInterpolation:!0};s()&&s()(E)}(t(p))},children:(F,E)=>{Tt(F,{})},$$slots:{default:!0}})};V($,R=>{t(A),t(p),k(()=>t(A)!==t(p).label)&&R(te)},_)};V(M,$=>{x(a()),t(p),k(()=>a().some(_=>_.name===t(p).label))?$(G):$(y,!1)}),r(N,C)}}}),r(j,g)}),r(U,L)},$$slots:{default:!0,header:(U,z)=>{var L=ao();ft(o(L),{$$slots:{"header-left":(D,j)=>{var p=ro(),g=o(p);Qr(g,{});var N=d(g,2);se(N,{color:"neutral",size:1,weight:"light",class:"card-title",children:(Z,C)=>{var M=I("Easy MCP Installation");r(Z,M)},$$slots:{default:!0}}),r(D,p)}}}),r(U,L)}},$$legacy:!0}),r(i,ee),Be()}const io={mcpDocsURL:"https://docs.augmentcode.com/setup-augment/mcp"},lo={mcpDocsURL:"https://docs.augmentcode.com/jetbrains/setup-augment/mcp"},co=Sr(),uo=new class{constructor(i){Ie(this,"strings");let e={[ls.vscode]:{},[ls.jetbrains]:lo,[ls.web]:{}};this.strings={...io,...e[i]}}get(i){return this.strings[i]}}(co.clientType);var vo=h('<div class="section-heading-text">MCP</div>'),go=h(`<div class="mcp-servers svelte-1vnq4q3"><div class="section-heading svelte-1vnq4q3"><!></div> <div class="description-text svelte-1vnq4q3">Configure a new Model Context Protocol server to connect Augment to custom tools. Find out more
    about MCP <a>in the docs</a>.</div> <!> <!></div> <!> <div class="add-mcp-button-container svelte-1vnq4q3"><!> <!> <!></div>`,1),ho=h('<div class="section-heading-text">Terminal</div>'),po=h("<!> <!>",1),mo=h('<div class="terminal-settings svelte-dndd5n"><!> <div class="shell-selector svelte-dndd5n"><!> <!></div> <div class="startup-script-container svelte-dndd5n"><!> <!></div></div>');function fo(i,e){Je(e,!1);const s=H();let a=S(e,"supportedShells",24,()=>[]),n=S(e,"selectedShell",24,()=>{}),c=S(e,"startupScript",28,()=>{}),u=S(e,"onShellSelect",8),b=S(e,"onStartupScriptChange",8),A=H();me(()=>x(n()),()=>{var L;v(s,n()?(L=n(),a().find(D=>D.friendlyName===L)):void 0)}),nt(),tt();var m=mo(),l=o(m);se(l,{size:1,weight:"regular",color:"secondary",children:(L,D)=>{var j=ho();r(L,j)},$$slots:{default:!0}});var Y=d(l,2),P=o(Y);se(P,{size:1,children:(L,D)=>{var j=I("Shell:");r(L,j)},$$slots:{default:!0}});var ee=d(P,2);Ze.Root(ee,{get requestClose(){return t(A)},set requestClose(L){v(A,L)},children:(L,D)=>{var j=po(),p=ge(j);Ze.Trigger(p,{children:(N,Z)=>{const C=_e(()=>(x(a()),k(()=>a().length===0)));Ge(N,{size:1,variant:"outline",color:"neutral",get disabled(){return t(C)},children:(M,G)=>{var y=Xe(),$=ge(y),_=R=>{var F=I();oe(()=>ke(F,`${t(s),k(()=>t(s).friendlyName)??""}
            (${t(s),k(()=>t(s).supportString)??""})`)),r(R,F)},te=(R,F)=>{var E=B=>{var we=I("No shells available");r(B,we)},J=B=>{var we=I("Select a shell");r(B,we)};V(R,B=>{x(a()),k(()=>a().length===0)?B(E):B(J,!1)},F)};V($,R=>{t(s),x(a()),k(()=>t(s)&&a().length>0)?R(_):R(te,!1)}),r(M,y)},$$slots:{default:!0,iconRight:(M,G)=>{Tr(M)}}})},$$slots:{default:!0}});var g=d(p,2);Ze.Content(g,{side:"bottom",align:"start",children:(N,Z)=>{var C=Xe(),M=ge(C),G=$=>{var _=Xe(),te=ge(_);et(te,1,a,R=>R.friendlyName,(R,F)=>{const E=_e(()=>(x(n()),t(F),k(()=>n()===t(F).friendlyName)));Ze.Item(R,{onSelect:()=>{u()(t(F).friendlyName),t(A)()},get highlight(){return t(E)},children:(J,B)=>{var we=I();oe(()=>ke(we,`${t(F),k(()=>t(F).friendlyName)??""}
              (${t(F),k(()=>t(F).supportString)??""})`)),r(J,we)},$$slots:{default:!0}})}),r($,_)},y=$=>{Ze.Label($,{children:(_,te)=>{var R=I("No shells available");r(_,R)},$$slots:{default:!0}})};V(M,$=>{x(a()),k(()=>a().length>0)?$(G):$(y,!1)}),r(N,C)},$$slots:{default:!0}}),r(L,j)},$$slots:{default:!0},$$legacy:!0});var ie=d(Y,2),U=o(ie);se(U,{size:1,children:(L,D)=>{var j=I("Start-up script: Code to run wherever a new terminal is opened");r(L,j)},$$slots:{default:!0}});var z=d(U,2);bs(z,{placeholder:"Enter shell commands to run on terminal startup",resize:"vertical",get value(){return c()},set value(L){c(L)},$$events:{change:function(L){const D=L.target;b()(D.value)}},$$legacy:!0}),r(i,m),Be()}var Co=h('<div class="section-heading-text">Sound Settings</div>'),$o=h('<div class="c-sound-setting__info svelte-8awonv" slot="header-left"><div><!></div> <div><!></div></div>'),wo=h('<div slot="header-right"><!></div>'),yo=h('<div class="c-sound-setting__info svelte-8awonv" slot="header-left"><div><!></div> <div><!></div></div>'),So=h('<div slot="header-right"><!></div>'),bo=h('<!> <div class="c-sound-settings svelte-8awonv"><!> <!></div>',1),Mo=h('<div class="section-heading-text">Agent Settings</div>'),ko=h('<div class="c-agent-setting__info svelte-mv39d5" slot="header-left"><div><!></div> <div><!></div> <div class="c-agent-setting__education svelte-mv39d5"><!></div></div>'),xo=h('<div slot="header-right"><!></div>'),Ao=h('<!> <div class="c-agent-settings svelte-mv39d5"><!></div>',1),zo=h('<div class="c-settings-tools svelte-181yusq"><!> <!> <!> <!> <!></div>');function Lo(i,e){let s=S(e,"tools",19,()=>[]),a=S(e,"isMCPEnabled",3,!0),n=S(e,"isMCPImportEnabled",3,!0),c=S(e,"isTerminalEnabled",3,!0),u=S(e,"isSoundCategoryEnabled",3,!1),b=S(e,"isAgentCategoryEnabled",3,!1),A=S(e,"isSwarmModeFeatureFlagEnabled",3,!1),m=S(e,"hasEverUsedRemoteAgent",3,!1),l=S(e,"onToolApprovalConfigChange",3,()=>{}),Y=S(e,"onCancel",19,()=>{}),P=S(e,"supportedShells",19,()=>[]),ee=S(e,"selectedShell",19,()=>{}),ie=S(e,"startupScript",19,()=>{}),U=S(e,"onShellSelect",3,()=>{}),z=S(e,"onStartupScriptChange",3,()=>{});var L=zo(),D=o(L);zn(D,{title:"Services",get tools(){return s()},get onAuthenticate(){return e.onAuthenticate},get onRevokeAccess(){return e.onRevokeAccess},onToolApprovalConfigChange:l()});var j=d(D,2),p=y=>{(function($,_){Je(_,!1);const[te,R]=gt(),F=()=>je(xe,"$enableNativeRemoteMcp",te),E=()=>je(T,"$allServers",te),J=H(),B=H();let we=S(_,"onMCPServerAdd",8),ue=S(_,"onMCPServerSave",8),ae=S(_,"onMCPServerDelete",8),Le=S(_,"onMCPServerToggleDisable",8),K=S(_,"onCancel",24,()=>{}),Re=S(_,"onMCPServerJSONImport",8),Ve=S(_,"isMCPImportEnabled",8,!0);const $e=Ws();function f(Fe){$e.startRemoteMCPAuth(Fe.name)}let w=H(null),X=H(null);function q(){var Fe;v(w,null),v(X,null),(Fe=K())==null||Fe()}let ce=H([]);const W=Et(Ft.key),ye=ys(),xe=W.getEnableNativeRemoteMcp(),T=ye.getServers();function Q(Fe){v(w,Fe.id)}function O(Fe){return async function(...Te){const Pe=await Fe(...Te);return v(X,null),v(w,null),Pe}}const le=O(we()),ne=O(ue()),ve=O(Re()),fe=O(ae()),Ce=O(Le()),Se=uo.get("mcpDocsURL");me(()=>(t(X),t(w)),()=>{v(J,t(X)==="add"||t(X)==="addJson"||t(X)==="addRemote"||t(w)!==null)}),me(()=>(F(),E()),()=>{F()?v(ce,Er(E())):v(ce,E())}),me(()=>t(ce),()=>{v(B,Wt.parseServerValidationMessages(t(ce)))}),nt(),tt();var be=go(),Ne=ge(be),he=o(Ne),de=o(he);se(de,{size:1,weight:"regular",color:"secondary",children:(Fe,Te)=>{var Pe=vo();r(Fe,Pe)},$$slots:{default:!0}});var re=d(he,2),pe=d(o(re)),Me=d(re,2);oo(Me,{get onMCPServerAdd(){return le},get servers(){return t(ce)}});var Ee=d(Me,2);et(Ee,1,()=>t(ce),Fe=>Fe.id,(Fe,Te)=>{const Pe=_e(()=>(t(w),t(Te),k(()=>t(w)===t(Te).id?"edit":"view"))),We=_e(()=>(t(B),t(Te),k(()=>t(B).errors.get(t(Te).id)))),vt=_e(()=>(t(B),t(Te),k(()=>t(B).warnings.get(t(Te).id))));Vs(Fe,{get mode(){return t(Pe)},get server(){return t(Te)},get onAdd(){return le},get onSave(){return ne},get onDelete(){return fe},get onToggleDisableServer(){return Ce},onEdit:Q,onCancel:q,get onJSONImport(){return ve},onAuthenticate:f,get disabledText(){return t(We)},get warningText(){return t(vt)}})});var Ae=d(Ne,2),ze=Fe=>{Vs(Fe,{get mode(){return t(X)},get onAdd(){return le},get onSave(){return ne},get onDelete(){return fe},get onToggleDisableServer(){return Ce},onEdit:Q,onCancel:q,get onJSONImport(){return ve},onAuthenticate:f})};V(Ae,Fe=>{t(X)!=="add"&&t(X)!=="addJson"&&t(X)!=="addRemote"||Fe(ze)});var Oe=d(Ae,2),He=o(Oe);Ge(He,{get disabled(){return t(J)},color:"neutral",variant:"soft",size:1,$$events:{click:()=>{v(X,"add")}},children:(Fe,Te)=>{var Pe=I("Add MCP");r(Fe,Pe)},$$slots:{default:!0,iconLeft:(Fe,Te)=>{Tt(Fe,{slot:"iconLeft"})}}});var st=d(He,2);Ge(st,{get disabled(){return t(J)},color:"neutral",variant:"soft",size:1,$$events:{click:()=>{v(X,"addRemote")}},children:(Fe,Te)=>{var Pe=I("Add remote MCP");r(Fe,Pe)},$$slots:{default:!0,iconLeft:(Fe,Te)=>{Tt(Fe,{slot:"iconLeft"})}}});var Ke=d(st,2),ht=Fe=>{Ge(Fe,{get disabled(){return t(J)},color:"neutral",variant:"soft",size:1,title:"Add MCP from JSON",$$events:{click:()=>{v(X,"addJson")}},children:(Te,Pe)=>{var We=I("Import from JSON");r(Te,We)},$$slots:{default:!0,iconLeft:(Te,Pe)=>{Ys(Te,{slot:"iconLeft"})}}})};V(Ke,Fe=>{Ve()&&Fe(ht)}),oe(()=>St(pe,"href",Se)),r($,be),Be(),R()})(y,{get onMCPServerAdd(){return e.onMCPServerAdd},get onMCPServerSave(){return e.onMCPServerSave},get onMCPServerDelete(){return e.onMCPServerDelete},get onMCPServerToggleDisable(){return e.onMCPServerToggleDisable},get onMCPServerJSONImport(){return e.onMCPServerJSONImport},get onCancel(){return Y()},get isMCPImportEnabled(){return n()}})};V(j,y=>{a()&&y(p)});var g=d(j,2),N=y=>{fo(y,{get supportedShells(){return P()},get selectedShell(){return ee()},get startupScript(){return ie()},onShellSelect:U(),onStartupScriptChange:z()})};V(g,y=>{c()&&y(N)});var Z=d(g,2),C=y=>{(function($,_){Je(_,!1);const[te,R]=gt(),F=()=>je(t(E),"$currentSettings",te),E=H(),J=H(),B=Et(Cs.key);async function we(){return await B.playAgentComplete(),"success"}me(()=>{},()=>{Jt(v(E,B.getCurrentSettings),"$currentSettings",te)}),me(()=>F(),()=>{v(J,F().enabled)}),nt(),tt();var ue=bo(),ae=ge(ue);se(ae,{size:1,weight:"regular",color:"secondary",children:($e,f)=>{var w=Co();r($e,w)},$$slots:{default:!0}});var Le=d(ae,2),K=o(Le);ft(K,{$$slots:{"header-left":($e,f)=>{var w=$o(),X=o(w),q=o(X);se(q,{size:2,weight:"medium",children:(ye,xe)=>{var T=I("Enable Sound Effects");r(ye,T)},$$slots:{default:!0}});var ce=d(X,2),W=o(ce);se(W,{size:1,weight:"medium",children:(ye,xe)=>{var T=I("Play a sound when an agent completes a task");r(ye,T)},$$slots:{default:!0}}),r($e,w)},"header-right":($e,f)=>{var w=wo(),X=o(w);fs(X,{size:1,get checked(){return t(J)},onchange:()=>B.updateEnabled(!t(J))}),r($e,w)}}});var Re=d(K,2),Ve=$e=>{ft($e,{$$slots:{"header-left":(f,w)=>{var X=yo(),q=o(X),ce=o(q);se(ce,{size:2,weight:"medium",children:(xe,T)=>{var Q=I("Test Sound");r(xe,Q)},$$slots:{default:!0}});var W=d(q,2),ye=o(W);se(ye,{size:1,weight:"medium",children:(xe,T)=>{var Q=I("Play a sample of the agent completion sound");r(xe,Q)},$$slots:{default:!0}}),r(f,X)},"header-right":(f,w)=>{var X=So(),q=o(X);const ce=_e(()=>t(J)?"":"Enable sound effects to test"),W=_e(()=>(x(Nt),k(()=>[Nt.Hover])));$t(q,{get content(){return t(ce)},get triggerOn(){return t(W)},children:(ye,xe)=>{const T=_e(()=>!t(J));aa(ye,{size:1,defaultColor:"neutral",get enabled(){return t(J)},stickyColor:!1,get disabled(){return t(T)},onClick:we,tooltip:{neutral:"Play a sample of the agent completion sound",success:"Played!"},children:(Q,O)=>{var le=I("Play");r(Q,le)},$$slots:{default:!0}})},$$slots:{default:!0}}),r(f,X)}}})};V(Re,$e=>{t(J)&&$e(Ve)}),r($,ue),Be(),R()})(y,{})};V(Z,y=>{u()&&y(C)});var M=d(Z,2),G=y=>{(function($,_){Je(_,!1);const[te,R]=gt(),F=()=>je(t(E),"$currentSettings",te),E=H(),J=H();let B=S(_,"isSwarmModeEnabled",8,!1),we=S(_,"hasEverUsedRemoteAgent",8,!1);const ue=Et(jt.key);me(()=>{},()=>{Jt(v(E,ue.getCurrentSettings),"$currentSettings",te)}),me(()=>F(),()=>{v(J,F().enabled)}),nt(),tt();var ae=Xe(),Le=ge(ae),K=Re=>{var Ve=Ao(),$e=ge(Ve);se($e,{size:1,weight:"regular",color:"secondary",children:(w,X)=>{var q=Mo();r(w,q)},$$slots:{default:!0}});var f=d($e,2);ft(o(f),{$$slots:{"header-left":(w,X)=>{var q=ko(),ce=o(q),W=o(ce);se(W,{size:2,weight:"medium",children:(O,le)=>{var ne=I("Enable Swarm Mode");r(O,ne)},$$slots:{default:!0}});var ye=d(ce,2),xe=o(ye);se(xe,{size:1,weight:"medium",children:(O,le)=>{var ne=I("Allow agents to coordinate and work together on complex tasks");r(O,ne)},$$slots:{default:!0}});var T=d(ye,2),Q=o(T);se(Q,{size:1,weight:"regular",color:"secondary",children:(O,le)=>{var ne=I(`Sub-agents run in isolated remote environments and communicate via git repositories.
            Each remote agent consumes credits from your account. Changes are reviewed before being
            applied to your local workspace.`);r(O,ne)},$$slots:{default:!0}}),r(w,q)},"header-right":(w,X)=>{var q=xo(),ce=o(q);fs(ce,{size:1,get checked(){return t(J)},onchange:()=>ue.updateEnabled(!t(J))}),r(w,q)}}}),r(Re,Ve)};V(Le,Re=>{B()&&we()&&Re(K)}),r($,ae),Be(),R()})(y,{get isSwarmModeEnabled(){return A()},get hasEverUsedRemoteAgent(){return m()}})};V(M,y=>{b()&&y(G)}),r(i,L)}var Ro=lt("<svg><!></svg>");function Io(i,e){const s=zt(e,["children","$$slots","$$events","$$legacy"]);var a=Ro();Pt(a,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 576 512",...s}));var n=o(a);Zt(n,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M288 0c-8.5 0-17 1.7-24.8 5.1L53.9 94.8C40.6 100.5 32 113.5 32 128s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2L312.8 5.1C305 1.7 296.5 0 288 0m-5.9 49.2c1.9-.8 3.9-1.2 5.9-1.2s4 .4 5.9 1.2L477.7 128l-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 128zM53.9 222.8C40.6 228.5 32 241.5 32 256s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 256l47.7-20.5-60.9-26.1zm0 128C40.6 356.5 32 369.5 32 384s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 384l47.7-20.5-60.9-26.1z"/>',!0),r(i,a)}var _o=lt("<svg><!></svg>");function No(i,e){const s=zt(e,["children","$$slots","$$events","$$legacy"]);var a=_o();Pt(a,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...s}));var n=o(a);Zt(n,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M63.2 379.3c-6.2-6.2-6.2-16.4 0-22.6l39.4-39.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 39.4-39.4c6.2-6.2 16.4-6.2 22.6 0l69.5 69.5c6.2 6.2 6.2 16.4 0 22.6L155.3 448.8c-6.2 6.2-16.4 6.2-22.6 0zm35.5 103.4c25 25 65.5 25 90.5 0l293.5-293.4c25-25 25-65.5 0-90.5l-69.4-69.5c-25-25-65.5-25-90.5 0L29.3 322.7c-25 25-25 65.5 0 90.5l69.5 69.5z"/>',!0),r(i,a)}var Eo=lt("<svg><!></svg>");function Ds(i,e){const s=zt(e,["children","$$slots","$$events","$$legacy"]);var a=Eo();Pt(a,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...s}));var n=o(a);Zt(n,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M304 128a80 80 0 1 0-160 0 80 80 0 1 0 160 0m-208 0a128 128 0 1 1 256 0 128 128 0 1 1-256 0M49.3 464h349.5c-8.9-63.3-63.3-112-129-112h-91.4c-65.7 0-120.1 48.7-129 112zM0 482.3C0 383.8 79.8 304 178.3 304h91.4c98.5 0 178.3 79.8 178.3 178.3 0 16.4-13.3 29.7-29.7 29.7H29.7C13.3 512 0 498.7 0 482.3"/>',!0),r(i,a)}var To=h('<div class="l-markdown-editor svelte-1dcrmc3"><div class="c-markdown-editor__header svelte-1dcrmc3"><!></div> <!> <div class="c-markdown-editor__content svelte-1dcrmc3"><!> <!></div></div> <div class="c-markdown-editor__status svelte-1dcrmc3"><!> <!></div>',1),Fo=h('<div class="c-user-guidelines-category__input svelte-10borzo"><!></div>');function nr(i,e){Je(e,!0);const[s,a]=gt();let n=S(e,"userGuidelines",15,""),c=S(e,"userGuidelinesLengthLimit",19,()=>{}),u=S(e,"updateUserGuideline",3,()=>!1);const b=Qe(void 0);function A(){const l=n().trim();if(je(b,"$originalValue",s)!==l){if(!u()(l))throw c()&&l.length>c()?`The user guideline must be less than ${c()} character long`:"An error occurred updating the user";Rs(b,l)}}Hs(()=>{Rs(b,n().trim())}),hs(()=>{A()});var m=Fo();(function(l,Y){Je(Y,!0);let P,ee=S(Y,"variant",3,"surface"),ie=S(Y,"size",3,2),U=S(Y,"color",19,()=>{}),z=S(Y,"resize",3,"none"),L=S(Y,"textInput",15),D=S(Y,"value",15,""),j=S(Y,"selectedText",15,""),p=S(Y,"selectionStart",15,0),g=S(Y,"selectionEnd",15,0),N=wr(Y,["$$slots","$$events","$$legacy","variant","size","color","resize","textInput","value","selectedText","selectionStart","selectionEnd","saveFunction","header","children","title"]),Z=Ls(!1),C=Ls(void 0);const M=async()=>{try{Y.saveFunction(),v(Z,!0),clearTimeout(P),P=setTimeout(()=>{v(Z,!1)},1500)}catch(K){v(C,K instanceof Error?K.message:String(K),!0)}};function G(){L()&&(p(L().selectionStart),g(L().selectionEnd),p()!==g()?j(D().substring(p(),g())):j(""))}hs(()=>{M()});var y=To(),$=ge(y),_=o($),te=o(_);is(te,()=>Y.header??os);var R=d(_,2);is(R,()=>Y.children??os);var F=d(R,2),E=o(F);is(E,()=>Y.title??os);var J=d(E,2);bs(J,Js({get variant(){return ee()},get size(){return ie()},get color(){return U()},get resize(){return z()},placeholder:"Enter markdown content..."},()=>N,{get textInput(){return L()},set textInput(K){L(K)},get value(){return D()},set value(K){D(K)},$$events:{select:G,mouseup:G,keyup:()=>{G()},blur:M,keydown:K=>{(K.key==="Escape"||(K.metaKey||K.ctrlKey)&&K.key==="s")&&(K.preventDefault(),M())}}}));var B=d($,2),we=o(B),ue=K=>{se(K,{size:1,weight:"light",color:"error",children:(Re,Ve)=>{var $e=I();oe(()=>ke($e,t(C))),r(Re,$e)},$$slots:{default:!0}})};V(we,K=>{t(C)&&K(ue)});var ae=d(we,2),Le=K=>{se(K,{size:1,weight:"light",color:"success",children:(Re,Ve)=>{var $e=I("Saved");r(Re,$e)},$$slots:{default:!0}})};V(ae,K=>{t(Z)&&K(Le)}),r(l,y),Be()})(o(m),{placeholder:"Add your guidelines for Augment Chat...",resize:"vertical",saveFunction:A,get value(){return n()},set value(l){n(l)}}),r(i,m),Be(),a()}var Po=h("<!> <!> <!>",1),Uo=h("<!> <!>",1),Oo=h('<input type="text" value="No existing rules found" readonly="" class="c-dropdown-input svelte-z1s6x7"/>'),Vo=h('<div class="c-dropdown-trigger svelte-z1s6x7"><input type="text" readonly="" class="c-dropdown-input svelte-z1s6x7"/> <!></div>'),Do=h("<!> <!>",1),qo=h("<!> <!>",1),Ho=h("<!> <!>",1),Go=h("<!> <!> <!> <!>",1),Jo=h('<div class="c-auto-import-rules-dialog svelte-z1s6x7"><!></div>'),Bo=h("<!> <!>",1);function jo(i,e){Je(e,!1);const[s,a]=gt(),n=()=>je(t(ee),"$focusedIndex",s),c=H(),u=Gs();let b=S(e,"show",8,!1),A=S(e,"options",24,()=>[]),m=S(e,"isLoading",8,!1),l=S(e,"errorMessage",8,""),Y=S(e,"successMessage",8,""),P=H(t(c)),ee=H(void 0),ie=H(()=>{});function U(){t(P)&&!m()&&u("select",t(P))}function z(){m()||(u("cancel"),v(P,t(c)))}me(()=>x(A()),()=>{v(c,A().length>0?A()[0]:null)}),me(()=>(x(b()),t(c)),()=>{b()&&v(P,t(c))}),nt(),tt(),mt("keydown",Ss,function(L){b()&&!m()&&(L.key==="Escape"?(L.preventDefault(),z()):L.key==="Enter"&&t(P)&&(L.preventDefault(),U()))}),tr(i,{get show(){return b()},title:"Auto Import Rules",ariaLabelledBy:"dialog-title",get preventBackdropClose(){return m()},get preventEscapeClose(){return m()},$$events:{cancel:z},body:j=>{var p=Jo(),g=o(p),N=C=>{var M=Oo();r(C,M)},Z=C=>{var M=Go(),G=ge(M);se(G,{size:2,color:"secondary",children:(E,J)=>{var B=I("Select existing rules to auto import to .augment/rules");r(E,B)},$$slots:{default:!0}});var y=d(G,2);const $=_e(()=>(x(A()),k(()=>A().length===0?[]:void 0)));Ze.Root(y,{get triggerOn(){return t($)},get requestClose(){return t(ie)},set requestClose(E){v(ie,E)},get focusedIndex(){return t(ee)},set focusedIndex(E){Jt(v(ee,E),"$focusedIndex",s)},children:(E,J)=>{var B=Ho(),we=ge(B);Ze.Trigger(we,{children:(ae,Le)=>{var K=Vo(),Re=o(K),Ve=d(Re,2);Ms(Ve,{class:"c-dropdown-chevron"}),oe(()=>fr(Re,(t(P),k(()=>t(P)?t(P).label:"Existing rules")))),r(ae,K)},$$slots:{default:!0}});var ue=d(we,2);Ze.Content(ue,{align:"start",side:"bottom",children:(ae,Le)=>{var K=qo(),Re=ge(K);et(Re,1,A,bt,(f,w)=>{const X=_e(()=>(t(P),t(w),k(()=>{var q;return((q=t(P))==null?void 0:q.label)===t(w).label})));Ze.Item(f,{onSelect:()=>function(q){v(P,q),t(ie)()}(t(w)),get highlight(){return t(X)},children:(q,ce)=>{var W=I();oe(()=>ke(W,(t(w),k(()=>t(w).label)))),r(q,W)},$$slots:{default:!0}})});var Ve=d(Re,2),$e=f=>{var w=Do(),X=ge(w);Ze.Separator(X,{});var q=d(X,2);Ze.Label(q,{children:(ce,W)=>{var ye=I();oe(()=>ke(ye,(n(),x(A()),t(P),k(()=>{var xe;return n()!==void 0?A()[n()].description:(xe=t(P))==null?void 0:xe.description})))),r(ce,ye)},$$slots:{default:!0}}),r(f,w)};V(Ve,f=>{(n()!==void 0||t(P))&&f($e)}),r(ae,K)},$$slots:{default:!0}}),r(E,B)},$$slots:{default:!0},$$legacy:!0});var _=d(y,2),te=E=>{Bt(E,{variant:"soft",color:"error",size:1,icon:B=>{ts(B,{})},children:(B,we)=>{var ue=I();oe(()=>ke(ue,l())),r(B,ue)},$$slots:{icon:!0,default:!0}})};V(_,E=>{l()&&E(te)});var R=d(_,2),F=E=>{Bt(E,{variant:"soft",color:"success",size:1,icon:B=>{Fr(B,{})},children:(B,we)=>{var ue=I();oe(()=>ke(ue,Y())),r(B,ue)},$$slots:{icon:!0,default:!0}})};V(R,E=>{Y()&&E(F)}),r(C,M)};V(g,C=>{x(A()),k(()=>A().length===0)?C(N):C(Z,!1)}),r(j,p)},footer:j=>{var p=Bo(),g=ge(p);Ge(g,{variant:"solid",color:"neutral",get disabled(){return m()},$$events:{click:z},children:(C,M)=>{var G=I("Cancel");r(C,G)},$$slots:{default:!0}});var N=d(g,2),Z=C=>{const M=_e(()=>!t(P)||m());Ge(C,{color:"accent",variant:"solid",get disabled(){return t(M)},get loading(){return m()},$$events:{click:U},children:(G,y)=>{var $=I();oe(()=>ke($,m()?"Importing...":"Import ")),r(G,$)},$$slots:{default:!0}})};V(N,C=>{x(A()),k(()=>A().length>0)&&C(Z)}),r(j,p)},$$slots:{body:!0,footer:!0}}),Be(),a()}var Zo=h('<div class="loading-container"><!> <!></div>'),Wo=h('<div class="c-rules-list-empty svelte-mrq2l0"><!></div>'),Ko=h('<div class="c-rule-item-info svelte-mrq2l0" slot="header-left"><div class="l-icon-wrapper svelte-mrq2l0"><!></div> <div class="c-rule-item-path svelte-mrq2l0"><!></div></div>'),Yo=h('<div class="server-actions" slot="header-right"><div class="status-controls svelte-mrq2l0"><div class="c-rules-dropdown"><!></div> <!> <!></div></div>'),Qo=h('<div class="c-rules-actions-button-content svelte-mrq2l0"><!> Create new rule file</div>'),Xo=h('<div class="c-rules-actions-button-content svelte-mrq2l0"><!> Import rules <!></div>'),ei=h("<!> <!>",1),ti=h("<!> <!>",1),si=h("<!> <!>",1),ri=h(`<div class="c-rules-category svelte-mrq2l0"><div class="c-rules-section svelte-mrq2l0"><!> <div>Rules are instructions for Augment Chat and Agent that can be applied automatically across all
      conversations or referenced in specific conversations using @mentions (e.g., @rule-file.md) <a href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines" target="_blank"><!></a></div> <!> <div class="c-rules-list svelte-mrq2l0"><!></div> <div class="c-rules-actions-container svelte-mrq2l0"><!> <!></div></div> <div class="c-user-guidelines-section svelte-mrq2l0"><!> <div>User Guidelines allow you to control Augment's behavior through natural language instructions.
      These guidelines are applied globally to all Chat and Agent interactions. <a href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines" target="_blank"><!></a></div> <!></div></div> <!> <!>`,1);function ai(i,e){Je(e,!1);const[s,a]=gt(),n=()=>je(D,"$rulesFiles",s),c=()=>je(t(b),"$isRulesLoading",s),u=()=>je(t(y),"$importFocusedIndex",s),b=H(),A=H(),m=H(),l=H();let Y=S(e,"userGuidelines",8,""),P=S(e,"userGuidelinesLengthLimit",24,()=>{}),ee=S(e,"workspaceGuidelinesLengthLimit",24,()=>{}),ie=S(e,"workspaceGuidelinesContent",8,""),U=S(e,"updateUserGuideline",8,()=>!1),z=S(e,"rulesModel",8),L=S(e,"rulesController",8);const D=z().getCachedRules(),j=L().getShowCreateRuleDialog(),p=L().getCreateRuleError();let g=H(!1),N=H([]),Z=H(!1),C=H(""),M=H("");const G=[{label:"Auto import existing rules in the workspace",id:"auto_import",description:"Choose existing rules in your workspace to auto import to Augment."},{label:"Select file(s) or directory to import",id:"select_file_or_directory",description:"Select an existing directory or list of markdown files to import to Augment."}];let y=H(void 0),$=H(()=>{});async function _(T){try{T.id==="select_file_or_directory"?await L().selectFileToImport():T.id==="auto_import"&&await async function(){try{v(C,""),v(M,"");const Q=await L().getAutoImportOptions();v(N,Q.data.options),v(g,!0)}catch(Q){console.error("Failed to get auto-import options:",Q),v(C,"Failed to detect existing rules in workspace.")}}()}catch(Q){console.error("Failed to handle import select:",Q)}t($)&&t($)()}me(()=>x(z()),()=>{Jt(v(b,z().getLoading()),"$isRulesLoading",s)}),me(()=>x(ee()),()=>{v(A,ee())}),me(()=>(t(m),t(l),n(),x(ie()),t(A)),()=>{var T;T=Br({rules:n(),workspaceGuidelinesContent:ie(),rulesAndGuidelinesLimit:t(A)}),v(m,T.isOverLimit),v(l,T.warningMessage)}),nt(),tt();var te=ri(),R=ge(te),F=o(R),E=o(F);se(E,{class:"c-section-header",size:3,color:"primary",children:(T,Q)=>{var O=I("Rules");r(T,O)},$$slots:{default:!0}});var J=d(E,2),B=d(o(J)),we=o(B);se(we,{size:1,weight:"regular",children:(T,Q)=>{var O=I("Learn more");r(T,O)},$$slots:{default:!0}});var ue=d(J,2),ae=T=>{Bt(T,{variant:"soft",color:"warning",size:1,icon:O=>{ts(O,{})},children:(O,le)=>{var ne=I();oe(()=>ke(ne,t(l))),r(O,ne)},$$slots:{icon:!0,default:!0}})};V(ue,T=>{t(m)&&T(ae)});var Le=d(ue,2),K=o(Le),Re=T=>{var Q=Zo(),O=o(Q);rs(O,{size:1});var le=d(O,2);se(le,{size:1,color:"secondary",children:(ne,ve)=>{var fe=I("Loading rules...");r(ne,fe)},$$slots:{default:!0}}),r(T,Q)},Ve=(T,Q)=>{var O=ne=>{var ve=Wo(),fe=o(ve);se(fe,{size:1,color:"neutral",children:(Ce,Se)=>{var be=I("No rules files found");r(Ce,be)},$$slots:{default:!0}}),r(ne,ve)},le=ne=>{var ve=Xe(),fe=ge(ve);et(fe,1,n,Ce=>Ce.path,(Ce,Se)=>{ft(Ce,{isClickable:!0,$$events:{click:()=>L().openRule(t(Se).path)},$$slots:{"header-left":(be,Ne)=>{var he=Ko(),de=o(he),re=o(de),pe=ze=>{$t(ze,{content:"No description found",children:(Oe,He)=>{ts(Oe,{})},$$slots:{default:!0}})},Me=ze=>{Or(ze,{})};V(re,ze=>{t(Se),x(Es),k(()=>t(Se).type===Es.AGENT_REQUESTED&&!t(Se).description)?ze(pe):ze(Me,!1)});var Ee=d(de,2),Ae=o(Ee);se(Ae,{size:1,color:"neutral",children:(ze,Oe)=>{var He=I();oe(()=>ke(He,(t(Se),k(()=>t(Se).path)))),r(ze,He)},$$slots:{default:!0}}),r(be,he)},"header-right":(be,Ne)=>{var he=Yo(),de=o(he),re=o(de),pe=o(re);na(pe,{get rule(){return t(Se)},onSave:async(Ae,ze)=>{await z().updateRuleContent({type:Ae,path:t(Se).path,content:t(Se).content,description:ze})}});var Me=d(re,2);Ge(Me,{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$events:{click:Ae=>{Ae.stopPropagation(),L().openRule(t(Se).path)}},$$slots:{iconRight:(Ae,ze)=>{Pr(Ae,{slot:"iconRight"})}}});var Ee=d(Me,2);Ge(Ee,{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$events:{click:Ae=>{Ae.stopPropagation(),L().deleteRule(t(Se).path)}},$$slots:{iconRight:(Ae,ze)=>{Ur(Ae,{slot:"iconRight"})}}}),r(be,he)}}})}),r(ne,ve)};V(T,ne=>{n(),k(()=>n().length===0)?ne(O):ne(le,!1)},Q)};V(K,T=>{c(),n(),k(()=>c()&&n().length===0)?T(Re):T(Ve,!1)});var $e=d(Le,2),f=o($e);Ge(f,{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$events:{click:()=>L().createRule()},children:(T,Q)=>{var O=Qo(),le=o(O);Tt(le,{slot:"iconLeft"}),r(T,O)},$$slots:{default:!0}});var w=d(f,2);Ze.Root(w,{get requestClose(){return t($)},set requestClose(T){v($,T)},get focusedIndex(){return t(y)},set focusedIndex(T){Jt(v(y,T),"$importFocusedIndex",s)},children:(T,Q)=>{var O=si(),le=ge(O);Ze.Trigger(le,{children:(ve,fe)=>{Ge(ve,{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",children:(Ce,Se)=>{var be=Xo(),Ne=o(be);Ys(Ne,{slot:"iconLeft"});var he=d(Ne,2);Ms(he,{slot:"iconRight"}),r(Ce,be)},$$slots:{default:!0}})},$$slots:{default:!0}});var ne=d(le,2);Ze.Content(ne,{align:"start",side:"bottom",children:(ve,fe)=>{var Ce=ti(),Se=ge(Ce);et(Se,1,()=>G,he=>he.id,(he,de)=>{Ze.Item(he,{onSelect:()=>_(t(de)),children:(re,pe)=>{var Me=I();oe(()=>ke(Me,(t(de),k(()=>t(de).label)))),r(re,Me)},$$slots:{default:!0}})});var be=d(Se,2),Ne=he=>{var de=ei(),re=ge(de);Ze.Separator(re,{});var pe=d(re,2);Ze.Label(pe,{children:(Me,Ee)=>{var Ae=I();oe(()=>ke(Ae,(u(),k(()=>u()!==void 0?G[u()].description:G[0])))),r(Me,Ae)},$$slots:{default:!0}}),r(he,de)};V(be,he=>{u()!==void 0&&he(Ne)}),r(ve,Ce)},$$slots:{default:!0}}),r(T,O)},$$slots:{default:!0},$$legacy:!0});var X=d(F,2),q=o(X);se(q,{class:"c-section-header",size:3,color:"primary",children:(T,Q)=>{var O=I("User Guidelines");r(T,O)},$$slots:{default:!0}});var ce=d(q,2),W=d(o(ce)),ye=o(W);se(ye,{size:1,weight:"regular",children:(T,Q)=>{var O=I("Learn more");r(T,O)},$$slots:{default:!0}}),nr(d(ce,2),{get userGuidelines(){return Y()},get userGuidelinesLengthLimit(){return P()},updateUserGuideline:U()});var xe=d(R,2);(function(T,Q){Je(Q,!1);const O=Gs();let le=S(Q,"show",8,!1),ne=S(Q,"errorMessage",8,""),ve=H(""),fe=H(void 0),Ce=H(!1);function Se(){t(ve).trim()&&!t(Ce)&&(v(Ce,!0),O("create",t(ve).trim()))}function be(){t(Ce)||(O("cancel"),v(ve,""))}function Ne(he){t(Ce)||(he.key==="Enter"?(he.preventDefault(),Se()):he.key==="Escape"&&(he.preventDefault(),be()))}me(()=>(x(le()),t(fe)),()=>{le()&&t(fe)&&setTimeout(()=>{var he;return(he=t(fe))==null?void 0:he.focus()},100)}),me(()=>(x(le()),x(ne())),()=>{le()&&!ne()||v(Ce,!1)}),me(()=>(x(le()),x(ne())),()=>{le()||ne()||v(ve,"")}),nt(),tt(),tr(T,{get show(){return le()},title:"Create New Rule",ariaLabelledBy:"dialog-title",get preventBackdropClose(){return t(Ce)},get preventEscapeClose(){return t(Ce)},$$events:{cancel:be,keydown:function(re){t(Ce)||re.detail.key==="Enter"&&(re.detail.preventDefault(),Se())}},body:re=>{var pe=Po(),Me=ge(pe);se(Me,{size:2,color:"secondary",children:(Oe,He)=>{var st=I("Enter a name for the new rule file (e.g., architecture.md):");r(Oe,st)},$$slots:{default:!0}});var Ee=d(Me,2);At(Ee,{placeholder:"rule-name.md",get disabled(){return t(Ce)},get value(){return t(ve)},set value(Oe){v(ve,Oe)},get textInput(){return t(fe)},set textInput(Oe){v(fe,Oe)},$$events:{keydown:Ne},$$legacy:!0});var Ae=d(Ee,2),ze=Oe=>{Bt(Oe,{variant:"soft",color:"error",size:1,icon:st=>{ts(st,{})},children:(st,Ke)=>{var ht=I();oe(()=>ke(ht,ne())),r(st,ht)},$$slots:{icon:!0,default:!0}})};V(Ae,Oe=>{ne()&&Oe(ze)}),r(re,pe)},footer:re=>{var pe=Uo(),Me=ge(pe);Ge(Me,{variant:"solid",color:"neutral",get disabled(){return t(Ce)},$$events:{click:be},children:(ze,Oe)=>{var He=I("Cancel");r(ze,He)},$$slots:{default:!0}});var Ee=d(Me,2);const Ae=_e(()=>(t(ve),t(Ce),k(()=>!t(ve).trim()||t(Ce))));Ge(Ee,{variant:"solid",color:"accent",get disabled(){return t(Ae)},get loading(){return t(Ce)},$$events:{click:Se},children:(ze,Oe)=>{var He=I();oe(()=>ke(He,t(Ce)?"Creating...":"Create")),r(ze,He)},$$slots:{default:!0}}),r(re,pe)},$$slots:{body:!0,footer:!0}}),Be()})(xe,{get show(){return je(j,"$showCreateRuleDialog",s)},get errorMessage(){return je(p,"$createRuleError",s)},$$events:{create:function(T){L().handleCreateRuleWithName(T.detail)},cancel:function(){L().hideCreateRuleDialog()}}}),jo(d(xe,2),{get show(){return t(g)},get options(){return t(N)},get isLoading(){return t(Z)},get errorMessage(){return t(C)},get successMessage(){return t(M)},$$events:{select:async function(T){const Q=T.detail;try{v(Z,!0),v(C,"");const O=await L().processAutoImportSelection(Q);let le=`Successfully imported ${O.importedRulesCount} rule${O.importedRulesCount!==1?"s":""} from ${Q.label}`;O.duplicatesCount>0&&(le+=`, ${O.duplicatesCount} duplicate${O.duplicatesCount!==1?"s":""} skipped`),O.totalAttempted>O.importedRulesCount+O.duplicatesCount&&(le+=`, ${O.totalAttempted-O.importedRulesCount-O.duplicatesCount} failed`),v(M,le),setTimeout(()=>{v(g,!1),v(M,"")},500)}catch(O){console.error("Failed to process auto-import selection:",O),v(C,"Failed to import rules. Please try again.")}finally{v(Z,!1)}},cancel:function(){v(g,!1),v(C,""),v(M,"")}}}),r(i,te),Be(),a()}var ni=lt("<svg><!></svg>"),oi=h('<div class="account-email svelte-wku0j5"><!> <!></div>'),ii=h("<!> <!>",1);function li(i,e){Je(e,!1);const[s,a]=gt(),n=()=>je(A,"$userEmailStore",s),c=H();let u=S(e,"onSignOut",8),b=H(!1);const A=Et(Ft.key).getUserEmail();function m(){u()(),v(b,!0)}me(()=>n(),()=>{v(c,n())}),nt(),tt(),rr(i,{title:"",loading:!1,children:(l,Y)=>{var P=ii(),ee=ge(P),ie=z=>{var L=oi(),D=o(L);se(D,{size:1,color:"secondary",children:(p,g)=>{var N=I("Signed in as");r(p,N)},$$slots:{default:!0}});var j=d(D,2);se(j,{size:1,weight:"medium",children:(p,g)=>{var N=I();oe(()=>ke(N,t(c))),r(p,N)},$$slots:{default:!0}}),r(z,L)};V(ee,z=>{t(c)&&z(ie)});var U=d(ee,2);Ge(U,{get loading(){return t(b)},variant:"soft","data-testid":"sign-out-button",$$events:{click:m},children:(z,L)=>{var D=I("Sign Out");r(z,D)},$$slots:{default:!0,iconLeft:(z,L)=>{(function(D,j){const p=zt(j,["children","$$slots","$$events","$$legacy"]);var g=ni();Pt(g,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...p}));var N=o(g);Zt(N,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M352 146.2 462 256 352 365.8V312c0-13.3-10.7-24-24-24H208v-64h120c13.3 0 24-10.7 24-24zM512 256c0-11.5-4.6-22.5-12.7-30.6L383.2 109.6c-8.7-8.7-20.5-13.6-32.8-13.6-25.6 0-46.4 20.8-46.4 46.4V176h-96c-26.5 0-48 21.5-48 48v64c0 26.5 21.5 48 48 48h96v33.6c0 25.6 20.8 46.4 46.4 46.4 12.3 0 24.1-4.9 32.8-13.6l116.1-115.8c8.1-8.1 12.7-19.1 12.7-30.6M168 80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88C39.4 32 0 71.4 0 120v272c0 48.6 39.4 88 88 88h80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88c-22.1 0-40-17.9-40-40V120c0-22.1 17.9-40 40-40z"/>',!0),r(D,g)})(z,{slot:"iconLeft"})}}}),r(l,P)},$$slots:{default:!0}}),Be(),a()}class ci{constructor(e,s,a){Ie(this,"_showCreateRuleDialog",Qe(!1));Ie(this,"_createRuleError",Qe(""));Ie(this,"_extensionClient");this._host=e,this._msgBroker=s,this._rulesModel=a;const n=new js;this._extensionClient=new Zs(e,s,n)}async createRule(){this._showCreateRuleDialog.set(!0)}async handleCreateRuleWithName(e){if(e&&e.trim()){this._createRuleError.set("");try{const s=await this._rulesModel.createRule(e.trim());s&&s.path&&await this.openRule(s.path),this._extensionClient.reportAgentSessionEvent({eventName:vs.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Yt.manuallyCreated,numFiles:1,source:""}}}),this.hideCreateRuleDialog()}catch{const a=`Failed to create rule "${e.trim()}"`;this._createRuleError.set(a)}}else this.hideCreateRuleDialog()}async openRule(e){try{const s=await this._rulesModel.getWorkspaceRoot();e===Ns?this._extensionClient.openFile({repoRoot:s,pathName:Ns}):this._extensionClient.openFile({repoRoot:s,pathName:`${Dr}/${qr}/${e}`})}catch(s){console.error("Failed to open rule:",s)}}async deleteRule(e){try{await this._extensionClient.openConfirmationModal({title:"Delete Rule",message:"Are you sure you want to delete this rule?",confirmButtonText:"Delete",cancelButtonText:"Cancel"})&&await this._rulesModel.deleteRule(e)}catch(s){console.error("Failed to delete rule:",s)}}async selectFileToImport(){try{const e=await this._msgBroker.send({type:qe.triggerImportDialogRequest},1e5);if(e.data.selectedPaths&&e.data.selectedPaths.length>0){const s=await this._rulesModel.processSelectedPaths(e.data.selectedPaths);this._showImportNotification(s),this._reportSelectedImportMetrics(s)}}catch(e){console.error("Failed to import files:",e)}}async getAutoImportOptions(){return await this._rulesModel.getAutoImportOptions()}async processAutoImportSelection(e){const s=await this._rulesModel.processAutoImportSelection(e);return this._showImportNotification(s),this._reportAutoImportMetrics(s),s}_showImportNotification(e){let s;e.importedRulesCount===0?s=e.source?`No new rules imported from ${e.source}`:"No new rules imported":(s=`Successfully imported ${e.importedRulesCount} rule${e.importedRulesCount!==1?"s":""}`,e.duplicatesCount&&e.duplicatesCount>0&&(s+=` and skipped ${e.duplicatesCount} duplicate${e.duplicatesCount!==1?"s":""}`)),this._extensionClient.showNotification({message:s,type:e.importedRulesCount>0?"info":"warning"})}_reportSelectedImportMetrics(e){const s=e.directoryOrFile==="directory"?Yt.selectedDirectory:(e.directoryOrFile,Yt.selectedFile);this._extensionClient.reportAgentSessionEvent({eventName:vs.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:s,numFiles:e.importedRulesCount,source:""}}})}_reportAutoImportMetrics(e){this._extensionClient.reportAgentSessionEvent({eventName:vs.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Yt.auto,numFiles:e.importedRulesCount,source:e.source}}})}getShowCreateRuleDialog(){return this._showCreateRuleDialog}getCreateRuleError(){return this._createRuleError}hideCreateRuleDialog(){this._showCreateRuleDialog.set(!1),this._createRuleError.set("")}}var di=h('<span slot="content"><!></span>');function ui(i,e){Je(e,!1);const[s,a]=gt(),n=()=>je(we,"$guidelines",s),c=()=>je(Z,"$settingsComponentSupported",s),u=()=>je(C,"$enableAgentMode",s),b=()=>je(y,"$terminalSettingsStore",s),A=H(),m=H(),l=H(),Y=H(),P=H(),ee=new Ft(Ye),ie=new Wt(Ye),U=new Sa(Ye),z=new br(Ye),L=new js,D=new Zs(Ye,z,L),j=new Cs(z),p=new jt(z),g=new Hr(z),N=new ci(Ye,z,g);z.registerConsumer(g),Kt(Cs.key,j),Kt(jt.key,p),Kt(Ft.key,ee),jr(D),function(f){Kt(ar,f)}(ie);const Z=ee.getSettingsComponentSupported(),C=ee.getEnableAgentMode(),M=ee.getEnableAgentSwarmMode(),G=ee.getHasEverUsedRemoteAgent();z.registerConsumer(ee),z.registerConsumer(ie),z.registerConsumer(U);const y=U.getTerminalSettings();let $=H();const _=[];let te=!0;function R(f,w){const X=window.setTimeout(()=>{te&&f()},w);return _.push(X),X}const F={handleMessageFromExtension(f){return f.data&&f.data.type===qe.navigateToSettingsSection?(f.data.data&&typeof f.data.data=="string"&&J(f.data.data),!0):!1}};function E(){const f=window.location.hash.substring(1);f&&J(f)}function J(f){v($,void 0);const w=document.getElementById(f);if(w){const q=w.closest("[data-section]"),ce=q==null?void 0:q.getAttribute("data-section");v($,ce??"tools")}else v($,"tools");function X(q=0,ce=20){const W=document.getElementById(f);W?(W.scrollIntoView({behavior:"smooth",block:"center"}),R(()=>{W.classList.remove("settings-section-highlight"),W.offsetHeight,W.classList.add("settings-section-highlight"),R(()=>{W.classList.remove("settings-section-highlight")},1050)},500)):q<ce&&R(()=>X(q+1,ce),200)}R(()=>X(),100)}z.registerConsumer(F),Hs(()=>{R(E,100);const f=()=>{E()};return window.addEventListener("hashchange",f),()=>{te=!1,_.forEach(clearTimeout),window.removeEventListener("hashchange",f)}});const B=ee.getDisplayableTools(),we=ee.getGuidelines();function ue(f){const w=f.trim();return!(t(m)&&w.length>t(m))&&(ee.updateLocalUserGuidelines(w),Ye.postMessage({type:qe.updateUserGuidelines,data:f}),!0)}function ae(f){Ye.postMessage({type:qe.toolConfigStartOAuth,data:f}),ee.startPolling()}async function Le(f){await D.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${f.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&Ye.postMessage({type:qe.toolConfigRevokeAccess,data:{toolId:f.identifier}})}function K(f){U.updateSelectedShell(f)}function Re(f){U.updateStartupScript(f)}function Ve(f,w){Ye.postMessage({type:qe.toolApprovalConfigSetRequest,data:{toolId:f,approvalConfig:w}})}function $e(){Ye.postMessage({type:qe.signOut})}hs(()=>{ee.dispose(),j.dispose(),p.dispose()}),ee.notifyLoaded(),Ye.postMessage({type:qe.getOrientationStatus}),Ye.postMessage({type:qe.settingsPanelLoaded}),me(()=>n(),()=>{var f;v(A,(f=n().userGuidelines)==null?void 0:f.contents)}),me(()=>n(),()=>{var f;v(m,(f=n().userGuidelines)==null?void 0:f.lengthLimit)}),me(()=>n(),()=>{var f,w;v(l,(w=(f=n().workspaceGuidelines)==null?void 0:f[0])==null?void 0:w.lengthLimit)}),me(()=>n(),()=>{var f,w;v(Y,((w=(f=n().workspaceGuidelines)==null?void 0:f[0])==null?void 0:w.contents)||"")}),me(()=>(c(),Ds),()=>{v(P,[c().remoteTools?Ht("Tools","",an,"section-tools"):void 0,c().userGuidelines&&!c().rules?Ht("User Guidelines","Guidelines for Augment Chat to follow.",Io,"guidelines"):void 0,c().rules?Ht("Rules and User Guidelines","",No,"guidelines"):void 0,c().workspaceContext?Ht("Context","",on,"context"):void 0,Ht("Account","Manage your Augment account settings.",Ds,"account")].filter(Boolean))}),me(()=>(t(P),t($)),()=>{var f;t(P).length>1&&!t($)&&v($,(f=t(P)[0])==null?void 0:f.id)}),nt(),tt(),mt("message",Ss,function(...f){var w;(w=z.onMessageFromExtension)==null||w.apply(this,f)}),Kr.Root(i,{children:(f,w)=>{sn(f,{get items(){return t(P)},mode:"tree",class:"c-settings-navigation",get selectedId(){return t($)},$$slots:{content:(X,q)=>{var ce=di();const W=_e(()=>q.item);var ye=o(ce),xe=Q=>{},T=(Q,O)=>{var le=ve=>{Va(ve,{})},ne=(ve,fe)=>{var Ce=be=>{var Ne=Xe(),he=ge(Ne),de=pe=>{ai(pe,{get userGuidelines(){return t(A)},get userGuidelinesLengthLimit(){return t(m)},get workspaceGuidelinesLengthLimit(){return t(l)},get workspaceGuidelinesContent(){return t(Y)},updateUserGuideline:ue,get rulesModel(){return g},get rulesController(){return N}})},re=pe=>{nr(pe,{get userGuidelines(){return t(A)},get userGuidelinesLengthLimit(){return t(m)},updateUserGuideline:ue})};V(he,pe=>{c(),k(()=>c().rules)?pe(de):pe(re,!1)}),r(be,Ne)},Se=(be,Ne)=>{var he=re=>{li(re,{onSignOut:$e})},de=re=>{const pe=_e(()=>(u(),c(),k(()=>u()&&c().mcpServerList))),Me=_e(()=>(u(),c(),k(()=>u()&&c().mcpServerImport)));Lo(re,{get tools(){return je(B,"$displayableTools",s)},onAuthenticate:ae,onRevokeAccess:Le,onToolApprovalConfigChange:Ve,onMCPServerAdd:Ee=>ie.addServer(Ee),onMCPServerSave:Ee=>ie.updateServer(Ee),onMCPServerDelete:Ee=>ie.deleteServer(Ee),onMCPServerToggleDisable:Ee=>ie.toggleDisabledServer(Ee),onMCPServerJSONImport:Ee=>ie.importServersFromJSON(Ee),get isMCPEnabled(){return t(pe)},get isMCPImportEnabled(){return t(Me)},get supportedShells(){return b(),k(()=>b().supportedShells)},get selectedShell(){return b(),k(()=>b().selectedShell)},get startupScript(){return b(),k(()=>b().startupScript)},onShellSelect:K,onStartupScriptChange:Re,get isTerminalEnabled(){return c(),k(()=>c().terminal)},isSoundCategoryEnabled:!0,get isAgentCategoryEnabled(){return u()},get isSwarmModeFeatureFlagEnabled(){return je(M,"$enableAgentSwarmMode",s)},get hasEverUsedRemoteAgent(){return je(G,"$hasEverUsedRemoteAgent",s)}})};V(be,re=>{x(t(W)),k(()=>{var pe;return((pe=t(W))==null?void 0:pe.id)==="account"})?re(he):re(de,!1)},Ne)};V(ve,be=>{x(t(W)),k(()=>{var Ne;return((Ne=t(W))==null?void 0:Ne.id)==="guidelines"})?be(Ce):be(Se,!1)},fe)};V(Q,ve=>{x(t(W)),k(()=>{var fe;return((fe=t(W))==null?void 0:fe.id)==="context"})?ve(le):ve(ne,!1)},O)};V(ye,Q=>{x(ws),x(t(W)),k(()=>!ws(t(W)))?Q(xe):Q(T,!1)}),r(X,ce)}}})},$$slots:{default:!0}}),Be(),a()}(async function(){Ye&&Ye.initialize&&await Ye.initialize(),Cr(ui,{target:document.getElementById("app")})})();
