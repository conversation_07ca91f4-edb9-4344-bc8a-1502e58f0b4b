var mt=Object.defineProperty;var Te=i=>{throw TypeError(i)};var Bt=(i,e,t)=>e in i?mt(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t;var A=(i,e,t)=>Bt(i,typeof e!="symbol"?e+"":e,t),wt=(i,e,t)=>e.has(i)||Te("Cannot "+t);var _e=(i,e,t)=>e.has(i)?Te("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(i):e.set(i,t);var oe=(i,e,t)=>(wt(i,e,"access private method"),t);import{x,I as ge,Q as f,P as N,m as X,J as fe,K as Z,L as _,B as S,a as me,z as v,b as p,G as b,y as B,o as E,D as De,u as w,M as ut,N as M,O as ue,R as ke,a1 as rt,E as ce,a3 as K,a6 as vt,a8 as Se,ag as yt,A as Ae,F as Ie,f as Be,aU as $t}from"./legacy-AoIeRrIA.js";import{p as g,g as I,j as zt,i as Le,s as Ee,k as st,a as Rt,b as Tt,l as _t}from"./SpinnerAugment-mywmfXFR.js";import{e as Y,i as ee}from"./host-qgbK079d.js";import{c as St,a as It,b as Lt,S as qt}from"./index-BdF7sLLk.js";import{e as it}from"./Filespan-BqOh8yIt.js";import{b as h}from"./CardAugment-DwIptXof.js";import{h as ot}from"./IconButtonAugment-DZyIKjh7.js";import"./toggleHighContrast-Cb9MCs64.js";import{c as Zt}from"./index-CJlvVQMt.js";function qe(...i){return"/"+i.flatMap(e=>e.split("/")).filter(e=>!!e).join("/")}function Ze(i){return i.startsWith("/")||i.startsWith("#")}function Pt(i,e){x(e,!1);let t=g(e,"token",8),n=g(e,"options",8);const u=void 0;let r=X();ge(()=>(f(t()),f(n())),()=>{var l;N(r,(l=t().text,n().slugger.slug(l).replace(/--+/g,"-")))}),fe(),Z();var s=_(),o=S(s);return it(o,()=>`h${t().depth}`,!1,(l,c)=>{me(l,()=>({id:v(r)}));var a=_(),F=S(a);I(F,e,"default",{},null),p(c,a)}),p(i,s),h(e,"renderers",u),b({renderers:u})}var Ot=B("<blockquote><!></blockquote>");function Mt(i,e){x(e,!1);const t=void 0,n=void 0,u=void 0;var r=Ot(),s=E(r);return I(s,e,"default",{},null),p(i,r),h(e,"token",t),h(e,"options",n),h(e,"renderers",u),b({token:t,options:n,renderers:u})}function he(i,e){let t=g(e,"tokens",8),n=g(e,"renderers",8),u=g(e,"options",8);var r=_(),s=S(r),o=l=>{var c=_(),a=S(c);Y(a,1,t,ee,(F,D)=>{lt(F,{get token(){return v(D)},get renderers(){return n()},get options(){return u()}})}),p(l,c)};De(s,l=>{t()&&l(o)}),p(i,r)}function lt(i,e){x(e,!1);let t=g(e,"token",8),n=g(e,"renderers",8),u=g(e,"options",8);Z();var r=_(),s=S(r),o=l=>{var c=_(),a=S(c);St(a,()=>n()[t().type],(F,D)=>{D(F,{get token(){return t()},get options(){return u()},get renderers(){return n()},children:(d,y)=>{var z=_(),R=S(z),O=L=>{he(L,{get tokens(){return f(t()),w(()=>t().tokens)},get renderers(){return n()},get options(){return u()}})},Q=L=>{var H=ut();M(()=>ue(H,(f(t()),w(()=>t().raw)))),p(L,H)};De(R,L=>{f(t()),w(()=>"tokens"in t()&&t().tokens)?L(O):L(Q,!1)}),p(d,z)},$$slots:{default:!0}})}),p(l,c)};De(s,l=>{f(n()),f(t()),w(()=>n()[t().type])&&l(o)}),p(i,r),b()}function Qt(i,e){x(e,!1);let t=g(e,"token",8),n=g(e,"options",8),u=g(e,"renderers",8),r=X();ge(()=>f(t()),()=>{N(r,t().ordered?"ol":"ul")}),fe(),Z();var s=_(),o=S(s);it(o,()=>v(r),!1,(l,c)=>{me(l,()=>({start:(f(t()),w(()=>t().start||1))}));var a=_(),F=S(a);Y(F,1,()=>(f(t()),w(()=>t().items)),ee,(D,d)=>{const y=ke(()=>(v(d),w(()=>({...v(d)}))));lt(D,{get token(){return v(y)},get options(){return n()},get renderers(){return u()}})}),p(c,a)}),p(i,s),b()}var Ht=B("<li><!></li>");function jt(i,e){x(e,!1);const t=void 0,n=void 0,u=void 0;var r=Ht(),s=E(r);return I(s,e,"default",{},null),p(i,r),h(e,"token",t),h(e,"options",n),h(e,"renderers",u),b({token:t,options:n,renderers:u})}var Vt=B("<br/>");function Wt(i,e){x(e,!1);const t=void 0,n=void 0,u=void 0;var r=Vt();return p(i,r),h(e,"token",t),h(e,"options",n),h(e,"renderers",u),b({token:t,options:n,renderers:u})}var Ut=B("<pre><code> </code></pre>");function Nt(i,e){x(e,!1);let t=g(e,"token",8);const n=void 0,u=void 0;Z();var r=Ut(),s=E(r),o=E(s);return M(()=>{rt(s,1,(f(t()),w(()=>`lang-${t().lang}`))),ue(o,(f(t()),w(()=>t().text)))}),p(i,r),h(e,"options",n),h(e,"renderers",u),b({options:n,renderers:u})}var Xt=B("<code> </code>");function Gt(i,e){x(e,!1);let t=g(e,"token",8);const n=void 0,u=void 0;Z();var r=Xt(),s=E(r);return M(o=>ue(s,o),[()=>(f(t()),w(()=>t().raw.slice(1,t().raw.length-1)))],ke),p(i,r),h(e,"options",n),h(e,"renderers",u),b({options:n,renderers:u})}var Jt=B('<th scope="col"><!></th>'),Kt=B("<td><!></td>"),Yt=B("<tr></tr>"),en=B("<table><thead><tr></tr></thead><tbody></tbody></table>");function tn(i,e){x(e,!1);let t=g(e,"token",8),n=g(e,"options",8),u=g(e,"renderers",8);Z();var r=en(),s=E(r),o=E(s);Y(o,5,()=>(f(t()),w(()=>t().header)),ee,(c,a)=>{var F=Jt();he(E(F),{get tokens(){return v(a),w(()=>v(a).tokens)},get options(){return n()},get renderers(){return u()}}),p(c,F)});var l=ce(s);Y(l,5,()=>(f(t()),w(()=>t().rows)),ee,(c,a)=>{var F=Yt();Y(F,5,()=>v(a),ee,(D,d)=>{var y=Kt();he(E(y),{get tokens(){return v(d),w(()=>v(d).tokens)},get options(){return n()},get renderers(){return u()}}),p(D,y)}),p(c,F)}),p(i,r),b()}function nn(i,e){x(e,!1);let t=g(e,"token",8);const n=void 0,u=void 0;Z();var r=_(),s=S(r);return ot(s,()=>(f(t()),w(()=>t().text))),p(i,r),h(e,"options",n),h(e,"renderers",u),b({options:n,renderers:u})}var un=B("<p><!></p>");function rn(i,e){x(e,!1);const t=void 0,n=void 0,u=void 0;var r=un(),s=E(r);return I(s,e,"default",{},null),p(i,r),h(e,"token",t),h(e,"options",n),h(e,"renderers",u),b({token:t,options:n,renderers:u})}var sn=B("<a><!></a>");function on(i,e){x(e,!1);let t=g(e,"token",8),n=g(e,"options",8);const u=void 0;Z();var r=sn(),s=E(r);return I(s,e,"default",{},null),M(o=>{K(r,"href",o),K(r,"title",(f(t()),w(()=>t().title)))},[()=>(f(Ze),f(t()),f(qe),f(n()),w(()=>Ze(t().href)?qe(n().baseUrl,t().href):t().href))],ke),p(i,r),h(e,"renderers",u),b({renderers:u})}function ln(i,e){x(e,!1);const t=void 0,n=void 0,u=void 0;var r=_(),s=S(r);return I(s,e,"default",{},null),p(i,r),h(e,"token",t),h(e,"options",n),h(e,"renderers",u),b({token:t,options:n,renderers:u})}var an=B("<dfn><!></dfn>");function cn(i,e){x(e,!1);const t=void 0,n=void 0,u=void 0;var r=an(),s=E(r);return I(s,e,"default",{},null),p(i,r),h(e,"token",t),h(e,"options",n),h(e,"renderers",u),b({token:t,options:n,renderers:u})}var Dn=B("<del><!></del>");function hn(i,e){x(e,!1);const t=void 0,n=void 0,u=void 0;var r=Dn(),s=E(r);return I(s,e,"default",{},null),p(i,r),h(e,"token",t),h(e,"options",n),h(e,"renderers",u),b({token:t,options:n,renderers:u})}var Fn=B("<em><!></em>");function pn(i,e){x(e,!1);const t=void 0,n=void 0,u=void 0;var r=Fn(),s=E(r);return I(s,e,"default",{},null),p(i,r),h(e,"token",t),h(e,"options",n),h(e,"renderers",u),b({token:t,options:n,renderers:u})}var dn=B("<hr/>");function gn(i,e){x(e,!1);const t=void 0,n=void 0,u=void 0;var r=dn();return p(i,r),h(e,"token",t),h(e,"options",n),h(e,"renderers",u),b({token:t,options:n,renderers:u})}var fn=B("<strong><!></strong>");function kn(i,e){x(e,!1);const t=void 0,n=void 0,u=void 0;var r=fn(),s=E(r);return I(s,e,"default",{},null),p(i,r),h(e,"token",t),h(e,"options",n),h(e,"renderers",u),b({token:t,options:n,renderers:u})}var Cn=B('<img class="markdown-image svelte-z38cge"/>');function An(i,e){x(e,!1);let t=g(e,"token",8);const n=void 0,u=void 0;Z();var r=Cn();return M(()=>{K(r,"src",(f(t()),w(()=>t().href))),K(r,"title",(f(t()),w(()=>t().title))),K(r,"alt",(f(t()),w(()=>t().text)))}),p(i,r),h(e,"options",n),h(e,"renderers",u),b({options:n,renderers:u})}function Pe(i,e){x(e,!1);const t=void 0,n=void 0,u=void 0;var r=_(),s=S(r);return I(s,e,"default",{},null),p(i,r),h(e,"token",t),h(e,"options",n),h(e,"renderers",u),b({token:t,options:n,renderers:u})}function En(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let W={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function Oe(i){W=i}const at=/[&<>"']/,xn=new RegExp(at.source,"g"),ct=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,bn=new RegExp(ct.source,"g"),mn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Me=i=>mn[i];function T(i,e){if(e){if(at.test(i))return i.replace(xn,Me)}else if(ct.test(i))return i.replace(bn,Me);return i}const Bn=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function wn(i){return i.replace(Bn,(e,t)=>(t=t.toLowerCase())==="colon"?":":t.charAt(0)==="#"?t.charAt(1)==="x"?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):"")}const vn=/(^|[^\[])\^/g;function C(i,e){let t=typeof i=="string"?i:i.source;e=e||"";const n={replace:(u,r)=>{let s=typeof r=="string"?r:r.source;return s=s.replace(vn,"$1"),t=t.replace(u,s),n},getRegex:()=>new RegExp(t,e)};return n}function Qe(i){try{i=encodeURI(i).replace(/%25/g,"%")}catch{return null}return i}const te={exec:()=>null};function He(i,e){const t=i.replace(/\|/g,(u,r,s)=>{let o=!1,l=r;for(;--l>=0&&s[l]==="\\";)o=!o;return o?"|":" |"}).split(/ \|/);let n=0;if(t[0].trim()||t.shift(),t.length>0&&!t[t.length-1].trim()&&t.pop(),e)if(t.length>e)t.splice(e);else for(;t.length<e;)t.push("");for(;n<t.length;n++)t[n]=t[n].trim().replace(/\\\|/g,"|");return t}function le(i,e,t){const n=i.length;if(n===0)return"";let u=0;for(;u<n&&i.charAt(n-u-1)===e;)u++;return i.slice(0,n-u)}function je(i,e,t,n){const u=e.href,r=e.title?T(e.title):null,s=i[1].replace(/\\([\[\]])/g,"$1");if(i[0].charAt(0)!=="!"){n.state.inLink=!0;const o={type:"link",raw:t,href:u,title:r,text:s,tokens:n.inlineTokens(s)};return n.state.inLink=!1,o}return{type:"image",raw:t,href:u,title:r,text:T(s)}}class Fe{constructor(e){A(this,"options");A(this,"rules");A(this,"lexer");this.options=e||W}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const n=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:le(n,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const n=t[0],u=function(r,s){const o=r.match(/^(\s+)(?:```)/);if(o===null)return s;const l=o[1];return s.split(`
`).map(c=>{const a=c.match(/^\s+/);if(a===null)return c;const[F]=a;return F.length>=l.length?c.slice(l.length):c}).join(`
`)}(n,t[3]||"");return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:u}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(/#$/.test(n)){const u=le(n,"#");this.options.pedantic?n=u.trim():u&&!/ $/.test(u)||(n=u.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const n=le(t[0].replace(/^ *>[ \t]?/gm,""),`
`),u=this.lexer.state.top;this.lexer.state.top=!0;const r=this.lexer.blockTokens(n);return this.lexer.state.top=u,{type:"blockquote",raw:t[0],tokens:r,text:n}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const u=n.length>1,r={type:"list",raw:"",ordered:u,start:u?+n.slice(0,-1):"",loose:!1,items:[]};n=u?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=u?n:"[*+-]");const s=new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`);let o="",l="",c=!1;for(;e;){let a=!1;if(!(t=s.exec(e))||this.rules.block.hr.test(e))break;o=t[0],e=e.substring(o.length);let F=t[2].split(`
`,1)[0].replace(/^\t+/,O=>" ".repeat(3*O.length)),D=e.split(`
`,1)[0],d=0;this.options.pedantic?(d=2,l=F.trimStart()):(d=t[2].search(/[^ ]/),d=d>4?1:d,l=F.slice(d),d+=t[1].length);let y=!1;if(!F&&/^ *$/.test(D)&&(o+=D+`
`,e=e.substring(D.length+1),a=!0),!a){const O=new RegExp(`^ {0,${Math.min(3,d-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),Q=new RegExp(`^ {0,${Math.min(3,d-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),L=new RegExp(`^ {0,${Math.min(3,d-1)}}(?:\`\`\`|~~~)`),H=new RegExp(`^ {0,${Math.min(3,d-1)}}#`);for(;e;){const U=e.split(`
`,1)[0];if(D=U,this.options.pedantic&&(D=D.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),L.test(D)||H.test(D)||O.test(D)||Q.test(e))break;if(D.search(/[^ ]/)>=d||!D.trim())l+=`
`+D.slice(d);else{if(y||F.search(/[^ ]/)>=4||L.test(F)||H.test(F)||Q.test(F))break;l+=`
`+D}y||D.trim()||(y=!0),o+=U+`
`,e=e.substring(U.length+1),F=D.slice(d)}}r.loose||(c?r.loose=!0:/\n *\n *$/.test(o)&&(c=!0));let z,R=null;this.options.gfm&&(R=/^\[[ xX]\] /.exec(l),R&&(z=R[0]!=="[ ] ",l=l.replace(/^\[[ xX]\] +/,""))),r.items.push({type:"list_item",raw:o,task:!!R,checked:z,loose:!1,text:l,tokens:[]}),r.raw+=o}r.items[r.items.length-1].raw=o.trimEnd(),r.items[r.items.length-1].text=l.trimEnd(),r.raw=r.raw.trimEnd();for(let a=0;a<r.items.length;a++)if(this.lexer.state.top=!1,r.items[a].tokens=this.lexer.blockTokens(r.items[a].text,[]),!r.loose){const F=r.items[a].tokens.filter(d=>d.type==="space"),D=F.length>0&&F.some(d=>/\n.*\n/.test(d.raw));r.loose=D}if(r.loose)for(let a=0;a<r.items.length;a++)r.items[a].loose=!0;return r}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const n=t[1].toLowerCase().replace(/\s+/g," "),u=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",r=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:u,title:r}}}table(e){const t=this.rules.block.table.exec(e);if(!t||!/[:|]/.test(t[2]))return;const n=He(t[1]),u=t[2].replace(/^\||\| *$/g,"").split("|"),r=t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split(`
`):[],s={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===u.length){for(const o of u)/^ *-+: *$/.test(o)?s.align.push("right"):/^ *:-+: *$/.test(o)?s.align.push("center"):/^ *:-+ *$/.test(o)?s.align.push("left"):s.align.push(null);for(const o of n)s.header.push({text:o,tokens:this.lexer.inline(o)});for(const o of r)s.rows.push(He(o,s.header.length).map(l=>({text:l,tokens:this.lexer.inline(l)})));return s}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:T(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const n=t[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;const s=le(n.slice(0,-1),"\\");if((n.length-s.length)%2==0)return}else{const s=function(o,l){if(o.indexOf(l[1])===-1)return-1;let c=0;for(let a=0;a<o.length;a++)if(o[a]==="\\")a++;else if(o[a]===l[0])c++;else if(o[a]===l[1]&&(c--,c<0))return a;return-1}(t[2],"()");if(s>-1){const o=(t[0].indexOf("!")===0?5:4)+t[1].length+s;t[2]=t[2].substring(0,s),t[0]=t[0].substring(0,o).trim(),t[3]=""}}let u=t[2],r="";if(this.options.pedantic){const s=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(u);s&&(u=s[1],r=s[3])}else r=t[3]?t[3].slice(1,-1):"";return u=u.trim(),/^</.test(u)&&(u=this.options.pedantic&&!/>$/.test(n)?u.slice(1):u.slice(1,-1)),je(t,{href:u&&u.replace(this.rules.inline.anyPunctuation,"$1"),title:r&&r.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const u=t[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!u){const r=n[0].charAt(0);return{type:"text",raw:r,text:r}}return je(n,u,n[0],this.lexer)}}emStrong(e,t,n=""){let u=this.rules.inline.emStrongLDelim.exec(e);if(u&&!(u[3]&&n.match(/[\p{L}\p{N}]/u))&&(!(u[1]||u[2])||!n||this.rules.inline.punctuation.exec(n))){const r=[...u[0]].length-1;let s,o,l=r,c=0;const a=u[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(a.lastIndex=0,t=t.slice(-1*e.length+r);(u=a.exec(t))!=null;){if(s=u[1]||u[2]||u[3]||u[4]||u[5]||u[6],!s)continue;if(o=[...s].length,u[3]||u[4]){l+=o;continue}if((u[5]||u[6])&&r%3&&!((r+o)%3)){c+=o;continue}if(l-=o,l>0)continue;o=Math.min(o,o+l+c);const F=[...u[0]][0].length,D=e.slice(0,r+u.index+F+o);if(Math.min(r,o)%2){const y=D.slice(1,-1);return{type:"em",raw:D,text:y,tokens:this.lexer.inlineTokens(y)}}const d=D.slice(2,-2);return{type:"strong",raw:D,text:d,tokens:this.lexer.inlineTokens(d)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(/\n/g," ");const u=/[^ ]/.test(n),r=/^ /.test(n)&&/ $/.test(n);return u&&r&&(n=n.substring(1,n.length-1)),n=T(n,!0),{type:"codespan",raw:t[0],text:n}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let n,u;return t[2]==="@"?(n=T(t[1]),u="mailto:"+n):(n=T(t[1]),u=n),{type:"link",raw:t[0],text:n,href:u,tokens:[{type:"text",raw:n,text:n}]}}}url(e){var n;let t;if(t=this.rules.inline.url.exec(e)){let u,r;if(t[2]==="@")u=T(t[0]),r="mailto:"+u;else{let s;do s=t[0],t[0]=((n=this.rules.inline._backpedal.exec(t[0]))==null?void 0:n[0])??"";while(s!==t[0]);u=T(t[0]),r=t[1]==="www."?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:u,href:r,tokens:[{type:"text",raw:u,text:u}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){let n;return n=this.lexer.state.inRawBlock?t[0]:T(t[0]),{type:"text",raw:t[0],text:n}}}}const re=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Dt=/(?:[*+-]|\d{1,9}[.)])/,ht=C(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,Dt).replace(/blockCode/g,/ {4}/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).getRegex(),we=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,ve=/(?!\s*\])(?:\\.|[^\[\]\\])+/,yn=C(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",ve).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),$n=C(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Dt).getRegex(),Ce="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",ye=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,zn=C("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",ye).replace("tag",Ce).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Ve=C(we).replace("hr",re).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ce).getRegex(),$e={blockquote:C(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Ve).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:yn,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:re,html:zn,lheading:ht,list:$n,newline:/^(?: *(?:\n|$))+/,paragraph:Ve,table:te,text:/^[^\n]+/},We=C("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",re).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ce).getRegex(),Rn={...$e,table:We,paragraph:C(we).replace("hr",re).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",We).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ce).getRegex()},Tn={...$e,html:C(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",ye).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:te,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:C(we).replace("hr",re).replace("heading",` *#{1,6} *[^
]`).replace("lheading",ht).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Ft=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,pt=/^( {2,}|\\)\n(?!\s*$)/,se="\\p{P}\\p{S}",_n=C(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,se).getRegex(),Sn=C(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,se).getRegex(),In=C("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,se).getRegex(),Ln=C("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,se).getRegex(),qn=C(/\\([punct])/,"gu").replace(/punct/g,se).getRegex(),Zn=C(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Pn=C(ye).replace("(?:-->|$)","-->").getRegex(),On=C("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Pn).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),pe=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Mn=C(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",pe).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Ue=C(/^!?\[(label)\]\[(ref)\]/).replace("label",pe).replace("ref",ve).getRegex(),Ne=C(/^!?\[(ref)\](?:\[\])?/).replace("ref",ve).getRegex(),ze={_backpedal:te,anyPunctuation:qn,autolink:Zn,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:pt,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:te,emStrongLDelim:Sn,emStrongRDelimAst:In,emStrongRDelimUnd:Ln,escape:Ft,link:Mn,nolink:Ne,punctuation:_n,reflink:Ue,reflinkSearch:C("reflink|nolink(?!\\()","g").replace("reflink",Ue).replace("nolink",Ne).getRegex(),tag:On,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:te},Qn={...ze,link:C(/^!?\[(label)\]\((.*?)\)/).replace("label",pe).getRegex(),reflink:C(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",pe).getRegex()},xe={...ze,escape:C(Ft).replace("])","~|])").getRegex(),url:C(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Hn={...xe,br:C(pt).replace("{2,}","*").getRegex(),text:C(xe.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},ae={normal:$e,gfm:Rn,pedantic:Tn},J={normal:ze,gfm:xe,breaks:Hn,pedantic:Qn};class q{constructor(e){A(this,"tokens");A(this,"options");A(this,"state");A(this,"tokenizer");A(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||W,this.options.tokenizer=this.options.tokenizer||new Fe,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:ae.normal,inline:J.normal};this.options.pedantic?(t.block=ae.pedantic,t.inline=J.pedantic):this.options.gfm&&(t.block=ae.gfm,this.options.breaks?t.inline=J.breaks:t.inline=J.gfm),this.tokenizer.rules=t}static get rules(){return{block:ae,inline:J}}static lex(e,t){return new q(t).lex(e)}static lexInline(e,t){return new q(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[]){let n,u,r,s;for(e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,(o,l,c)=>l+"    ".repeat(c.length));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(o=>!!(n=o.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.space(e))e=e.substring(n.raw.length),n.raw.length===1&&t.length>0?t[t.length-1].raw+=`
`:t.push(n);else if(n=this.tokenizer.code(e))e=e.substring(n.raw.length),u=t[t.length-1],!u||u.type!=="paragraph"&&u.type!=="text"?t.push(n):(u.raw+=`
`+n.raw,u.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=u.text);else if(n=this.tokenizer.fences(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.heading(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.hr(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.blockquote(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.list(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.html(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.def(e))e=e.substring(n.raw.length),u=t[t.length-1],!u||u.type!=="paragraph"&&u.type!=="text"?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(u.raw+=`
`+n.raw,u.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=u.text);else if(n=this.tokenizer.table(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.lheading(e))e=e.substring(n.raw.length),t.push(n);else{if(r=e,this.options.extensions&&this.options.extensions.startBlock){let o=1/0;const l=e.slice(1);let c;this.options.extensions.startBlock.forEach(a=>{c=a.call({lexer:this},l),typeof c=="number"&&c>=0&&(o=Math.min(o,c))}),o<1/0&&o>=0&&(r=e.substring(0,o+1))}if(this.state.top&&(n=this.tokenizer.paragraph(r)))u=t[t.length-1],s&&u.type==="paragraph"?(u.raw+=`
`+n.raw,u.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=u.text):t.push(n),s=r.length!==e.length,e=e.substring(n.raw.length);else if(n=this.tokenizer.text(e))e=e.substring(n.raw.length),u=t[t.length-1],u&&u.type==="text"?(u.raw+=`
`+n.raw,u.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=u.text):t.push(n);else if(e){const o="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(o);break}throw new Error(o)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n,u,r,s,o,l,c=e;if(this.tokens.links){const a=Object.keys(this.tokens.links);if(a.length>0)for(;(s=this.tokenizer.rules.inline.reflinkSearch.exec(c))!=null;)a.includes(s[0].slice(s[0].lastIndexOf("[")+1,-1))&&(c=c.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(s=this.tokenizer.rules.inline.blockSkip.exec(c))!=null;)c=c.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(s=this.tokenizer.rules.inline.anyPunctuation.exec(c))!=null;)c=c.slice(0,s.index)+"++"+c.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(o||(l=""),o=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(a=>!!(n=a.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.escape(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(e))e=e.substring(n.raw.length),u=t[t.length-1],u&&n.type==="text"&&u.type==="text"?(u.raw+=n.raw,u.text+=n.text):t.push(n);else if(n=this.tokenizer.link(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(n.raw.length),u=t[t.length-1],u&&n.type==="text"&&u.type==="text"?(u.raw+=n.raw,u.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(e,c,l))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(e))e=e.substring(n.raw.length),t.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(e))){if(r=e,this.options.extensions&&this.options.extensions.startInline){let a=1/0;const F=e.slice(1);let D;this.options.extensions.startInline.forEach(d=>{D=d.call({lexer:this},F),typeof D=="number"&&D>=0&&(a=Math.min(a,D))}),a<1/0&&a>=0&&(r=e.substring(0,a+1))}if(n=this.tokenizer.inlineText(r))e=e.substring(n.raw.length),n.raw.slice(-1)!=="_"&&(l=n.raw.slice(-1)),o=!0,u=t[t.length-1],u&&u.type==="text"?(u.raw+=n.raw,u.text+=n.text):t.push(n);else if(e){const a="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(a);break}throw new Error(a)}}else e=e.substring(n.raw.length),t.push(n);return t}}class de{constructor(e){A(this,"options");this.options=e||W}code(e,t,n){var r;const u=(r=(t||"").match(/^\S*/))==null?void 0:r[0];return e=e.replace(/\n$/,"")+`
`,u?'<pre><code class="language-'+T(u)+'">'+(n?e:T(e,!0))+`</code></pre>
`:"<pre><code>"+(n?e:T(e,!0))+`</code></pre>
`}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e,t){return e}heading(e,t,n){return`<h${t}>${e}</h${t}>
`}hr(){return`<hr>
`}list(e,t,n){const u=t?"ol":"ul";return"<"+u+(t&&n!==1?' start="'+n+'"':"")+`>
`+e+"</"+u+`>
`}listitem(e,t,n){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(e){return`<p>${e}</p>
`}table(e,t){return t&&(t=`<tbody>${t}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+t+`</table>
`}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,t){const n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return"<br>"}del(e){return`<del>${e}</del>`}link(e,t,n){const u=Qe(e);if(u===null)return n;let r='<a href="'+(e=u)+'"';return t&&(r+=' title="'+t+'"'),r+=">"+n+"</a>",r}image(e,t,n){const u=Qe(e);if(u===null)return n;let r=`<img src="${e=u}" alt="${n}"`;return t&&(r+=` title="${t}"`),r+=">",r}text(e){return e}}class Re{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class P{constructor(e){A(this,"options");A(this,"renderer");A(this,"textRenderer");this.options=e||W,this.options.renderer=this.options.renderer||new de,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Re}static parse(e,t){return new P(t).parse(e)}static parseInline(e,t){return new P(t).parseInline(e)}parse(e,t=!0){let n="";for(let u=0;u<e.length;u++){const r=e[u];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[r.type]){const s=r,o=this.options.extensions.renderers[s.type].call({parser:this},s);if(o!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(s.type)){n+=o||"";continue}}switch(r.type){case"space":continue;case"hr":n+=this.renderer.hr();continue;case"heading":{const s=r;n+=this.renderer.heading(this.parseInline(s.tokens),s.depth,wn(this.parseInline(s.tokens,this.textRenderer)));continue}case"code":{const s=r;n+=this.renderer.code(s.text,s.lang,!!s.escaped);continue}case"table":{const s=r;let o="",l="";for(let a=0;a<s.header.length;a++)l+=this.renderer.tablecell(this.parseInline(s.header[a].tokens),{header:!0,align:s.align[a]});o+=this.renderer.tablerow(l);let c="";for(let a=0;a<s.rows.length;a++){const F=s.rows[a];l="";for(let D=0;D<F.length;D++)l+=this.renderer.tablecell(this.parseInline(F[D].tokens),{header:!1,align:s.align[D]});c+=this.renderer.tablerow(l)}n+=this.renderer.table(o,c);continue}case"blockquote":{const s=r,o=this.parse(s.tokens);n+=this.renderer.blockquote(o);continue}case"list":{const s=r,o=s.ordered,l=s.start,c=s.loose;let a="";for(let F=0;F<s.items.length;F++){const D=s.items[F],d=D.checked,y=D.task;let z="";if(D.task){const R=this.renderer.checkbox(!!d);c?D.tokens.length>0&&D.tokens[0].type==="paragraph"?(D.tokens[0].text=R+" "+D.tokens[0].text,D.tokens[0].tokens&&D.tokens[0].tokens.length>0&&D.tokens[0].tokens[0].type==="text"&&(D.tokens[0].tokens[0].text=R+" "+D.tokens[0].tokens[0].text)):D.tokens.unshift({type:"text",text:R+" "}):z+=R+" "}z+=this.parse(D.tokens,c),a+=this.renderer.listitem(z,y,!!d)}n+=this.renderer.list(a,o,l);continue}case"html":{const s=r;n+=this.renderer.html(s.text,s.block);continue}case"paragraph":{const s=r;n+=this.renderer.paragraph(this.parseInline(s.tokens));continue}case"text":{let s=r,o=s.tokens?this.parseInline(s.tokens):s.text;for(;u+1<e.length&&e[u+1].type==="text";)s=e[++u],o+=`
`+(s.tokens?this.parseInline(s.tokens):s.text);n+=t?this.renderer.paragraph(o):o;continue}default:{const s='Token with "'+r.type+'" type was not found.';if(this.options.silent)return console.error(s),"";throw new Error(s)}}}return n}parseInline(e,t){t=t||this.renderer;let n="";for(let u=0;u<e.length;u++){const r=e[u];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[r.type]){const s=this.options.extensions.renderers[r.type].call({parser:this},r);if(s!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(r.type)){n+=s||"";continue}}switch(r.type){case"escape":{const s=r;n+=t.text(s.text);break}case"html":{const s=r;n+=t.html(s.text);break}case"link":{const s=r;n+=t.link(s.href,s.title,this.parseInline(s.tokens,t));break}case"image":{const s=r;n+=t.image(s.href,s.title,s.text);break}case"strong":{const s=r;n+=t.strong(this.parseInline(s.tokens,t));break}case"em":{const s=r;n+=t.em(this.parseInline(s.tokens,t));break}case"codespan":{const s=r;n+=t.codespan(s.text);break}case"br":n+=t.br();break;case"del":{const s=r;n+=t.del(this.parseInline(s.tokens,t));break}case"text":{const s=r;n+=t.text(s.text);break}default:{const s='Token with "'+r.type+'" type was not found.';if(this.options.silent)return console.error(s),"";throw new Error(s)}}}return n}}class ne{constructor(e){A(this,"options");this.options=e||W}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}}A(ne,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var V,be,dt,nt;const j=new(nt=class{constructor(...i){_e(this,V);A(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});A(this,"options",this.setOptions);A(this,"parse",oe(this,V,be).call(this,q.lex,P.parse));A(this,"parseInline",oe(this,V,be).call(this,q.lexInline,P.parseInline));A(this,"Parser",P);A(this,"Renderer",de);A(this,"TextRenderer",Re);A(this,"Lexer",q);A(this,"Tokenizer",Fe);A(this,"Hooks",ne);this.use(...i)}walkTokens(i,e){var n,u;let t=[];for(const r of i)switch(t=t.concat(e.call(this,r)),r.type){case"table":{const s=r;for(const o of s.header)t=t.concat(this.walkTokens(o.tokens,e));for(const o of s.rows)for(const l of o)t=t.concat(this.walkTokens(l.tokens,e));break}case"list":{const s=r;t=t.concat(this.walkTokens(s.items,e));break}default:{const s=r;(u=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&u[s.type]?this.defaults.extensions.childTokens[s.type].forEach(o=>{const l=s[o].flat(1/0);t=t.concat(this.walkTokens(l,e))}):s.tokens&&(t=t.concat(this.walkTokens(s.tokens,e)))}}return t}use(...i){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return i.forEach(t=>{const n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(u=>{if(!u.name)throw new Error("extension name required");if("renderer"in u){const r=e.renderers[u.name];e.renderers[u.name]=r?function(...s){let o=u.renderer.apply(this,s);return o===!1&&(o=r.apply(this,s)),o}:u.renderer}if("tokenizer"in u){if(!u.level||u.level!=="block"&&u.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const r=e[u.level];r?r.unshift(u.tokenizer):e[u.level]=[u.tokenizer],u.start&&(u.level==="block"?e.startBlock?e.startBlock.push(u.start):e.startBlock=[u.start]:u.level==="inline"&&(e.startInline?e.startInline.push(u.start):e.startInline=[u.start]))}"childTokens"in u&&u.childTokens&&(e.childTokens[u.name]=u.childTokens)}),n.extensions=e),t.renderer){const u=this.defaults.renderer||new de(this.defaults);for(const r in t.renderer){if(!(r in u))throw new Error(`renderer '${r}' does not exist`);if(r==="options")continue;const s=r,o=t.renderer[s],l=u[s];u[s]=(...c)=>{let a=o.apply(u,c);return a===!1&&(a=l.apply(u,c)),a||""}}n.renderer=u}if(t.tokenizer){const u=this.defaults.tokenizer||new Fe(this.defaults);for(const r in t.tokenizer){if(!(r in u))throw new Error(`tokenizer '${r}' does not exist`);if(["options","rules","lexer"].includes(r))continue;const s=r,o=t.tokenizer[s],l=u[s];u[s]=(...c)=>{let a=o.apply(u,c);return a===!1&&(a=l.apply(u,c)),a}}n.tokenizer=u}if(t.hooks){const u=this.defaults.hooks||new ne;for(const r in t.hooks){if(!(r in u))throw new Error(`hook '${r}' does not exist`);if(r==="options")continue;const s=r,o=t.hooks[s],l=u[s];ne.passThroughHooks.has(r)?u[s]=c=>{if(this.defaults.async)return Promise.resolve(o.call(u,c)).then(F=>l.call(u,F));const a=o.call(u,c);return l.call(u,a)}:u[s]=(...c)=>{let a=o.apply(u,c);return a===!1&&(a=l.apply(u,c)),a}}n.hooks=u}if(t.walkTokens){const u=this.defaults.walkTokens,r=t.walkTokens;n.walkTokens=function(s){let o=[];return o.push(r.call(this,s)),u&&(o=o.concat(u.call(this,s))),o}}this.defaults={...this.defaults,...n}}),this}setOptions(i){return this.defaults={...this.defaults,...i},this}lexer(i,e){return q.lex(i,e??this.defaults)}parser(i,e){return P.parse(i,e??this.defaults)}},V=new WeakSet,be=function(i,e){return(t,n)=>{const u={...n},r={...this.defaults,...u};this.defaults.async===!0&&u.async===!1&&(r.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),r.async=!0);const s=oe(this,V,dt).call(this,!!r.silent,!!r.async);if(t==null)return s(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return s(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));if(r.hooks&&(r.hooks.options=r),r.async)return Promise.resolve(r.hooks?r.hooks.preprocess(t):t).then(o=>i(o,r)).then(o=>r.hooks?r.hooks.processAllTokens(o):o).then(o=>r.walkTokens?Promise.all(this.walkTokens(o,r.walkTokens)).then(()=>o):o).then(o=>e(o,r)).then(o=>r.hooks?r.hooks.postprocess(o):o).catch(s);try{r.hooks&&(t=r.hooks.preprocess(t));let o=i(t,r);r.hooks&&(o=r.hooks.processAllTokens(o)),r.walkTokens&&this.walkTokens(o,r.walkTokens);let l=e(o,r);return r.hooks&&(l=r.hooks.postprocess(l)),l}catch(o){return s(o)}}},dt=function(i,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,i){const n="<p>An error occurred:</p><pre>"+T(t.message+"",!0)+"</pre>";return e?Promise.resolve(n):n}if(e)return Promise.reject(t);throw t}},nt);function k(i,e){return j.parse(i,e)}k.options=k.setOptions=function(i){return j.setOptions(i),k.defaults=j.defaults,Oe(k.defaults),k},k.getDefaults=En,k.defaults=W,k.use=function(...i){return j.use(...i),k.defaults=j.defaults,Oe(k.defaults),k},k.walkTokens=function(i,e){return j.walkTokens(i,e)},k.parseInline=j.parseInline,k.Parser=P,k.parser=P.parse,k.Renderer=de,k.TextRenderer=Re,k.Lexer=q,k.lexer=q.lex,k.Tokenizer=Fe,k.Hooks=ne,k.parse=k,k.options,k.setOptions,k.use,k.walkTokens,k.parseInline,P.parse,q.lex;const jn=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,Vn=Object.hasOwnProperty;class Wn{constructor(){this.occurrences,this.reset()}slug(e,t){const n=this;let u=function(s,o){return typeof s!="string"?"":(o||(s=s.toLowerCase()),s.replace(jn,"").replace(/ /g,"-"))}(e,t===!0);const r=u;for(;Vn.call(n.occurrences,u);)n.occurrences[r]++,u=r+"-"+n.occurrences[r];return n.occurrences[u]=0,u}reset(){this.occurrences=Object.create(null)}}const Un=()=>({heading:Pt,blockquote:Mt,list:Qt,list_item:jt,br:Wt,code:Nt,codespan:Gt,table:tn,html:nn,paragraph:rn,link:on,text:ln,def:cn,del:hn,em:pn,hr:gn,strong:kn,image:An,space:Pe,escape:Pe}),Nn=()=>({baseUrl:"/",slugger:new Wn});function Xn(i,e){x(e,!1),function(){const l=console.warn;console.warn=c=>{c.includes("unknown prop")||c.includes("unexpected slot")||l(c)},vt(()=>{console.warn=l})}();let t=g(e,"source",8),n=g(e,"options",24,()=>({})),u=g(e,"renderers",24,()=>({})),r=X(),s=X(),o=X();ge(()=>(f(t()),f(u()),f(n())),()=>{var l;N(r,(l=t(),new q().lex(l))),N(s,{...Un(),...u()}),N(o,{...Nn(),...n()})}),fe(),Z(),he(i,{get tokens(){return v(r)},get renderers(){return v(s)},get options(){return v(o)}}),b()}var Gn=B('<div class="c-codeblock svelte-11afieb" role="button" tabindex="0"><div class="c-codeblock__top-bar-anchor monaco-component svelte-11afieb"><div class="c-codeblock__top-bar-left svelte-11afieb"><!></div> <!></div> <!> <div class="c-codeblock__actions-bar-anchor svelte-11afieb"><!></div></div>');function Xe(i,e){x(e,!0);const[t,n]=Rt(),u=zt();let r=g(e,"scroll",3,!1),s=g(e,"language",19,()=>{}),o=g(e,"padding",19,()=>({top:0,bottom:0})),l=g(e,"editorInstance",15),c=g(e,"element",15),a=g(e,"height",19,()=>{});const F=It.getContext().monaco,D=Lt(),d=()=>{var ie;if(!l())return;const m=l().getSelections();if(!(m!=null&&m.length))return;const $=l().getModel();if(m.map(G=>($==null?void 0:$.getValueLengthInRange(G))||0).reduce((G,bt)=>G+bt,0)!==0)return m.sort((ie=Tt(F,"$monaco",t))==null?void 0:ie.Range.compareRangesUsingStarts).map(G=>($==null?void 0:$.getValueInRange(G))||"").join(`
`)},y=()=>{if(l())return l().getValue()||""};Le(()=>{var $;var m;m=o(),($=l())==null||$.updateOptions({padding:m})}),Le(()=>{var m;(m=l())==null||m.updateOptions({scrollbar:{vertical:a()!==void 0?"auto":"hidden"}})});var z=Gn();Se("focus",yt,()=>D.requestLayout());var R=Ae(()=>u("mouseenter")),O=E(z),Q=E(O),L=E(Q);Ee(L,()=>e.topBarLeft??Ie);var H=ce(Q,2);Ee(H,()=>e.topBarRight??Ie);var U=ce(O,2);const gt=Ae(()=>({lineNumbers:"off",wrappingIndent:"same",padding:o(),wordWrap:r()?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"})),ft=Ae(()=>s()||e.token.lang);qt(U,{get options(){return v(gt)},get text(){return e.token.text},get lang(){return v(ft)},get height(){return a()},get editorInstance(){return l()},set editorInstance(m){l(m)}});var kt=ce(U,2),Ct=E(kt),At=m=>{var $=_(),ie=S($);Ee(ie,()=>e.actionsBar),p(m,$)},Et=m=>{};De(Ct,m=>{e.actionsBar?m(At):m(Et,!1)}),st(z,m=>c(m),()=>c()),Se("mouseenter",z,function(...m){var $;($=v(R))==null||$.apply(this,m)}),p(i,z);var xt=b({getSelectionOrContents:()=>l()&&(d()||y())||"",getSelections:d,getContents:y});return n(),xt}var Jn=B('<span><code class="markdown-codespan svelte-1dofrdh"><!></code></span>');function Ge(i,e){x(e,!1);const t=X();let n=g(e,"token",8),u=g(e,"element",28,()=>{});ge(()=>f(n()),()=>{N(t,n().raw.slice(1,n().raw.length-1))}),fe(),Z();var r=Jn(),s=E(r),o=E(s);I(o,e,"default",{get codespanContents(){return v(t)}},l=>{var c=ut();M(()=>ue(c,v(t))),p(l,c)}),st(r,l=>u(l),()=>u()),p(i,r),b()}var Kn=B("<span> </span>");function Je(i,e){x(e,!0);var t=Kn(),n=E(t);M(()=>ue(n,`~${e.token.text??""}~`)),p(i,t),b()}var Yn=B('<p class="augment-markdown-paragraph svelte-1edcdk9"><!></p>');function Ke(i,e){var t=Yn(),n=E(t);I(n,e,"default",{},null),p(i,t)}var eu=B('<div class="c-markdown svelte-vgp0rq"><!></div>');function gu(i,e){let t=g(e,"markdown",8),n=g(e,"renderers",24,()=>({}));var u=eu(),r=E(u);const s=ke(()=>(f(Ge),f(Xe),f(Ke),f(Je),f(n()),w(()=>({codespan:Ge,code:Xe,paragraph:Ke,del:Je,...n()}))));Xn(r,{get source(){return t()},get renderers(){return v(s)}}),p(i,u)}var tu=Be('<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M5.26047 5.79388C5.07301 5.98134 5.07301 6.28525 5.26047 6.47271C5.44792 6.66015 5.75184 6.66015 5.93929 6.47271L7.99988 4.41211L10.0605 6.47271C10.2479 6.66015 10.5518 6.66015 10.7393 6.47271C10.9267 6.28525 10.9267 5.98134 10.7393 5.79388L8.33929 3.39388C8.24926 3.30387 8.12717 3.2533 7.99988 3.2533C7.87257 3.2533 7.75048 3.30387 7.66046 3.39388L5.26047 5.79388ZM10.7393 10.206C10.9267 10.0186 10.9267 9.71467 10.7393 9.52722C10.5518 9.33977 10.2479 9.33977 10.0605 9.52722L7.99988 11.5878L5.93929 9.52722C5.75184 9.33977 5.44792 9.33977 5.26047 9.52722C5.07301 9.71467 5.07301 10.0186 5.26047 10.206L7.66046 12.6061C7.84792 12.7935 8.15184 12.7935 8.33929 12.6061L10.7393 10.206Z" fill="currentColor" fill-opacity="1"></path></svg>');function fu(i){var e=tu();p(i,e)}var nu=Be("<svg><!></svg>");function ku(i,e){const t=_t(e,["children","$$slots","$$events","$$legacy"]);var n=nu();me(n,()=>({xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16","data-ds-icon":"fa",viewBox:"0 1 16 16",...t}));var u=E(n);ot(u,()=>'<path fill-rule="evenodd" d="M4.049 3.252a.53.53 0 0 1 .524-.015l9.6 5.067a.533.533 0 0 1 0 .943l-9.6 5.067a.533.533 0 0 1-.782-.472V3.71c0-.187.098-.36.258-.457" clip-rule="evenodd"/>',!0),p(i,n)}var uu=Be('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M12 13C12.5523 13 13 12.5523 13 12V3C13 2.44771 12.5523 2 12 2H3C2.44771 2 2 2.44771 2 3V6.5C2 6.77614 2.22386 7 2.5 7C2.77614 7 3 6.77614 3 6.5V3H12V12H8.5C8.22386 12 8 12.2239 8 12.5C8 12.7761 8.22386 13 8.5 13H12ZM9 6.5C9 6.5001 9 6.50021 9 6.50031V6.50035V9.5C9 9.77614 8.77614 10 8.5 10C8.22386 10 8 9.77614 8 9.5V7.70711L2.85355 12.8536C2.65829 13.0488 2.34171 13.0488 2.14645 12.8536C1.95118 12.6583 1.95118 12.3417 2.14645 12.1464L7.29289 7H5.5C5.22386 7 5 6.77614 5 6.5C5 6.22386 5.22386 6 5.5 6H8.5C8.56779 6 8.63244 6.01349 8.69139 6.03794C8.74949 6.06198 8.80398 6.09744 8.85143 6.14433C8.94251 6.23434 8.9992 6.35909 8.99999 6.49708L8.99999 6.49738" fill="currentColor"></path></svg>');function Cu(i,e){let t=g(e,"class",3,"");var n=uu();M(()=>rt(n,0,$t(t()))),p(i,n)}function Ye(i){let e=0;const t=1e4,n=i.length>t?i.substring(0,5e3)+i.substring(i.length-5e3):i;for(let u=0;u<n.length;u++)e=(e<<5)-e+n.charCodeAt(u),e|=0;return Math.abs(e).toString(36)}function et(i,e,t,n,u={}){const{context:r=3,generateId:s=!0}=u,o=Zt(i,e,t,n,"","",{context:r}),l=e||i;let c;return s?c=`${Ye(l)}-${Ye(t+n)}`:c=Math.random().toString(36).substring(2,15),{id:c,path:l,diff:o,originalCode:t,modifiedCode:n}}function tt(i){const e=i.split(`
`);return{additions:e.filter(t=>t.startsWith("+")&&!t.startsWith("+++")).length,deletions:e.filter(t=>t.startsWith("-")&&!t.startsWith("---")).length}}function ru(i){return!i.originalCode||i.originalCode.trim()===""}function su(i){return!i.modifiedCode||i.modifiedCode.trim()===""}const Au=100,Eu="Too many files changed to display in the diff view. Please review the files in the remote workspace directly to inspect changes.";class xu{static generateDiff(e,t,n,u){return et(e,t,n,u)}static generateDiffs(e){return function(t,n={}){return t.map(u=>et(u.oldPath,u.newPath,u.oldContent,u.newContent,n))}(e)}static getDiffStats(e){return tt(e)}static getDiffObjectStats(e){return tt(e.diff)}static isNewFile(e){return ru(e)}static isDeletedFile(e){return su(e)}}export{Ge as C,xu as D,fu as E,gu as M,Cu as O,ku as P,tt as a,su as b,Xe as c,Au as d,Eu as e,Ye as g,ru as i,k as m};
