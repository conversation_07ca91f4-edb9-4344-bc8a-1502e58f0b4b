var Ri=Object.defineProperty;var Vi=(he,n,te)=>n in he?Ri(he,n,{enumerable:!0,configurable:!0,writable:!0,value:te}):he[n]=te;var ii=(he,n,te)=>Vi(he,typeof n!="symbol"?n+"":n,te);import{x as it,y as r,D as E,z as e,A as xe,o as l,N as _e,a1 as Vt,a2 as Yt,b as t,G as nt,M as se,O as Pe,R as Me,u as Y,Q as S,P as i,S as Ke,a6 as Xt,W as bi,E as C,a3 as Qe,a8 as fi,a5 as Zi,T as wi,Z as li,B as me,L as At,Y as pi,X as Bt,m as H,I as oe,J as Li,K as hi,av as Hi,a4 as Ii,f as qi,aq as Ut,ag as Ui,au as Bi}from"./legacy-AoIeRrIA.js";import{p as I,k as oi,i as Rt,b as kt,a as Jt,o as Ai,T as De,S as ai}from"./SpinnerAugment-mywmfXFR.js";import"./design-system-init-Creeq9bS.js";/* empty css                                */import{a as Wi,b as Ji,g as Gi,c as Ki,M as Qi}from"./index-BdF7sLLk.js";import{W as ki,e as ct,i as ut,h as Ei}from"./host-qgbK079d.js";import{M as Yi}from"./message-broker-EhzME3pO.js";import{c as gi,p as Xi,R as Wt}from"./index-CJlvVQMt.js";import{C as en,a as tn,T as zi,b as nn,k as sn}from"./CollapseButtonAugment-BXWAqMp6.js";import{t as an,s as ln}from"./index-Dtf_gCqL.js";import{M as on,g as Tt,a as rn,i as dn,b as cn,P as ri,O as Di,D as vn,C as fn,E as pn}from"./diff-operations-DEY-phZ8.js";import{a as hn,I as Ci}from"./IconButtonAugment-DZyIKjh7.js";import{V as ui}from"./VSCodeCodicon-Bh752rpS.js";import{d as gn,T as Nt,a as St,C as un}from"./CardAugment-DwIptXof.js";import{B as mt}from"./ButtonAugment-D7YBjBq5.js";import{M as mi}from"./MaterialIcon-B_3jxWk_.js";import{n as mn,g as Oe,a as ni}from"./focusTrapStack-CaEmYw0i.js";import{i as _n,g as Qt,a as yn,b as Fi,M as wn}from"./file-type-utils-D6OEcQY2.js";import{F as Cn,g as si,p as xi,d as $n}from"./index-B528snJk.js";import{L as Ni}from"./LanguageIcon-D_oFq7vE.js";import{A as bn}from"./async-messaging-Dmg2N9Pf.js";import{E as Mi}from"./exclamation-triangle-DKq0zBWN.js";import{F as An}from"./Filespan-BqOh8yIt.js";import{M as kn}from"./ModalAugment-CfGbMl4M.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./event-modifiers-Bz4QCcZc.js";import"./chat-types-BfwvR7Kn.js";class di{constructor(n){ii(this,"_opts",null);ii(this,"_subscribers",new Set);this._asyncMsgSender=n}subscribe(n){return this._subscribers.add(n),n(this),()=>{this._subscribers.delete(n)}}notifySubscribers(){this._subscribers.forEach(n=>n(this))}get opts(){return this._opts}updateOpts(n){this._opts=n,this.notifySubscribers()}async onPanelLoaded(){try{this.updateOpts(null);const n=await this._asyncMsgSender.send({type:ki.remoteAgentDiffPanelLoaded});this.updateOpts(n.data)}catch(n){console.error("Failed to load diff panel:",n),this.updateOpts(null)}}handleMessageFromExtension(n){const te=n.data;return!(!te||!te.type)&&te.type===ki.remoteAgentDiffPanelSetOpts&&(this.updateOpts(te.data),!0)}}ii(di,"key","remoteAgentDiffModel");var zn=r("<span><code><!></code></span>");function Fn(he,n){it(n,!0);let te=I(n,"element",15),re=xe(()=>n.token.raw.slice(1,n.token.raw.length-1)),d=xe(()=>e(re).startsWith('"')),y=xe(()=>/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(e(re))),X=xe(()=>e(y)&&function(o){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(o))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let W,ue,J;return o.length===4?(W=parseInt(o.charAt(1),16),ue=parseInt(o.charAt(2),16),J=parseInt(o.charAt(3),16),W*=17,ue*=17,J*=17):(W=parseInt(o.slice(1,3),16),ue=parseInt(o.slice(3,5),16),J=parseInt(o.slice(5,7),16)),.299*W+.587*ue+.114*J<130}(e(re)));var D=zn(),U=l(D);let Fe;var B=l(U),fe=o=>{var W=se();_e(()=>Pe(W,e(re))),t(o,W)},k=o=>{var W=se();_e(()=>Pe(W,e(re))),t(o,W)};E(B,o=>{e(y)?o(fe):o(k,!1)}),oi(D,o=>te(o),()=>te()),_e(o=>{Fe=Vt(U,1,"markdown-codespan svelte-11ta4gi",null,Fe,o),Yt(U,e(y)?`background-color: ${e(re)}; color: ${e(X)?"white":"black"}`:"")},[()=>({"markdown-string":e(d)})]),t(he,D),nt()}function _i(he,n){let te=I(n,"markdown",8);const re={codespan:Fn},d=Me(()=>(S(te()),Y(()=>te().replace(/`?#[0-9a-fA-F]{3,6}`?/g,y=>y.startsWith("`")?y:`\`${y}\``))));on(he,{get markdown(){return e(d)},get renderers(){return re}})}const vi=(he,n)=>{let te=null,re=null,d=null,y=!1;function X(){d&&cancelAnimationFrame(d),d=requestAnimationFrame(()=>{const{path:B,onCollapseStateChange:fe}=n;if(y)return void(d=null);const k=Array.from(document.querySelectorAll(`[data-description-id^="${B}:"]`));let o=!1;for(const W of k)if(W!==he&&D(he,W)){o=!0;break}o&&(y=!0),fe&&fe(o),d=null})}function D(B,fe){const k=B.getBoundingClientRect(),o=fe.getBoundingClientRect();return!(k.bottom<=o.top||o.bottom<=k.top)}function U(){Fe(),te=new MutationObserver(()=>{X()});const B=he.closest(".descriptions")||document.body;te.observe(B,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["style","data-description-id"]}),window.ResizeObserver&&(re=new ResizeObserver(()=>{X()}),re.observe(he)),window.addEventListener("resize",X),window.addEventListener("scroll",X)}function Fe(){te&&(te.disconnect(),te=null),re&&(re.disconnect(),re=null),d&&(cancelAnimationFrame(d),d=null),window.removeEventListener("resize",X),window.removeEventListener("scroll",X)}return document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>{U(),X()}):requestAnimationFrame(()=>{U(),X()}),{update:B=>{n=B,y=!1,X()},destroy:Fe}};var xn=r('<div role="region" aria-label="Code diff description"><div class="c-diff-description__content svelte-wweiw1"><!></div> <div class="c-diff-description__truncated-content svelte-wweiw1"><!> <div class="c-diff-description__expand-hint svelte-wweiw1">hover to expand</div></div></div>'),Mn=r('<div class="toggle-button svelte-1r29xbx"><!></div> <div class="descriptions svelte-1r29xbx"></div>',1),Ln=r('<div><div class="editor-container svelte-1r29xbx"></div> <!></div>');function qn(he,n){it(n,!0);const[te,re]=Jt(),d=()=>kt(ue,"$monaco",te),y=Zi();let X=I(n,"originalCode",3,""),D=I(n,"modifiedCode",3,""),U=I(n,"descriptions",19,()=>[]),Fe=I(n,"lineOffset",3,0),B=I(n,"extraPrefixLines",19,()=>[]),fe=I(n,"extraSuffixLines",19,()=>[]),k=I(n,"areDescriptionsVisible",15,!0),o=I(n,"isNewFile",3,!1),W=I(n,"isDeletedFile",3,!1);const ue=Wi.getContext().monaco;let J,u=Ke(void 0),p=Ke(void 0),A=Ke(void 0),N=[],P=Ke(void 0);const x=Ji();let $,T=li(0),a=Ke(wi(o()?20*D().split(`
`).length+40:100));const M=d()?d().languages.getLanguages().map(h=>h.id):[];function z(h,c){var L,K,le;if(c){const ne=(L=c.split(".").pop())==null?void 0:L.toLowerCase();if(ne){const Le=(le=(K=d())==null?void 0:K.languages.getLanguages().find(be=>{var s;return(s=be.extensions)==null?void 0:s.includes("."+ne)}))==null?void 0:le.id;if(Le&&M.includes(Le))return Le}}return"plaintext"}const we=li({});let de=null;function Se(){if(!e(u))return;N=N.filter(L=>(L.dispose(),!1));const h=e(u).getOriginalEditor(),c=e(u).getModifiedEditor();N.push(h.onDidScrollChange(()=>{Ai(T,h.getScrollTop())}),c.onDidScrollChange(()=>{Ai(T,c.getScrollTop())}))}function Ce(){if(!e(u)||!e(P))return;const h=e(u).getOriginalEditor(),c=e(u).getModifiedEditor();N.push(c.onDidContentSizeChange(()=>x.requestLayout()),h.onDidContentSizeChange(()=>x.requestLayout()),e(u).onDidUpdateDiff(()=>x.requestLayout()),c.onDidChangeHiddenAreas(()=>x.requestLayout()),h.onDidChangeHiddenAreas(()=>x.requestLayout()),c.onDidLayoutChange(()=>x.requestLayout()),h.onDidLayoutChange(()=>x.requestLayout()),c.onDidFocusEditorWidget(()=>{Ae(!0)}),h.onDidFocusEditorWidget(()=>{Ae(!0)}),c.onDidBlurEditorWidget(()=>{Ae(!1)}),h.onDidBlurEditorWidget(()=>{Ae(!1)}),c.onDidChangeModelContent(()=>{var ne;Ve=!0,je=Date.now();const L=((ne=e(A))==null?void 0:ne.getValue())||"";if(L===D())return;const K=L.replace(B().join(""),"").replace(fe().join(""),"");y("codeChange",{modifiedCode:K});const le=setTimeout(()=>{Ve=!1},500);N.push({dispose:()=>clearTimeout(le)})})),function(){!e(P)||!e(u)||(de&&clearTimeout(de),de=setTimeout(()=>{var L;if(!e(P).__hasClickListener){const K=le=>{const ne=le.target;ne&&(ne.closest('[title="Show Unchanged Region"]')||ne.closest('[title="Hide Unchanged Region"]'))&&pe()};(L=e(P))==null||L.addEventListener("click",K),e(P).__hasClickListener=!0,N.push({dispose:()=>{var le;(le=e(P))==null||le.removeEventListener("click",K)}})}e(u)&&N.push(e(u).onDidUpdateDiff(()=>{pe()}))},300))}()}bi(()=>{var h,c,L;(h=e(u))==null||h.dispose(),(c=e(p))==null||c.dispose(),J==null||J.dispose(),(L=e(A))==null||L.dispose(),N.forEach(K=>K.dispose()),de&&clearTimeout(de),$==null||$()});let ye=null;function pe(){ye&&clearTimeout(ye),ye=setTimeout(()=>{x.requestLayout(),ye=null},100),ye&&N.push({dispose:()=>{ye&&(clearTimeout(ye),ye=null)}})}function ae(h,c,L,K=[],le=[]){var be;if(!d())return void console.error("Monaco not loaded. Diff view cannot be updated.");J==null||J.dispose(),(be=e(A))==null||be.dispose(),c=c||"",L=L||"";const ne=K.join(""),Le=le.join("");if(c=o()?L.split(`
`).map(()=>" ").join(`
`):ne+c+Le,L=ne+L+Le,J=d().editor.createModel(c,void 0,h!==void 0?d().Uri.parse("file://"+h+`#${crypto.randomUUID()}`):void 0),W()&&(L=L.split(`
`).map(()=>" ").join(`
`)),i(A,d().editor.createModel(L,void 0,h!==void 0?d().Uri.parse("file://"+h+`#${crypto.randomUUID()}`):void 0),!0),e(u)){e(u).setModel({original:J,modified:e(A)});const s=e(u).getOriginalEditor();s&&s.updateOptions({lineNumbers:"off"}),Se(),de&&clearTimeout(de),de=setTimeout(()=>{Ce(),de=null},300)}}Xt(()=>{if(d()&&e(P))if(o()){i(p,d().editor.create(e(P),{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:n.theme,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:K=>`${Fe()-B().length+K}`}),!0);const h=z(D(),n.path);i(A,d().editor.createModel(D(),h,n.path!==void 0?d().Uri.parse("file://"+n.path+`#${crypto.randomUUID()}`):void 0),!0),e(p).setModel(e(A)),N.push(e(p).onDidChangeModelContent(()=>{var ne;Ve=!0,je=Date.now();const K=((ne=e(A))==null?void 0:ne.getValue())||"";if(K===D())return;y("codeChange",{modifiedCode:K});const le=setTimeout(()=>{Ve=!1},500);N.push({dispose:()=>clearTimeout(le)})})),N.push(e(p).onDidFocusEditorWidget(()=>{var K;(K=e(p))==null||K.updateOptions({scrollbar:{handleMouseWheel:!0}})}),e(p).onDidBlurEditorWidget(()=>{var K;(K=e(p))==null||K.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const c=e(p).getContentHeight();i(a,Math.max(c,60),!0);const L=setTimeout(()=>{var K;(K=e(p))==null||K.layout()},0);N.push({dispose:()=>clearTimeout(L)})}else i(u,d().editor.createDiffEditor(e(P),{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:n.theme,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:h=>`${Fe()-B().length+h}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}}),!0),$&&$(),$=x.registerEditor({editor:e(u),updateHeight:Be,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),ae(n.path,X(),D(),B(),fe()),Se(),Ce(),de&&clearTimeout(de),de=setTimeout(()=>{x.requestLayout(),de=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let Ve=!1,je=0;function Re(h,c=!0){return e(u)?(c?e(u).getModifiedEditor():e(u).getOriginalEditor()).getTopForLineNumber(h):18*h}function Be(){if(!e(u))return;const h=e(u).getModel(),c=h==null?void 0:h.original,L=h==null?void 0:h.modified;if(!c||!L)return;const K=e(u).getOriginalEditor(),le=e(u).getModifiedEditor(),ne=e(u).getLineChanges()||[];let Le;if(ne.length===0){const be=K.getContentHeight(),s=le.getContentHeight();Le=Math.max(100,be,s)}else{let be=0,s=0;for(const m of ne)m.originalEndLineNumber>0&&(be=Math.max(be,m.originalEndLineNumber)),m.modifiedEndLineNumber>0&&(s=Math.max(s,m.modifiedEndLineNumber));be=Math.min(be+3,c.getLineCount()),s=Math.min(s+3,L.getLineCount());const O=K.getTopForLineNumber(be),b=le.getTopForLineNumber(s);Le=Math.max(O,b)+60}i(a,Math.min(Le,2e4),!0),e(u).layout(),We()}function Ae(h){if(!e(u))return;const c=e(u).getOriginalEditor(),L=e(u).getModifiedEditor();c.updateOptions({scrollbar:{handleMouseWheel:h}}),L.updateOptions({scrollbar:{handleMouseWheel:h}})}function $e(h){if(!e(u))return e(p)?e(p).getTopForLineNumber(h.range.start+1):0;const c=e(u).getModel(),L=c==null?void 0:c.original,K=c==null?void 0:c.modified;if(!L||!K)return 0;const le=Re(h.range.start+1,!1),ne=Re(h.range.start+1,!0);return le&&!ne?le:!le&&ne?ne:Math.min(le,ne)}function We(){if(!e(u)&&!e(p)||U().length===0)return;const h={};U().forEach((c,L)=>{h[L]=$e(c)}),function(c,L=50){const K=Object.keys(c).sort((le,ne)=>c[Number(le)]-c[Number(ne)]);for(let le=0;le<K.length-1;le++){const ne=Number(K[le]),Le=c[ne];c[ne+1]-Le<L&&(c[Number(K[le+1])]=Le+L)}}(h),we.set(h)}const Ye=crypto.randomUUID();Rt(()=>{if(h=D(),!(Ve||Date.now()-je<1e3||e(A)&&e(A).getValue()===B().join("")+h+fe().join("")))if(o()&&e(p)){if(e(A))e(A).setValue(D());else{const c=z(D(),n.path);d()&&i(A,d().editor.createModel(D(),c,n.path!==void 0?d().Uri.parse("file://"+n.path+`#${crypto.randomUUID()}`):void 0),!0),e(A)&&e(p).setModel(e(A))}i(a,20*D().split(`
`).length+40),e(p).layout()}else!o()&&e(u)&&(ae(n.path,X(),D(),B(),fe()),x.requestLayout());var h}),Rt(()=>{(e(u)||e(p))&&U().length>0&&We()}),Rt(()=>{if(o()&&D()&&e(p)){const h=e(p).getContentHeight();i(a,Math.max(h,60),!0),e(p).layout()}});var Ze=Ln();let st;var at=l(Ze);oi(at,h=>i(P,h),()=>e(P));var He=C(at,2),lt=h=>{var c=Mn(),L=me(c),K=l(L);Ci(K,{variant:"ghost",color:"neutral",size:1,onclick:()=>k(!k()),children:(ne,Le)=>{var be=At(),s=me(be),O=m=>{ui(m,{icon:"x"})},b=m=>{ui(m,{icon:"book"})};E(s,m=>{k()?m(O):m(b,!1)}),t(ne,be)},$$slots:{default:!0}});var le=C(L,2);ct(le,21,U,ut,(ne,Le,be)=>{const s=xe(()=>kt(we,"$descriptionPositions",te)[be]||$e(e(Le)));(function(O,b){it(b,!0);let m=Ke(!1),R=Ke(!1),g=Ke(void 0),j=Ke(0);const v=gn(_=>{i(m,_,!0)},100);function F(_){const q=document.createElement("canvas").getContext("2d");return q?q.measureText(_).width:8*_.length}function f(){if(e(g)){const _=e(g).getBoundingClientRect();i(j,_.width-128)}}let V=null;Xt(()=>{e(g)&&typeof ResizeObserver<"u"&&(V=new ResizeObserver(()=>{f()}),V.observe(e(g)),f())}),bi(()=>{V&&V.disconnect()}),Rt(()=>{e(g)&&f()});let ie=xe(()=>(()=>{const _=b.description.text.split(`
`)[0].split(" ");let q="";if(e(j)<=0)for(const ge of _){const ce=q+(q?" ":"")+ge;if(ce.length>30)break;q=ce}else for(const ge of _){const ce=q+(q?" ":"")+ge;if(F(ce+"...")>e(j))break;q=ce}return q+"..."})());var Z=xn();let w;var ee=l(Z);_i(l(ee),{get markdown(){return b.description.text}});var G=C(ee,2);_i(l(G),{get markdown(){return e(ie)}}),oi(Z,_=>i(g,_),()=>e(g)),hn(Z,(_,q)=>vi==null?void 0:vi(_,q),()=>({path:b.fileId,onCollapseStateChange:_=>i(R,_,!0)})),_e((_,q)=>{w=Vt(Z,1,"c-diff-description svelte-wweiw1",null,w,_),Yt(Z,`top: ${b.position??""}px;`),Qe(Z,"data-description-id",q)},[()=>({"c-diff-description__collapsed":e(R)&&!e(m),"c-diff-description__hovered":e(m)}),()=>function(_,q){return`${_}:${q}`}(b.fileId,b.index)]),fi("mouseenter",Z,_=>{v.cancel(),i(m,!0),_.stopPropagation()}),fi("mouseleave",Z,_=>{v(!1),_.stopPropagation()}),t(O,Z),nt()})(ne,{get description(){return e(Le)},get position(){return e(s)},get fileId(){return Ye},index:be})}),_e(()=>Yt(le,`transform: translateY(${-kt(T,"$scrollY",te)}px)`)),t(h,c)};E(He,h=>{U().length>0&&h(lt)}),_e(h=>{st=Vt(Ze,1,"monaco-diff-container svelte-1r29xbx",null,st,h),Yt(at,`height: ${e(a)??""}px`)},[()=>({"monaco-diff-container-with-descriptions":U().length>0&&k()})]),t(he,Ze),nt(),re()}const Pi=Symbol("focusedPath");function Oi(){return Bt(Pi)}function yi(he){return`file-diff-${Tt(he)}`}var En=r('<!> <img class="image-preview svelte-1536g7w"/>',1),Dn=r("<!> <!>",1),Nn=r("<!> ",1),Pn=r('<!> <img class="image-preview image-preview--previous svelte-1536g7w"/>',1),On=r('<!> <img class="image-preview svelte-1536g7w"/> <!>',1),jn=r('<div class="image-container svelte-1536g7w"><!></div>'),Tn=r("<!> No text preview available.",1),Sn=r('<div class="binary-file-message svelte-1536g7w"><!></div>'),Rn=r('<div class="too-large-message svelte-1536g7w"><!></div>'),Vn=r('<div class="changes svelte-1536g7w"><!></div>'),Zn=r('<span class="c-directory svelte-1536g7w"> </span>'),Hn=r('<span class="new-file-badge svelte-1536g7w">New File</span>'),In=r('<span class="additions svelte-1536g7w"><!></span>'),Un=r('<span class="deletions svelte-1536g7w"><!></span>'),Bn=r('<div class="changes-indicator svelte-1536g7w"><!> <!></div>'),Wn=r('<div class="applied svelte-1536g7w"><!></div>'),Jn=r('<div class="applied__icon svelte-1536g7w"><!></div>'),Gn=r("<!> <!>",1),Kn=r('<div slot="header" class="header svelte-1536g7w"><!> <div class="c-path svelte-1536g7w"><!> <!></div> <!> <!> <!></div>'),Qn=r("<div><!></div>");function ji(he,n){it(n,!1);const[te,re]=Jt(),d=()=>kt(Hi,"$themeStore",te),y=H(),X=H(),D=H(),U=H(),Fe=H(),B=H(),fe=H(),k=H(),o=H(),W=H(),ue=H(),J=H(),u=H(),p=H(),A=H(),N=H(),P=H(),x=H(),$=H(),T=H(),a=H();let M=I(n,"path",8),z=I(n,"change",12),we=I(n,"descriptions",24,()=>[]),de=I(n,"areDescriptionsVisible",12,!0),Se=I(n,"isExpandedDefault",8),Ce=I(n,"isCollapsed",28,()=>!Se()),ye=I(n,"isApplying",8),pe=I(n,"hasApplied",8),ae=I(n,"onApplyChanges",24,()=>{}),Ve=I(n,"onCodeChange",24,()=>{}),je=I(n,"onOpenFile",24,()=>{}),Re=I(n,"isAgentFromDifferentRepo",8,!1);const Be=Oi(),Ae=Bt(Wt.key);let $e=H(z().modifiedCode);function We(c){var L;i($e,c.detail.modifiedCode),(L=Ve())==null||L(e($e))}function Ye(){var c,L;Ae.reportApplyChangesEvent(),z(z().modifiedCode=e($e),!0),(c=Ve())==null||c(e($e)),(L=ae())==null||L()}let Ze=H(e($));function st(){i(Ze,`Open ${e($)??"file"}`)}async function at(){je()&&(i(Ze,"Opening file..."),await je()()?st():(i(Ze,"Failed to open file. Does the file exist?"),setTimeout(()=>{st()},2e3)))}Xt(()=>{st()}),oe(()=>S(z()),()=>{i($e,z().modifiedCode)}),oe(()=>S(z()),()=>{i(y,rn(z().diff))}),oe(()=>e(y),()=>{i(X,e(y).additions)}),oe(()=>e(y),()=>{i(D,e(y).deletions)}),oe(()=>S(z()),()=>{i(U,dn(z()))}),oe(()=>S(z()),()=>{i(Fe,cn(z()))}),oe(()=>S(M()),()=>{i(B,_n(M()))}),oe(()=>S(M()),()=>{i(fe,Qt(M()))}),oe(()=>S(M()),()=>{i(k,yn(M()))}),oe(()=>S(z()),()=>{var c;i(o,((c=z().originalCode)==null?void 0:c.length)||0)}),oe(()=>e($e),()=>{var c;i(W,((c=e($e))==null?void 0:c.length)||0)}),oe(()=>e(o),()=>{i(ue,Fi(e(o)))}),oe(()=>e(W),()=>{i(J,Fi(e(W)))}),oe(()=>(e($e),S(z())),()=>{i(u,!e($e)&&!!z().originalCode)}),oe(()=>(e($e),S(z())),()=>{i(p,!!e($e)&&!z().originalCode)}),oe(()=>e(B),()=>{i(A,e(B))}),oe(()=>(e(B),e(k)),()=>{i(N,!e(B)&&e(k))}),oe(()=>(e(B),e(k),e(J),e(u),e(ue),e(p)),()=>{i(P,!e(B)&&!e(k)&&(e(J)||e(u)&&e(ue)||e(p)&&e(J)))}),oe(()=>d(),()=>{var c,L;i(x,Gi((c=d())==null?void 0:c.category,(L=d())==null?void 0:L.intensity))}),oe(()=>S(M()),()=>{i($,mn(M()))}),oe(()=>(S(ye()),S(Re())),()=>{i(T,ye()||Re())}),oe(()=>(S(ye()),S(pe()),S(Re())),()=>{i(a,ye()?"Applying changes...":pe()?"Reapply changes to local file":Re()?"Cannot apply changes from a different repository locally":"Apply changes to local file")}),Li(),hi();var He=Qn();let lt;var h=l(He);en(h,{stickyHeader:!0,get collapsed(){return Ce()},set collapsed(c){Ce(c)},children:(c,L)=>{var K=Vn(),le=l(K),ne=be=>{var s=jn(),O=l(s),b=R=>{var g=Dn(),j=me(g);De(j,{class:"image-info-text",children:(f,V)=>{var ie=se();_e(Z=>Pe(ie,`Image deleted: ${Z??""}`),[()=>(S(Oe),e($),Y(()=>Oe(e($))))],Me),t(f,ie)},$$slots:{default:!0}});var v=C(j,2),F=f=>{var V=En(),ie=me(V);De(ie,{class:"image-info-text",children:(w,ee)=>{var G=se("Previous version:");t(w,G)},$$slots:{default:!0}});var Z=C(ie,2);_e((w,ee,G)=>{Qe(Z,"src",`data:${w??""};base64,${ee??""}`),Qe(Z,"alt",`Original ${G??""}`)},[()=>(S(Qt),S(M()),Y(()=>Qt(M()))),()=>(S(z()),Y(()=>btoa(z().originalCode))),()=>(S(Oe),e($),Y(()=>Oe(e($))))],Me),t(f,V)};E(v,f=>{S(z()),Y(()=>z().originalCode)&&f(F)}),t(R,g)},m=(R,g)=>{var j=v=>{var F=On(),f=me(F);De(f,{class:"image-info-text",children:(w,ee)=>{var G=Nn(),_=me(G),q=Q=>{var ke=se("New image added");t(Q,ke)},ge=Q=>{var ke=se("Image modified");t(Q,ke)};E(_,Q=>{e(U)||e(p)?Q(q):Q(ge,!1)});var ce=C(_);_e(Q=>Pe(ce,`: ${Q??""}`),[()=>(S(Oe),e($),Y(()=>Oe(e($))))],Me),t(w,G)},$$slots:{default:!0}});var V=C(f,2),ie=C(V,2),Z=w=>{var ee=Pn(),G=me(ee);De(G,{class:"image-info-text",children:(q,ge)=>{var ce=se("Previous version:");t(q,ce)},$$slots:{default:!0}});var _=C(G,2);_e((q,ge,ce)=>{Qe(_,"src",`data:${q??""};base64,${ge??""}`),Qe(_,"alt",`Original ${ce??""}`)},[()=>(S(Qt),S(M()),Y(()=>Qt(M()))),()=>(S(z()),Y(()=>btoa(z().originalCode))),()=>(S(Oe),e($),Y(()=>Oe(e($))))],Me),t(w,ee)};E(ie,w=>{S(z()),e($e),e(U),Y(()=>z().originalCode&&e($e)!==z().originalCode&&!e(U))&&w(Z)}),_e((w,ee)=>{Qe(V,"src",`data:${e(fe)??""};base64,${w??""}`),Qe(V,"alt",`Current ${ee??""}`)},[()=>(e($e),Y(()=>btoa(e($e)))),()=>(S(Oe),e($),Y(()=>Oe(e($))))],Me),t(v,F)};E(R,v=>{e($e)&&v(j)},g)};E(O,R=>{e(u)?R(b):R(m,!1)}),t(be,s)},Le=(be,s)=>{var O=m=>{var R=Sn(),g=l(R);De(g,{children:(j,v)=>{var F=Tn(),f=me(F),V=Z=>{var w=se();_e(ee=>Pe(w,`Binary file added: ${ee??""}.`),[()=>(S(Oe),e($),Y(()=>Oe(e($))))],Me),t(Z,w)},ie=(Z,w)=>{var ee=_=>{var q=se();_e(ge=>Pe(q,`Binary file deleted: ${ge??""}.`),[()=>(S(Oe),e($),Y(()=>Oe(e($))))],Me),t(_,q)},G=_=>{var q=se();_e(ge=>Pe(q,`Binary file modified: ${ge??""}.`),[()=>(S(Oe),e($),Y(()=>Oe(e($))))],Me),t(_,q)};E(Z,_=>{e(u)?_(ee):_(G,!1)},w)};E(f,Z=>{e(U)||e(p)?Z(V):Z(ie,!1)}),t(j,F)},$$slots:{default:!0}}),t(m,R)},b=(m,R)=>{var g=v=>{var F=Rn(),f=l(F);De(f,{size:1,children:(V,ie)=>{var Z=se();_e(w=>Pe(Z,`File "${w??""}" is too large to display a diff (size: ${(e(u)?e(o):e(W))??""} bytes, max: ${wn} bytes).`),[()=>(S(Oe),e($),Y(()=>Oe(e($))))],Me),t(V,Z)},$$slots:{default:!0}}),t(v,F)},j=v=>{qn(v,{get path(){return M()},get originalCode(){return S(z()),Y(()=>z().originalCode)},get modifiedCode(){return e($e)},get theme(){return e(x)},get descriptions(){return we()},get isNewFile(){return e(U)},get isDeletedFile(){return e(Fe)},get areDescriptionsVisible(){return de()},set areDescriptionsVisible(F){de(F)},$$events:{codeChange:We},$$legacy:!0})};E(m,v=>{e(P)?v(g):v(j,!1)},R)};E(be,m=>{e(N)?m(O):m(b,!1)},s)};E(le,be=>{e(A)?be(ne):be(Le,!1)}),t(c,K)},$$slots:{default:!0,header:(c,L)=>{var K=Kn(),le=l(K);tn(le,{});var ne=C(le,2),Le=l(ne);const be=Me(()=>(S(St),Y(()=>[St.Hover])));Nt(Le,{get content(){return e(Ze)},get triggerOn(){return e(be)},delayDurationMs:300,children:(f,V)=>{mt(f,{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$events:{click:at},children:(ie,Z)=>{var w=se();_e(ee=>Pe(w,ee),[()=>(S(Oe),e($),Y(()=>Oe(e($))))],Me),t(ie,w)},$$slots:{default:!0}})},$$slots:{default:!0}});var s=C(Le,2),O=f=>{var V=Zn(),ie=l(V);_e(Z=>Pe(ie,Z),[()=>(S(ni),e($),Y(()=>ni(e($))))],Me),t(f,V)};E(s,f=>{S(ni),e($),Y(()=>ni(e($)))&&f(O)});var b=C(ne,2),m=f=>{var V=Hn();t(f,V)},R=f=>{var V=Bn(),ie=l(V),Z=G=>{var _=In(),q=l(_);De(q,{size:1,children:(ge,ce)=>{var Q=se();_e(()=>Pe(Q,`+${e(X)??""}`)),t(ge,Q)},$$slots:{default:!0}}),t(G,_)};E(ie,G=>{e(X)>0&&G(Z)});var w=C(ie,2),ee=G=>{var _=Un(),q=l(_);De(q,{size:1,children:(ge,ce)=>{var Q=se();_e(()=>Pe(Q,`-${e(D)??""}`)),t(ge,Q)},$$slots:{default:!0}}),t(G,_)};E(w,G=>{e(D)>0&&G(ee)}),t(f,V)};E(b,f=>{e(U)?f(m):f(R,!1)});var g=C(b,2);const j=Me(()=>(S(St),Y(()=>[St.Hover])));Nt(g,{get content(){return e(a)},get triggerOn(){return e(j)},delayDurationMs:300,children:(f,V)=>{mt(f,{variant:"ghost-block",color:"neutral",size:2,get disabled(){return e(T)},$$events:{click:Ye},children:(ie,Z)=>{var w=Gn(),ee=me(w),G=Q=>{var ke=se("Applied");t(Q,ke)},_=Q=>{var ke=se("Apply");t(Q,ke)};E(ee,Q=>{pe()?Q(G):Q(_,!1)});var q=C(ee,2),ge=Q=>{var ke=Wn(),Ie=l(ke);mi(Ie,{iconName:"check"}),t(Q,ke)},ce=Q=>{var ke=Jn(),Ie=l(ke);ri(Ie,{}),t(Q,ke)};E(q,Q=>{pe()?Q(ge):Q(ce,!1)}),t(ie,w)},$$slots:{default:!0}})},$$slots:{default:!0}});var v=C(g,2),F=f=>{const V=Me(()=>(S(St),Y(()=>[St.Hover])));Nt(f,{get content(){return e(Ze)},get triggerOn(){return e(V)},delayDurationMs:300,children:(ie,Z)=>{Ci(ie,{size:1,variant:"ghost",color:"neutral",$$events:{click:at},children:(w,ee)=>{Di(w,{})},$$slots:{default:!0}})},$$slots:{default:!0}})};E(v,f=>{pe()&&f(F)}),t(c,K)}},$$legacy:!0}),_e((c,L)=>{lt=Vt(He,1,"c svelte-1536g7w",null,lt,c),Qe(He,"id",L)},[()=>({focused:kt(Be,"$focusedPath",te)===M()}),()=>(S(yi),S(M()),Y(()=>yi(M())))],Me),t(he,He),nt(),re()}var Yn=(he,n)=>he.key==="Enter"&&n(),Xn=r('<span class="full-path-text svelte-qnxoj"> </span>'),es=r('<div class="tree-node__children svelte-qnxoj" role="group"></div>'),ts=r('<div class="tree-node svelte-qnxoj"><div role="treeitem" tabindex="0"><div class="tree-node__indent svelte-qnxoj"></div> <div class="tree-node__icon-container svelte-qnxoj"><!></div> <span><!></span></div> <!></div>');function Ti(he,n){it(n,!0);const[te,re]=Jt(),d=()=>kt(D,"$focusedPath",te);let y=I(n,"node",15),X=I(n,"indentLevel",3,0);const D=Oi();function U(){y().isFile?D.set(y().path):y(y().isExpanded=!y().isExpanded,!0)}var Fe=ts(),B=l(Fe);let fe;B.__click=U,B.__keydown=[Yn,U];var k=l(B),o=C(k,2),W=l(o),ue=x=>{const $=xe(()=>y().isExpanded?"chevron-down":"chevron-right");ui(x,{get icon(){return e($)}})},J=x=>{Ni(x,{get filename(){return y().name}})};E(W,x=>{y().isFile?x(J,!1):x(ue)});var u=C(o,2);let p;var A=l(u);De(A,{size:1,children:(x,$)=>{var T=Xn(),a=l(T);_e(()=>Pe(a,y().displayName||y().name)),t(x,T)},$$slots:{default:!0}});var N=C(B,2),P=x=>{var $=es();ct($,21,()=>Array.from(y().children.values()).sort((T,a)=>T.isFile===a.isFile?T.name.localeCompare(a.name):T.isFile?1:-1),ut,(T,a)=>{const M=xe(()=>X()+1);Ti(T,{get node(){return e(a)},get indentLevel(){return e(M)}})}),t(x,$)};E(N,x=>{!y().isFile&&y().isExpanded&&y().children.size>0&&x(P)}),_e((x,$)=>{fe=Vt(B,1,"tree-node__content svelte-qnxoj",null,fe,x),Qe(B,"aria-selected",y().path===d()),Qe(B,"aria-expanded",y().isFile?void 0:y().isExpanded),Yt(k,`width: ${6*X()}px`),p=Vt(u,1,"tree-node__label svelte-qnxoj",null,p,$),Qe(u,"title",y().displayName||y().name)},[()=>({selected:y().path===d(),"collapsed-folder":y().displayName&&!y().isFile}),()=>({"full-path":y().displayName})]),t(he,Fe),nt(),re()}Ii(["click","keydown"]);var is=r('<div class="tree-view__loading svelte-1tnd9l7"><div class="tree-view__skeleton svelte-1tnd9l7"><div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="width: 70%;"></div></div></div>'),ns=r('<div class="tree-view__empty svelte-1tnd9l7"><!></div>'),ss=r('<div class="tree-view svelte-1tnd9l7"><div class="tree-view__content svelte-1tnd9l7" role="tree" aria-label="Changed Files"><!></div></div>');function Si(he,n){it(n,!0);let te=I(n,"changedFiles",19,()=>[]),re=I(n,"isLoading",3,!1);function d(k){const o={name:"",path:"",isFile:!1,children:new Map,isExpanded:!0};return k.forEach(W=>{const ue=W.change_type===Cn.deleted?W.old_path:W.new_path;ue&&function(J,u){const p=u.split("/");let A=J;for(let N=0;N<p.length;N++){const P=p[N],x=N===p.length-1,$=p.slice(0,N+1).join("/");A.children.has(P)||A.children.set(P,{name:P,path:$,isFile:x,children:new Map,isExpanded:!0}),A=A.children.get(P)}}(o,ue)}),function(W){if(!W.isFile){if(W.path===""){const ue=Array.from(W.children.values()).filter(J=>!J.isFile);for(const J of ue)y(J,!0);return}y(W)}}(o),o}function y(k,o=!1){if(k.isFile)return;let W="";o&&(W=function(p){let A=p.path.split("/"),N=p;for(;;){const P=Array.from(N.children.values()).filter($=>!$.isFile),x=Array.from(N.children.values()).filter($=>$.isFile);if(P.length!==1||x.length!==0)break;N=P[0],A.push(N.name)}return A.join("/")}(k));const ue=Array.from(k.children.values()).filter(p=>!p.isFile);for(const p of ue)y(p);const J=Array.from(k.children.values()).filter(p=>!p.isFile),u=Array.from(k.children.values()).filter(p=>p.isFile);if(J.length===1&&u.length===0){const p=J[0],A=p.name;if(o){k.displayName=W||`${k.name}/${A}`;for(const[N,P]of p.children.entries()){const x=`${N}`;k.children.set(x,P)}k.children.delete(A)}else{k.displayName?p.displayName=`${k.displayName}/${A}`:p.displayName=`${k.name}/${A}`;for(const[N,P]of p.children.entries()){const x=`${A}/${N}`;k.children.set(x,P)}k.children.delete(A)}}}let X=xe(()=>d(te()));var D=ss(),U=l(D),Fe=l(U),B=k=>{var o=is();t(k,o)},fe=(k,o)=>{var W=J=>{var u=ns(),p=l(u);De(p,{size:1,color:"neutral",children:(A,N)=>{var P=se("No changed files");t(A,P)},$$slots:{default:!0}}),t(J,u)},ue=J=>{var u=At(),p=me(u);ct(p,17,()=>Array.from(e(X).children.values()).sort((A,N)=>A.isFile===N.isFile?A.name.localeCompare(N.name):A.isFile?1:-1),ut,(A,N)=>{Ti(A,{get node(){return e(N)},indentLevel:0})}),t(J,u)};E(k,J=>{e(X).children.size===0?J(W):J(ue,!1)},o)};E(Fe,k=>{re()?k(B):k(fe,!1)}),t(he,D),nt()}var as=r('<!> <div class="c-edits-list-controls__icon svelte-6iqvaj"><!></div>',1),ls=r("<div><!></div>"),os=r('<div class="c-edits-list-header svelte-6iqvaj"><div class="c-edits-list-controls svelte-6iqvaj"><!></div></div> <div class="c-edits-list svelte-6iqvaj"><div class="c-edits-section svelte-6iqvaj"></div></div>',1),rs=r('<div class="c-edits-list c-edits-list--empty svelte-6iqvaj"><!></div>'),ds=r('<div class="c-edits-list-container svelte-6iqvaj"><div class="c-file-explorer__layout svelte-6iqvaj"><div class="c-file-explorer__tree svelte-6iqvaj"><div class="c-file-explorer__tree__header svelte-6iqvaj"><!> <!></div></div> <div class="c-file-explorer__details svelte-6iqvaj"><!></div></div></div>');function cs(he,n){it(n,!0);let te=I(n,"onOpenFile",19,()=>{}),re=I(n,"pendingFiles",19,()=>[]),d=I(n,"appliedFiles",19,()=>[]),y=I(n,"isLoadingTreeView",3,!1),X=Ke(wi({})),D=Ke(!1),U=Ke(!1);function Fe(){if(!n.onApplyChanges)return;const T=e(o).map(M=>M.qualifiedPathName.relPath);if(T.every(M=>d().includes(M)))return void i(U,!0);const a=T.filter(M=>!d().includes(M)&&!re().includes(M));a.length!==0&&(i(D,!0),i(U,!1),a.forEach(M=>{const z=e(o).find(we=>we.qualifiedPathName.relPath===M);if(z){const we=e(X)[M]||z.newContents;n.onApplyChanges(M,z.oldContents,we)}}))}let B=xe(()=>JSON.stringify(n.changedFiles)),fe=xe(()=>JSON.stringify(d())),k=xe(()=>JSON.stringify(re()));Rt(()=>{e(B)&&(i(X,{},!0),i(D,!1),i(U,!1))});let o=xe(()=>n.changedFiles.map(T=>{const a=T.new_path||T.old_path,M=T.old_contents||"",z=T.new_contents||"",we=vn.generateDiff(T.old_path,T.new_path,M,z),de=function(Se,Ce){const ye=gi("oldFile","newFile",Se,Ce,"","",{context:3}),pe=Xi(ye);let ae=0,Ve=0,je=[];for(const Re of pe)for(const Be of Re.hunks)for(const Ae of Be.lines){const $e=Ae.startsWith("+"),We=Ae.startsWith("-");$e&&ae++,We&&Ve++,je.push({value:Ae,added:$e,removed:We})}return{totalAddedLines:ae,totalRemovedLines:Ve,changes:je,diff:ye}}(M,z);return e(X)[a]||(e(X)[a]=z),{qualifiedPathName:{rootPath:"",relPath:a},lineChanges:de,oldContents:M,newContents:z,diff:we}})),W=xe(()=>(()=>{if(e(B)&&e(fe)&&e(k)){const T=e(o).map(a=>a.qualifiedPathName.relPath);return T.length!==0&&T.some(a=>!d().includes(a)&&!re().includes(a))}return!1})());Rt(()=>{if(e(D)){const T=e(o).map(a=>a.qualifiedPathName.relPath);T.filter(a=>!d().includes(a)&&!re().includes(a)).length===0&&T.every(a=>d().includes(a)||re().includes(a))&&re().length===0&&d().length>0&&(i(D,!1),i(U,!0))}}),Rt(()=>{if(e(o).length>0&&!e(D)&&e(fe)){const T=e(o).map(a=>a.qualifiedPathName.relPath);if(T.length>0){const a=T.every(M=>d().includes(M));a&&d().length>0?i(U,!0):!a&&e(U)&&i(U,!1)}}});var ue=ds(),J=l(ue),u=l(J),p=l(u),A=l(p);De(A,{size:1,class:"c-file-explorer__tree__header__label",children:(T,a)=>{var M=se("Changed files");t(T,M)},$$slots:{default:!0}}),Si(C(A,2),{get changedFiles(){return n.changedFiles},get isLoading(){return y()}});var N=C(u,2),P=l(N),x=T=>{var a=os(),M=me(a),z=l(M),we=l(z),de=ye=>{const pe=xe(()=>e(D)||e(U)||re().length>0||!e(W));mt(ye,{variant:"ghost-block",color:"neutral",size:2,get disabled(){return e(pe)},$$events:{click:Fe},children:(ae,Ve)=>{var je=as(),Re=me(je),Be=Ye=>{var Ze=se("Applying...");t(Ye,Ze)},Ae=(Ye,Ze)=>{var st=He=>{var lt=se("All applied");t(He,lt)},at=He=>{var lt=se("Apply all");t(He,lt)};E(Ye,He=>{e(U)?He(st):He(at,!1)},Ze)};E(Re,Ye=>{e(D)?Ye(Be):Ye(Ae,!1)});var $e=C(Re,2),We=l($e);ri(We,{}),t(ae,je)},$$slots:{default:!0}})};E(we,ye=>{e(o).length>0&&ye(de)});var Se=C(M,2),Ce=l(Se);ct(Ce,21,()=>e(o),ye=>ye.qualifiedPathName.relPath,(ye,pe)=>{var ae=ls(),Ve=l(ae);const je=xe(()=>re().includes(e(pe).qualifiedPathName.relPath)),Re=xe(()=>d().includes(e(pe).qualifiedPathName.relPath)),Be=xe(()=>te()?()=>te()(e(pe).qualifiedPathName.relPath):void 0);ji(Ve,{get path(){return e(pe).qualifiedPathName.relPath},get change(){return e(pe).diff},get isApplying(){return e(je)},get hasApplied(){return e(Re)},onCodeChange:Ae=>{(function($e,We){e(X)[$e]=We})(e(pe).qualifiedPathName.relPath,Ae)},onApplyChanges:()=>{const Ae=e(X)[e(pe).qualifiedPathName.relPath]||e(pe).newContents;n.onApplyChanges(e(pe).qualifiedPathName.relPath,e(pe).oldContents,Ae)},get onOpenFile(){return e(Be)},isExpandedDefault:!0}),an(3,ae,()=>ln),t(ye,ae)}),t(T,a)},$=T=>{var a=rs(),M=l(a);De(M,{size:1,color:"neutral",children:(z,we)=>{var de=se("No changes to show");t(z,de)},$$slots:{default:!0}}),t(T,a)};E(P,T=>{e(o).length>0?T(x):T($,!1)}),t(he,ue),nt()}var vs=qi('<path fill-rule="evenodd" clip-rule="evenodd"></path>'),fs=qi('<svg width="14" viewBox="0 0 20 20" fill="currentColor" class="svelte-10h4f31"><!></svg>'),ps=r('<div class="c-skeleton-diff__controls svelte-1eiztmz"><div class="c-skeleton-diff__button svelte-1eiztmz"></div></div>'),hs=r('<div class="c-skeleton-diff__changes-item svelte-1eiztmz"><div class="c-skeleton-diff__file-header svelte-1eiztmz"><div class="c-skeleton-diff__file-info svelte-1eiztmz"><div class="c-skeleton-diff__file-icon svelte-1eiztmz"></div> <div class="c-skeleton-diff__file-path svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__file-actions svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__code-block svelte-1eiztmz"><div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 70%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 85%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 60%;"></span></div></div></div>'),gs=r('<div class="c-skeleton-diff__subsection svelte-1eiztmz"><div class="c-skeleton-diff__header svelte-1eiztmz"><div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__subtitle svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__icon svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__changes svelte-1eiztmz"></div></div>'),us=r('<div class="c-skeleton-diff__section svelte-1eiztmz"><div class="c-skeleton-diff__header svelte-1eiztmz"><div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__title svelte-1eiztmz"></div> <div class="c-skeleton-diff__description svelte-1eiztmz"><div class="c-skeleton-diff__line svelte-1eiztmz"></div> <div class="c-skeleton-diff__line svelte-1eiztmz" style="width: 85%;"></div></div></div> <!></div> <!></div>'),ms=r('<div class="c-skeleton-diff svelte-1eiztmz"></div>'),_s=r("<!> <!>",1),ys=r('<div class="c-conflicts-card__file svelte-1bce35u"><!> <!></div>'),ws=r('<div class="c-conflicts-card__header svelte-1bce35u"><div class="c-conflicts-card__icon svelte-1bce35u"><!></div> <span class="c-conflicts-card__title svelte-1bce35u"> </span></div> <div class="c-conflicts-card__description svelte-1bce35u"><!></div> <!>',1),Cs=r('<div class="c-conflicts-card"><!></div>'),$s=r('<div class="c-unstaged-changes-modal__stash-button-loading svelte-9eyy34"><!></div>'),bs=r("<!> <span>Stash & Apply Locally</span>",1),As=r('<div class="c-unstaged-changes-modal__footer svelte-9eyy34"><!> <!></div>'),ks=r(`There are unstaged changes in your working directory. Please commit your changes or we will
      run <!> to stash your changes before applying changes from the remote agent.`,1),zs=r('<div class="c-unstaged-changes-modal__body svelte-9eyy34"><!> <!></div>'),Fs=r('<div class="c-diff-view__error svelte-ibi4q5"><!> <!> <!></div>'),xs=r('<div class="c-diff-view__empty svelte-ibi4q5"><!></div>'),Ms=r("<!> <!>",1),Ls=r("<!> <!>",1),qs=r('<div class="c-diff-view__applying svelte-ibi4q5"><!> <!></div>'),Es=r("Applied <!>",1),Ds=r('<div class="c-diff-view__applied svelte-ibi4q5"><!></div>'),Ns=r('Apply All <div class="c-diff-view__controls__icon svelte-ibi4q5"><!></div>',1),Ps=r('<div class="c-diff-view__controls svelte-ibi4q5"><!></div> <!>',1),Os=r('<div class="c-diff-view__skeleton-title svelte-ibi4q5"></div>'),js=r('<div class="c-diff-view__skeleton-text svelte-ibi4q5"></div> <div class="c-diff-view__skeleton-text svelte-ibi4q5"></div>',1),Ts=r("<!> Collapse All",1),Ss=r("<!> Expand All",1),Rs=r('<div class="c-diff-view__applying svelte-ibi4q5"><!> <!></div>'),Vs=r('<div class="c-diff-view__applied svelte-ibi4q5"><!> <!></div>'),Zs=r('Apply All <div class="c-diff-view__controls__icon svelte-ibi4q5"><!></div>',1),Hs=r('<div class="c-diff-view__controls svelte-ibi4q5"><!> <!></div>'),Is=r('<div class="c-diff-view__skeleton-text svelte-ibi4q5"></div>'),Us=r('<div class="c-diff-view__warning svelte-ibi4q5"><!> </div>'),Bs=r('<div class="c-diff-view__changes-item svelte-ibi4q5"><!></div>'),Ws=r('<div class="c-diff-view__subsection svelte-ibi4q5"><div class="c-diff-view__header svelte-ibi4q5"><div class="c-diff-view__content svelte-ibi4q5"><div class="c-diff-view__icon svelte-ibi4q5"><!></div> <h5 class="c-diff-view__title svelte-ibi4q5"><!></h5> <!></div></div> <div class="c-diff-view__changes svelte-ibi4q5"></div></div>'),Js=r('<div class="c-diff-view__section svelte-ibi4q5"><div class="c-diff-view__header svelte-ibi4q5"><div class="c-diff-view__content svelte-ibi4q5"><h5 class="c-diff-view__title svelte-ibi4q5"><!></h5> <div class="c-diff-view__description svelte-ibi4q5"><!></div></div> <!></div> <!> <!></div>'),Gs=r('<div class="c-diff-view__layout svelte-ibi4q5"><div class="c-diff-view__tree svelte-ibi4q5"><div class="c-diff-view__tree__header svelte-ibi4q5"><!> <!> <!> <!></div></div> <div class="c-diff-view__explanation svelte-ibi4q5"><!></div></div>'),Ks=r('<div class="c-diff-view svelte-ibi4q5"><!> <!></div> <!>',1);function Qs(he,n){it(n,!1);const[te,re]=Jt(),d=()=>kt(Be,"$diffViewFilesMap",te),y=H(),X=H(),D=H(),U=H(),Fe=H(),B=H(),fe=H(),k=H();let o=I(n,"changedFiles",8),W=I(n,"agentLabel",24,()=>{}),ue=I(n,"latestUserPrompt",24,()=>{}),J=I(n,"onApplyChanges",24,()=>{}),u=I(n,"onOpenFile",24,()=>{}),p=I(n,"onRenderBackup",24,()=>{}),A=I(n,"preloadedExplanation",24,()=>{}),N=I(n,"isAgentFromDifferentRepo",8,!1),P=I(n,"conflictFiles",24,()=>new Set);const x=Bt(Wt.key);let $="",T=H(!1),a=H([]),M=H([]),z=H(!1),we=H(!1),de=H(null),Se=H(!0),Ce=H({}),ye=H([]),pe=H(!1),ae=H(!1),Ve=H(!0),je=H(new Set),Re=H(!1);const Be=li({});let Ae=H({});function $e(){const s=si(e(a)),O=Object.values(e(Ce)).some(Boolean);i(Se,O),Array.from(s).forEach(b=>{Ut(Ce,e(Ce)[b]=!e(Se))})}async function We(s,O,b){if(J())return Be.update(m=>(m[s]="pending",m)),new Promise(m=>{var R;(R=J())==null||R(s,O,b).then(()=>{Be.update(g=>(g[s]="applied",g)),m()})})}async function Ye(){const s=await x.canApplyChanges();s.canApply?Ze():s.hasUnstagedChanges&&i(Re,!0)}function Ze(){if(!J())return;x.reportApplyChangesEvent(),i(pe,!0),i(ae,!1);const{filesToApply:s,areAllPathsApplied:O}=xi(e(a),o(),e(Ae));O||s.length===0?i(ae,O):$n(s,We).then(()=>{i(pe,!1),i(ae,!0)})}function st(s){const O={title:"Changed Files",description:`${s.length} files were changed`,sections:[]},b=[],m=[],R=[];return s.forEach(g=>{g.old_path?g.new_path?m.push(g):R.push(g):b.push(g)}),b.length>0&&O.sections.push(at("Added files","feature",b)),m.length>0&&O.sections.push(at("Modified files","fix",m)),R.length>0&&O.sections.push(at("Deleted files","chore",R)),[O]}function at(s,O,b){const m=[];return b.forEach(R=>{const g=R.new_path||R.old_path,j=R.old_contents||"",v=R.new_contents||"",F=R.old_path?R.old_path:"",f=gi(F,R.new_path||"/dev/null",j,v,"","",{context:3}),V=`${Tt(g)}-${Tt(j+v)}`;m.push({id:V,path:g,diff:f,originalCode:j,modifiedCode:v})}),{title:s,descriptions:[],type:O,changes:m}}async function He(){if(!e(T))return;if(i(z,!0),i(we,!1),i(de,null),i(M,[]),i(a,[]),e(Fe))return void i(z,!1);const s=102400;let O=0;if(o().forEach(b=>{var m,R;O+=(((m=b.old_contents)==null?void 0:m.length)||0)+(((R=b.new_contents)==null?void 0:R.length)||0)}),o().length>12||O>512e3){try{i(a,st(o()))}catch(b){console.error("Failed to create simple explanation:",b),i(de,"Failed to create explanation for large changes.")}i(z,!1)}else try{const b=new bn(g=>Ei.postMessage(g)),m=new Map,R=o().map(g=>{const j=g.new_path||g.old_path,v=g.old_contents||"",F=g.new_contents||"",f=`${Tt(j)}-${Tt(v+F)}`;return m.set(f,{old_path:g.old_path,new_path:g.new_path,old_contents:v,new_contents:F,change_type:g.change_type}),{id:f,old_path:g.old_path,new_path:g.new_path,change_type:g.change_type}});try{const g=R.length===1;let j=[];g?j=R.map(v=>({path:v.new_path||v.old_path,changes:[{id:v.id,path:v.new_path||v.old_path,diff:`File: ${v.new_path||v.old_path}
Change type: ${v.change_type||"modified"}`,originalCode:"",modifiedCode:""}]})):j=(await b.send({type:"get-diff-group-changes-request",data:{changedFiles:R,changesById:!0,apikey:$}},3e4)).data.groupedChanges,i(M,j.map(v=>({path:v.path,changes:v.changes.map(F=>{if(F.id&&m.has(F.id)){const f=m.get(F.id);let V=F.diff;return V&&!V.startsWith("File:")||(V=gi(f.old_path||"",f.new_path||"",f.old_contents||"",f.new_contents||"")),{...F,diff:V,old_path:f.old_path,new_path:f.new_path,old_contents:f.old_contents,new_contents:f.new_contents,change_type:f.change_type,originalCode:f.old_contents||"",modifiedCode:f.new_contents||""}}return F})})))}catch(g){console.error("Failed to group changes with LLM, falling back to simple grouping:",g);try{const j=R.map(v=>{if(v.id&&m.has(v.id)){const F=m.get(v.id);return{...v,old_path:F.old_path,new_path:F.new_path,old_contents:F.old_contents||"",new_contents:F.new_contents||"",change_type:F.change_type}}return v});i(a,st(j)),i(M,e(a)[0].sections.map(v=>({path:v.title,changes:v.changes}))),i(we,!1)}catch(j){console.error("Failed to create simple explanation:",j),i(de,"Failed to group changes. Please try again.")}}if(i(z,!1),!e(M)||e(M).length===0)throw new Error("Failed to group changes");if(!e(a)||e(a).length===0){i(a,function(j){const v={title:"Loading...",description:"",sections:[]};return j.forEach(F=>{const f=F.changes.map(ie=>{if(ie.id)return ie;const Z=Tt(ie.path),w=Tt(ie.originalCode+ie.modifiedCode);return{...ie,id:`${Z}-${w}`}}),V={title:F.path,descriptions:[],type:"other",changes:f};v.sections.push(V)}),[v]}(e(M)));const g=e(a)[0].sections.map(j=>({path:j.title,changes:j.changes.map(v=>{var ie,Z,w;const F=((ie=v.originalCode)==null?void 0:ie.length)||0,f=((Z=v.modifiedCode)==null?void 0:Z.length)||0,V=((w=v.diff)==null?void 0:w.length)||0;return F>s||f>s||V>s?{id:v.id,path:v.path,diff:`File: ${v.path}
Content too large to include in explanation request (${Math.max(F,f,V)} bytes)`,originalCode:F>s?`[File content too large: ${F} bytes]`:v.originalCode,modifiedCode:f>s?`[File content too large: ${f} bytes]`:v.modifiedCode}:{id:v.id,path:v.path,diff:v.diff,originalCode:v.originalCode,modifiedCode:v.modifiedCode}})}));i(we,!0);try{const{explanation:j,error:v}=await x.getDescriptions(g,$);if(v==="Token limit exceeded")return i(a,st(o())),i(z,!1),void i(we,!1);j&&j.length>0&&j.forEach((F,f)=>{F.sections&&F.sections.forEach((V,ie)=>{V.changes&&V.changes.forEach(Z=>{const w=e(a)[f];if(w&&w.sections){const ee=w.sections[ie];if(ee&&ee.changes){const G=ee.changes.find(_=>_.id===Z.id);G&&(Z.originalCode=G.originalCode,Z.modifiedCode=G.modifiedCode,Z.diff=G.diff)}}})})}),i(a,j)}catch(j){console.error("Failed to get descriptions, using skeleton explanation:",j)}}e(a).length===0&&i(de,"Failed to generate explanation.")}catch(b){console.error("Failed to get explanation:",b),i(de,b instanceof Error?b.message:"An error occurred while generating the explanation.")}finally{i(z,!1),i(we,!1)}}Xt(()=>{const s=localStorage.getItem("anthropic_apikey");s&&($=s),i(T,!0)});let lt=H(""),h=H("Apply all changes locally");oe(()=>(S(o()),d()),()=>{o()&&Be.set(o().reduce((s,O)=>{const b=O.new_path||O.old_path;return s[b]=d()[b]??"none",s},{}))}),oe(()=>e(Ce),()=>{i(y,Object.values(e(Ce)).some(Boolean))}),oe(()=>S(o()),()=>{i(B,JSON.stringify(o()))}),oe(()=>(e(T),e(B),e(lt),S(A())),()=>{e(T)&&e(B)&&e(B)!==e(lt)&&(i(lt,e(B)),A()&&A().length>0?(i(a,A()),i(z,!1),i(we,!1)):He(),i(pe,!1),i(ae,!1),i(Ae,{}))}),oe(()=>(e(a),e(Ae)),()=>{e(a)&&e(a).length>0&&e(a).flatMap(s=>s.sections||[]).flatMap(s=>s.changes).forEach(s=>{e(Ae)[s.path]||Ut(Ae,e(Ae)[s.path]=s.modifiedCode)})}),oe(()=>e(a),()=>{i(X,JSON.stringify(e(a)))}),oe(()=>(e(a),e(Ce),e(Se)),()=>{if(e(a)&&e(a).length>0){const s=si(e(a));Array.from(s).forEach(m=>{e(Ce)[m]===void 0&&Ut(Ce,e(Ce)[m]=!e(Se))});const O=Object.keys(e(Ce)).filter(m=>e(Ce)[m]),b=Array.from(s);b.length>0&&i(Se,!b.some(m=>O.includes(m)))}}),oe(()=>(e(X),d(),e(a)),()=>{i(D,(()=>{if(e(X)&&d()){const s=si(e(a));return s.size!==0&&Array.from(s).some(O=>d()[O]!=="applied")}return!1})())}),oe(()=>d(),()=>{i(ae,Object.keys(d()).every(s=>d()[s]==="applied"))}),oe(()=>d(),()=>{i(U,Object.keys(d()).filter(s=>d()[s]==="pending"))}),oe(()=>(e(a),S(o()),e(Ae)),()=>{(async function(s,O,b){const{filesToApply:m}=xi(s,O,b),R=new Set;for(const g of m)(await x.previewApplyChanges(g.path,g.originalCode,g.newCode)).hasConflicts&&R.add(g.path);i(je,R)})(e(a),o(),e(Ae))}),oe(()=>S(o()),()=>{i(Fe,o().length===0)}),oe(()=>(e(X),e(ae),e(a),d()),()=>{if(e(X)&&e(ae)){const s=si(e(a));Array.from(s).every(O=>d()[O]==="applied")||i(ae,!1)}}),oe(()=>(e(ae),S(P())),()=>{i(fe,e(ae)&&P().size>0)}),oe(()=>(S(N()),e(pe),e(ae),e(U),e(D)),()=>{i(k,N()||e(pe)||e(ae)||e(U).length>0||!e(D))}),oe(()=>(e(k),S(N()),e(pe),e(fe),e(ae),e(U),e(D)),()=>{e(k)?N()?i(h,"Cannot apply changes from a different repository locally"):e(pe)?i(h,"Applying changes..."):e(fe)?i(h,"All changes applied, but conflicts need to be resolved manually"):e(ae)?i(h,"All changes applied"):e(U).length>0?i(h,"Waiting for changes to apply"):e(D)||i(h,"No changes to apply"):i(h,"Apply all changes locally")}),Li(),hi();var c=Ks(),L=me(c),K=l(L),le=s=>{var O=Fs(),b=l(O);Mi(b,{});var m=C(b),R=C(m);mt(R,{variant:"ghost",size:1,$$events:{click:He},children:(v,F)=>{var f=se("Retry");t(v,f)},$$slots:{default:!0}});var g=C(R,2),j=v=>{mt(v,{variant:"ghost",size:1,$$events:{click(...F){var f;(f=p())==null||f.apply(this,F)}},children:(F,f)=>{var V=se("Render as list");t(F,V)},$$slots:{default:!0}})};E(g,v=>{p()&&v(j)}),_e(()=>Pe(m,` ${e(de)??""} `)),t(s,O)};E(K,s=>{e(de)&&s(le)});var ne=C(K,2),Le=s=>{var O=xs(),b=l(O);De(b,{size:2,color:"secondary",children:(m,R)=>{var g=se("No files changed");t(m,g)},$$slots:{default:!0}}),t(s,O)},be=s=>{var O=Gs(),b=l(O),m=l(b),R=l(m),g=w=>{var ee=Ms(),G=me(ee);De(G,{size:1,class:"c-diff-view__tree__header__label",children:(q,ge)=>{var ce=se("Changes from agent");t(q,ce)},$$slots:{default:!0}});var _=C(G,2);De(_,{size:1,weight:"medium",class:"c-diff-view__tree__header__title",children:(q,ge)=>{var ce=se();_e(()=>Pe(ce,W())),t(q,ce)},$$slots:{default:!0}}),t(w,ee)};E(R,w=>{W()&&ue()!==W()&&w(g)});var j=C(R,2),v=w=>{var ee=Ls(),G=me(ee);De(G,{size:1,class:"c-diff-view__tree__header__label",children:(q,ge)=>{var ce=se("Last user prompt");t(q,ce)},$$slots:{default:!0}});var _=C(G,2);De(_,{size:1,weight:"medium",class:"c-diff-view__tree__header__title",children:(q,ge)=>{var ce=se();_e(()=>Pe(ce,ue())),t(q,ce)},$$slots:{default:!0}}),t(w,ee)};E(j,w=>{ue()&&w(v)});var F=C(j,2);De(F,{size:1,class:"c-diff-view__tree__header__label",children:(w,ee)=>{var G=se("Changed files");t(w,G)},$$slots:{default:!0}}),Si(C(F,2),{get changedFiles(){return o()}});var f=C(b,2),V=l(f),ie=w=>{var ee=Ps(),G=me(ee),_=l(G);const q=Me(()=>e(pe)?"Applying changes...":e(ae)?"All changes applied":e(D)?"Apply all changes":"No changes to apply");Nt(_,{get content(){return e(q)},children:(ge,ce)=>{const Q=Me(()=>(e(pe),e(ae),e(U),e(D),Y(()=>e(pe)||e(ae)||e(U).length>0||!e(D))));mt(ge,{variant:"ghost-block",color:"neutral",size:2,get disabled(){return e(Q)},$$events:{click:Ye},children:(ke,Ie)=>{var _t=At(),zt=me(_t),Pt=gt=>{var Ft=qs(),xt=l(Ft);ai(xt,{size:1,useCurrentColor:!0});var vt=C(xt,2);De(vt,{size:2,children:(Xe,yt)=>{var Mt=se("Applying...");t(Xe,Mt)},$$slots:{default:!0}}),t(gt,Ft)},Zt=(gt,Ft)=>{var xt=Xe=>{var yt=Ds(),Mt=l(yt);De(Mt,{size:2,children:(Lt,Gt)=>{var Ht=Es(),ei=C(me(Ht));mi(ei,{iconName:"check"}),t(Lt,Ht)},$$slots:{default:!0}}),t(Xe,yt)},vt=Xe=>{var yt=Ns(),Mt=C(me(yt)),Lt=l(Mt);ri(Lt,{}),t(Xe,yt)};E(gt,Xe=>{e(ae)?Xe(xt):Xe(vt,!1)},Ft)};E(zt,gt=>{e(pe)?gt(Pt):gt(Zt,!1)}),t(ke,_t)},$$slots:{default:!0}})},$$slots:{default:!0}}),function(ge,ce){let Q=I(ce,"count",3,2);var ke=ms();ct(ke,21,()=>Array(Q()),ut,(Ie,_t,zt)=>{var Pt=us(),Zt=l(Pt),gt=C(l(Zt),2),Ft=vt=>{var Xe=ps();t(vt,Xe)};E(gt,vt=>{zt===0&&vt(Ft)});var xt=C(Zt,2);ct(xt,16,()=>Array(2),ut,(vt,Xe,yt,Mt)=>{var Lt=gs(),Gt=C(l(Lt),2);ct(Gt,20,()=>Array(2),ut,(Ht,ei,qe,ze)=>{var wt=hs();t(Ht,wt)}),t(vt,Lt)}),t(Ie,Pt)}),t(ge,ke)}(C(G,2),{count:2}),t(w,ee)},Z=(w,ee)=>{var G=_=>{var q=At(),ge=me(q);ct(ge,1,()=>e(a),ut,(ce,Q,ke)=>{var Ie=Js();Qe(Ie,"id",`section-${ke}`);var _t=l(Ie),zt=l(_t),Pt=l(zt),Zt=l(Pt),gt=qe=>{var ze=Os();t(qe,ze)},Ft=qe=>{var ze=se();_e(()=>Pe(ze,(e(Q),Y(()=>e(Q).title)))),t(qe,ze)};E(Zt,qe=>{e(we),e(Q),Y(()=>e(we)&&e(Q).title==="Loading...")?qe(gt):qe(Ft,!1)});var xt=C(Pt,2),vt=l(xt),Xe=qe=>{var ze=js();t(qe,ze)},yt=qe=>{_i(qe,{get markdown(){return e(Q),Y(()=>e(Q).description)}})};E(vt,qe=>{e(we),e(Q),Y(()=>e(we)&&e(Q).description==="")?qe(Xe):qe(yt,!1)});var Mt=C(zt,2),Lt=qe=>{var ze=Hs(),wt=l(ze);mt(wt,{variant:"ghost-block",color:"neutral",size:2,$$events:{click:$e},children:(qt,It)=>{var Ct=At(),Ot=me(Ct),jt=et=>{var ot=Ts(),Je=me(ot);nn(Je,{}),t(et,ot)},Et=et=>{var ot=Ss(),Je=me(ot);pn(Je),t(et,ot)};E(Ot,et=>{e(y)?et(Et,!1):et(jt)}),t(qt,Ct)},$$slots:{default:!0}});var ft=C(wt,2);Nt(ft,{get content(){return e(h)},children:(qt,It)=>{mt(qt,{variant:"ghost-block",color:"neutral",size:2,get disabled(){return e(k)},$$events:{click:Ye},children:(Ct,Ot)=>{var jt=At(),Et=me(jt),et=Je=>{var Dt=Rs(),Ne=l(Dt);ai(Ne,{size:1,useCurrentColor:!0});var ve=C(Ne,2);De(ve,{size:2,children:(Te,Ue)=>{var rt=se("Applying...");t(Te,rt)},$$slots:{default:!0}}),t(Je,Dt)},ot=(Je,Dt)=>{var Ne=Te=>{var Ue=Vs(),rt=l(Ue);De(rt,{size:2,children:(Ee,pt)=>{var ht=se("Applied");t(Ee,ht)},$$slots:{default:!0}});var $t=C(rt,2),Ge=Ee=>{zi(Ee,{slot:"rightIcon"})},tt=Ee=>{mi(Ee,{iconName:"check"})};E($t,Ee=>{e(fe)?Ee(Ge):Ee(tt,!1)}),t(Te,Ue)},ve=Te=>{var Ue=Zs(),rt=C(me(Ue)),$t=l(rt);ri($t,{}),t(Te,Ue)};E(Je,Te=>{e(ae)?Te(Ne):Te(ve,!1)},Dt)};E(Et,Je=>{e(pe)?Je(et):Je(ot,!1)}),t(Ct,jt)},$$slots:{default:!0}})},$$slots:{default:!0}}),t(qe,ze)};E(Mt,qe=>{ke===0&&qe(Lt)});var Gt=C(_t,2),Ht=qe=>{const ze=Me(()=>e(ae)?P():e(je));(function(wt,ft){it(ft,!0);let qt=I(ft,"onOpenFile",19,()=>{});var It=Cs(),Ct=l(It);un(Ct,{includeBackground:!1,children:(Ot,jt)=>{var Et=ws(),et=me(Et),ot=l(et),Je=l(ot);zi(Je,{});var Dt=C(ot,2),Ne=l(Dt),ve=C(et,2),Te=l(ve),Ue=Ge=>{var tt=se("The following files have merge conflicts that need to be resolved manually.");t(Ge,tt)},rt=Ge=>{var tt=se(`The following files will have merge conflicts if applied locally. Conflict markers will be
        added to the file which can be resolved manually after applying.`);t(Ge,tt)};E(Te,Ge=>{ft.hasAppliedAll?Ge(Ue):Ge(rt,!1)});var $t=C(ve,2);ct($t,17,()=>ft.files,ut,(Ge,tt)=>{var Ee=ys(),pt=l(Ee);Nt(pt,{get content(){return e(tt)},nested:!0,children:(dt,ti)=>{var bt=_s(),Kt=me(bt);Ni(Kt,{get filename(){return e(tt)}});var ci=C(Kt,2);An(ci,{get filepath(){return e(tt)}}),t(dt,bt)},$$slots:{default:!0}});var ht=C(pt,2);Nt(ht,{content:"Open file",children:(dt,ti)=>{Ci(dt,{size:1,variant:"ghost-block",color:"neutral",onclick:()=>{var bt;return(bt=qt())==null?void 0:bt(e(tt))},children:(bt,Kt)=>{Di(bt,{})},$$slots:{default:!0}})},$$slots:{default:!0}}),t(Ge,Ee)}),_e(()=>Pe(Ne,`Conflicts (${ft.files.size??""})`)),t(Ot,Et)},$$slots:{default:!0}}),t(wt,It),nt()})(qe,{get files(){return e(ze)},get hasAppliedAll(){return e(ae)},get onOpenFile(){return u()}})};E(Gt,qe=>{e(ae),S(P()),e(je),Y(()=>(e(ae)&&P().size>0||!e(ae)&&e(je).size>0)&&ke===0)&&qe(Ht)});var ei=C(Gt,2);ct(ei,1,()=>(e(Q),Y(()=>e(Q).sections||[])),ut,(qe,ze,wt)=>{var ft=Ws();Qe(ft,"id",`subsection-${ke}-${wt}`);var qt=l(ft),It=l(qt),Ct=l(It);(function(Ne,ve){it(ve,!0);const Te={fix:{paths:["M6.56 1.14a.75.75 0 0 1 .177 1.045 3.989 3.989 0 0 0-.464.86c.185.17.382.329.59.473A3.993 3.993 0 0 1 10 2c1.272 0 2.405.594 3.137 1.518.208-.144.405-.302.59-.473a3.989 3.989 0 0 0-.464-.86.75.75 0 0 1 1.222-.869c.369.519.65 1.105.822 1.736a.75.75 0 0 1-.174.707 7.03 7.03 0 0 1-1.299 1.098A4 4 0 0 1 14 6c0 .52-.301.963-.723 1.187a6.961 6.961 0 0 1-1.158.486c.13.208.231.436.296.679 1.413-.174 2.779-.5 4.081-.96a19.655 19.655 0 0 0-.09-2.319.75.75 0 1 1 1.493-.146 21.239 21.239 0 0 1 .08 3.028.75.75 0 0 1-.482.667 20.873 20.873 0 0 1-5.153 1.249 2.521 2.521 0 0 1-.107.247 20.945 20.945 0 0 1 5.252 1.257.75.75 0 0 1 .482.74 20.945 20.945 0 0 1-.908 5.107.75.75 0 0 1-1.433-.444c.415-1.34.69-2.743.806-4.191-.495-.173-1-.327-1.512-.46.05.284.076.575.076.873 0 1.814-.517 3.312-1.426 4.37A4.639 4.639 0 0 1 10 19a4.639 4.639 0 0 1-3.574-1.63C5.516 16.311 5 14.813 5 13c0-.298.026-.59.076-.873-.513.133-1.017.287-1.512.46.116 1.448.39 2.85.806 4.191a.75.75 0 1 1-1.433.444 20.94 20.94 0 0 1-.908-5.107.75.75 0 0 1 .482-.74 20.838 20.838 0 0 1 5.252-1.257 2.493 2.493 0 0 1-.107-.247 20.874 20.874 0 0 1-5.153-1.249.75.75 0 0 1-.482-.667 21.342 21.342 0 0 1 .08-3.028.75.75 0 1 1 1.493.146 19.745 19.745 0 0 0-.09 2.319c1.302.46 2.668.786 4.08.96.066-.243.166-.471.297-.679a6.962 6.962 0 0 1-1.158-.486A1.348 1.348 0 0 1 6 6a4 4 0 0 1 .166-1.143 7.032 7.032 0 0 1-1.3-1.098.75.75 0 0 1-.173-.707 5.48 5.48 0 0 1 .822-1.736.75.75 0 0 1 1.046-.177Z"],color:"var(--ds-color-warning-9)"},feature:{paths:["M14 6a2.5 2.5 0 0 0-4-3 2.5 2.5 0 0 0-4 3H3.25C2.56 6 2 6.56 2 7.25v.5C2 8.44 2.56 9 3.25 9h6V6h1.5v3h6C17.44 9 18 8.44 18 7.75v-.5C18 6.56 17.44 6 16.75 6H14Zm-1-1.5a1 1 0 0 1-1 1h-1v-1a1 1 0 1 1 2 0Zm-6 0a1 1 0 0 0 1 1h1v-1a1 1 0 0 0-2 0Z","M9.25 10.5H3v4.75A2.75 2.75 0 0 0 5.75 18h3.5v-7.5ZM10.75 18v-7.5H17v4.75A2.75 2.75 0 0 1 14.25 18h-3.5Z"],color:"var(--ds-color-warning-9)"},refactor:{paths:["M8.157 2.176a1.5 1.5 0 0 0-1.147 0l-4.084 1.69A1.5 1.5 0 0 0 2 5.25v10.877a1.5 1.5 0 0 0 2.074 1.386l3.51-1.452 4.26 1.762a1.5 1.5 0 0 0 1.146 0l4.083-1.69A1.5 1.5 0 0 0 18 14.75V3.872a1.5 1.5 0 0 0-2.073-1.386l-3.51 1.452-4.26-1.762ZM7.58 5a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0v-6.5A.75.75 0 0 1 7.58 5Zm5.59 2.75a.75.75 0 0 0-1.5 0v6.5a.75.75 0 0 0 1.5 0v-6.5Z"],color:"var(--ds-color-warning-9)"},documentation:{paths:["M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm2.25 8.5a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Zm0 3a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z"],color:"var(--ds-color-warning-9)"},style:{paths:["M15.993 1.385a1.87 1.87 0 0 1 2.623 2.622l-4.03 5.27a12.749 12.749 0 0 1-4.237 3.562 4.508 4.508 0 0 0-3.188-3.188 12.75 12.75 0 0 1 3.562-4.236l5.27-4.03ZM6 11a3 3 0 0 0-3 3 .5.5 0 0 1-.72.45.75.75 0 0 0-1.035.931A4.001 4.001 0 0 0 9 14.004V14a3.01 3.01 0 0 0-1.66-2.685A2.99 2.99 0 0 0 6 11Z"],color:"var(--ds-color-warning-9)"},test:{paths:["M8.5 3.528v4.644c0 .729-.29 1.428-.805 1.944l-1.217 1.216a8.75 8.75 0 0 1 3.55.621l.502.201a7.25 7.25 0 0 0 4.178.365l-2.403-2.403a2.75 2.75 0 0 1-.805-1.944V3.528a40.205 40.205 0 0 0-3 0Zm4.5.084.19.015a.75.75 0 1 0 .12-1.495 41.364 41.364 0 0 0-6.62 0 .75.75 0 0 0 .12 1.495L7 3.612v4.56c0 .331-.132.649-.366.883L2.6 13.09c-1.496 1.496-.817 4.15 1.403 4.475C5.961 17.852 7.963 18 10 18s4.039-.148 5.997-.436c2.22-.325 2.9-2.979 1.403-4.475l-4.034-4.034A1.25 1.25 0 0 1 13 8.172v-4.56Z"],color:"var(--ds-color-warning-9)"},chore:{paths:["m6.75.98-.884.883a1.25 1.25 0 1 0 1.768 0L6.75.98ZM13.25.98l-.884.883a1.25 1.25 0 1 0 1.768 0L13.25.98ZM10 .98l.884.883a1.25 1.25 0 1 1-1.768 0L10 .98ZM7.5 5.75a.75.75 0 0 0-1.5 0v.464c-1.179.304-2 1.39-2 2.622v.094c.1-.02.202-.038.306-.052A42.867 42.867 0 0 1 10 8.5c1.93 0 3.83.129 5.694.378.104.014.206.032.306.052v-.094c0-1.232-.821-2.317-2-2.622V5.75a.75.75 0 0 0-1.5 0v.318a45.645 45.645 0 0 0-1.75-.062V5.75a.75.75 0 0 0-1.5 0v.256c-.586.01-1.17.03-1.75.062V5.75ZM4.505 10.365A41.36 41.36 0 0 1 10 10c1.863 0 3.697.124 5.495.365C16.967 10.562 18 11.838 18 13.28v.693a3.72 3.72 0 0 1-1.665-.393 5.222 5.222 0 0 0-4.67 0 3.722 3.722 0 0 1-3.33 0 5.222 5.222 0 0 0-4.67 0A3.72 3.72 0 0 1 2 13.972v-.693c0-1.441 1.033-2.717 2.505-2.914ZM15.665 14.92a5.22 5.22 0 0 0 2.335.552V16.5a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 2 16.5v-1.028c.8 0 1.6-.184 2.335-.551a3.722 3.722 0 0 1 3.33 0c1.47.735 3.2.735 4.67 0a3.722 3.722 0 0 1 3.33 0Z"],color:"var(--ds-color-warning-9)"},performance:{paths:["M4.606 12.97a.75.75 0 0 1-.134 1.051 2.494 2.494 0 0 0-.93 2.437 2.494 2.494 0 0 0 2.437-.93.75.75 0 1 1 1.186.918 3.995 3.995 0 0 1-4.482 1.332.75.75 0 0 1-.461-.461 3.994 3.994 0 0 1 1.332-4.482.75.75 0 0 1 1.052.134Z","M5.752 12A13.07 13.07 0 0 0 8 14.248v4.002c0 .414.336.75.75.75a5 5 0 0 0 4.797-6.414 12.984 12.984 0 0 0 5.45-10.848.75.75 0 0 0-.735-.735 12.984 12.984 0 0 0-10.849 5.45A5 5 0 0 0 1 11.25c.001.414.337.75.751.75h4.002ZM13 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"],color:"var(--ds-color-warning-9)"},revert:{paths:["M7.50043 1.37598C7.2023 1.37598 6.91637 1.49431 6.70543 1.70499L6.70521 1.70521L1.70521 6.70521L1.70499 6.70543C1.49431 6.91637 1.37598 7.2023 1.37598 7.50043C1.37598 7.79855 1.49431 8.08449 1.70499 8.29543L1.70521 8.29565L6.69987 13.2903C6.80149 13.3974 6.92322 13.4835 7.05815 13.5436C7.19615 13.6051 7.34512 13.6382 7.49617 13.6408C7.64723 13.6435 7.79727 13.6157 7.93735 13.5591C8.07744 13.5026 8.20469 13.4183 8.31151 13.3115C8.41834 13.2047 8.50256 13.0774 8.55914 12.9374C8.61572 12.7973 8.64351 12.6472 8.64084 12.4962C8.63818 12.3451 8.60511 12.1961 8.54363 12.0581C8.48351 11.9232 8.39743 11.8015 8.29032 11.6999L5.21587 8.62543H12.5004C13.0093 8.62543 13.5132 8.72566 13.9833 8.92039C14.4535 9.11513 14.8806 9.40056 15.2405 9.76039C15.6003 10.1202 15.8857 10.5474 16.0805 11.0175C16.2752 11.4877 16.3754 11.9916 16.3754 12.5004C16.3754 13.0093 16.2752 13.5132 16.0805 13.9833C15.8857 14.4535 15.6003 14.8806 15.2405 15.2405C14.8806 15.6003 14.4535 15.8857 13.9833 16.0805C13.5132 16.2752 13.0093 16.3754 12.5004 16.3754H10.0004C9.70206 16.3754 9.41591 16.494 9.20493 16.7049C8.99395 16.9159 8.87543 17.2021 8.87543 17.5004C8.87543 17.7988 8.99395 18.0849 9.20493 18.2959C9.41591 18.5069 9.70206 18.6254 10.0004 18.6254H12.5004C14.1249 18.6254 15.6828 17.9801 16.8315 16.8315C17.9801 15.6828 18.6254 14.1249 18.6254 12.5004C18.6254 10.876 17.9801 9.31806 16.8315 8.1694C15.6828 7.02074 14.1249 6.37543 12.5004 6.37543H5.21587L8.29565 3.29565L8.29587 3.29543C8.50654 3.08449 8.62488 2.79855 8.62488 2.50043C8.62488 2.2023 8.50654 1.91636 8.29587 1.70543L8.29543 1.70499C8.08449 1.49431 7.79855 1.37598 7.50043 1.37598Z","M7.712 4.818A1.5 1.5 0 0 1 10 6.095v2.972c.104-.13.234-.248.389-.343l6.323-3.906A1.5 1.5 0 0 1 19 6.095v7.81a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.505 1.505 0 0 1-.389-.344v2.973a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.5 1.5 0 0 1 0-2.552l6.323-3.906Z"],color:"var(--ds-color-warning-9)"},other:{paths:["M2 4.25C2 3.65326 2.23705 3.08097 2.65901 2.65901C3.08097 2.23705 3.65326 2 4.25 2H6.75C7.34674 2 7.91903 2.23705 8.34099 2.65901C8.76295 3.08097 9 3.65326 9 4.25V6.75C9 7.34674 8.76295 7.91903 8.34099 8.34099C7.91903 8.76295 7.34674 9 6.75 9H4.25C3.65326 9 3.08097 8.76295 2.65901 8.34099C2.23705 7.91903 2 7.34674 2 6.75V4.25ZM15.25 11.75C15.25 11.5511 15.171 11.3603 15.0303 11.2197C14.8897 11.079 14.6989 11 14.5 11C14.3011 11 14.1103 11.079 13.9697 11.2197C13.829 11.3603 13.75 11.5511 13.75 11.75V13.75H11.75C11.5511 13.75 11.3603 13.829 11.2197 13.9697C11.079 14.1103 11 14.3011 11 14.5C11 14.6989 11.079 14.8897 11.2197 15.0303C11.3603 15.171 11.5511 15.25 11.75 15.25H13.75V17.25C13.75 17.4489 13.829 17.6397 13.9697 17.7803C14.1103 17.921 14.3011 18 14.5 18C14.6989 18 14.8897 17.921 15.0303 17.7803C15.171 17.6397 15.25 17.4489 15.25 17.25V15.25H17.25C17.4489 15.25 17.6397 15.171 17.7803 15.0303C17.921 14.8897 18 14.6989 18 14.5C18 14.3011 17.921 14.1103 17.7803 13.9697C17.6397 13.829 17.4489 13.75 17.25 13.75H15.25V11.75Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M9 14.5C9 16.433 7.433 18 5.5 18C3.567 18 2 16.433 2 14.5C2 12.567 3.567 11 5.5 11C7.433 11 9 12.567 9 14.5Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M 9 14.5 A 3.5 3.5 0 1 1 2 14.5 A 3.5 3.5 0 1 1 9 14.5 Z"],color:"var(--ds-color-warning-9)"}};let Ue=xe(()=>Te[ve.type]??Te.other);const rt=xe(()=>`This is a ${ve.type} change`),$t=xe(()=>[St.Hover]);Nt(Ne,{get content(){return e(rt)},get triggerOn(){return e($t)},children:(Ge,tt)=>{var Ee=fs(),pt=l(Ee),ht=dt=>{var ti=At(),bt=me(ti);ct(bt,17,()=>e(Ue).paths,ut,(Kt,ci)=>{var $i=vs();_e(()=>Qe($i,"d",e(ci))),t(Kt,$i)}),t(dt,ti)};E(pt,dt=>{e(Ue)&&dt(ht)}),t(Ge,Ee)},$$slots:{default:!0}}),nt()})(l(Ct),{get type(){return e(ze),Y(()=>e(ze).type)}});var Ot=C(Ct,2),jt=l(Ot),Et=Ne=>{var ve=Is();t(Ne,ve)},et=Ne=>{var ve=se();_e(()=>Pe(ve,(e(ze),Y(()=>e(ze).title)))),t(Ne,ve)};E(jt,Ne=>{e(we),e(ze),Y(()=>e(we)&&e(ze).descriptions.length===0)?Ne(Et):Ne(et,!1)});var ot=C(Ot,2),Je=Ne=>{var ve=Us(),Te=l(ve);Mi(Te,{});var Ue=C(Te);_e(()=>Pe(Ue,` ${e(ze),Y(()=>e(ze).warning)??""}`)),t(Ne,ve)};E(ot,Ne=>{e(we),e(ze),Y(()=>!e(we)&&e(ze).warning)&&Ne(Je)});var Dt=C(qt,2);ct(Dt,5,()=>(e(ze),Y(()=>e(ze).changes)),Ne=>Ne.id,(Ne,ve)=>{var Te=Bs(),Ue=l(Te);const rt=Me(()=>(e(Ce),e(ve),e(Se),Y(()=>e(Ce)[e(ve).path]!==void 0?!e(Ce)[e(ve).path]:e(Se)))),$t=Me(()=>(d(),e(ve),Y(()=>d()[e(ve).path]==="pending"))),Ge=Me(()=>(d(),e(ve),Y(()=>d()[e(ve).path]==="applied"))),tt=Me(()=>u()?()=>u()(e(ve).path):void 0);oi(ji(Ue,{get path(){return e(ve),Y(()=>e(ve).path)},get change(){return e(ve)},get descriptions(){return e(ze),Y(()=>e(ze).descriptions)},get isExpandedDefault(){return e(rt)},get isApplying(){return e($t)},get hasApplied(){return e(Ge)},onCodeChange:Ee=>{(function(pt,ht){Ut(Ae,e(Ae)[pt]=ht)})(e(ve).path,Ee)},onApplyChanges:()=>{We(e(ve).path,e(ve).originalCode,e(ve).modifiedCode)},get onOpenFile(){return e(tt)},get isAgentFromDifferentRepo(){return N()},get isCollapsed(){return e(Ce)[e(ve).path]},set isCollapsed(Ee){Ut(Ce,e(Ce)[e(ve).path]=Ee)},get areDescriptionsVisible(){return e(Ve)},set areDescriptionsVisible(Ee){i(Ve,Ee)},$$legacy:!0}),(Ee,pt,ht,dt)=>Ut(ye,e(ye)[100*pt+10*ht+dt.path.length%10]=Ee),(Ee,pt,ht)=>{var dt;return(dt=e(ye))==null?void 0:dt[100*Ee+10*pt+ht.path.length%10]},()=>[ke,wt,e(ve)]),t(Ne,Te)}),t(qe,ft)}),t(ce,Ie)}),t(_,q)};E(w,_=>{e(a),Y(()=>e(a)&&e(a).length>0)&&_(G)},ee)};E(V,w=>{e(z),e(M),Y(()=>e(z)&&e(M).length===0)?w(ie):w(Z,!1)}),t(s,O)};E(ne,s=>{e(Fe)?s(Le):s(be,!1)}),function(s,O){it(O,!1);let b=I(O,"showModal",12,!1),m=I(O,"applyAllChanges",8);const R=Bt(Wt.key);let g=H(void 0),j=H(!1);async function v(){if(i(j,!0),!await R.stashUnstagedChanges())return i(g,"Failed to stash changes. Please manually stash or commit your unstaged changes."),void i(j,!1);await new Promise(f=>setTimeout(f,1500)),i(g,void 0),b(!1),m()(),i(j,!1)}function F(){b(!1),i(g,void 0)}hi(),kn(s,{get show(){return b()},title:"Unstaged changes",$$events:{cancel:F},footer:V=>{var ie=As(),Z=l(ie);const w=Me(()=>!!e(g)||e(j));mt(Z,{variant:"solid",color:"accent",get disabled(){return e(w)},$$events:{click:v},children:(G,_)=>{var q=bs(),ge=me(q),ce=Ie=>{var _t=$s(),zt=l(_t);ai(zt,{size:1}),t(Ie,_t)};E(ge,Ie=>{e(j)&&Ie(ce)});var Q=C(ge,2);let ke;_e(Ie=>ke=Vt(Q,1,"c-unstaged-changes-modal__stash-button-text svelte-9eyy34",null,ke,Ie),[()=>({loading:e(j)})],Me),t(G,q)},$$slots:{default:!0}});var ee=C(Z,2);mt(ee,{variant:"solid",color:"neutral",get disabled(){return e(j)},$$events:{click:F},children:(G,_)=>{var q=se("Abort");t(G,q)},$$slots:{default:!0}}),t(V,ie)},children:(V,ie)=>{var Z=zs(),w=l(Z);De(w,{children:(_,q)=>{var ge=ks(),ce=C(me(ge));fn(ce,{token:{type:"codespan",text:"`git stash`",raw:"`git stash`"}}),t(_,ge)},$$slots:{default:!0}});var ee=C(w,2),G=_=>{De(_,{children:(q,ge)=>{var ce=se();_e(()=>Pe(ce,e(g))),t(q,ce)},$$slots:{default:!0}})};E(ee,_=>{e(g)&&_(G)}),t(V,Z)},$$slots:{footer:!0,default:!0}}),nt()}(C(L,2),{applyAllChanges:Ze,get showModal(){return e(Re)},set showModal(s){i(Re,s)},$$legacy:!0}),t(he,c),nt(),re()}var Ys=r('<div class="file-explorer-contents svelte-5tfpo4"><!></div>'),Xs=r('<div class="diff-page svelte-5tfpo4"><div class="file-explorer-main svelte-5tfpo4"><!></div></div>');function ea(he,n){it(n,!0);const[te,re]=Jt(),d=()=>kt(o,"$diffModel",te);let y=I(n,"changedFiles",19,()=>[]),X=I(n,"pendingFiles",19,()=>[]),D=I(n,"appliedFiles",19,()=>[]),U=I(n,"agentLabel",19,()=>{}),Fe=I(n,"latestUserPrompt",19,()=>{}),B=I(n,"isAgentFromDifferentRepo",3,!1),fe=Ke(wi(new Set));const k=Bt(Wt.key),o=Bt(di.key);let W=Ke("summary");const ue=async(P,x,$)=>{const{success:T,hasConflicts:a}=await k.applyChanges(P,x,$);T&&a&&i(fe,new Set([...e(fe),P]),!0)},J=P=>k.openFile(P);(function(P){P.subscribe(x=>{if(x){const $=document.getElementById(yi(x));$&&$.scrollIntoView({behavior:"smooth",block:"center"})}})})(function(P=null){const x=li(P);return pi(Pi,x),x}(null));var u=Xs(),p=l(u),A=l(p),N=P=>{var x=Ys(),$=l(x);sn($,()=>d().opts,T=>{var a=At(),M=me(a),z=de=>{cs(de,{get changedFiles(){return y()},onApplyChanges:ue,onOpenFile:J,get pendingFiles(){return X()},get appliedFiles(){return D()}})},we=de=>{const Se=xe(()=>{var Ce,ye;return(ye=(Ce=d())==null?void 0:Ce.opts)==null?void 0:ye.preloadedExplanation});Qs(de,{get changedFiles(){return y()},onApplyChanges:ue,onOpenFile:J,get agentLabel(){return U()},get latestUserPrompt(){return Fe()},onRenderBackup:()=>{i(W,"changedFiles")},get preloadedExplanation(){return e(Se)},get isAgentFromDifferentRepo(){return B()},get conflictFiles(){return e(fe)}})};E(M,de=>{e(W)==="changedFiles"?de(z):de(we,!1)}),t(T,a)}),t(P,x)};E(A,P=>{y()&&P(N)}),t(he,u),nt(),re()}var ta=r('<div class="l-center svelte-ccste2"><!> <p>Loading diff view...</p></div>'),ia=r('<div class="l-main svelte-ccste2"><!></div>');Bi(function(he,n){it(n,!0);const[te,re]=Jt();let d=new Yi(Ei),y=new di(d);d.registerConsumer(y);let X=new Wt(d);pi(Wt.key,X),pi(di.key,y);let D=xe(()=>kt(y,"$remoteAgentDiffModel",te).opts);Xt(()=>(y.onPanelLoaded(),()=>{d.dispose()}));var U=At();fi("message",Ui,function(...B){var fe;(fe=d.onMessageFromExtension)==null||fe.apply(this,B)});var Fe=me(U);Ki(Fe,()=>Qi.Root,(B,fe)=>{fe(B,{children:(k,o)=>{var W=ia(),ue=l(W),J=p=>{const A=xe(()=>X.applyingFilePaths||[]),N=xe(()=>X.appliedFilePaths||[]),P=xe(()=>e(D).isAgentFromDifferentRepo||!1);ea(p,{get changedFiles(){return e(D).changedFiles},get agentLabel(){return e(D).sessionSummary},get latestUserPrompt(){return e(D).userPrompt},get pendingFiles(){return e(A)},get appliedFiles(){return e(N)},get isAgentFromDifferentRepo(){return e(P)}})},u=p=>{var A=ta(),N=l(A);ai(N,{size:1}),t(p,A)};E(ue,p=>{e(D)?p(J):p(u,!1)}),t(k,W)},$$slots:{default:!0}})}),t(he,U),nt(),re()},{target:document.getElementById("app")});
