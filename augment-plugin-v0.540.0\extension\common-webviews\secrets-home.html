<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Secrets Manager</title>
    <script nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <script type="module" crossorigin src="./assets/secrets-home-B-j-0JND.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/legacy-AoIeRrIA.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-Creeq9bS.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/host-qgbK079d.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/input-DCBQtNgo.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/event-modifiers-Bz4QCcZc.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-Dmg2N9Pf.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-EhzME3pO.js" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
    <link rel="stylesheet" crossorigin href="./assets/secrets-home-a4ud0lsU.css" nonce="nonce-hebkccJ4PtKpwiV7RtTFnQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
