var X=Object.defineProperty;var j=(n,e,t)=>e in n?X(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var u=(n,e,t)=>j(n,typeof e!="symbol"?e+"":e,t);import{f as y,b as v,a4 as q,x as J,y as I,D as K,z as G,A as ee,o as m,E as V,N as H,a1 as w,a3 as te,a8 as O,G as se,B as ne,O as P,Z as oe,aO as k,a as E,Y as ae,X as ie}from"./legacy-AoIeRrIA.js";import{a as ce}from"./input-DCBQtNgo.js";import{p,l as $}from"./SpinnerAugment-mywmfXFR.js";import{R as C}from"./chat-types-BfwvR7Kn.js";import{f as b}from"./index-BLDiLrXG.js";import{h as T}from"./IconButtonAugment-DZyIKjh7.js";var le=y('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z" fill="currentColor"></path></svg>');function _e(n){var e=le();v(n,e)}function re(n,e,t,s){var c;e()||(n.key!=="Enter"&&n.key!==" "||(n.preventDefault(),t(!t())),(c=s.onkeydown)==null||c.call(s,n))}function de(n,e){var t;(t=e.onchange)==null||t.call(e,n)}var ue=I("<span> </span> <span> </span>",1),he=I('<label><!> <input type="checkbox" role="switch"/></label>');function Ae(n,e){J(e,!0);let t=p(e,"checked",15,!1),s=p(e,"disabled",3,!1),c=p(e,"size",3,2),x=p(e,"ariaLabel",19,()=>{}),d=p(e,"onText",19,()=>{}),g=p(e,"offText",19,()=>{}),o=ee(()=>d()||g());var a=he();let f;var M=m(a),U=l=>{var i=ue(),S=ne(i);let z;var Y=m(S),B=V(S,2);let D;var Z=m(B);H((Q,W)=>{z=w(S,1,"c-toggle-text c-toggle-text--off svelte-xr5g0k",null,z,Q),P(Y,g()||""),D=w(B,1,"c-toggle-text c-toggle-text--on svelte-xr5g0k",null,D,W),P(Z,d()||"")},[()=>({visible:!t()&&g()}),()=>({visible:t()&&d()})]),v(l,i)};K(M,l=>{G(o)&&l(U)});var r=V(M,2);let F;r.__keydown=[re,s,t,e],r.__change=[de,e],r.__click=function(...l){var i;(i=e.onclick)==null||i.apply(this,l)},H((l,i)=>{f=w(a,1,`c-toggle-track c-toggle-track-size--${c()??""}`,"svelte-xr5g0k",f,l),F=w(r,1,"c-toggle-input svelte-xr5g0k",null,F,i),r.disabled=s(),te(r,"aria-label",x())},[()=>({checked:t(),disabled:s(),"has-text":G(o)}),()=>({disabled:s()})]),O("blur",r,function(...l){var i;(i=e.onblur)==null||i.apply(this,l)}),O("focus",r,function(...l){var i;(i=e.onfocus)==null||i.apply(this,l)}),ce(r,t),v(n,a),se()}function Ee(n){const{rules:e,workspaceGuidelinesContent:t,contextRules:s=[]}=n,c=e.filter(o=>o.type===C.ALWAYS_ATTACHED).reduce((o,a)=>o+a.content.length+a.path.length,0),x=e.filter(o=>o.type===C.AGENT_REQUESTED).reduce((o,a)=>{var f;return o+100+(((f=a.description)==null?void 0:f.length)??0)+a.path.length},0),d=c+e.filter(o=>o.type===C.MANUAL).filter(o=>s.some(a=>a.path===o.path)).reduce((o,a)=>o+a.content.length+a.path.length,0)+x+t.length,g=n.rulesAndGuidelinesLimit&&d>n.rulesAndGuidelinesLimit;return{totalCharacterCount:d,isOverLimit:g,warningMessage:g&&n.rulesAndGuidelinesLimit?`Total number of characters in included rules and workspace guidelines (${d} chars)
        exceeds the limit of ${n.rulesAndGuidelinesLimit} characters, remove some rules
        or reduce the length of your guidelines.`:void 0}}q(["keydown","change","click"]);const L={enabled:!1,volume:.5},$e={enabled:!0},ge=""+new URL("agent-complete-DO0gyADk.mp3",import.meta.url).href;var N=(n=>(n.AGENT_COMPLETE="agent-complete",n))(N||{});const pe={"agent-complete":ge},h=class h{constructor(){u(this,"audioCache",new Map)}static getInstance(){return h._instance||(h._instance=new h),h._instance}retrieveAudioElement(e,t){let s=this.audioCache.get(e);return s?s.volume=t.volume:(s=new Audio,s.src=pe[e],s.volume=t.volume,s.preload="auto",s._isUnlocked=!1,this.audioCache.set(e,s)),s}async playSound(e,t){if(t.enabled)try{const s=this.retrieveAudioElement(e,t);s.currentTime=0,await s.play()}catch(s){if(s instanceof DOMException&&s.name==="NotAllowedError")return void console.error("Audio blocked by browser policy. Sound will work after user interaction.");console.error("Failed to play sound:",s)}}async unlockSoundForConfig(e){if(!e.enabled)return;const t=this.retrieveAudioElement("agent-complete",e);if(!t._isUnlocked)try{await this.playSound("agent-complete",{enabled:!0,volume:0}),t._isUnlocked=!0}catch(s){console.warn("Failed to unlock sound:",s)}}disposeSounds(){this.audioCache.forEach(e=>{e.pause(),e.src="",e._isUnlocked=!1}),this.audioCache.clear()}};u(h,"_instance");let A=h;const _=A.getInstance();class me{constructor(e){u(this,"_soundSettings",oe(L));u(this,"_isLoaded",!1);u(this,"dispose",()=>{_.disposeSounds()});this._msgBroker=e,this.initialize()}async refreshSettings(){try{const e=await this._msgBroker.sendToSidecar({type:b.getSoundSettings});e.data&&this._soundSettings.set(e.data)}catch(e){console.warn("Failed to refresh sound settings:",e)}}async unlockSound(){k(this._soundSettings).enabled&&_.unlockSoundForConfig(k(this._soundSettings))}async playAgentComplete(){const e=k(this._soundSettings);await _.playSound(N.AGENT_COMPLETE,e)}get getCurrentSettings(){return this._soundSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:b.getSoundSettings});e.data&&this._soundSettings.set(e.data),this._isLoaded=!0}catch(e){console.warn("Failed to load sound settings, using defaults:",e),this._soundSettings.set(L),this._isLoaded=!0}}async updateSettings(e){try{await this._msgBroker.sendToSidecar({type:b.updateSoundSettings,data:e}),this._soundSettings.update(t=>({...t,...e}))}catch(t){throw console.error("Failed to update sound settings:",t),t}}async setEnabled(e){await this.updateSettings({enabled:e})}async setVolume(e){const t=Math.max(0,Math.min(1,e));await this.updateSettings({volume:t})}async resetToDefaults(){await this.updateSettings(L)}updateEnabled(e){this.setEnabled(e).catch(t=>{console.error("Failed to update enabled setting:",t)})}updateVolume(e){this.setVolume(e).catch(t=>{console.error("Failed to update volume setting:",t)})}}u(me,"key","soundModel");var ve=y("<svg><!></svg>");function Te(n,e){const t=$(e,["children","$$slots","$$events","$$legacy"]);var s=ve();E(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...t}));var c=m(s);T(c,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248 72c0-13.3-10.7-24-24-24s-24 10.7-24 24v160H40c-13.3 0-24 10.7-24 24s10.7 24 24 24h160v160c0 13.3 10.7 24 24 24s24-10.7 24-24V280h160c13.3 0 24-10.7 24-24s-10.7-24-24-24H248z"/>',!0),v(n,s)}var fe=y("<svg><!></svg>");function Me(n,e){const t=$(e,["children","$$slots","$$events","$$legacy"]);var s=fe();E(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...t}));var c=m(s);T(c,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M280 24c0-13.3-10.7-24-24-24s-24 10.7-24 24v270.1l-95-95c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9L239 369c9.4 9.4 24.6 9.4 33.9 0L409 233c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-95 95zM128.8 304H64c-35.3 0-64 28.7-64 64v80c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-80c0-35.3-28.7-64-64-64h-64.8l-48 48H448c8.8 0 16 7.2 16 16v80c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-80c0-8.8 7.2-16 16-16h112.8zM432 408a24 24 0 1 0-48 0 24 24 0 1 0 48 0"/>',!0),v(n,s)}var we=y("<svg><!></svg>");function Fe(n,e){const t=$(e,["children","$$slots","$$events","$$legacy"]);var s=we();E(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...t}));var c=m(s);T(c,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m170.5 51.6-19 28.4h145l-19-28.4c-1.5-2.2-4-3.6-6.7-3.6h-93.7c-2.7 0-5.2 1.3-6.7 3.6zm147-26.6 36.7 55H424c13.3 0 24 10.7 24 24s-10.7 24-24 24h-8v304c0 44.2-35.8 80-80 80H112c-44.2 0-80-35.8-80-80V128h-8c-13.3 0-24-10.7-24-24s10.7-24 24-24h69.8l36.7-55.1C140.9 9.4 158.4 0 177.1 0h93.7c18.7 0 36.2 9.4 46.6 24.9zM80 128v304c0 17.7 14.3 32 32 32h224c17.7 0 32-14.3 32-32V128zm80 64v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16"/>',!0),v(n,s)}const R="extensionClient";function ze(n){ae(R,n)}function Be(){const n=ie(R);if(!n)throw new Error("ExtensionClient context not found. Make sure setExtensionClientContext() was called in a parent component.");return n}export{_e as C,$e as D,Te as P,me as S,Fe as T,Ae as a,Me as b,Ee as c,Be as g,ze as s};
