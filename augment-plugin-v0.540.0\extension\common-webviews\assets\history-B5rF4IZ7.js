var ln=Object.defineProperty;var dn=(n,e,t)=>e in n?ln(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var We=(n,e,t)=>dn(n,typeof e!="symbol"?e+"":e,t);import{x as Ct,K as Ne,y as k,E as N,o as f,N as ot,O as st,b as y,D as B,z as r,R as Vt,u as be,Q as we,G as qt,W as Je,m as Gt,P as S,I as $e,J as mn,L as Me,B as vt,M as ye,a1 as xe,aq as he,a4 as Ze,S as mt,T as Yt,a8 as Ke,ag as Ve,A as P,au as hn}from"./legacy-AoIeRrIA.js";import{p as X,k as Ae,g as fn,T as gn,i as tn}from"./SpinnerAugment-mywmfXFR.js";import"./design-system-init-Creeq9bS.js";import{e as Wt,i as ee,h as $t,W as ct}from"./host-qgbK079d.js";import{d as pn,S as le,c as vn,M as bn}from"./index-BdF7sLLk.js";import{t as bt,d as wn,m as en,a as ze,b as Re,F as pt}from"./feedback-rating-RDPz08ah.js";import{C as yn}from"./CopyButton-DfcpqvjI.js";import{B as ke}from"./ButtonAugment-D7YBjBq5.js";import{e as xn,R as Ut}from"./toggleHighContrast-Cb9MCs64.js";import{C as Oe}from"./next-edit-types-904A5ehg.js";import{T as kn}from"./TextAreaAugment-B1LKPxPr.js";import{C as _n}from"./CardAugment-DwIptXof.js";import{B as Nn,I as Ee}from"./IconButtonAugment-DZyIKjh7.js";import{o as je}from"./keypress-DD1aQVr0.js";import{M as Fe}from"./MaterialIcon-B_3jxWk_.js";import"./SuccessfulButton-xgZ5Aax4.js";import"./preload-helper-Dv6uf1Os.js";import"./input-DCBQtNgo.js";import"./BaseTextInput-D2MYbf3a.js";import"./event-modifiers-Bz4QCcZc.js";function At(n,e){return n instanceof Date?new n.constructor(e):new Date(e)}let Mn={};function me(){return Mn}function ne(n,e){var m,c,h,u;const t=me(),i=(e==null?void 0:e.weekStartsOn)??((c=(m=e==null?void 0:e.locale)==null?void 0:m.options)==null?void 0:c.weekStartsOn)??t.weekStartsOn??((u=(h=t.locale)==null?void 0:h.options)==null?void 0:u.weekStartsOn)??0,a=bt(n),o=a.getDay(),v=(o<i?7:0)+o-i;return a.setDate(a.getDate()-v),a.setHours(0,0,0,0),a}function de(n){return ne(n,{weekStartsOn:1})}function nn(n){const e=bt(n),t=e.getFullYear(),i=At(n,0);i.setFullYear(t+1,0,4),i.setHours(0,0,0,0);const a=de(i),o=At(n,0);o.setFullYear(t,0,4),o.setHours(0,0,0,0);const v=de(o);return e.getTime()>=a.getTime()?t+1:e.getTime()>=v.getTime()?t:t-1}function Tn(n){if(e=n,!(e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"||typeof n=="number"))return!1;var e;const t=bt(n);return!isNaN(Number(t))}const Pn={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function fe(n){return(e={})=>{const t=e.width?String(e.width):n.defaultWidth;return n.formats[t]||n.formats[n.defaultWidth]}}const Dn={date:fe({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:fe({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:fe({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},Sn={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function Xt(n){return(e,t)=>{let i;if((t!=null&&t.context?String(t.context):"standalone")==="formatting"&&n.formattingValues){const a=n.defaultFormattingWidth||n.defaultWidth,o=t!=null&&t.width?String(t.width):a;i=n.formattingValues[o]||n.formattingValues[a]}else{const a=n.defaultWidth,o=t!=null&&t.width?String(t.width):n.defaultWidth;i=n.values[o]||n.values[a]}return i[n.argumentCallback?n.argumentCallback(e):e]}}const Cn={ordinalNumber:(n,e)=>{const t=Number(n),i=t%100;if(i>20||i<10)switch(i%10){case 1:return t+"st";case 2:return t+"nd";case 3:return t+"rd"}return t+"th"},era:Xt({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:Xt({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:n=>n-1}),month:Xt({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:Xt({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:Xt({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})};function Jt(n){return(e,t={})=>{const i=t.width,a=i&&n.matchPatterns[i]||n.matchPatterns[n.defaultMatchWidth],o=e.match(a);if(!o)return null;const v=o[0],m=i&&n.parsePatterns[i]||n.parsePatterns[n.defaultParseWidth],c=Array.isArray(m)?function(u,p){for(let b=0;b<u.length;b++)if(p(u[b]))return b}(m,u=>u.test(v)):function(u,p){for(const b in u)if(Object.prototype.hasOwnProperty.call(u,b)&&p(u[b]))return b}(m,u=>u.test(v));let h;return h=n.valueCallback?n.valueCallback(c):c,h=t.valueCallback?t.valueCallback(h):h,{value:h,rest:e.slice(v.length)}}}const qn={ordinalNumber:(Zt={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:n=>parseInt(n,10)},(n,e={})=>{const t=n.match(Zt.matchPattern);if(!t)return null;const i=t[0],a=n.match(Zt.parsePattern);if(!a)return null;let o=Zt.valueCallback?Zt.valueCallback(a[0]):a[0];return o=e.valueCallback?e.valueCallback(o):o,{value:o,rest:n.slice(i.length)}}),era:Jt({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:Jt({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:n=>n+1}),month:Jt({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:Jt({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:Jt({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})};var Zt;const Wn={code:"en-US",formatDistance:(n,e,t)=>{let i;const a=Pn[n];return i=typeof a=="string"?a:e===1?a.one:a.other.replace("{{count}}",e.toString()),t!=null&&t.addSuffix?t.comparison&&t.comparison>0?"in "+i:i+" ago":i},formatLong:Dn,formatRelative:(n,e,t,i)=>Sn[n],localize:Cn,match:qn,options:{weekStartsOn:0,firstWeekContainsDate:1}};function $n(n){const e=bt(n);return wn(e,function(i){const a=bt(i),o=At(i,0);return o.setFullYear(a.getFullYear(),0,1),o.setHours(0,0,0,0),o}(e))+1}function An(n){const e=bt(n),t=+de(e)-+function(i){const a=nn(i),o=At(i,0);return o.setFullYear(a,0,4),o.setHours(0,0,0,0),de(o)}(e);return Math.round(t/en)+1}function rn(n,e){var u,p,b,x;const t=bt(n),i=t.getFullYear(),a=me(),o=(e==null?void 0:e.firstWeekContainsDate)??((p=(u=e==null?void 0:e.locale)==null?void 0:u.options)==null?void 0:p.firstWeekContainsDate)??a.firstWeekContainsDate??((x=(b=a.locale)==null?void 0:b.options)==null?void 0:x.firstWeekContainsDate)??1,v=At(n,0);v.setFullYear(i+1,0,o),v.setHours(0,0,0,0);const m=ne(v,e),c=At(n,0);c.setFullYear(i,0,o),c.setHours(0,0,0,0);const h=ne(c,e);return t.getTime()>=m.getTime()?i+1:t.getTime()>=h.getTime()?i:i-1}function zn(n,e){const t=bt(n),i=+ne(t,e)-+function(a,o){var u,p,b,x;const v=me(),m=(o==null?void 0:o.firstWeekContainsDate)??((p=(u=o==null?void 0:o.locale)==null?void 0:u.options)==null?void 0:p.firstWeekContainsDate)??v.firstWeekContainsDate??((x=(b=v.locale)==null?void 0:b.options)==null?void 0:x.firstWeekContainsDate)??1,c=rn(a,o),h=At(a,0);return h.setFullYear(c,0,m),h.setHours(0,0,0,0),ne(h,o)}(t,e);return Math.round(i/en)+1}function M(n,e){return(n<0?"-":"")+Math.abs(n).toString().padStart(e,"0")}const Mt={y(n,e){const t=n.getFullYear(),i=t>0?t:1-t;return M(e==="yy"?i%100:i,e.length)},M(n,e){const t=n.getMonth();return e==="M"?String(t+1):M(t+1,2)},d:(n,e)=>M(n.getDate(),e.length),a(n,e){const t=n.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return t.toUpperCase();case"aaa":return t;case"aaaaa":return t[0];default:return t==="am"?"a.m.":"p.m."}},h:(n,e)=>M(n.getHours()%12||12,e.length),H:(n,e)=>M(n.getHours(),e.length),m:(n,e)=>M(n.getMinutes(),e.length),s:(n,e)=>M(n.getSeconds(),e.length),S(n,e){const t=e.length,i=n.getMilliseconds();return M(Math.trunc(i*Math.pow(10,t-3)),e.length)}},Rn="midnight",On="noon",En="morning",jn="afternoon",Fn="evening",In="night",Ie={G:function(n,e,t){const i=n.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return t.era(i,{width:"abbreviated"});case"GGGGG":return t.era(i,{width:"narrow"});default:return t.era(i,{width:"wide"})}},y:function(n,e,t){if(e==="yo"){const i=n.getFullYear(),a=i>0?i:1-i;return t.ordinalNumber(a,{unit:"year"})}return Mt.y(n,e)},Y:function(n,e,t,i){const a=rn(n,i),o=a>0?a:1-a;return e==="YY"?M(o%100,2):e==="Yo"?t.ordinalNumber(o,{unit:"year"}):M(o,e.length)},R:function(n,e){return M(nn(n),e.length)},u:function(n,e){return M(n.getFullYear(),e.length)},Q:function(n,e,t){const i=Math.ceil((n.getMonth()+1)/3);switch(e){case"Q":return String(i);case"QQ":return M(i,2);case"Qo":return t.ordinalNumber(i,{unit:"quarter"});case"QQQ":return t.quarter(i,{width:"abbreviated",context:"formatting"});case"QQQQQ":return t.quarter(i,{width:"narrow",context:"formatting"});default:return t.quarter(i,{width:"wide",context:"formatting"})}},q:function(n,e,t){const i=Math.ceil((n.getMonth()+1)/3);switch(e){case"q":return String(i);case"qq":return M(i,2);case"qo":return t.ordinalNumber(i,{unit:"quarter"});case"qqq":return t.quarter(i,{width:"abbreviated",context:"standalone"});case"qqqqq":return t.quarter(i,{width:"narrow",context:"standalone"});default:return t.quarter(i,{width:"wide",context:"standalone"})}},M:function(n,e,t){const i=n.getMonth();switch(e){case"M":case"MM":return Mt.M(n,e);case"Mo":return t.ordinalNumber(i+1,{unit:"month"});case"MMM":return t.month(i,{width:"abbreviated",context:"formatting"});case"MMMMM":return t.month(i,{width:"narrow",context:"formatting"});default:return t.month(i,{width:"wide",context:"formatting"})}},L:function(n,e,t){const i=n.getMonth();switch(e){case"L":return String(i+1);case"LL":return M(i+1,2);case"Lo":return t.ordinalNumber(i+1,{unit:"month"});case"LLL":return t.month(i,{width:"abbreviated",context:"standalone"});case"LLLLL":return t.month(i,{width:"narrow",context:"standalone"});default:return t.month(i,{width:"wide",context:"standalone"})}},w:function(n,e,t,i){const a=zn(n,i);return e==="wo"?t.ordinalNumber(a,{unit:"week"}):M(a,e.length)},I:function(n,e,t){const i=An(n);return e==="Io"?t.ordinalNumber(i,{unit:"week"}):M(i,e.length)},d:function(n,e,t){return e==="do"?t.ordinalNumber(n.getDate(),{unit:"date"}):Mt.d(n,e)},D:function(n,e,t){const i=$n(n);return e==="Do"?t.ordinalNumber(i,{unit:"dayOfYear"}):M(i,e.length)},E:function(n,e,t){const i=n.getDay();switch(e){case"E":case"EE":case"EEE":return t.day(i,{width:"abbreviated",context:"formatting"});case"EEEEE":return t.day(i,{width:"narrow",context:"formatting"});case"EEEEEE":return t.day(i,{width:"short",context:"formatting"});default:return t.day(i,{width:"wide",context:"formatting"})}},e:function(n,e,t,i){const a=n.getDay(),o=(a-i.weekStartsOn+8)%7||7;switch(e){case"e":return String(o);case"ee":return M(o,2);case"eo":return t.ordinalNumber(o,{unit:"day"});case"eee":return t.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return t.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return t.day(a,{width:"short",context:"formatting"});default:return t.day(a,{width:"wide",context:"formatting"})}},c:function(n,e,t,i){const a=n.getDay(),o=(a-i.weekStartsOn+8)%7||7;switch(e){case"c":return String(o);case"cc":return M(o,e.length);case"co":return t.ordinalNumber(o,{unit:"day"});case"ccc":return t.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return t.day(a,{width:"narrow",context:"standalone"});case"cccccc":return t.day(a,{width:"short",context:"standalone"});default:return t.day(a,{width:"wide",context:"standalone"})}},i:function(n,e,t){const i=n.getDay(),a=i===0?7:i;switch(e){case"i":return String(a);case"ii":return M(a,e.length);case"io":return t.ordinalNumber(a,{unit:"day"});case"iii":return t.day(i,{width:"abbreviated",context:"formatting"});case"iiiii":return t.day(i,{width:"narrow",context:"formatting"});case"iiiiii":return t.day(i,{width:"short",context:"formatting"});default:return t.day(i,{width:"wide",context:"formatting"})}},a:function(n,e,t){const i=n.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return t.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"aaa":return t.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return t.dayPeriod(i,{width:"narrow",context:"formatting"});default:return t.dayPeriod(i,{width:"wide",context:"formatting"})}},b:function(n,e,t){const i=n.getHours();let a;switch(a=i===12?On:i===0?Rn:i/12>=1?"pm":"am",e){case"b":case"bb":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return t.dayPeriod(a,{width:"narrow",context:"formatting"});default:return t.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(n,e,t){const i=n.getHours();let a;switch(a=i>=17?Fn:i>=12?jn:i>=4?En:In,e){case"B":case"BB":case"BBB":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return t.dayPeriod(a,{width:"narrow",context:"formatting"});default:return t.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(n,e,t){if(e==="ho"){let i=n.getHours()%12;return i===0&&(i=12),t.ordinalNumber(i,{unit:"hour"})}return Mt.h(n,e)},H:function(n,e,t){return e==="Ho"?t.ordinalNumber(n.getHours(),{unit:"hour"}):Mt.H(n,e)},K:function(n,e,t){const i=n.getHours()%12;return e==="Ko"?t.ordinalNumber(i,{unit:"hour"}):M(i,e.length)},k:function(n,e,t){let i=n.getHours();return i===0&&(i=24),e==="ko"?t.ordinalNumber(i,{unit:"hour"}):M(i,e.length)},m:function(n,e,t){return e==="mo"?t.ordinalNumber(n.getMinutes(),{unit:"minute"}):Mt.m(n,e)},s:function(n,e,t){return e==="so"?t.ordinalNumber(n.getSeconds(),{unit:"second"}):Mt.s(n,e)},S:function(n,e){return Mt.S(n,e)},X:function(n,e,t){const i=n.getTimezoneOffset();if(i===0)return"Z";switch(e){case"X":return He(i);case"XXXX":case"XX":return St(i);default:return St(i,":")}},x:function(n,e,t){const i=n.getTimezoneOffset();switch(e){case"x":return He(i);case"xxxx":case"xx":return St(i);default:return St(i,":")}},O:function(n,e,t){const i=n.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+Ye(i,":");default:return"GMT"+St(i,":")}},z:function(n,e,t){const i=n.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+Ye(i,":");default:return"GMT"+St(i,":")}},t:function(n,e,t){return M(Math.trunc(n.getTime()/1e3),e.length)},T:function(n,e,t){return M(n.getTime(),e.length)}};function Ye(n,e=""){const t=n>0?"-":"+",i=Math.abs(n),a=Math.trunc(i/60),o=i%60;return o===0?t+String(a):t+String(a)+e+M(o,2)}function He(n,e){return n%60==0?(n>0?"-":"+")+M(Math.abs(n)/60,2):St(n,e)}function St(n,e=""){const t=n>0?"-":"+",i=Math.abs(n);return t+M(Math.trunc(i/60),2)+e+M(i%60,2)}const Be=(n,e)=>{switch(n){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});default:return e.date({width:"full"})}},Le=(n,e)=>{switch(n){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});default:return e.time({width:"full"})}},Yn={p:Le,P:(n,e)=>{const t=n.match(/(P+)(p+)?/)||[],i=t[1],a=t[2];if(!a)return Be(n,e);let o;switch(i){case"P":o=e.dateTime({width:"short"});break;case"PP":o=e.dateTime({width:"medium"});break;case"PPP":o=e.dateTime({width:"long"});break;default:o=e.dateTime({width:"full"})}return o.replace("{{date}}",Be(i,e)).replace("{{time}}",Le(a,e))}},Hn=/^D+$/,Bn=/^Y+$/,Ln=["D","DD","YY","YYYY"],Qn=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Gn=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Un=/^'([^]*?)'?$/,Xn=/''/g,Jn=/[a-zA-Z]/;function Qe(n,e,t){var u,p,b,x,W,T,O,_;const i=me(),a=(t==null?void 0:t.locale)??i.locale??Wn,o=(t==null?void 0:t.firstWeekContainsDate)??((p=(u=t==null?void 0:t.locale)==null?void 0:u.options)==null?void 0:p.firstWeekContainsDate)??i.firstWeekContainsDate??((x=(b=i.locale)==null?void 0:b.options)==null?void 0:x.firstWeekContainsDate)??1,v=(t==null?void 0:t.weekStartsOn)??((T=(W=t==null?void 0:t.locale)==null?void 0:W.options)==null?void 0:T.weekStartsOn)??i.weekStartsOn??((_=(O=i.locale)==null?void 0:O.options)==null?void 0:_.weekStartsOn)??0,m=bt(n);if(!Tn(m))throw new RangeError("Invalid time value");let c=e.match(Gn).map(l=>{const s=l[0];return s==="p"||s==="P"?(0,Yn[s])(l,a.formatLong):l}).join("").match(Qn).map(l=>{if(l==="''")return{isToken:!1,value:"'"};const s=l[0];if(s==="'")return{isToken:!1,value:Zn(l)};if(Ie[s])return{isToken:!0,value:l};if(s.match(Jn))throw new RangeError("Format string contains an unescaped latin alphabet character `"+s+"`");return{isToken:!1,value:l}});a.localize.preprocessor&&(c=a.localize.preprocessor(m,c));const h={firstWeekContainsDate:o,weekStartsOn:v,locale:a};return c.map(l=>{if(!l.isToken)return l.value;const s=l.value;return(!(t!=null&&t.useAdditionalWeekYearTokens)&&function(d){return Bn.test(d)}(s)||!(t!=null&&t.useAdditionalDayOfYearTokens)&&function(d){return Hn.test(d)}(s))&&function(d,z,C){const F=function(Z,nt,L){const it=Z[0]==="Y"?"years":"days of the month";return`Use \`${Z.toLowerCase()}\` instead of \`${Z}\` (in \`${nt}\`) for formatting ${it} to the input \`${L}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(d,z,C);if(console.warn(F),Ln.includes(d))throw new RangeError(F)}(s,e,String(n)),(0,Ie[s[0]])(m,s,a.localize,h)}).join("")}function Zn(n){const e=n.match(Un);return e?e[1].replace(Xn,"'"):n}function ge(n,e){const t=function(m){const c={},h=m.split(ce.dateTimeDelimiter);let u;if(h.length>2)return c;if(/:/.test(h[0])?u=h[0]:(c.date=h[0],u=h[1],ce.timeZoneDelimiter.test(c.date)&&(c.date=m.split(ce.timeZoneDelimiter)[0],u=m.substr(c.date.length,m.length))),u){const p=ce.timezone.exec(u);p?(c.time=u.replace(p[1],""),c.timezone=p[1]):c.time=u}return c}(n);let i;if(t.date){const m=function(c,h){const u=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+h)+"})|(\\d{2}|[+-]\\d{"+(2+h)+"})$)"),p=c.match(u);if(!p)return{year:NaN,restDateString:""};const b=p[1]?parseInt(p[1]):null,x=p[2]?parseInt(p[2]):null;return{year:x===null?b:100*x,restDateString:c.slice((p[1]||p[2]).length)}}(t.date,2);i=function(c,h){if(h===null)return new Date(NaN);const u=c.match(Kn);if(!u)return new Date(NaN);const p=!!u[4],b=Kt(u[1]),x=Kt(u[2])-1,W=Kt(u[3]),T=Kt(u[4]),O=Kt(u[5])-1;if(p)return function(_,l,s){return l>=1&&l<=53&&s>=0&&s<=6}(0,T,O)?function(_,l,s){const d=new Date(0);d.setUTCFullYear(_,0,4);const z=d.getUTCDay()||7,C=7*(l-1)+s+1-z;return d.setUTCDate(d.getUTCDate()+C),d}(h,T,O):new Date(NaN);{const _=new Date(0);return function(l,s,d){return s>=0&&s<=11&&d>=1&&d<=(ei[s]||(Ge(l)?29:28))}(h,x,W)&&function(l,s){return s>=1&&s<=(Ge(l)?366:365)}(h,b)?(_.setUTCFullYear(h,x,Math.max(b,W)),_):new Date(NaN)}}(m.restDateString,m.year)}if(!i||isNaN(i.getTime()))return new Date(NaN);const a=i.getTime();let o,v=0;if(t.time&&(v=function(m){const c=m.match(Vn);if(!c)return NaN;const h=pe(c[1]),u=pe(c[2]),p=pe(c[3]);return function(b,x,W){return b===24?x===0&&W===0:W>=0&&W<60&&x>=0&&x<60&&b>=0&&b<25}(h,u,p)?h*ze+u*Re+1e3*p:NaN}(t.time),isNaN(v)))return new Date(NaN);if(!t.timezone){const m=new Date(a+v),c=new Date(0);return c.setFullYear(m.getUTCFullYear(),m.getUTCMonth(),m.getUTCDate()),c.setHours(m.getUTCHours(),m.getUTCMinutes(),m.getUTCSeconds(),m.getUTCMilliseconds()),c}return o=function(m){if(m==="Z")return 0;const c=m.match(ti);if(!c)return 0;const h=c[1]==="+"?-1:1,u=parseInt(c[2]),p=c[3]&&parseInt(c[3])||0;return function(b,x){return x>=0&&x<=59}(0,p)?h*(u*ze+p*Re):NaN}(t.timezone),isNaN(o)?new Date(NaN):new Date(a+v+o)}const ce={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},Kn=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,Vn=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,ti=/^([+-])(\d{2})(?::?(\d{2}))?$/;function Kt(n){return n?parseInt(n):1}function pe(n){return n&&parseFloat(n.replace(",","."))||0}const ei=[31,null,31,30,31,30,31,31,30,31,30,31];function Ge(n){return n%400==0||n%4==0&&n%100!=0}var ni=k('<span slot="text" class="c-history-header--ellipsis svelte-8btr94"> </span>'),ii=k('<span class="c-history-header--ellipsis-left svelte-8btr94"> </span>'),ri=k('<div class="c-history-header__item svelte-8btr94"><span class="c-history-header__label svelte-8btr94">File:</span> <!></div>'),ai=k('<div class="c-history-header__item svelte-8btr94"><span class="c-history-header__label svelte-8btr94">Instruction:</span> <span class="c-history-header--ellipsis svelte-8btr94"> </span></div>'),oi=k('<div class="c-history-header__item svelte-8btr94"><span class="c-history-header--ellipsis svelte-8btr94"> </span></div>'),si=k('<div class="c-history-header svelte-8btr94"><div class="c-history-header__timestamp svelte-8btr94"> </div> <div class="c-history-header__metadata svelte-8btr94"><div class="c-history-header__item svelte-8btr94"><span class="c-history-header__label svelte-8btr94">Request ID:</span> <!></div> <!> <!> <!></div></div>');function an(n,e){Ct(e,!1);let t=X(e,"occuredAt",8),i=X(e,"requestID",8),a=X(e,"pathName",8,""),o=X(e,"repoRoot",8),v=X(e,"prompt",8,""),m=X(e,"others",24,()=>[]);function c(){$t.postMessage({type:ct.openFile,data:{repoRoot:o(),pathName:a()}})}Ne();var h=si(),u=f(h),p=f(u),b=N(u,2),x=f(b),W=N(f(x),2);yn(W,{get text(){return i()},variant:"ghost-block",color:"neutral",size:1,tooltip:"Copy Request ID",$$slots:{text:(d,z)=>{var C=ni(),F=f(C);ot(()=>st(F,i())),y(d,C)}}});var T=N(x,2),O=d=>{var z=ri(),C=N(f(z),2);ke(C,{variant:"ghost-block",color:"neutral",size:1,title:"Click to open file",$$events:{click:c},children:(F,Z)=>{var nt=ii(),L=f(nt);ot(()=>st(L,`‎${a()??""}`)),y(F,nt)},$$slots:{default:!0}}),y(d,z)};B(T,d=>{a()&&d(O)});var _=N(T,2),l=d=>{var z=ai(),C=N(f(z),2),F=f(C);ot(()=>st(F,v())),y(d,z)};B(_,d=>{v()&&d(l)});var s=N(_,2);Wt(s,1,m,ee,(d,z)=>{var C=oi(),F=f(C),Z=f(F);ot(()=>st(Z,r(z))),y(d,C)}),ot(d=>st(p,d),[()=>(we(Qe),we(t()),be(()=>Qe(t(),"p 'on' P")))],Vt),y(n,h),qt()}const _e={lineNumbers:"off",padding:{top:18,bottom:18}};var ci=k('<div class="c-completion-code-block svelte-krgqjl"><div class="c-completion-code-block__content svelte-krgqjl"><!></div></div>');const Ue=["Thanks for the feedback!","Thanks for improving Augment!","Thanks for taking the time!","Thanks for helping Augment improve!","Thanks for helping us enhance Augment!","We value your input. Thanks for improving Augment!","Your insights are making a difference. Cheers!"],te=new class{constructor(){We(this,"_state");this._state=$t.getState()||{},this._state.feedback=this._state.feedback||{}}getFeedback(n){return this._state.feedback[n]?this._state.feedback[n]:{selectedRating:pt.unset,feedbackNote:""}}setFeedback(n,e){this._state.feedback[n]=e,$t.setState(this._state)}cleanupFeedback(n){for(const e of Object.keys(this._state.feedback))n[e]||delete this._state.feedback[e];$t.setState(this._state)}};function ue(n){return typeof n=="string"?n:n==null?void 0:n.value}var ui=k('<div><div class="background-slider svelte-axvozx"></div> <!></div>');function on(n,e){Ct(e,!1);let t=X(e,"options",8),i=X(e,"size",8,2),a=X(e,"disabled",8,!1),o=X(e,"onSelectOption",8),v=X(e,"activeOption",28,()=>ue(t()[0])),m=Gt(),c=Gt();function h(){var s;const _=(s=r(m))==null?void 0:s.querySelectorAll(".c-toggle-button__button");if(!_)return;const l=_[t().findIndex(d=>ue(d)===v())];if(r(m)&&r(c)&&l){const d=l.getBoundingClientRect(),z=r(m).getBoundingClientRect();he(c,r(c).style.left=d.left-z.left+"px"),he(c,r(c).style.width=`${d.width}px`),he(c,r(c).style.height=`${d.height}px`)}}let u=Gt(),p=Gt(!1),b=Gt();Je(()=>{var _;(_=r(u))==null||_.disconnect(),S(u,void 0),clearTimeout(r(b))}),$e(()=>we(v()),()=>{v()&&h()}),$e(()=>(r(m),r(u),r(b)),()=>{r(m)&&!r(u)&&(S(u,new ResizeObserver(()=>{S(p,!0),h(),clearTimeout(r(b)),S(b,setTimeout(()=>{S(p,!1)},100))})),r(u).observe(r(m)))}),mn(),Ne();var x=ui();let W;var T=f(x);Ae(T,_=>S(c,_),()=>r(c));var O=N(T,2);Wt(O,1,t,ee,(_,l)=>{const s=Vt(()=>r(l)===v()?"c-toggle-button__button--active":"");Nn(_,{get size(){return i()},get disabled(){return a()},variant:"ghost",color:"neutral",get class(){return`c-toggle-button__button ${r(s)??""}`},$$events:{click:()=>function(d){!a()&&o()(d)&&v(d)}(ue(r(l)))},children:(d,z)=>{var C=Me(),F=vt(C);const Z=Vt(()=>(r(l),be(()=>ue(r(l)))));fn(F,e,"option-button-contents",{get option(){return r(Z)},get size(){return i()}},nt=>{const L=Vt(()=>i()===.5?1:i());gn(nt,{get size(){return r(L)},children:(it,ut)=>{var ht=ye();ot(()=>st(ht,(r(l),be(()=>typeof r(l)=="string"?r(l):r(l).label)))),y(it,ht)},$$slots:{default:!0}})}),y(d,C)},$$slots:{default:!0}})}),Ae(x,_=>S(m,_),()=>r(m)),ot(_=>W=xe(x,1,"c-toggle-button svelte-axvozx",null,W,_),[()=>({"c-toggle-button--disabled":a(),"c-toggle-button--size-0_5":i()===.5,"c-toggle-button--size-1":i()===1,"c-toggle-button--size-2":i()===2,"c-toggle-button--size-3":i()===3,"c-toggle-button--size-4":i()===4,"c-toggle-button--resizing":r(p)})],Vt),y(n,x),qt()}var li=k('<div class="c-unified-history-item__tabs svelte-179mxe5"><!></div>'),di=k('<div class="c-unified-history-item__code-block svelte-179mxe5"><!></div>'),mi=(n,e,t)=>e(r(t)),hi=k('<div class="c-completion-code-block" role="button" tabindex="0"><pre data-language="plaintext"><code><span> </span></code></pre></div> <div> </div> <section>original: <!> modified: <!></section>',1),fi=(n,e,t)=>e(r(t)),gi=k('<div class="c-completion-code-block" role="button" tabindex="0"><pre data-language="plaintext" class="c-next-edit-addition svelte-179mxe5"><code><span> </span></code></pre></div>'),pi=k('<section><!> <div class="c-unified-history-item__no-modifications svelte-179mxe5">Unchanged locations:</div> <!></section>'),vi=k('<div class="c-unified-history-item__feedback-area svelte-179mxe5"><div class="c-unified-history-item__feedback-content svelte-179mxe5"><!></div> <div class="c-unified-history-item__feedback-actions svelte-179mxe5"><!> <!></div></div>'),bi=k('<div class="c-unified-history-item__header svelte-179mxe5"><!></div> <!> <div class="c-unified-history-item__content svelte-179mxe5"><!></div> <div class="c-unified-history-item__footer svelte-179mxe5"><div class="c-unified-history-item__ratings svelte-179mxe5"><div class="c-unified-history-item__rating-buttons svelte-179mxe5"><!> <!></div> <div class="c-unified-history-item__thankyou svelte-179mxe5"> </div></div> <!></div>',1);function ve(n,e){Ct(e,!0);let t=X(e,"completion",19,()=>{}),i=X(e,"nextEdit",19,()=>{}),a=X(e,"demo",3,!1),o=P(()=>t()?"completion":"nextEdit"),v=P(()=>{var w,$;return((w=t())==null?void 0:w.requestId)||(($=i())==null?void 0:$.requestId)||""}),m=P(()=>{var w,$;return((w=t())==null?void 0:w.occuredAt)||(($=i())==null?void 0:$.occurredAt)||new Date}),c=P(()=>{var w;return((w=t())==null?void 0:w.pathName)||""}),h=P(()=>{var w,$,J;return((w=t())==null?void 0:w.repoRoot)||((J=($=i())==null?void 0:$.qualifiedPathName)==null?void 0:J.rootPath)||""}),u=P(()=>r(o)==="completion"?"Completion":"Next Edit");const p=t()?["Completion"]:["Next Edit"];let b,x,W=P(()=>"Leave feedback about this "+(r(o)==="completion"?"completion":"next edit")),T=P(()=>te.getFeedback(r(v))),O=mt(!1),_=mt(""),l=mt(!1),s=null,d=mt(""),z=mt(void 0),C=mt(Yt([])),F=mt(Yt([]));function Z(){S(_,Ue[Math.floor(Math.random()*Ue.length)],!0),b&&clearTimeout(b),b=setTimeout(()=>{S(_,"")},4e3)}function nt(w){r(T).selectedRating=w,s=w,S(d,""),S(l,!0)}function L(){S(l,!1),s=null,S(d,""),r(T).selectedRating=pt.unset}function it(){s&&r(d).trim().length!==0&&(function(w,$){if(Z(),x=r(T).selectedRating,w!==pt.unset&&(r(T).selectedRating=w),a())return;let J=$||r(T).feedbackNote;te.setFeedback(r(v),r(T)),S(O,!0);const zt=r(o)==="completion"?ct.completionRating:ct.nextEditRating;$t.postMessage({type:zt,data:{requestId:r(v),rating:w,note:J.trim()}})}(s,r(d).trim()),L())}function ut(w){$t.postMessage({type:ct.openFile,data:{repoRoot:w.qualifiedPathName.rootPath,pathName:w.result.path,range:w.lineRange,differentTab:!0}}),S(z,w,!0)}function ht(w){return S(u,w),!0}tn(()=>{i()&&(S(C,i().suggestions.filter(w=>w.changeType!==Oe.noop),!0),S(F,i().suggestions.filter(w=>w.changeType===Oe.noop),!0))}),Ke("message",Ve,function(w){if(a())return;const $=w.data;switch($.type){case ct.completionRatingDone:{const{requestId:J}=$.data;if(J!==r(v))return;S(O,!1),$.data.success||(r(T).selectedRating=x,te.setFeedback(r(v),r(T)));break}case ct.nextEditRatingDone:{const{requestId:J}=$.data;if(J!==r(v))return;S(O,!1),$.data.success||(r(T).selectedRating=x,te.setFeedback(r(v),r(T)));break}}});const Ht=P(()=>"c-unified-history-item "+(r(O)?"c-unified-history-item--sending-feedback":""));_n(n,{size:2,variant:"surface",get class(){return r(Ht)},children:(w,$)=>{var J=bi(),zt=vt(J),K=f(zt);const lt=P(()=>i()?[`Request type: ${i().mode}/${i().scope}`]:void 0);an(K,{get occuredAt(){return r(m)},get requestID(){return r(v)},get pathName(){return r(c)},get repoRoot(){return r(h)},get others(){return r(lt)}});var dt=N(zt,2),V=A=>{var U=li();on(f(U),{get options(){return p},onSelectOption:ht,get activeOption(){return r(u)},size:1}),y(A,U)};B(dt,A=>{p.length>1&&A(V)});var wt=N(dt,2),Rt=f(wt),Tt=A=>{var U=Me(),tt=vt(U);Wt(tt,17,()=>t().completions,ee,(ft,Q)=>{var I=di();(function(et,j){Ct(j,!0);const g=function(Nt){const at=Nt.split(`
`).slice(-6);for(let gt=0;gt<at.length;gt++)if(at[gt].trim().length>0)return at.slice(gt).join(`
`);return""}(j.prefix),E=(Y=j.suffix,!!(xt=j.completion.skippedSuffix)&&Y.indexOf(xt)===0);var Y,xt;const Dt=E?function(Nt,at){return at?Nt.indexOf(at)!==0?Nt:Nt.slice(at.length):Nt}(j.suffix,j.completion.skippedSuffix):j.suffix,kt=function(Nt){const at=Nt.split(`
`).slice(0,6);for(let gt=at.length-1;gt>=0;gt--)if(at[gt].trim().length>0)return at.slice(0,gt+1).join(`
`);return""}(Dt),R=j.completion.text,G=E?j.completion.skippedSuffix:"",rt=j.completion.suffixReplacementText,_t=g+R+G+rt+kt,H=xn.createModel(_t,"plaintext"),oe=H.getPositionAt(0),Lt=H.getPositionAt(g.length),se=H.getPositionAt(g.length),It=H.getPositionAt(g.length+R.length),Qt=H.getPositionAt(g.length+R.length),Te=H.getPositionAt(g.length+R.length+G.length),Pe=H.getPositionAt(g.length+R.length+G.length),De=H.getPositionAt(g.length+R.length+G.length+rt.length),Se=H.getPositionAt(g.length+R.length+G.length+rt.length),Ce=H.getPositionAt(_t.length),sn=[{range:new Ut(oe.lineNumber,oe.column,Lt.lineNumber,Lt.column),options:{inlineClassName:"c-completion-code-block--dull"}},{range:new Ut(Se.lineNumber,Se.column,Ce.lineNumber,Ce.column),options:{inlineClassName:"c-completion-code-block--dull"}},{range:new Ut(se.lineNumber,se.column,It.lineNumber,It.column),options:{inlineClassName:"c-completion-code-block--addition"}},{range:new Ut(Pe.lineNumber,Pe.column,De.lineNumber,De.column),options:{inlineClassName:"c-completion-code-block--addition"}},{range:new Ut(Qt.lineNumber,Qt.column,Te.lineNumber,Te.column),options:{inlineClassName:"c-completion-code-block--strikethrough"}}];Je(()=>{H==null||H.dispose()});var qe=ci(),cn=f(qe),un=f(cn);pn(un,{get options(){return _e},get model(){return H},get decorations(){return sn}}),y(et,qe),qt()})(f(I),{get completion(){return r(Q)},get prefix(){return t().prefix},get suffix(){return t().suffix}}),y(ft,I)}),y(A,U)},Ot=(A,U)=>{var tt=ft=>{var Q=pi(),I=f(Q);Wt(I,17,()=>r(C),ee,(j,g)=>{var E=hi(),Y=vt(E);Y.__click=[mi,ut,g];var xt=P(()=>je("Enter",()=>ut(r(g))));Y.__keydown=function(...It){var Qt;(Qt=r(xt))==null||Qt.apply(this,It)};var Dt=f(Y),kt=f(Dt),R=f(kt);let G;var rt=f(R),_t=N(Y,2),H=f(_t),oe=N(_t,2),Lt=N(f(oe));le(Lt,{get text(){return r(g).result.existingCode},get pathName(){return r(g).qualifiedPathName.relPath},options:{lineNumbers:"off"}});var se=N(Lt,2);le(se,{get text(){return r(g).result.suggestedCode},get pathName(){return r(g).qualifiedPathName.relPath},options:{lineNumbers:"off"}}),ot(It=>{G=xe(R,1,"c-next-edit-addition svelte-179mxe5",null,G,It),st(rt,`${r(g).qualifiedPathName.relPath??""}: ${r(g).lineRange.start+(r(g).lineRange.start<r(g).lineRange.stop?1:0)}-${r(g).lineRange.stop??""}`),st(H,r(g).result.changeDescription)},[()=>({"c-next-edit-addition-clicked":r(z)===r(g)})]),y(j,E)});var et=N(I,4);Wt(et,17,()=>r(F),ee,(j,g)=>{var E=gi();E.__click=[fi,ut,g];var Y=P(()=>je("Enter",()=>ut(r(g))));E.__keydown=function(...rt){var _t;(_t=r(Y))==null||_t.apply(this,rt)};var xt=f(E),Dt=f(xt),kt=f(Dt);let R;var G=f(kt);ot(rt=>{R=xe(kt,1,"c-next-edit-addition svelte-179mxe5",null,R,rt),st(G,`${r(g).qualifiedPathName.relPath??""}: ${r(g).lineRange.start+(r(g).lineRange.start<r(g).lineRange.stop?1:0)}-${r(g).lineRange.stop??""}`)},[()=>({"c-next-edit-addition-clicked":r(z)===r(g)})]),y(j,E)}),y(ft,Q)};B(A,ft=>{r(o)==="nextEdit"&&i()&&ft(tt)},U)};B(Rt,A=>{r(o)==="completion"&&t()?A(Tt):A(Ot,!1)});var Bt=N(wt,2),Et=f(Bt),jt=f(Et),Ft=f(jt);const q=P(()=>r(T).selectedRating===pt.positive?"success":"neutral");Ee(Ft,{variant:"ghost",get color(){return r(q)},size:2,get disabled(){return r(O)},get title(){return r(W)},onclick:()=>nt(pt.positive),children:(A,U)=>{const tt=P(()=>r(T).selectedRating===pt.positive);Fe(A,{iconName:"thumb_up",get fill(){return r(tt)}})},$$slots:{default:!0}});var D=N(Ft,2);const Pt=P(()=>r(T).selectedRating===pt.negative?"error":"neutral");Ee(D,{variant:"ghost",get color(){return r(Pt)},size:2,get disabled(){return r(O)},get title(){return r(W)},onclick:()=>nt(pt.negative),children:(A,U)=>{const tt=P(()=>r(T).selectedRating===pt.negative);Fe(A,{iconName:"thumb_down",get fill(){return r(tt)}})},$$slots:{default:!0}});var yt=N(jt,2),ie=f(yt),re=N(Et,2),ae=A=>{var U=vi(),tt=f(U),ft=f(tt);kn(ft,{rows:"4",placeholder:"Enter your feedback...",resize:"none",get value(){return r(d)},set value(g){S(d,g,!0)}});var Q=N(tt,2),I=f(Q);ke(I,{variant:"ghost",size:2,onclick:L,children:(g,E)=>{var Y=ye("Cancel");y(g,Y)},$$slots:{default:!0}});var et=N(I,2);const j=P(()=>r(d).trim().length===0);ke(et,{variant:"solid",size:2,get disabled(){return r(j)},onclick:it,children:(g,E)=>{var Y=ye("Share Feedback");y(g,Y)},$$slots:{default:!0}}),y(A,U)};B(re,A=>{r(l)&&A(ae)}),ot(()=>st(ie,r(_))),y(w,J)},$$slots:{default:!0}}),qt()}Ze(["click","keydown"]);var wi=k(`<div class="l-no-items svelte-10bvc8"><div class="l-no-items__msg svelte-10bvc8"><h2>History.</h2> <p>As you use Augment, we'll display the most recent suggestions here so you can tell us about
      any particularly good, or bad, suggestions.</p> <p>Below is an example of the information and feedback form we'll display for each suggestion.</p></div> <div class="l-no-items__divider svelte-10bvc8"></div> <div class="l-no-items__example svelte-10bvc8"><!></div></div>`);function Xe(n,e,t){S(e,t.instruction.requestId,!0)}var yi=k('<div class="c-instruction-item__no-modifications svelte-15p7ohn">No modification to original code</div>'),xi=k("modified: <!>",1),ki=k('<div class="c-instruction-item__no-modifications svelte-15p7ohn">No modification to original code</div>'),_i=k("<section>original: <!> <!></section>"),Ni=k('<div class="c-instruction-item__no-modifications svelte-15p7ohn" role="button" tabindex="0">Click to view diff</div>'),Mi=k('<div class="c-instruction-item svelte-15p7ohn"><!> <!></div>');Ze(["keyup","click"]);var Ti=k('<div class="l-items-list__empty svelte-5e6wj2"><!></div>'),Pi=k('<div class="l-items-list__divider svelte-5e6wj2"></div>'),Di=k('<div class="l-items-list__item svelte-5e6wj2"><!></div> <!>',1),Si=k('<div class="l-items-list__instructions-section svelte-5e6wj2"><h3 class="l-items-list__section-title svelte-5e6wj2">Instructions</h3> <div class="l-items-list__content svelte-5e6wj2"></div></div>'),Ci=k('<div class="l-items-list__empty-panel svelte-5e6wj2"><p> </p></div>'),qi=k('<div class="l-items-list__divider svelte-5e6wj2"></div>'),Wi=k('<div class="l-items-list__item svelte-5e6wj2"><!></div> <!>',1),$i=k('<div class="l-items-list__content svelte-5e6wj2"></div>'),Ai=k('<!> <!> <div class="l-items-list__panel-content"><!></div>',1),zi=k('<main class="l-items-list svelte-5e6wj2"><!></main>');hn(function(n,e){Ct(e,!0);let t=Yt({}),i=Yt({}),a=Yt({});function o(l){for(const s of l)t[s.requestId]||(t[s.requestId]={...s,occuredAt:ge(s.occuredAt)});te.cleanupFeedback(t)}function v(l){for(const s of l)if(!i[s.requestId]){if(typeof s.occuredAt=="string"){const d=s.occuredAt;s.occuredAt=ge(d)}i[s.requestId]={...s}}}function m(l){for(const s of l)s.suggestions.length!==0&&(a[s.requestId]={requestId:s.requestId,occuredAt:ge(s.occurredAt),result:s})}$t.postMessage({type:ct.historyLoaded});let c=mt(Yt([]));tn(()=>{S(c,[...Object.values(i),...Object.values(t),...Object.values(a)].sort((l,s)=>s.occuredAt.getTime()-l.occuredAt.getTime()),!0)});let h=mt("Completions"),u=P(()=>r(c).filter(l=>"completions"in l)),p=P(()=>r(c).filter(l=>"result"in l)),b=P(()=>r(c).filter(l=>"prompt"in l)),x=P(()=>[{value:"Completions",label:`Completions ${r(u).length}`},{value:"Next Edits",label:`Next Edits ${r(p).length}`}]),W=P(()=>r(h)==="Completions"?r(u):r(p));function T(l){return S(h,l,!0),!0}var O=Me();Ke("message",Ve,function(l){const s=l.data;switch(s.type){case ct.historyInitialize:v(s.data.instructions),o(s.data.completionRequests),m(s.data.nextEdits);break;case ct.completions:o(s.data);break;case ct.instructions:v(s.data);break;case ct.nextEditSuggestions:m([s.data])}});var _=vt(O);vn(_,()=>bn.Root,(l,s)=>{s(l,{children:(d,z)=>{var C=zi(),F=f(C),Z=L=>{var it=Ti();(function(ut,ht){Ct(ht,!1);const Ht={occuredAt:new Date,requestId:"12345678-1234-1234-1234-123456789123",repoRoot:"/home/<USER>/projects/example-project",pathName:"src/example.js",prefix:"co",completions:[{text:'nsole.log("Hello World.");',skippedSuffix:"",suffixReplacementText:""}],suffix:`

`};Ne();var w=wi(),$=N(f(w),4);ve(f($),{get completion(){return Ht},demo:!0}),y(ut,w),qt()})(f(it),{}),y(L,it)},nt=L=>{var it=Ai(),ut=vt(it);on(ut,{get options(){return r(x)},onSelectOption:T,get activeOption(){return r(h)},size:2});var ht=N(ut,2),Ht=K=>{var lt=Si(),dt=N(f(lt),2);Wt(dt,23,()=>r(b),V=>V.requestId,(V,wt,Rt)=>{var Tt=Di(),Ot=vt(Tt),Bt=f(Ot);const Et=P(()=>function(q){if(!("prompt"in q))throw new Error("wrong type");if("completions"in q)throw new Error("wrong type");return q}(r(wt)));(function(q,D){Ct(D,!0);let Pt=mt(void 0);function yt(Q){const I=Q.split(`
`);for(let et=I.length-1;et>=0;et--)if(I[et].trim().length>0)return I.slice(0,et+1).join(`
`);return""}let ie=P(()=>yt(D.instruction.selectedText)),re=P(()=>yt(D.instruction.modifiedText));var ae=Mi(),A=f(ae);an(A,{get occuredAt(){return D.instruction.occuredAt},get requestID(){return D.instruction.requestId},get pathName(){return D.instruction.pathName},get repoRoot(){return D.instruction.repoRoot},get prompt(){return D.instruction.prompt}});var U=N(A,2),tt=Q=>{var I=yi();y(Q,I)},ft=(Q,I)=>{var et=g=>{var E=_i(),Y=N(f(E));le(Y,{get options(){return _e},get text(){return r(ie)},get pathName(){return D.instruction.pathName}});var xt=N(Y,2),Dt=R=>{var G=xi(),rt=N(vt(G));le(rt,{get options(){return _e},get text(){return r(re)},get pathName(){return D.instruction.pathName}}),y(R,G)},kt=R=>{var G=ki();y(R,G)};B(xt,R=>{r(ie)!==r(re)?R(Dt):R(kt,!1)}),y(g,E)},j=g=>{var E=Ni();E.__keyup=[Xe,Pt,D],E.__click=[Xe,Pt,D],y(g,E)};B(Q,g=>{D.instruction.userRequested||r(Pt)===D.instruction.requestId?g(et):g(j,!1)},I)};B(U,Q=>{D.instruction.selectedText===D.instruction.modifiedText?Q(tt):Q(ft,!1)}),y(q,ae),qt()})(Bt,{get instruction(){return r(Et)}});var jt=N(Ot,2),Ft=q=>{var D=Pi();y(q,D)};B(jt,q=>{r(Rt)<r(b).length-1&&q(Ft)}),y(V,Tt)}),y(K,lt)};B(ht,K=>{r(b).length>0&&K(Ht)});var w=N(ht,2),$=f(w),J=K=>{var lt=Ci(),dt=f(lt),V=f(dt);ot(wt=>st(V,`No ${wt??""} found.`),[()=>r(h).toLowerCase()]),y(K,lt)},zt=K=>{var lt=$i();Wt(lt,23,()=>r(W),dt=>dt.requestId,(dt,V,wt)=>{var Rt=Wi(),Tt=vt(Rt),Ot=f(Tt),Bt=q=>{ve(q,{get completion(){return r(V)}})},Et=(q,D)=>{var Pt=yt=>{ve(yt,{get nextEdit(){return r(V).result}})};B(q,yt=>{"result"in r(V)&&yt(Pt)},D)};B(Ot,q=>{"completions"in r(V)?q(Bt):q(Et,!1)});var jt=N(Tt,2),Ft=q=>{var D=qi();y(q,D)};B(jt,q=>{r(wt)<r(W).length-1&&q(Ft)}),y(dt,Rt)}),y(K,lt)};B($,K=>{r(W).length===0?K(J):K(zt,!1)}),y(L,it)};B(F,L=>{r(c).length?L(nt,!1):L(Z)}),y(d,C)},$$slots:{default:!0}})}),y(n,O),qt()},{target:document.getElementById("app")});
