import{g as we,d as ke}from"./chunk-ASOPGD6M-CUkBzM7N.js";import{_ as g,s as Te,g as ve,a as Re,b as De,c as Nt,d as Vt,l as he,e as Ne,f as Pe,h as wt,i as ye,j as Be,w as Me,k as Gt,m as de}from"./AugmentMessage-DuZeVL4h.js";import"./legacy-AoIeRrIA.js";import"./host-qgbK079d.js";import"./index-Dtf_gCqL.js";import"./SpinnerAugment-mywmfXFR.js";import"./CalloutAugment-Db5scVK5.js";import"./CardAugment-DwIptXof.js";import"./IconButtonAugment-DZyIKjh7.js";import"./event-modifiers-Bz4QCcZc.js";import"./index-BLDiLrXG.js";import"./async-messaging-Dmg2N9Pf.js";import"./chat-types-BfwvR7Kn.js";import"./focusTrapStack-CaEmYw0i.js";import"./isObjectLike-CnrzNkq5.js";import"./input-DCBQtNgo.js";import"./BaseTextInput-D2MYbf3a.js";import"./lodash-CwNMWAFx.js";import"./index-BdF7sLLk.js";import"./Filespan-BqOh8yIt.js";import"./diff-operations-DEY-phZ8.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./index-CJlvVQMt.js";import"./keypress-DD1aQVr0.js";import"./folder-opened-D5l3axuC.js";import"./message-broker-EhzME3pO.js";import"./file-type-utils-D6OEcQY2.js";import"./types-CGlLNakm.js";import"./chat-model-context-DPgWxlAp.js";import"./await-BdVIougb.js";import"./OpenFileButton-CWm-PsCk.js";import"./index-B528snJk.js";import"./remote-agents-client-BMR_qMG5.js";import"./SuccessfulButton-xgZ5Aax4.js";import"./ButtonAugment-D7YBjBq5.js";import"./CollapseButtonAugment-BXWAqMp6.js";import"./partner-mcp-utils-BH31APX7.js";import"./MaterialIcon-B_3jxWk_.js";import"./CopyButton-DfcpqvjI.js";import"./TextAreaAugment-B1LKPxPr.js";import"./ellipsis-5yZhsJie.js";import"./LanguageIcon-D_oFq7vE.js";import"./augment-logo-BqyYuvys.js";import"./BadgeRoot-CMDpgWKP.js";import"./repository-utils-DzBkqZ7a.js";var Yt=function(){var e=g(function(m,k,x,b){for(x=x||{},b=m.length;b--;x[m[b]]=k);return x},"o"),t=[1,24],s=[1,25],l=[1,26],o=[1,27],i=[1,28],r=[1,63],n=[1,64],a=[1,65],d=[1,66],p=[1,67],u=[1,68],f=[1,69],O=[1,29],v=[1,30],R=[1,31],P=[1,32],X=[1,33],Y=[1,34],H=[1,35],q=[1,36],V=[1,37],G=[1,38],K=[1,39],J=[1,40],Z=[1,41],tt=[1,42],et=[1,43],it=[1,44],at=[1,45],nt=[1,46],rt=[1,47],st=[1,48],ot=[1,50],lt=[1,51],ct=[1,52],ht=[1,53],dt=[1,54],pt=[1,55],ut=[1,56],yt=[1,57],gt=[1,58],ft=[1,59],bt=[1,60],Ct=[14,42],Wt=[14,34,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],vt=[12,14,34,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],E=[1,82],A=[1,83],S=[1,84],C=[1,85],w=[12,14,42],ne=[12,14,33,42],Bt=[12,14,33,42,76,77,79,80],xt=[12,33],Qt=[34,36,37,38,39,40,41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],$t={trace:g(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mermaidDoc:4,direction:5,direction_tb:6,direction_bt:7,direction_rl:8,direction_lr:9,graphConfig:10,C4_CONTEXT:11,NEWLINE:12,statements:13,EOF:14,C4_CONTAINER:15,C4_COMPONENT:16,C4_DYNAMIC:17,C4_DEPLOYMENT:18,otherStatements:19,diagramStatements:20,otherStatement:21,title:22,accDescription:23,acc_title:24,acc_title_value:25,acc_descr:26,acc_descr_value:27,acc_descr_multiline_value:28,boundaryStatement:29,boundaryStartStatement:30,boundaryStopStatement:31,boundaryStart:32,LBRACE:33,ENTERPRISE_BOUNDARY:34,attributes:35,SYSTEM_BOUNDARY:36,BOUNDARY:37,CONTAINER_BOUNDARY:38,NODE:39,NODE_L:40,NODE_R:41,RBRACE:42,diagramStatement:43,PERSON:44,PERSON_EXT:45,SYSTEM:46,SYSTEM_DB:47,SYSTEM_QUEUE:48,SYSTEM_EXT:49,SYSTEM_EXT_DB:50,SYSTEM_EXT_QUEUE:51,CONTAINER:52,CONTAINER_DB:53,CONTAINER_QUEUE:54,CONTAINER_EXT:55,CONTAINER_EXT_DB:56,CONTAINER_EXT_QUEUE:57,COMPONENT:58,COMPONENT_DB:59,COMPONENT_QUEUE:60,COMPONENT_EXT:61,COMPONENT_EXT_DB:62,COMPONENT_EXT_QUEUE:63,REL:64,BIREL:65,REL_U:66,REL_D:67,REL_L:68,REL_R:69,REL_B:70,REL_INDEX:71,UPDATE_EL_STYLE:72,UPDATE_REL_STYLE:73,UPDATE_LAYOUT_CONFIG:74,attribute:75,STR:76,STR_KEY:77,STR_VALUE:78,ATTRIBUTE:79,ATTRIBUTE_EMPTY:80,$accept:0,$end:1},terminals_:{2:"error",6:"direction_tb",7:"direction_bt",8:"direction_rl",9:"direction_lr",11:"C4_CONTEXT",12:"NEWLINE",14:"EOF",15:"C4_CONTAINER",16:"C4_COMPONENT",17:"C4_DYNAMIC",18:"C4_DEPLOYMENT",22:"title",23:"accDescription",24:"acc_title",25:"acc_title_value",26:"acc_descr",27:"acc_descr_value",28:"acc_descr_multiline_value",33:"LBRACE",34:"ENTERPRISE_BOUNDARY",36:"SYSTEM_BOUNDARY",37:"BOUNDARY",38:"CONTAINER_BOUNDARY",39:"NODE",40:"NODE_L",41:"NODE_R",42:"RBRACE",44:"PERSON",45:"PERSON_EXT",46:"SYSTEM",47:"SYSTEM_DB",48:"SYSTEM_QUEUE",49:"SYSTEM_EXT",50:"SYSTEM_EXT_DB",51:"SYSTEM_EXT_QUEUE",52:"CONTAINER",53:"CONTAINER_DB",54:"CONTAINER_QUEUE",55:"CONTAINER_EXT",56:"CONTAINER_EXT_DB",57:"CONTAINER_EXT_QUEUE",58:"COMPONENT",59:"COMPONENT_DB",60:"COMPONENT_QUEUE",61:"COMPONENT_EXT",62:"COMPONENT_EXT_DB",63:"COMPONENT_EXT_QUEUE",64:"REL",65:"BIREL",66:"REL_U",67:"REL_D",68:"REL_L",69:"REL_R",70:"REL_B",71:"REL_INDEX",72:"UPDATE_EL_STYLE",73:"UPDATE_REL_STYLE",74:"UPDATE_LAYOUT_CONFIG",76:"STR",77:"STR_KEY",78:"STR_VALUE",79:"ATTRIBUTE",80:"ATTRIBUTE_EMPTY"},productions_:[0,[3,1],[3,1],[5,1],[5,1],[5,1],[5,1],[4,1],[10,4],[10,4],[10,4],[10,4],[10,4],[13,1],[13,1],[13,2],[19,1],[19,2],[19,3],[21,1],[21,1],[21,2],[21,2],[21,1],[29,3],[30,3],[30,3],[30,4],[32,2],[32,2],[32,2],[32,2],[32,2],[32,2],[32,2],[31,1],[20,1],[20,2],[20,3],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,1],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[35,1],[35,2],[75,1],[75,2],[75,1],[75,1]],performAction:g(function(m,k,x,b,T,h,Rt){var y=h.length-1;switch(T){case 3:b.setDirection("TB");break;case 4:b.setDirection("BT");break;case 5:b.setDirection("RL");break;case 6:b.setDirection("LR");break;case 8:case 9:case 10:case 11:case 12:b.setC4Type(h[y-3]);break;case 19:b.setTitle(h[y].substring(6)),this.$=h[y].substring(6);break;case 20:b.setAccDescription(h[y].substring(15)),this.$=h[y].substring(15);break;case 21:this.$=h[y].trim(),b.setTitle(this.$);break;case 22:case 23:this.$=h[y].trim(),b.setAccDescription(this.$);break;case 28:h[y].splice(2,0,"ENTERPRISE"),b.addPersonOrSystemBoundary(...h[y]),this.$=h[y];break;case 29:h[y].splice(2,0,"SYSTEM"),b.addPersonOrSystemBoundary(...h[y]),this.$=h[y];break;case 30:b.addPersonOrSystemBoundary(...h[y]),this.$=h[y];break;case 31:h[y].splice(2,0,"CONTAINER"),b.addContainerBoundary(...h[y]),this.$=h[y];break;case 32:b.addDeploymentNode("node",...h[y]),this.$=h[y];break;case 33:b.addDeploymentNode("nodeL",...h[y]),this.$=h[y];break;case 34:b.addDeploymentNode("nodeR",...h[y]),this.$=h[y];break;case 35:b.popBoundaryParseStack();break;case 39:b.addPersonOrSystem("person",...h[y]),this.$=h[y];break;case 40:b.addPersonOrSystem("external_person",...h[y]),this.$=h[y];break;case 41:b.addPersonOrSystem("system",...h[y]),this.$=h[y];break;case 42:b.addPersonOrSystem("system_db",...h[y]),this.$=h[y];break;case 43:b.addPersonOrSystem("system_queue",...h[y]),this.$=h[y];break;case 44:b.addPersonOrSystem("external_system",...h[y]),this.$=h[y];break;case 45:b.addPersonOrSystem("external_system_db",...h[y]),this.$=h[y];break;case 46:b.addPersonOrSystem("external_system_queue",...h[y]),this.$=h[y];break;case 47:b.addContainer("container",...h[y]),this.$=h[y];break;case 48:b.addContainer("container_db",...h[y]),this.$=h[y];break;case 49:b.addContainer("container_queue",...h[y]),this.$=h[y];break;case 50:b.addContainer("external_container",...h[y]),this.$=h[y];break;case 51:b.addContainer("external_container_db",...h[y]),this.$=h[y];break;case 52:b.addContainer("external_container_queue",...h[y]),this.$=h[y];break;case 53:b.addComponent("component",...h[y]),this.$=h[y];break;case 54:b.addComponent("component_db",...h[y]),this.$=h[y];break;case 55:b.addComponent("component_queue",...h[y]),this.$=h[y];break;case 56:b.addComponent("external_component",...h[y]),this.$=h[y];break;case 57:b.addComponent("external_component_db",...h[y]),this.$=h[y];break;case 58:b.addComponent("external_component_queue",...h[y]),this.$=h[y];break;case 60:b.addRel("rel",...h[y]),this.$=h[y];break;case 61:b.addRel("birel",...h[y]),this.$=h[y];break;case 62:b.addRel("rel_u",...h[y]),this.$=h[y];break;case 63:b.addRel("rel_d",...h[y]),this.$=h[y];break;case 64:b.addRel("rel_l",...h[y]),this.$=h[y];break;case 65:b.addRel("rel_r",...h[y]),this.$=h[y];break;case 66:b.addRel("rel_b",...h[y]),this.$=h[y];break;case 67:h[y].splice(0,1),b.addRel("rel",...h[y]),this.$=h[y];break;case 68:b.updateElStyle("update_el_style",...h[y]),this.$=h[y];break;case 69:b.updateRelStyle("update_rel_style",...h[y]),this.$=h[y];break;case 70:b.updateLayoutConfig("update_layout_config",...h[y]),this.$=h[y];break;case 71:this.$=[h[y]];break;case 72:h[y].unshift(h[y-1]),this.$=h[y];break;case 73:case 75:this.$=h[y].trim();break;case 74:let Et={};Et[h[y-1].trim()]=h[y].trim(),this.$=Et;break;case 76:this.$=""}},"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],7:[1,6],8:[1,7],9:[1,8],10:4,11:[1,9],15:[1,10],16:[1,11],17:[1,12],18:[1,13]},{1:[3]},{1:[2,1]},{1:[2,2]},{1:[2,7]},{1:[2,3]},{1:[2,4]},{1:[2,5]},{1:[2,6]},{12:[1,14]},{12:[1,15]},{12:[1,16]},{12:[1,17]},{12:[1,18]},{13:19,19:20,20:21,21:22,22:t,23:s,24:l,26:o,28:i,29:49,30:61,32:62,34:r,36:n,37:a,38:d,39:p,40:u,41:f,43:23,44:O,45:v,46:R,47:P,48:X,49:Y,50:H,51:q,52:V,53:G,54:K,55:J,56:Z,57:tt,58:et,59:it,60:at,61:nt,62:rt,63:st,64:ot,65:lt,66:ct,67:ht,68:dt,69:pt,70:ut,71:yt,72:gt,73:ft,74:bt},{13:70,19:20,20:21,21:22,22:t,23:s,24:l,26:o,28:i,29:49,30:61,32:62,34:r,36:n,37:a,38:d,39:p,40:u,41:f,43:23,44:O,45:v,46:R,47:P,48:X,49:Y,50:H,51:q,52:V,53:G,54:K,55:J,56:Z,57:tt,58:et,59:it,60:at,61:nt,62:rt,63:st,64:ot,65:lt,66:ct,67:ht,68:dt,69:pt,70:ut,71:yt,72:gt,73:ft,74:bt},{13:71,19:20,20:21,21:22,22:t,23:s,24:l,26:o,28:i,29:49,30:61,32:62,34:r,36:n,37:a,38:d,39:p,40:u,41:f,43:23,44:O,45:v,46:R,47:P,48:X,49:Y,50:H,51:q,52:V,53:G,54:K,55:J,56:Z,57:tt,58:et,59:it,60:at,61:nt,62:rt,63:st,64:ot,65:lt,66:ct,67:ht,68:dt,69:pt,70:ut,71:yt,72:gt,73:ft,74:bt},{13:72,19:20,20:21,21:22,22:t,23:s,24:l,26:o,28:i,29:49,30:61,32:62,34:r,36:n,37:a,38:d,39:p,40:u,41:f,43:23,44:O,45:v,46:R,47:P,48:X,49:Y,50:H,51:q,52:V,53:G,54:K,55:J,56:Z,57:tt,58:et,59:it,60:at,61:nt,62:rt,63:st,64:ot,65:lt,66:ct,67:ht,68:dt,69:pt,70:ut,71:yt,72:gt,73:ft,74:bt},{13:73,19:20,20:21,21:22,22:t,23:s,24:l,26:o,28:i,29:49,30:61,32:62,34:r,36:n,37:a,38:d,39:p,40:u,41:f,43:23,44:O,45:v,46:R,47:P,48:X,49:Y,50:H,51:q,52:V,53:G,54:K,55:J,56:Z,57:tt,58:et,59:it,60:at,61:nt,62:rt,63:st,64:ot,65:lt,66:ct,67:ht,68:dt,69:pt,70:ut,71:yt,72:gt,73:ft,74:bt},{14:[1,74]},e(Ct,[2,13],{43:23,29:49,30:61,32:62,20:75,34:r,36:n,37:a,38:d,39:p,40:u,41:f,44:O,45:v,46:R,47:P,48:X,49:Y,50:H,51:q,52:V,53:G,54:K,55:J,56:Z,57:tt,58:et,59:it,60:at,61:nt,62:rt,63:st,64:ot,65:lt,66:ct,67:ht,68:dt,69:pt,70:ut,71:yt,72:gt,73:ft,74:bt}),e(Ct,[2,14]),e(Wt,[2,16],{12:[1,76]}),e(Ct,[2,36],{12:[1,77]}),e(vt,[2,19]),e(vt,[2,20]),{25:[1,78]},{27:[1,79]},e(vt,[2,23]),{35:80,75:81,76:E,77:A,79:S,80:C},{35:86,75:81,76:E,77:A,79:S,80:C},{35:87,75:81,76:E,77:A,79:S,80:C},{35:88,75:81,76:E,77:A,79:S,80:C},{35:89,75:81,76:E,77:A,79:S,80:C},{35:90,75:81,76:E,77:A,79:S,80:C},{35:91,75:81,76:E,77:A,79:S,80:C},{35:92,75:81,76:E,77:A,79:S,80:C},{35:93,75:81,76:E,77:A,79:S,80:C},{35:94,75:81,76:E,77:A,79:S,80:C},{35:95,75:81,76:E,77:A,79:S,80:C},{35:96,75:81,76:E,77:A,79:S,80:C},{35:97,75:81,76:E,77:A,79:S,80:C},{35:98,75:81,76:E,77:A,79:S,80:C},{35:99,75:81,76:E,77:A,79:S,80:C},{35:100,75:81,76:E,77:A,79:S,80:C},{35:101,75:81,76:E,77:A,79:S,80:C},{35:102,75:81,76:E,77:A,79:S,80:C},{35:103,75:81,76:E,77:A,79:S,80:C},{35:104,75:81,76:E,77:A,79:S,80:C},e(w,[2,59]),{35:105,75:81,76:E,77:A,79:S,80:C},{35:106,75:81,76:E,77:A,79:S,80:C},{35:107,75:81,76:E,77:A,79:S,80:C},{35:108,75:81,76:E,77:A,79:S,80:C},{35:109,75:81,76:E,77:A,79:S,80:C},{35:110,75:81,76:E,77:A,79:S,80:C},{35:111,75:81,76:E,77:A,79:S,80:C},{35:112,75:81,76:E,77:A,79:S,80:C},{35:113,75:81,76:E,77:A,79:S,80:C},{35:114,75:81,76:E,77:A,79:S,80:C},{35:115,75:81,76:E,77:A,79:S,80:C},{20:116,29:49,30:61,32:62,34:r,36:n,37:a,38:d,39:p,40:u,41:f,43:23,44:O,45:v,46:R,47:P,48:X,49:Y,50:H,51:q,52:V,53:G,54:K,55:J,56:Z,57:tt,58:et,59:it,60:at,61:nt,62:rt,63:st,64:ot,65:lt,66:ct,67:ht,68:dt,69:pt,70:ut,71:yt,72:gt,73:ft,74:bt},{12:[1,118],33:[1,117]},{35:119,75:81,76:E,77:A,79:S,80:C},{35:120,75:81,76:E,77:A,79:S,80:C},{35:121,75:81,76:E,77:A,79:S,80:C},{35:122,75:81,76:E,77:A,79:S,80:C},{35:123,75:81,76:E,77:A,79:S,80:C},{35:124,75:81,76:E,77:A,79:S,80:C},{35:125,75:81,76:E,77:A,79:S,80:C},{14:[1,126]},{14:[1,127]},{14:[1,128]},{14:[1,129]},{1:[2,8]},e(Ct,[2,15]),e(Wt,[2,17],{21:22,19:130,22:t,23:s,24:l,26:o,28:i}),e(Ct,[2,37],{19:20,20:21,21:22,43:23,29:49,30:61,32:62,13:131,22:t,23:s,24:l,26:o,28:i,34:r,36:n,37:a,38:d,39:p,40:u,41:f,44:O,45:v,46:R,47:P,48:X,49:Y,50:H,51:q,52:V,53:G,54:K,55:J,56:Z,57:tt,58:et,59:it,60:at,61:nt,62:rt,63:st,64:ot,65:lt,66:ct,67:ht,68:dt,69:pt,70:ut,71:yt,72:gt,73:ft,74:bt}),e(vt,[2,21]),e(vt,[2,22]),e(w,[2,39]),e(ne,[2,71],{75:81,35:132,76:E,77:A,79:S,80:C}),e(Bt,[2,73]),{78:[1,133]},e(Bt,[2,75]),e(Bt,[2,76]),e(w,[2,40]),e(w,[2,41]),e(w,[2,42]),e(w,[2,43]),e(w,[2,44]),e(w,[2,45]),e(w,[2,46]),e(w,[2,47]),e(w,[2,48]),e(w,[2,49]),e(w,[2,50]),e(w,[2,51]),e(w,[2,52]),e(w,[2,53]),e(w,[2,54]),e(w,[2,55]),e(w,[2,56]),e(w,[2,57]),e(w,[2,58]),e(w,[2,60]),e(w,[2,61]),e(w,[2,62]),e(w,[2,63]),e(w,[2,64]),e(w,[2,65]),e(w,[2,66]),e(w,[2,67]),e(w,[2,68]),e(w,[2,69]),e(w,[2,70]),{31:134,42:[1,135]},{12:[1,136]},{33:[1,137]},e(xt,[2,28]),e(xt,[2,29]),e(xt,[2,30]),e(xt,[2,31]),e(xt,[2,32]),e(xt,[2,33]),e(xt,[2,34]),{1:[2,9]},{1:[2,10]},{1:[2,11]},{1:[2,12]},e(Wt,[2,18]),e(Ct,[2,38]),e(ne,[2,72]),e(Bt,[2,74]),e(w,[2,24]),e(w,[2,35]),e(Qt,[2,25]),e(Qt,[2,26],{12:[1,138]}),e(Qt,[2,27])],defaultActions:{2:[2,1],3:[2,2],4:[2,7],5:[2,3],6:[2,4],7:[2,5],8:[2,6],74:[2,8],126:[2,9],127:[2,10],128:[2,11],129:[2,12]},parseError:g(function(m,k){if(!k.recoverable){var x=new Error(m);throw x.hash=k,x}this.trace(m)},"parseError"),parse:g(function(m){var k=this,x=[0],b=[],T=[null],h=[],Rt=this.table,y="",Et=0,re=0,Ce=h.slice.call(arguments,1),D=Object.create(this.lexer),At={yy:{}};for(var Ht in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Ht)&&(At.yy[Ht]=this.yy[Ht]);D.setInput(m,At.yy),At.yy.lexer=D,At.yy.parser=this,D.yylloc===void 0&&(D.yylloc={});var qt=D.yylloc;h.push(qt);var Oe=D.options&&D.options.ranges;function se(){var j;return typeof(j=b.pop()||D.lex()||1)!="number"&&(j instanceof Array&&(j=(b=j).pop()),j=k.symbols_[j]||j),j}typeof At.yy.parseError=="function"?this.parseError=At.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,g(function(j){x.length=x.length-2*j,T.length=T.length-j,h.length=h.length-j},"popStack"),g(se,"lex");for(var B,St,M,oe,jt,Q,le,It,Ot={};;){if(St=x[x.length-1],this.defaultActions[St]?M=this.defaultActions[St]:(B==null&&(B=se()),M=Rt[St]&&Rt[St][B]),M===void 0||!M.length||!M[0]){var ce="";for(jt in It=[],Rt[St])this.terminals_[jt]&&jt>2&&It.push("'"+this.terminals_[jt]+"'");ce=D.showPosition?"Parse error on line "+(Et+1)+`:
`+D.showPosition()+`
Expecting `+It.join(", ")+", got '"+(this.terminals_[B]||B)+"'":"Parse error on line "+(Et+1)+": Unexpected "+(B==1?"end of input":"'"+(this.terminals_[B]||B)+"'"),this.parseError(ce,{text:D.match,token:this.terminals_[B]||B,line:D.yylineno,loc:qt,expected:It})}if(M[0]instanceof Array&&M.length>1)throw new Error("Parse Error: multiple actions possible at state: "+St+", token: "+B);switch(M[0]){case 1:x.push(B),T.push(D.yytext),h.push(D.yylloc),x.push(M[1]),B=null,re=D.yyleng,y=D.yytext,Et=D.yylineno,qt=D.yylloc;break;case 2:if(Q=this.productions_[M[1]][1],Ot.$=T[T.length-Q],Ot._$={first_line:h[h.length-(Q||1)].first_line,last_line:h[h.length-1].last_line,first_column:h[h.length-(Q||1)].first_column,last_column:h[h.length-1].last_column},Oe&&(Ot._$.range=[h[h.length-(Q||1)].range[0],h[h.length-1].range[1]]),(oe=this.performAction.apply(Ot,[y,re,Et,At.yy,M[1],T,h].concat(Ce)))!==void 0)return oe;Q&&(x=x.slice(0,-1*Q*2),T=T.slice(0,-1*Q),h=h.slice(0,-1*Q)),x.push(this.productions_[M[1]][0]),T.push(Ot.$),h.push(Ot._$),le=Rt[x[x.length-2]][x[x.length-1]],x.push(le);break;case 3:return!0}}return!0},"parse")},Se=function(){return{EOF:1,parseError:g(function(m,k){if(!this.yy.parser)throw new Error(m);this.yy.parser.parseError(m,k)},"parseError"),setInput:g(function(m,k){return this.yy=k||this.yy||{},this._input=m,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:g(function(){var m=this._input[0];return this.yytext+=m,this.yyleng++,this.offset++,this.match+=m,this.matched+=m,m.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),m},"input"),unput:g(function(m){var k=m.length,x=m.split(/(?:\r\n?|\n)/g);this._input=m+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-k),this.offset-=k;var b=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),x.length-1&&(this.yylineno-=x.length-1);var T=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:x?(x.length===b.length?this.yylloc.first_column:0)+b[b.length-x.length].length-x[0].length:this.yylloc.first_column-k},this.options.ranges&&(this.yylloc.range=[T[0],T[0]+this.yyleng-k]),this.yyleng=this.yytext.length,this},"unput"),more:g(function(){return this._more=!0,this},"more"),reject:g(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:g(function(m){this.unput(this.match.slice(m))},"less"),pastInput:g(function(){var m=this.matched.substr(0,this.matched.length-this.match.length);return(m.length>20?"...":"")+m.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:g(function(){var m=this.match;return m.length<20&&(m+=this._input.substr(0,20-m.length)),(m.substr(0,20)+(m.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:g(function(){var m=this.pastInput(),k=new Array(m.length+1).join("-");return m+this.upcomingInput()+`
`+k+"^"},"showPosition"),test_match:g(function(m,k){var x,b,T;if(this.options.backtrack_lexer&&(T={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(T.yylloc.range=this.yylloc.range.slice(0))),(b=m[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=b.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:b?b[b.length-1].length-b[b.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+m[0].length},this.yytext+=m[0],this.match+=m[0],this.matches=m,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(m[0].length),this.matched+=m[0],x=this.performAction.call(this,this.yy,this,k,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),x)return x;if(this._backtrack){for(var h in T)this[h]=T[h];return!1}return!1},"test_match"),next:g(function(){if(this.done)return this.EOF;var m,k,x,b;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var T=this._currentRules(),h=0;h<T.length;h++)if((x=this._input.match(this.rules[T[h]]))&&(!k||x[0].length>k[0].length)){if(k=x,b=h,this.options.backtrack_lexer){if((m=this.test_match(x,T[h]))!==!1)return m;if(this._backtrack){k=!1;continue}return!1}if(!this.options.flex)break}return k?(m=this.test_match(k,T[b]))!==!1&&m:this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:g(function(){var m=this.next();return m||this.lex()},"lex"),begin:g(function(m){this.conditionStack.push(m)},"begin"),popState:g(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:g(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:g(function(m){return(m=this.conditionStack.length-1-Math.abs(m||0))>=0?this.conditionStack[m]:"INITIAL"},"topState"),pushState:g(function(m){this.begin(m)},"pushState"),stateStackSize:g(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:g(function(m,k,x,b){switch(x){case 0:return 6;case 1:return 7;case 2:return 8;case 3:return 9;case 4:return 22;case 5:return 23;case 6:return this.begin("acc_title"),24;case 7:return this.popState(),"acc_title_value";case 8:return this.begin("acc_descr"),26;case 9:return this.popState(),"acc_descr_value";case 10:this.begin("acc_descr_multiline");break;case 11:case 73:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:case 16:case 70:break;case 14:c;break;case 15:return 12;case 17:return 11;case 18:return 15;case 19:return 16;case 20:return 17;case 21:return 18;case 22:return this.begin("person_ext"),45;case 23:return this.begin("person"),44;case 24:return this.begin("system_ext_queue"),51;case 25:return this.begin("system_ext_db"),50;case 26:return this.begin("system_ext"),49;case 27:return this.begin("system_queue"),48;case 28:return this.begin("system_db"),47;case 29:return this.begin("system"),46;case 30:return this.begin("boundary"),37;case 31:return this.begin("enterprise_boundary"),34;case 32:return this.begin("system_boundary"),36;case 33:return this.begin("container_ext_queue"),57;case 34:return this.begin("container_ext_db"),56;case 35:return this.begin("container_ext"),55;case 36:return this.begin("container_queue"),54;case 37:return this.begin("container_db"),53;case 38:return this.begin("container"),52;case 39:return this.begin("container_boundary"),38;case 40:return this.begin("component_ext_queue"),63;case 41:return this.begin("component_ext_db"),62;case 42:return this.begin("component_ext"),61;case 43:return this.begin("component_queue"),60;case 44:return this.begin("component_db"),59;case 45:return this.begin("component"),58;case 46:case 47:return this.begin("node"),39;case 48:return this.begin("node_l"),40;case 49:return this.begin("node_r"),41;case 50:return this.begin("rel"),64;case 51:return this.begin("birel"),65;case 52:case 53:return this.begin("rel_u"),66;case 54:case 55:return this.begin("rel_d"),67;case 56:case 57:return this.begin("rel_l"),68;case 58:case 59:return this.begin("rel_r"),69;case 60:return this.begin("rel_b"),70;case 61:return this.begin("rel_index"),71;case 62:return this.begin("update_el_style"),72;case 63:return this.begin("update_rel_style"),73;case 64:return this.begin("update_layout_config"),74;case 65:return"EOF_IN_STRUCT";case 66:return this.begin("attribute"),"ATTRIBUTE_EMPTY";case 67:this.begin("attribute");break;case 68:case 79:this.popState(),this.popState();break;case 69:case 71:return 80;case 72:this.begin("string");break;case 74:case 80:return"STR";case 75:this.begin("string_kv");break;case 76:return this.begin("string_kv_key"),"STR_KEY";case 77:this.popState(),this.begin("string_kv_value");break;case 78:return"STR_VALUE";case 81:return"LBRACE";case 82:return"RBRACE";case 83:return"SPACE";case 84:return"EOL";case 85:return 14}},"anonymous"),rules:[/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:title\s[^#\n;]+)/,/^(?:accDescription\s[^#\n;]+)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:%%(?!\{)*[^\n]*(\r?\n?)+)/,/^(?:%%[^\n]*(\r?\n)*)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:C4Context\b)/,/^(?:C4Container\b)/,/^(?:C4Component\b)/,/^(?:C4Dynamic\b)/,/^(?:C4Deployment\b)/,/^(?:Person_Ext\b)/,/^(?:Person\b)/,/^(?:SystemQueue_Ext\b)/,/^(?:SystemDb_Ext\b)/,/^(?:System_Ext\b)/,/^(?:SystemQueue\b)/,/^(?:SystemDb\b)/,/^(?:System\b)/,/^(?:Boundary\b)/,/^(?:Enterprise_Boundary\b)/,/^(?:System_Boundary\b)/,/^(?:ContainerQueue_Ext\b)/,/^(?:ContainerDb_Ext\b)/,/^(?:Container_Ext\b)/,/^(?:ContainerQueue\b)/,/^(?:ContainerDb\b)/,/^(?:Container\b)/,/^(?:Container_Boundary\b)/,/^(?:ComponentQueue_Ext\b)/,/^(?:ComponentDb_Ext\b)/,/^(?:Component_Ext\b)/,/^(?:ComponentQueue\b)/,/^(?:ComponentDb\b)/,/^(?:Component\b)/,/^(?:Deployment_Node\b)/,/^(?:Node\b)/,/^(?:Node_L\b)/,/^(?:Node_R\b)/,/^(?:Rel\b)/,/^(?:BiRel\b)/,/^(?:Rel_Up\b)/,/^(?:Rel_U\b)/,/^(?:Rel_Down\b)/,/^(?:Rel_D\b)/,/^(?:Rel_Left\b)/,/^(?:Rel_L\b)/,/^(?:Rel_Right\b)/,/^(?:Rel_R\b)/,/^(?:Rel_Back\b)/,/^(?:RelIndex\b)/,/^(?:UpdateElementStyle\b)/,/^(?:UpdateRelStyle\b)/,/^(?:UpdateLayoutConfig\b)/,/^(?:$)/,/^(?:[(][ ]*[,])/,/^(?:[(])/,/^(?:[)])/,/^(?:,,)/,/^(?:,)/,/^(?:[ ]*["]["])/,/^(?:[ ]*["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:[ ]*[\$])/,/^(?:[^=]*)/,/^(?:[=][ ]*["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:[^,]+)/,/^(?:\{)/,/^(?:\})/,/^(?:[\s]+)/,/^(?:[\n\r]+)/,/^(?:$)/],conditions:{acc_descr_multiline:{rules:[11,12],inclusive:!1},acc_descr:{rules:[9],inclusive:!1},acc_title:{rules:[7],inclusive:!1},string_kv_value:{rules:[78,79],inclusive:!1},string_kv_key:{rules:[77],inclusive:!1},string_kv:{rules:[76],inclusive:!1},string:{rules:[73,74],inclusive:!1},attribute:{rules:[68,69,70,71,72,75,80],inclusive:!1},update_layout_config:{rules:[65,66,67,68],inclusive:!1},update_rel_style:{rules:[65,66,67,68],inclusive:!1},update_el_style:{rules:[65,66,67,68],inclusive:!1},rel_b:{rules:[65,66,67,68],inclusive:!1},rel_r:{rules:[65,66,67,68],inclusive:!1},rel_l:{rules:[65,66,67,68],inclusive:!1},rel_d:{rules:[65,66,67,68],inclusive:!1},rel_u:{rules:[65,66,67,68],inclusive:!1},rel_bi:{rules:[],inclusive:!1},rel:{rules:[65,66,67,68],inclusive:!1},node_r:{rules:[65,66,67,68],inclusive:!1},node_l:{rules:[65,66,67,68],inclusive:!1},node:{rules:[65,66,67,68],inclusive:!1},index:{rules:[],inclusive:!1},rel_index:{rules:[65,66,67,68],inclusive:!1},component_ext_queue:{rules:[],inclusive:!1},component_ext_db:{rules:[65,66,67,68],inclusive:!1},component_ext:{rules:[65,66,67,68],inclusive:!1},component_queue:{rules:[65,66,67,68],inclusive:!1},component_db:{rules:[65,66,67,68],inclusive:!1},component:{rules:[65,66,67,68],inclusive:!1},container_boundary:{rules:[65,66,67,68],inclusive:!1},container_ext_queue:{rules:[65,66,67,68],inclusive:!1},container_ext_db:{rules:[65,66,67,68],inclusive:!1},container_ext:{rules:[65,66,67,68],inclusive:!1},container_queue:{rules:[65,66,67,68],inclusive:!1},container_db:{rules:[65,66,67,68],inclusive:!1},container:{rules:[65,66,67,68],inclusive:!1},birel:{rules:[65,66,67,68],inclusive:!1},system_boundary:{rules:[65,66,67,68],inclusive:!1},enterprise_boundary:{rules:[65,66,67,68],inclusive:!1},boundary:{rules:[65,66,67,68],inclusive:!1},system_ext_queue:{rules:[65,66,67,68],inclusive:!1},system_ext_db:{rules:[65,66,67,68],inclusive:!1},system_ext:{rules:[65,66,67,68],inclusive:!1},system_queue:{rules:[65,66,67,68],inclusive:!1},system_db:{rules:[65,66,67,68],inclusive:!1},system:{rules:[65,66,67,68],inclusive:!1},person_ext:{rules:[65,66,67,68],inclusive:!1},person:{rules:[65,66,67,68],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,8,10,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,81,82,83,84,85],inclusive:!0}}}}();function Mt(){this.yy={}}return $t.lexer=Se,g(Mt,"Parser"),Mt.prototype=$t,$t.Parser=Mt,new Mt}();Yt.parser=Yt;var ge,je=Yt,F=[],mt=[""],N="global",U="",W=[{alias:"global",label:{text:"global"},type:{text:"global"},tags:null,link:null,parentBoundary:""}],Pt=[],te="",ee=!1,Ut=4,Ft=2,Ie=g(function(){return ge},"getC4Type"),Le=g(function(e){ge=ye(e,Nt())},"setC4Type"),Ye=g(function(e,t,s,l,o,i,r,n,a){if(e==null||t==null||s==null||l==null)return;let d={};const p=Pt.find(u=>u.from===t&&u.to===s);if(p?d=p:Pt.push(d),d.type=e,d.from=t,d.to=s,d.label={text:l},o==null)d.techn={text:""};else if(typeof o=="object"){let[u,f]=Object.entries(o)[0];d[u]={text:f}}else d.techn={text:o};if(i==null)d.descr={text:""};else if(typeof i=="object"){let[u,f]=Object.entries(i)[0];d[u]={text:f}}else d.descr={text:i};if(typeof r=="object"){let[u,f]=Object.entries(r)[0];d[u]=f}else d.sprite=r;if(typeof n=="object"){let[u,f]=Object.entries(n)[0];d[u]=f}else d.tags=n;if(typeof a=="object"){let[u,f]=Object.entries(a)[0];d[u]=f}else d.link=a;d.wrap=_t()},"addRel"),Ue=g(function(e,t,s,l,o,i,r){if(t===null||s===null)return;let n={};const a=F.find(d=>d.alias===t);if(a&&t===a.alias?n=a:(n.alias=t,F.push(n)),n.label=s==null?{text:""}:{text:s},l==null)n.descr={text:""};else if(typeof l=="object"){let[d,p]=Object.entries(l)[0];n[d]={text:p}}else n.descr={text:l};if(typeof o=="object"){let[d,p]=Object.entries(o)[0];n[d]=p}else n.sprite=o;if(typeof i=="object"){let[d,p]=Object.entries(i)[0];n[d]=p}else n.tags=i;if(typeof r=="object"){let[d,p]=Object.entries(r)[0];n[d]=p}else n.link=r;n.typeC4Shape={text:e},n.parentBoundary=N,n.wrap=_t()},"addPersonOrSystem"),Fe=g(function(e,t,s,l,o,i,r,n){if(t===null||s===null)return;let a={};const d=F.find(p=>p.alias===t);if(d&&t===d.alias?a=d:(a.alias=t,F.push(a)),a.label=s==null?{text:""}:{text:s},l==null)a.techn={text:""};else if(typeof l=="object"){let[p,u]=Object.entries(l)[0];a[p]={text:u}}else a.techn={text:l};if(o==null)a.descr={text:""};else if(typeof o=="object"){let[p,u]=Object.entries(o)[0];a[p]={text:u}}else a.descr={text:o};if(typeof i=="object"){let[p,u]=Object.entries(i)[0];a[p]=u}else a.sprite=i;if(typeof r=="object"){let[p,u]=Object.entries(r)[0];a[p]=u}else a.tags=r;if(typeof n=="object"){let[p,u]=Object.entries(n)[0];a[p]=u}else a.link=n;a.wrap=_t(),a.typeC4Shape={text:e},a.parentBoundary=N},"addContainer"),Xe=g(function(e,t,s,l,o,i,r,n){if(t===null||s===null)return;let a={};const d=F.find(p=>p.alias===t);if(d&&t===d.alias?a=d:(a.alias=t,F.push(a)),a.label=s==null?{text:""}:{text:s},l==null)a.techn={text:""};else if(typeof l=="object"){let[p,u]=Object.entries(l)[0];a[p]={text:u}}else a.techn={text:l};if(o==null)a.descr={text:""};else if(typeof o=="object"){let[p,u]=Object.entries(o)[0];a[p]={text:u}}else a.descr={text:o};if(typeof i=="object"){let[p,u]=Object.entries(i)[0];a[p]=u}else a.sprite=i;if(typeof r=="object"){let[p,u]=Object.entries(r)[0];a[p]=u}else a.tags=r;if(typeof n=="object"){let[p,u]=Object.entries(n)[0];a[p]=u}else a.link=n;a.wrap=_t(),a.typeC4Shape={text:e},a.parentBoundary=N},"addComponent"),ze=g(function(e,t,s,l,o){if(e===null||t===null)return;let i={};const r=W.find(n=>n.alias===e);if(r&&e===r.alias?i=r:(i.alias=e,W.push(i)),i.label=t==null?{text:""}:{text:t},s==null)i.type={text:"system"};else if(typeof s=="object"){let[n,a]=Object.entries(s)[0];i[n]={text:a}}else i.type={text:s};if(typeof l=="object"){let[n,a]=Object.entries(l)[0];i[n]=a}else i.tags=l;if(typeof o=="object"){let[n,a]=Object.entries(o)[0];i[n]=a}else i.link=o;i.parentBoundary=N,i.wrap=_t(),U=N,N=e,mt.push(U)},"addPersonOrSystemBoundary"),We=g(function(e,t,s,l,o){if(e===null||t===null)return;let i={};const r=W.find(n=>n.alias===e);if(r&&e===r.alias?i=r:(i.alias=e,W.push(i)),i.label=t==null?{text:""}:{text:t},s==null)i.type={text:"container"};else if(typeof s=="object"){let[n,a]=Object.entries(s)[0];i[n]={text:a}}else i.type={text:s};if(typeof l=="object"){let[n,a]=Object.entries(l)[0];i[n]=a}else i.tags=l;if(typeof o=="object"){let[n,a]=Object.entries(o)[0];i[n]=a}else i.link=o;i.parentBoundary=N,i.wrap=_t(),U=N,N=e,mt.push(U)},"addContainerBoundary"),Qe=g(function(e,t,s,l,o,i,r,n){if(t===null||s===null)return;let a={};const d=W.find(p=>p.alias===t);if(d&&t===d.alias?a=d:(a.alias=t,W.push(a)),a.label=s==null?{text:""}:{text:s},l==null)a.type={text:"node"};else if(typeof l=="object"){let[p,u]=Object.entries(l)[0];a[p]={text:u}}else a.type={text:l};if(o==null)a.descr={text:""};else if(typeof o=="object"){let[p,u]=Object.entries(o)[0];a[p]={text:u}}else a.descr={text:o};if(typeof r=="object"){let[p,u]=Object.entries(r)[0];a[p]=u}else a.tags=r;if(typeof n=="object"){let[p,u]=Object.entries(n)[0];a[p]=u}else a.link=n;a.nodeType=e,a.parentBoundary=N,a.wrap=_t(),U=N,N=t,mt.push(U)},"addDeploymentNode"),$e=g(function(){N=U,mt.pop(),U=mt.pop(),mt.push(U)},"popBoundaryParseStack"),He=g(function(e,t,s,l,o,i,r,n,a,d,p){let u=F.find(f=>f.alias===t);if(u!==void 0||(u=W.find(f=>f.alias===t),u!==void 0)){if(s!=null)if(typeof s=="object"){let[f,O]=Object.entries(s)[0];u[f]=O}else u.bgColor=s;if(l!=null)if(typeof l=="object"){let[f,O]=Object.entries(l)[0];u[f]=O}else u.fontColor=l;if(o!=null)if(typeof o=="object"){let[f,O]=Object.entries(o)[0];u[f]=O}else u.borderColor=o;if(i!=null)if(typeof i=="object"){let[f,O]=Object.entries(i)[0];u[f]=O}else u.shadowing=i;if(r!=null)if(typeof r=="object"){let[f,O]=Object.entries(r)[0];u[f]=O}else u.shape=r;if(n!=null)if(typeof n=="object"){let[f,O]=Object.entries(n)[0];u[f]=O}else u.sprite=n;if(a!=null)if(typeof a=="object"){let[f,O]=Object.entries(a)[0];u[f]=O}else u.techn=a;if(d!=null)if(typeof d=="object"){let[f,O]=Object.entries(d)[0];u[f]=O}else u.legendText=d;if(p!=null)if(typeof p=="object"){let[f,O]=Object.entries(p)[0];u[f]=O}else u.legendSprite=p}},"updateElStyle"),qe=g(function(e,t,s,l,o,i,r){const n=Pt.find(a=>a.from===t&&a.to===s);if(n!==void 0){if(l!=null)if(typeof l=="object"){let[a,d]=Object.entries(l)[0];n[a]=d}else n.textColor=l;if(o!=null)if(typeof o=="object"){let[a,d]=Object.entries(o)[0];n[a]=d}else n.lineColor=o;if(i!=null)if(typeof i=="object"){let[a,d]=Object.entries(i)[0];n[a]=parseInt(d)}else n.offsetX=parseInt(i);if(r!=null)if(typeof r=="object"){let[a,d]=Object.entries(r)[0];n[a]=parseInt(d)}else n.offsetY=parseInt(r)}},"updateRelStyle"),Ve=g(function(e,t,s){let l=Ut,o=Ft;if(typeof t=="object"){const i=Object.values(t)[0];l=parseInt(i)}else l=parseInt(t);if(typeof s=="object"){const i=Object.values(s)[0];o=parseInt(i)}else o=parseInt(s);l>=1&&(Ut=l),o>=1&&(Ft=o)},"updateLayoutConfig"),Ge=g(function(){return Ut},"getC4ShapeInRow"),Ke=g(function(){return Ft},"getC4BoundaryInRow"),Je=g(function(){return N},"getCurrentBoundaryParse"),Ze=g(function(){return U},"getParentBoundaryParse"),fe=g(function(e){return e==null?F:F.filter(t=>t.parentBoundary===e)},"getC4ShapeArray"),t0=g(function(e){return F.find(t=>t.alias===e)},"getC4Shape"),e0=g(function(e){return Object.keys(fe(e))},"getC4ShapeKeys"),be=g(function(e){return e==null?W:W.filter(t=>t.parentBoundary===e)},"getBoundaries"),i0=be,a0=g(function(){return Pt},"getRels"),n0=g(function(){return te},"getTitle"),r0=g(function(e){ee=e},"setWrap"),_t=g(function(){return ee},"autoWrap"),s0=g(function(){F=[],W=[{alias:"global",label:{text:"global"},type:{text:"global"},tags:null,link:null,parentBoundary:""}],U="",N="global",mt=[""],Pt=[],mt=[""],te="",ee=!1,Ut=4,Ft=2},"clear"),o0=g(function(e){te=ye(e,Nt())},"setTitle"),Kt={addPersonOrSystem:Ue,addPersonOrSystemBoundary:ze,addContainer:Fe,addContainerBoundary:We,addComponent:Xe,addDeploymentNode:Qe,popBoundaryParseStack:$e,addRel:Ye,updateElStyle:He,updateRelStyle:qe,updateLayoutConfig:Ve,autoWrap:_t,setWrap:r0,getC4ShapeArray:fe,getC4Shape:t0,getC4ShapeKeys:e0,getBoundaries:be,getBoundarys:i0,getCurrentBoundaryParse:Je,getParentBoundaryParse:Ze,getRels:a0,getTitle:n0,getC4Type:Ie,getC4ShapeInRow:Ge,getC4BoundaryInRow:Ke,setAccTitle:De,getAccTitle:Re,getAccDescription:ve,setAccDescription:Te,getConfig:g(()=>Nt().c4,"getConfig"),clear:s0,LINETYPE:{SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25},ARROWTYPE:{FILLED:0,OPEN:1},PLACEMENT:{LEFTOF:0,RIGHTOF:1,OVER:2},setTitle:o0,setC4Type:Le},ie=g(function(e,t){return ke(e,t)},"drawRect"),me=g(function(e,t,s,l,o,i){const r=e.append("image");r.attr("width",t),r.attr("height",s),r.attr("x",l),r.attr("y",o);let n=i.startsWith("data:image/png;base64")?i:Be(i);r.attr("xlink:href",n)},"drawImage"),l0=g((e,t,s)=>{const l=e.append("g");let o=0;for(let i of t){let r=i.textColor?i.textColor:"#444444",n=i.lineColor?i.lineColor:"#444444",a=i.offsetX?parseInt(i.offsetX):0,d=i.offsetY?parseInt(i.offsetY):0,p="";if(o===0){let f=l.append("line");f.attr("x1",i.startPoint.x),f.attr("y1",i.startPoint.y),f.attr("x2",i.endPoint.x),f.attr("y2",i.endPoint.y),f.attr("stroke-width","1"),f.attr("stroke",n),f.style("fill","none"),i.type!=="rel_b"&&f.attr("marker-end","url("+p+"#arrowhead)"),i.type!=="birel"&&i.type!=="rel_b"||f.attr("marker-start","url("+p+"#arrowend)"),o=-1}else{let f=l.append("path");f.attr("fill","none").attr("stroke-width","1").attr("stroke",n).attr("d","Mstartx,starty Qcontrolx,controly stopx,stopy ".replaceAll("startx",i.startPoint.x).replaceAll("starty",i.startPoint.y).replaceAll("controlx",i.startPoint.x+(i.endPoint.x-i.startPoint.x)/2-(i.endPoint.x-i.startPoint.x)/4).replaceAll("controly",i.startPoint.y+(i.endPoint.y-i.startPoint.y)/2).replaceAll("stopx",i.endPoint.x).replaceAll("stopy",i.endPoint.y)),i.type!=="rel_b"&&f.attr("marker-end","url("+p+"#arrowhead)"),i.type!=="birel"&&i.type!=="rel_b"||f.attr("marker-start","url("+p+"#arrowend)")}let u=s.messageFont();$(s)(i.label.text,l,Math.min(i.startPoint.x,i.endPoint.x)+Math.abs(i.endPoint.x-i.startPoint.x)/2+a,Math.min(i.startPoint.y,i.endPoint.y)+Math.abs(i.endPoint.y-i.startPoint.y)/2+d,i.label.width,i.label.height,{fill:r},u),i.techn&&i.techn.text!==""&&(u=s.messageFont(),$(s)("["+i.techn.text+"]",l,Math.min(i.startPoint.x,i.endPoint.x)+Math.abs(i.endPoint.x-i.startPoint.x)/2+a,Math.min(i.startPoint.y,i.endPoint.y)+Math.abs(i.endPoint.y-i.startPoint.y)/2+s.messageFontSize+5+d,Math.max(i.label.width,i.techn.width),i.techn.height,{fill:r,"font-style":"italic"},u))}},"drawRels"),c0=g(function(e,t,s){const l=e.append("g");let o=t.bgColor?t.bgColor:"none",i=t.borderColor?t.borderColor:"#444444",r=t.fontColor?t.fontColor:"black",n={"stroke-width":1,"stroke-dasharray":"7.0,7.0"};t.nodeType&&(n={"stroke-width":1});let a={x:t.x,y:t.y,fill:o,stroke:i,width:t.width,height:t.height,rx:2.5,ry:2.5,attrs:n};ie(l,a);let d=s.boundaryFont();d.fontWeight="bold",d.fontSize=d.fontSize+2,d.fontColor=r,$(s)(t.label.text,l,t.x,t.y+t.label.Y,t.width,t.height,{fill:"#444444"},d),t.type&&t.type.text!==""&&(d=s.boundaryFont(),d.fontColor=r,$(s)(t.type.text,l,t.x,t.y+t.type.Y,t.width,t.height,{fill:"#444444"},d)),t.descr&&t.descr.text!==""&&(d=s.boundaryFont(),d.fontSize=d.fontSize-2,d.fontColor=r,$(s)(t.descr.text,l,t.x,t.y+t.descr.Y,t.width,t.height,{fill:"#444444"},d))},"drawBoundary"),h0=g(function(e,t,s){var u;let l=t.bgColor?t.bgColor:s[t.typeC4Shape.text+"_bg_color"],o=t.borderColor?t.borderColor:s[t.typeC4Shape.text+"_border_color"],i=t.fontColor?t.fontColor:"#FFFFFF",r="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=";switch(t.typeC4Shape.text){case"person":r="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=";break;case"external_person":r="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAB6ElEQVR4Xu2YLY+EMBCG9+dWr0aj0Wg0Go1Go0+j8Xdv2uTCvv1gpt0ebHKPuhDaeW4605Z9mJvx4AdXUyTUdd08z+u6flmWZRnHsWkafk9DptAwDPu+f0eAYtu2PEaGWuj5fCIZrBAC2eLBAnRCsEkkxmeaJp7iDJ2QMDdHsLg8SxKFEJaAo8lAXnmuOFIhTMpxxKATebo4UiFknuNo4OniSIXQyRxEA3YsnjGCVEjVXD7yLUAqxBGUyPv/Y4W2beMgGuS7kVQIBycH0fD+oi5pezQETxdHKmQKGk1eQEYldK+jw5GxPfZ9z7Mk0Qnhf1W1m3w//EUn5BDmSZsbR44QQLBEqrBHqOrmSKaQAxdnLArCrxZcM7A7ZKs4ioRq8LFC+NpC3WCBJsvpVw5edm9iEXFuyNfxXAgSwfrFQ1c0iNda8AdejvUgnktOtJQQxmcfFzGglc5WVCj7oDgFqU18boeFSs52CUh8LE8BIVQDT1ABrB0HtgSEYlX5doJnCwv9TXocKCaKbnwhdDKPq4lf3SwU3HLq4V/+WYhHVMa/3b4IlfyikAduCkcBc7mQ3/z/Qq/cTuikhkzB12Ae/mcJC9U+Vo8Ej1gWAtgbeGgFsAMHr50BIWOLCbezvhpBFUdY6EJuJ/QDW0XoMX60zZ0AAAAASUVORK5CYII="}const n=e.append("g");n.attr("class","person-man");const a=we();switch(t.typeC4Shape.text){case"person":case"external_person":case"system":case"external_system":case"container":case"external_container":case"component":case"external_component":a.x=t.x,a.y=t.y,a.fill=l,a.width=t.width,a.height=t.height,a.stroke=o,a.rx=2.5,a.ry=2.5,a.attrs={"stroke-width":.5},ie(n,a);break;case"system_db":case"external_system_db":case"container_db":case"external_container_db":case"component_db":case"external_component_db":n.append("path").attr("fill",l).attr("stroke-width","0.5").attr("stroke",o).attr("d","Mstartx,startyc0,-10 half,-10 half,-10c0,0 half,0 half,10l0,heightc0,10 -half,10 -half,10c0,0 -half,0 -half,-10l0,-height".replaceAll("startx",t.x).replaceAll("starty",t.y).replaceAll("half",t.width/2).replaceAll("height",t.height)),n.append("path").attr("fill","none").attr("stroke-width","0.5").attr("stroke",o).attr("d","Mstartx,startyc0,10 half,10 half,10c0,0 half,0 half,-10".replaceAll("startx",t.x).replaceAll("starty",t.y).replaceAll("half",t.width/2));break;case"system_queue":case"external_system_queue":case"container_queue":case"external_container_queue":case"component_queue":case"external_component_queue":n.append("path").attr("fill",l).attr("stroke-width","0.5").attr("stroke",o).attr("d","Mstartx,startylwidth,0c5,0 5,half 5,halfc0,0 0,half -5,halfl-width,0c-5,0 -5,-half -5,-halfc0,0 0,-half 5,-half".replaceAll("startx",t.x).replaceAll("starty",t.y).replaceAll("width",t.width).replaceAll("half",t.height/2)),n.append("path").attr("fill","none").attr("stroke-width","0.5").attr("stroke",o).attr("d","Mstartx,startyc-5,0 -5,half -5,halfc0,half 5,half 5,half".replaceAll("startx",t.x+t.width).replaceAll("starty",t.y).replaceAll("half",t.height/2))}let d=_0(s,t.typeC4Shape.text);switch(n.append("text").attr("fill",i).attr("font-family",d.fontFamily).attr("font-size",d.fontSize-2).attr("font-style","italic").attr("lengthAdjust","spacing").attr("textLength",t.typeC4Shape.width).attr("x",t.x+t.width/2-t.typeC4Shape.width/2).attr("y",t.y+t.typeC4Shape.Y).text("<<"+t.typeC4Shape.text+">>"),t.typeC4Shape.text){case"person":case"external_person":me(n,48,48,t.x+t.width/2-24,t.y+t.image.Y,r)}let p=s[t.typeC4Shape.text+"Font"]();return p.fontWeight="bold",p.fontSize=p.fontSize+2,p.fontColor=i,$(s)(t.label.text,n,t.x,t.y+t.label.Y,t.width,t.height,{fill:i},p),p=s[t.typeC4Shape.text+"Font"](),p.fontColor=i,t.techn&&((u=t.techn)==null?void 0:u.text)!==""?$(s)(t.techn.text,n,t.x,t.y+t.techn.Y,t.width,t.height,{fill:i,"font-style":"italic"},p):t.type&&t.type.text!==""&&$(s)(t.type.text,n,t.x,t.y+t.type.Y,t.width,t.height,{fill:i,"font-style":"italic"},p),t.descr&&t.descr.text!==""&&(p=s.personFont(),p.fontColor=i,$(s)(t.descr.text,n,t.x,t.y+t.descr.Y,t.width,t.height,{fill:i},p)),t.height},"drawC4Shape"),d0=g(function(e){e.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")},"insertDatabaseIcon"),p0=g(function(e){e.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")},"insertComputerIcon"),u0=g(function(e){e.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")},"insertClockIcon"),y0=g(function(e){e.append("defs").append("marker").attr("id","arrowhead").attr("refX",9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z")},"insertArrowHead"),g0=g(function(e){e.append("defs").append("marker").attr("id","arrowend").attr("refX",1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 10 0 L 0 5 L 10 10 z")},"insertArrowEnd"),f0=g(function(e){e.append("defs").append("marker").attr("id","filled-head").attr("refX",18).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"insertArrowFilledHead"),b0=g(function(e){e.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)},"insertDynamicNumber"),m0=g(function(e){const t=e.append("defs").append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",16).attr("refY",4);t.append("path").attr("fill","black").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1px").attr("d","M 9,2 V 6 L16,4 Z"),t.append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1px").attr("d","M 0,1 L 6,7 M 6,1 L 0,7")},"insertArrowCrossHead"),_0=g((e,t)=>({fontFamily:e[t+"FontFamily"],fontSize:e[t+"FontSize"],fontWeight:e[t+"FontWeight"]}),"getC4ShapeFont"),$=function(){function e(o,i,r,n,a,d,p){l(i.append("text").attr("x",r+a/2).attr("y",n+d/2+5).style("text-anchor","middle").text(o),p)}function t(o,i,r,n,a,d,p,u){const{fontSize:f,fontFamily:O,fontWeight:v}=u,R=o.split(Gt.lineBreakRegex);for(let P=0;P<R.length;P++){const X=P*f-f*(R.length-1)/2,Y=i.append("text").attr("x",r+a/2).attr("y",n).style("text-anchor","middle").attr("dominant-baseline","middle").style("font-size",f).style("font-weight",v).style("font-family",O);Y.append("tspan").attr("dy",X).text(R[P]).attr("alignment-baseline","mathematical"),l(Y,p)}}function s(o,i,r,n,a,d,p,u){const f=i.append("switch"),O=f.append("foreignObject").attr("x",r).attr("y",n).attr("width",a).attr("height",d).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");O.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(o),t(o,f,r,n,a,0,p,u),l(O,p)}function l(o,i){for(const r in i)i.hasOwnProperty(r)&&o.attr(r,i[r])}return g(e,"byText"),g(t,"byTspan"),g(s,"byFo"),g(l,"_setTextAttrs"),function(o){return o.textPlacement==="fo"?s:o.textPlacement==="old"?e:t}}(),z={drawRect:ie,drawBoundary:c0,drawC4Shape:h0,drawRels:l0,drawImage:me,insertArrowHead:y0,insertArrowEnd:g0,insertArrowFilledHead:f0,insertDynamicNumber:b0,insertArrowCrossHead:m0,insertDatabaseIcon:d0,insertComputerIcon:p0,insertClockIcon:u0},Xt=0,zt=0,_e=4,Jt=2;Yt.yy=Kt;var _={},kt,xe=(kt=class{constructor(t){this.name="",this.data={},this.data.startx=void 0,this.data.stopx=void 0,this.data.starty=void 0,this.data.stopy=void 0,this.data.widthLimit=void 0,this.nextData={},this.nextData.startx=void 0,this.nextData.stopx=void 0,this.nextData.starty=void 0,this.nextData.stopy=void 0,this.nextData.cnt=0,Zt(t.db.getConfig())}setData(t,s,l,o){this.nextData.startx=this.data.startx=t,this.nextData.stopx=this.data.stopx=s,this.nextData.starty=this.data.starty=l,this.nextData.stopy=this.data.stopy=o}updateVal(t,s,l,o){t[s]===void 0?t[s]=l:t[s]=o(l,t[s])}insert(t){this.nextData.cnt=this.nextData.cnt+1;let s=this.nextData.startx===this.nextData.stopx?this.nextData.stopx+t.margin:this.nextData.stopx+2*t.margin,l=s+t.width,o=this.nextData.starty+2*t.margin,i=o+t.height;(s>=this.data.widthLimit||l>=this.data.widthLimit||this.nextData.cnt>_e)&&(s=this.nextData.startx+t.margin+_.nextLinePaddingX,o=this.nextData.stopy+2*t.margin,this.nextData.stopx=l=s+t.width,this.nextData.starty=this.nextData.stopy,this.nextData.stopy=i=o+t.height,this.nextData.cnt=1),t.x=s,t.y=o,this.updateVal(this.data,"startx",s,Math.min),this.updateVal(this.data,"starty",o,Math.min),this.updateVal(this.data,"stopx",l,Math.max),this.updateVal(this.data,"stopy",i,Math.max),this.updateVal(this.nextData,"startx",s,Math.min),this.updateVal(this.nextData,"starty",o,Math.min),this.updateVal(this.nextData,"stopx",l,Math.max),this.updateVal(this.nextData,"stopy",i,Math.max)}init(t){this.name="",this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0,widthLimit:void 0},this.nextData={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0,cnt:0},Zt(t.db.getConfig())}bumpLastMargin(t){this.data.stopx+=t,this.data.stopy+=t}},g(kt,"Bounds"),kt),Zt=g(function(e){Pe(_,e),e.fontFamily&&(_.personFontFamily=_.systemFontFamily=_.messageFontFamily=e.fontFamily),e.fontSize&&(_.personFontSize=_.systemFontSize=_.messageFontSize=e.fontSize),e.fontWeight&&(_.personFontWeight=_.systemFontWeight=_.messageFontWeight=e.fontWeight)},"setConf"),Dt=g((e,t)=>({fontFamily:e[t+"FontFamily"],fontSize:e[t+"FontSize"],fontWeight:e[t+"FontWeight"]}),"c4ShapeFont"),Lt=g(e=>({fontFamily:e.boundaryFontFamily,fontSize:e.boundaryFontSize,fontWeight:e.boundaryFontWeight}),"boundaryFont"),x0=g(e=>({fontFamily:e.messageFontFamily,fontSize:e.messageFontSize,fontWeight:e.messageFontWeight}),"messageFont");function L(e,t,s,l,o){if(!t[e].width)if(s)t[e].text=Me(t[e].text,o,l),t[e].textLines=t[e].text.split(Gt.lineBreakRegex).length,t[e].width=o,t[e].height=de(t[e].text,l);else{let i=t[e].text.split(Gt.lineBreakRegex);t[e].textLines=i.length;let r=0;t[e].height=0,t[e].width=0;for(const n of i)t[e].width=Math.max(wt(n,l),t[e].width),r=de(n,l),t[e].height=t[e].height+r}}g(L,"calcC4ShapeTextWH");var Ee=g(function(e,t,s){t.x=s.data.startx,t.y=s.data.starty,t.width=s.data.stopx-s.data.startx,t.height=s.data.stopy-s.data.starty,t.label.y=_.c4ShapeMargin-35;let l=t.wrap&&_.wrap,o=Lt(_);o.fontSize=o.fontSize+2,o.fontWeight="bold",L("label",t,l,o,wt(t.label.text,o)),z.drawBoundary(e,t,_)},"drawBoundary"),Ae=g(function(e,t,s,l){let o=0;for(const i of l){o=0;const r=s[i];let n=Dt(_,r.typeC4Shape.text);switch(n.fontSize=n.fontSize-2,r.typeC4Shape.width=wt("«"+r.typeC4Shape.text+"»",n),r.typeC4Shape.height=n.fontSize+2,r.typeC4Shape.Y=_.c4ShapePadding,o=r.typeC4Shape.Y+r.typeC4Shape.height-4,r.image={width:0,height:0,Y:0},r.typeC4Shape.text){case"person":case"external_person":r.image.width=48,r.image.height=48,r.image.Y=o,o=r.image.Y+r.image.height}r.sprite&&(r.image.width=48,r.image.height=48,r.image.Y=o,o=r.image.Y+r.image.height);let a=r.wrap&&_.wrap,d=_.width-2*_.c4ShapePadding,p=Dt(_,r.typeC4Shape.text);p.fontSize=p.fontSize+2,p.fontWeight="bold",L("label",r,a,p,d),r.label.Y=o+8,o=r.label.Y+r.label.height,r.type&&r.type.text!==""?(r.type.text="["+r.type.text+"]",L("type",r,a,Dt(_,r.typeC4Shape.text),d),r.type.Y=o+5,o=r.type.Y+r.type.height):r.techn&&r.techn.text!==""&&(r.techn.text="["+r.techn.text+"]",L("techn",r,a,Dt(_,r.techn.text),d),r.techn.Y=o+5,o=r.techn.Y+r.techn.height);let u=o,f=r.label.width;r.descr&&r.descr.text!==""&&(L("descr",r,a,Dt(_,r.typeC4Shape.text),d),r.descr.Y=o+20,o=r.descr.Y+r.descr.height,f=Math.max(r.label.width,r.descr.width),u=o-5*r.descr.textLines),f+=_.c4ShapePadding,r.width=Math.max(r.width||_.width,f,_.width),r.height=Math.max(r.height||_.height,u,_.height),r.margin=r.margin||_.c4ShapeMargin,e.insert(r),z.drawC4Shape(t,r,_)}e.bumpLastMargin(_.c4ShapeMargin)},"drawC4ShapeArray"),Tt,I=(Tt=class{constructor(t,s){this.x=t,this.y=s}},g(Tt,"Point"),Tt),pe=g(function(e,t){let s=e.x,l=e.y,o=t.x,i=t.y,r=s+e.width/2,n=l+e.height/2,a=Math.abs(s-o),d=Math.abs(l-i),p=d/a,u=e.height/e.width,f=null;return l==i&&s<o?f=new I(s+e.width,n):l==i&&s>o?f=new I(s,n):s==o&&l<i?f=new I(r,l+e.height):s==o&&l>i&&(f=new I(r,l)),s>o&&l<i?f=u>=p?new I(s,n+p*e.width/2):new I(r-a/d*e.height/2,l+e.height):s<o&&l<i?f=u>=p?new I(s+e.width,n+p*e.width/2):new I(r+a/d*e.height/2,l+e.height):s<o&&l>i?f=u>=p?new I(s+e.width,n-p*e.width/2):new I(r+e.height/2*a/d,l):s>o&&l>i&&(f=u>=p?new I(s,n-e.width/2*p):new I(r-e.height/2*a/d,l)),f},"getIntersectPoint"),E0=g(function(e,t){let s={x:0,y:0};s.x=t.x+t.width/2,s.y=t.y+t.height/2;let l=pe(e,s);return s.x=e.x+e.width/2,s.y=e.y+e.height/2,{startPoint:l,endPoint:pe(t,s)}},"getIntersectPoints"),A0=g(function(e,t,s,l){let o=0;for(let i of t){o+=1;let r=i.wrap&&_.wrap,n=x0(_);l.db.getC4Type()==="C4Dynamic"&&(i.label.text=o+": "+i.label.text);let a=wt(i.label.text,n);L("label",i,r,n,a),i.techn&&i.techn.text!==""&&(a=wt(i.techn.text,n),L("techn",i,r,n,a)),i.descr&&i.descr.text!==""&&(a=wt(i.descr.text,n),L("descr",i,r,n,a));let d=s(i.from),p=s(i.to),u=E0(d,p);i.startPoint=u.startPoint,i.endPoint=u.endPoint}z.drawRels(e,t,_)},"drawRels");function ae(e,t,s,l,o){let i=new xe(o);i.data.widthLimit=s.data.widthLimit/Math.min(Jt,l.length);for(let[r,n]of l.entries()){let a=0;n.image={width:0,height:0,Y:0},n.sprite&&(n.image.width=48,n.image.height=48,n.image.Y=a,a=n.image.Y+n.image.height);let d=n.wrap&&_.wrap,p=Lt(_);if(p.fontSize=p.fontSize+2,p.fontWeight="bold",L("label",n,d,p,i.data.widthLimit),n.label.Y=a+8,a=n.label.Y+n.label.height,n.type&&n.type.text!==""&&(n.type.text="["+n.type.text+"]",L("type",n,d,Lt(_),i.data.widthLimit),n.type.Y=a+5,a=n.type.Y+n.type.height),n.descr&&n.descr.text!==""){let v=Lt(_);v.fontSize=v.fontSize-2,L("descr",n,d,v,i.data.widthLimit),n.descr.Y=a+20,a=n.descr.Y+n.descr.height}if(r==0||r%Jt==0){let v=s.data.startx+_.diagramMarginX,R=s.data.stopy+_.diagramMarginY+a;i.setData(v,v,R,R)}else{let v=i.data.stopx!==i.data.startx?i.data.stopx+_.diagramMarginX:i.data.startx,R=i.data.starty;i.setData(v,v,R,R)}i.name=n.alias;let u=o.db.getC4ShapeArray(n.alias),f=o.db.getC4ShapeKeys(n.alias);f.length>0&&Ae(i,e,u,f),t=n.alias;let O=o.db.getBoundarys(t);O.length>0&&ae(e,t,i,O,o),n.alias!=="global"&&Ee(e,n,i),s.data.stopy=Math.max(i.data.stopy+_.c4ShapeMargin,s.data.stopy),s.data.stopx=Math.max(i.data.stopx+_.c4ShapeMargin,s.data.stopx),Xt=Math.max(Xt,s.data.stopx),zt=Math.max(zt,s.data.stopy)}}g(ae,"drawInsideBoundary");var ue={drawPersonOrSystemArray:Ae,drawBoundary:Ee,setConf:Zt,draw:g(function(e,t,s,l){_=Nt().c4;const o=Nt().securityLevel;let i;o==="sandbox"&&(i=Vt("#i"+t));const r=Vt(o==="sandbox"?i.nodes()[0].contentDocument.body:"body");let n=l.db;l.db.setWrap(_.wrap),_e=n.getC4ShapeInRow(),Jt=n.getC4BoundaryInRow(),he.debug(`C:${JSON.stringify(_,null,2)}`);const a=o==="sandbox"?r.select(`[id="${t}"]`):Vt(`[id="${t}"]`);z.insertComputerIcon(a),z.insertDatabaseIcon(a),z.insertClockIcon(a);let d=new xe(l);d.setData(_.diagramMarginX,_.diagramMarginX,_.diagramMarginY,_.diagramMarginY),d.data.widthLimit=screen.availWidth,Xt=_.diagramMarginX,zt=_.diagramMarginY;const p=l.db.getTitle();ae(a,"",d,l.db.getBoundarys(""),l),z.insertArrowHead(a),z.insertArrowEnd(a),z.insertArrowCrossHead(a),z.insertArrowFilledHead(a),A0(a,l.db.getRels(),l.db.getC4Shape,l),d.data.stopx=Xt,d.data.stopy=zt;const u=d.data;let f=u.stopy-u.starty+2*_.diagramMarginY;const O=u.stopx-u.startx+2*_.diagramMarginX;p&&a.append("text").text(p).attr("x",(u.stopx-u.startx)/2-4*_.diagramMarginX).attr("y",u.starty+_.diagramMarginY),Ne(a,f,O,_.useMaxWidth);const v=p?60:0;a.attr("viewBox",u.startx-_.diagramMarginX+" -"+(_.diagramMarginY+v)+" "+O+" "+(f+v)),he.debug("models:",u)},"draw")},g1={parser:je,db:Kt,renderer:ue,styles:g(e=>`.person {
    stroke: ${e.personBorder};
    fill: ${e.personBkg};
  }
`,"getStyles"),init:g(({c4:e,wrap:t})=>{ue.setConf(e),Kt.setWrap(t)},"init")};export{g1 as diagram};
