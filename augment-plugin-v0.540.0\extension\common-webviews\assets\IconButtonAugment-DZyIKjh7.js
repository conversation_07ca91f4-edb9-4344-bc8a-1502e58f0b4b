import{N as I,ad as V,aP as W,aQ as X,ab as Y,aR as k,a7 as Z,u as aa,v as ta,Q as ea,aN as sa,x as L,y as S,a as na,D as ra,b as f,G as P,z as C,A as _,C as ia,o as w,B as x,E as la,F as B,L as A,H as oa,a1 as ca}from"./legacy-AoIeRrIA.js";import{j as ua,p as n,d as da,m as ha,S as va,s as E,r as D,f as j}from"./SpinnerAugment-mywmfXFR.js";import{b as i}from"./host-qgbK079d.js";function ka(d,a,s=!1,r=!1,v=!1){var u=d,l="";I(()=>{var h=V;if(l!==(l=a()??"")&&(h.nodes_start!==null&&(W(h.nodes_start,h.nodes_end),h.nodes_start=h.nodes_end=null),l!=="")){var o=l+"";s?o=`<svg>${o}</svg>`:r&&(o=`<math>${o}</math>`);var c=X(o);if((s||r)&&(c=k(c)),Y(k(c),c.lastChild),s||r)for(;k(c);)u.before(k(c));else u.before(c)}})}function Ca(d,a,s){Z(()=>{var r=aa(()=>a(d,s==null?void 0:s())||{});if(s&&(r!=null&&r.update)){var v=!1,u={};ta(()=>{var l=s();ea(l),v&&sa(u,l)&&(u=l,r.update(l))}),v=!0}if(r!=null&&r.destroy)return()=>r.destroy()})}function q(d){return String(d).replace(".","_")}var ga=S('<div class="c-base-btn__loading svelte-5auyf2"><!></div> <span class="c-base-btn__hidden-content svelte-5auyf2"><!></span>',1),ba=S("<button><!></button>");function ma(d,a){L(a,!0);const s=ua();let r=n(a,"size",3,2),v=n(a,"variant",3,"solid"),u=n(a,"color",3,"accent"),l=n(a,"disabled",3,!1),h=n(a,"highContrast",3,!1),o=n(a,"loading",3,!1),c=n(a,"alignment",3,"center"),t=n(a,"radius",3,"medium"),y=D(a,["$$slots","$$events","$$legacy","size","variant","color","disabled","highContrast","loading","alignment","radius","children"]),m=_(()=>a.class),e=_(()=>oa(y,["class"]));var p=ba();na(p,(g,b,$,G,H,N,Q,R,K,M,O,T,U)=>({...g,...b,class:$,disabled:l()||o(),onclick:G,onkeyup:H,onkeydown:N,onmousedown:Q,onmouseover:R,onfocus:K,onmouseleave:M,onblur:O,oncontextmenu:T,...C(e),[ia]:U}),[()=>da(u()),()=>ha(t()),()=>`c-base-btn c-base-btn--size-${q(r())} c-base-btn--${v()} c-base-btn--${u()} ${C(m)} c-base-btn--alignment-${c()}`,()=>s("click"),()=>s("keyup"),()=>s("keydown"),()=>s("mousedown"),()=>s("mouseover"),()=>s("focus"),()=>s("mouseleave"),()=>s("blur"),()=>s("contextmenu"),()=>({"c-base-btn--highContrast":h(),"c-base-btn--loading":o()})],"svelte-5auyf2");var z=w(p),F=g=>{var b=ga(),$=x(b),G=w($);const H=_(()=>function(R){switch(R){case 0:case .5:case 1:return 1;case 2:case 3:return 2;case 4:return 3}}(r()));va(G,{get size(){return C(H)}});var N=la($,2),Q=w(N);E(Q,()=>a.children??B),f(g,b)},J=g=>{var b=A(),$=x(b);E($,()=>a.children??B),f(g,b)};ra(z,g=>{o()?g(F):g(J,!1)}),f(d,p),P()}var $a=S("<div><!></div>");function fa(d,a){L(a,!0);let s=n(a,"size",3,2),r=n(a,"variant",3,"solid"),v=n(a,"color",3,"accent"),u=n(a,"highContrast",3,!1),l=n(a,"disabled",3,!1),h=n(a,"radius",3,"medium"),o=n(a,"loading",3,!1),c=D(a,["$$slots","$$events","$$legacy","size","variant","color","highContrast","disabled","radius","class","loading","children"]);var t=$a(),y=w(t);const m=_(()=>l()||void 0);ma(y,j({get size(){return s()},get variant(){return r()},get color(){return v()},get highContrast(){return u()},get disabled(){return C(m)},get radius(){return h()},get loading(){return o()},get class(){return a.class}},()=>c,{$$events:{click(e){i.call(this,a,e)},keyup(e){i.call(this,a,e)},keydown(e){i.call(this,a,e)},mousedown(e){i.call(this,a,e)},mouseover(e){i.call(this,a,e)},focus(e){i.call(this,a,e)},mouseleave(e){i.call(this,a,e)},blur(e){i.call(this,a,e)},contextmenu(e){i.call(this,a,e)}},children:(e,p)=>{var z=A(),F=x(z);E(F,()=>a.children??B),f(e,z)},$$slots:{default:!0}})),I(e=>ca(t,1,e,"svelte-1mz435m"),[()=>`c-icon-btn c-icon-btn--size-${q(s())}`]),f(d,t),P()}function _a(d,a){let s=n(a,"size",3,2),r=n(a,"variant",3,"solid"),v=n(a,"color",3,"neutral"),u=n(a,"highContrast",3,!1),l=n(a,"disabled",3,!1),h=n(a,"radius",3,"medium"),o=n(a,"loading",3,!1),c=D(a,["$$slots","$$events","$$legacy","size","variant","color","highContrast","disabled","radius","loading","children"]);fa(d,j({get size(){return s()},get variant(){return r()},get color(){return v()},get highContrast(){return u()},get disabled(){return l()},get radius(){return h()},get loading(){return o()}},()=>c,{$$events:{click(t){i.call(this,a,t)},keyup(t){i.call(this,a,t)},keydown(t){i.call(this,a,t)},mousedown(t){i.call(this,a,t)},mouseover(t){i.call(this,a,t)},focus(t){i.call(this,a,t)},mouseleave(t){i.call(this,a,t)},blur(t){i.call(this,a,t)},contextmenu(t){i.call(this,a,t)}},children:(t,y)=>{var m=A(),e=x(m);E(e,()=>a.children??B),f(t,m)},$$slots:{default:!0}}))}export{ma as B,fa as C,_a as I,Ca as a,ka as h};
