import{y as u,D as g,u as h,E as y,o as c,N as K,a1 as M,b as o,L as O,B as P,z,R as x}from"./legacy-AoIeRrIA.js";import{h as Q,l as C,p as i,f as U,g as m,T as V}from"./SpinnerAugment-mywmfXFR.js";import{b as a}from"./host-qgbK079d.js";import{B as W}from"./IconButtonAugment-DZyIKjh7.js";var X=u('<div class="c-button--icon svelte-p25y67"><!></div>'),Y=u('<div class="c-button--text svelte-p25y67"><!></div>'),Z=u('<div class="c-button--icon svelte-p25y67"><!></div>'),_=u("<div><!> <!> <!></div>");function it(w,t){const d=Q(t),B=C(t,["children","$$slots","$$events","$$legacy"]),L=C(B,["size","variant","color","highContrast","disabled","radius","loading","alignment"]);let r=i(t,"size",8,2),f=i(t,"variant",8,"solid"),R=i(t,"color",8,"neutral"),D=i(t,"highContrast",8,!1),E=i(t,"disabled",8,!1),N=i(t,"radius",8,"medium"),j=i(t,"loading",8,!1),A=i(t,"alignment",8,"center");W(w,U({get size(){return r()},get variant(){return f()},get color(){return R()},get highContrast(){return D()},get disabled(){return E()},get loading(){return j()},get alignment(){return A()},get radius(){return N()}},()=>L,{$$events:{click(e){a.call(this,t,e)},keyup(e){a.call(this,t,e)},keydown(e){a.call(this,t,e)},mousedown(e){a.call(this,t,e)},mouseover(e){a.call(this,t,e)},focus(e){a.call(this,t,e)},mouseleave(e){a.call(this,t,e)},blur(e){a.call(this,t,e)},contextmenu(e){a.call(this,t,e)}},children:(e,k)=>{var v=_(),$=c(v),I=s=>{var l=X(),n=c(l);m(n,t,"iconLeft",{},null),o(s,l)};g($,s=>{h(()=>d.iconLeft)&&s(I)});var b=y($,2),S=s=>{var l=Y(),n=c(l);const F=x(()=>r()===.5?1:r()),G=x(()=>f()==="ghost"||r()===.5?"regular":"medium");V(n,{get size(){return z(F)},get weight(){return z(G)},children:(H,tt)=>{var p=O(),J=P(p);m(J,t,"default",{},null),o(H,p)},$$slots:{default:!0}}),o(s,l)};g(b,s=>{h(()=>d.default)&&s(S)});var T=y(b,2),q=s=>{var l=Z(),n=c(l);m(n,t,"iconRight",{},null),o(s,l)};g(T,s=>{h(()=>d.iconRight)&&s(q)}),K(()=>M(v,1,`c-button--content c-button--size-${r()}`,"svelte-p25y67")),o(e,v)},$$slots:{default:!0}}))}export{it as B};
