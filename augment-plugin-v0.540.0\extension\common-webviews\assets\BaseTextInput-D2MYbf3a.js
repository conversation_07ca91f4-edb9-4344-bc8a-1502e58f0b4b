import{x as b,y as m,a as h,z as y,C as z,A as $,L as g,B as j,F as x,b as c,o as A,G as B}from"./legacy-AoIeRrIA.js";import{p as s,T as k,s as C,d as n}from"./SpinnerAugment-mywmfXFR.js";import"./IconButtonAugment-DZyIKjh7.js";var F=m("<div><!></div>");function w(p,a){b(a,!0);let l=s(a,"variant",3,"surface"),i=s(a,"size",3,2),u=s(a,"type",3,"default"),t=s(a,"color",19,()=>{}),v=$(()=>t()?n(t()):n("accent"));var e=F();h(e,r=>({...y(v),class:`c-base-text-input c-base-text-input--${l()} c-base-text-input--size-${i()}`,[z]:r}),[()=>({"c-base-text-input--has-color":t()!==void 0})],"svelte-11yhjko");var d=A(e);k(d,{get type(){return u()},get size(){return i()},children:(r,G)=>{var o=g(),f=j(o);C(f,()=>a.children??x),c(r,o)},$$slots:{default:!0}}),c(p,e),B()}export{w as B};
