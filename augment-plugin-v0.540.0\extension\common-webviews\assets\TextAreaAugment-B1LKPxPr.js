import{f as P,a as C,o as f,b as p,x as S,a6 as T,y as g,D as A,E as H,z as d,C as Y,A as y,a7 as J,G as K,H as Q,F as U,N as W,a3 as X,O as Z}from"./legacy-AoIeRrIA.js";import{h as ee,a as te}from"./IconButtonAugment-DZyIKjh7.js";import{l as se,j as ae,p as o,k as oe,r as ne,s as ie}from"./SpinnerAugment-mywmfXFR.js";import{b as re}from"./input-DCBQtNgo.js";import{B as ce}from"./BaseTextInput-D2MYbf3a.js";var le=P("<svg><!></svg>");function xe(w,e){const s=se(e,["children","$$slots","$$events","$$legacy"]);var c=le();C(c,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...s}));var x=f(c);ee(x,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M384 336H192c-8.8 0-16-7.2-16-16V64c0-8.8 7.2-16 16-16h140.1l67.9 67.9V320c0 8.8-7.2 16-16 16m-192 48h192c35.3 0 64-28.7 64-64V115.9c0-12.7-5.1-24.9-14.1-33.9l-67.8-67.9c-9-9-21.2-14.1-33.9-14.1H192c-35.3 0-64 28.7-64 64v256c0 35.3 28.7 64 64 64M64 128c-35.3 0-64 28.7-64 64v256c0 35.3 28.7 64 64 64h192c35.3 0 64-28.7 64-64v-32h-48v32c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16V192c0-8.8 7.2-16 16-16h32v-48z"/>',!0),p(w,c)}var ve=g('<label class="c-text-area-label svelte-c1sr7w"> </label>'),ue=g('<div class="c-text-area-label-container svelte-c1sr7w"><!> <!></div>'),he=g("<textarea></textarea>"),pe=g('<div class="c-text-area svelte-c1sr7w"><!> <!></div>');function be(w,e){S(e,!0);const s=ae();let c=o(e,"label",19,()=>{}),x=o(e,"variant",3,"surface"),E=o(e,"size",3,2),L=o(e,"color",19,()=>{}),m=o(e,"resize",3,"none"),t=o(e,"textInput",15),F=o(e,"type",3,"default"),R=o(e,"value",15),M=o(e,"id",19,()=>{}),V=ne(e,["$$slots","$$events","$$legacy","label","variant","size","color","resize","textInput","type","value","id","topRightAction","focus"]),$=y(()=>M()||`text-field-${Math.random().toString(36).substring(2,11)}`),_=y(()=>e.class),I=y(()=>Q(V,["class"]));function u(){if(!t())return;t(t().style.height="auto",!0);const a=.8*window.innerHeight,h=Math.min(t().scrollHeight,a);t(t().style.height=`${h}px`,!0),t(t().style.overflowY=t().scrollHeight>a?"auto":"hidden",!0)}T(()=>{if(t()){e.focus&&t().focus(),requestAnimationFrame(u);const a=()=>u();return window.addEventListener("resize",a),()=>{window.removeEventListener("resize",a)}}});var z=pe(),k=f(z),q=a=>{var h=ue(),n=f(h),l=i=>{var r=ve(),b=f(r);W(()=>{X(r,"for",d($)),Z(b,c())}),p(i,r)};A(n,i=>{c()&&i(l)});var v=H(n,2);ie(v,()=>e.topRightAction??U),p(a,h)};A(k,a=>{(c()||e.topRightAction)&&a(q)});var B=H(k,2);ce(B,{get type(){return F()},get variant(){return x()},get size(){return E()},get color(){return L()},children:(a,h)=>{var n=he();C(n,(l,v,i,r,b,O,j,D,G,N)=>({id:d($),spellCheck:"false",class:`c-text-area__input c-base-text-input__input ${d(_)}`,...d(I),onclick:l,onfocus:v,onkeydown:i,onchange:r,oninput:b,onkeyup:O,onblur:j,onselect:D,onmouseup:G,[Y]:N}),[()=>s("click"),()=>s("focus"),()=>s("keydown"),()=>s("change"),()=>s("input"),()=>s("keyup"),()=>s("blur"),()=>s("select"),()=>s("mouseup"),()=>({"c-textarea--resize-none":m()==="none","c-textarea--resize-both":m()==="both","c-textarea--resize-horizontal":m()==="horizontal","c-textarea--resize-vertical":m()==="vertical"})],"svelte-c1sr7w"),oe(n,l=>t(l),()=>t()),J(()=>re(n,R)),te(n,l=>function(v){requestAnimationFrame(u);const i=()=>u();v.addEventListener("input",i);const r=new ResizeObserver(u);return r.observe(v),{destroy(){v.removeEventListener("input",i),r.disconnect()}}}(l)),p(a,n)},$$slots:{default:!0}}),p(w,z),K()}export{xe as C,be as T};
