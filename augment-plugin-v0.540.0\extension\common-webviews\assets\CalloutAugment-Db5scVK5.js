import{x as H,y as r,a as j,z as h,C as T,A as f,B as k,D as q,E as I,F as g,o,b as i,G as J,H as K}from"./legacy-AoIeRrIA.js";import{p as s,d as L,T as M,s as z,r as N}from"./SpinnerAugment-mywmfXFR.js";var O=r('<div class="c-callout-icon svelte-149tvwv"><!></div>'),P=r('<!> <div class="c-callout-body svelte-149tvwv"><!></div>',1),Q=r("<div><!></div>");function U(C,a){H(a,!0);let v=s(a,"color",3,"info"),m=s(a,"variant",3,"soft"),e=s(a,"size",3,2),p=s(a,"highContrast",3,!1),y=N(a,["$$slots","$$events","$$legacy","color","variant","size","highContrast","icon","children"]);const b=e();let x=f(()=>a.class),A=f(()=>K(y,["class"]));var t=Q();j(t,(l,n)=>({...l,class:`c-callout c-callout--${v()} c-callout--${m()} c-callout--size-${e()} ${h(x)}`,...h(A),[T]:n}),[()=>L(v()),()=>({"c-callout--highContrast":p()})],"svelte-149tvwv");var B=o(t);M(B,{get size(){return b},children:(l,n)=>{var $=P(),d=k($),D=c=>{var u=O(),G=o(u);z(G,()=>a.icon??g),i(c,u)};q(d,c=>{a.icon&&c(D)});var E=I(d,2),F=o(E);z(F,()=>a.children??g),i(l,$)},$$slots:{default:!0}}),i(C,t),J()}export{U as C};
