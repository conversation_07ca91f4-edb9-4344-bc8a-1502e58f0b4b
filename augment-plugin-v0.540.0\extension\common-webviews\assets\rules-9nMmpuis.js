import{x as W,y,P as C,S as me,z as e,A as v,o as n,b as l,G as j,B as fe,E as g,M,D as Q,a6 as ge,K as he,a8 as ye,ag as be,Z as $e,au as ke}from"./legacy-AoIeRrIA.js";import{T as L,b as Ee,a as Fe}from"./SpinnerAugment-mywmfXFR.js";import"./design-system-init-Creeq9bS.js";import{h,W as D}from"./host-qgbK079d.js";import{l as we}from"./lodash-CwNMWAFx.js";import{M as U}from"./message-broker-EhzME3pO.js";import{B as xe}from"./ButtonAugment-D7YBjBq5.js";import{M as ze}from"./MonacoMarkdownEditor-DpNSkR_h.js";import{R as Re,a as x,E as Te,T as Ce,C as Me}from"./index-BLDiLrXG.js";import{T as Le}from"./CardAugment-DwIptXof.js";import{C as De}from"./chevron-left-h-peZKbD.js";import{R as Ne}from"./chat-types-BfwvR7Kn.js";import{O as Se}from"./OpenFileButton-CWm-PsCk.js";import{R as _e}from"./RulesModeSelector-DckNSDsy.js";import"./async-messaging-Dmg2N9Pf.js";import"./IconButtonAugment-DZyIKjh7.js";import"./index-BdF7sLLk.js";import"./focusTrapStack-CaEmYw0i.js";import"./isObjectLike-CnrzNkq5.js";import"./input-DCBQtNgo.js";import"./BaseTextInput-D2MYbf3a.js";import"./event-modifiers-Bz4QCcZc.js";import"./chat-model-context-DPgWxlAp.js";import"./index-B528snJk.js";import"./index-CJlvVQMt.js";import"./remote-agents-client-BMR_qMG5.js";import"./types-CGlLNakm.js";import"./SuccessfulButton-xgZ5Aax4.js";import"./chevron-down-CUZr9PGN.js";var Ae=y('<div class="c-rule-config svelte-1ivykbn"><div class="c-rule-field c-rule-field-full-width svelte-1ivykbn"><!> <!></div></div>'),Be=y('<div class="l-file-controls svelte-1ivykbn"><div class="l-file-controls-left svelte-1ivykbn"><div class="c-trigger-section svelte-1ivykbn"><!> <!> <!></div></div> <!></div> <!>',1),Ge=y('<div class="l-rules-editor svelte-1ivykbn"><div class="c-rules-editor__content svelte-1ivykbn"><!></div></div>'),Ie=y("<div>Loading...</div>"),Ke=y('<div class="c-rules-container svelte-1vbu0zh"><!></div>');ke(function(V,Z){W(Z,!1);const[q,X]=Fe(),N=()=>Ee(_,"$rule",q),S=new U(h),_=$e(null),Y={handleMessageFromExtension(s){const t=s.data;if(t&&t.type===D.loadFile&&t){const o=t.data.content;if(o!==void 0){const i=o.replace(/^\n+/,""),d=x.getDescriptionFrontmatterKey(i),b=x.getRuleTypeFromContent(i),c=x.extractContent(i);_.set({path:t.data.pathName,content:c,type:b,description:d})}}return!0}};ge(()=>{S.registerConsumer(Y),h.postMessage({type:D.rulesLoaded})}),he();var A=Ke();ye("message",be,function(...s){var t;(t=S.onMessageFromExtension)==null||t.apply(this,s)});var H=n(A),J=s=>{(function(t,o){W(o,!0);let i=v(()=>o.rule.path),d=v(()=>o.rule.type),b=v(()=>o.rule.content),c=v(()=>o.rule.description),B=v(()=>x.extractContent(e(b))),$=me(void 0),te=v(()=>({path:e(i),type:e(d),content:e(b),description:e(c)}));const G=new U(h),se=new Me,re=new Te(h,G,se),ae=new Re(G),I=async(u,r)=>{if(!e($))return;const k=e($).getValue(),E=u??e(d),F=r??e(c),z={type:E,path:e(i),content:k,description:F};try{await ae.updateRuleContent(z)}catch(m){if(console.error("Failed to save rule:",m),u===void 0&&r===void 0)throw m}},K=(u,r)=>I(u,r),oe=we.debounce(K,500),ie=()=>{h.postMessage({type:D.openSettingsPage,data:"guidelines"})};var O=Ge(),ne=n(O),le=n(ne);ze(le,{saveFunction:I,get value(){return e(B)},set value(r){C(B,r)},get editorInstance(){return e($)},set editorInstance(r){C($,r,!0)},header:r=>{var k=Be(),E=fe(k),F=n(E),z=n(F),m=n(z);Le(m,{content:"Navigate back to all Rules & Guidelines",children:(a,f)=>{xe(a,{size:1,variant:"ghost-block",color:"neutral",class:"c-back-button",$$events:{click:ie},$$slots:{iconLeft:(p,w)=>{De(p,{slot:"iconLeft"})}}})},$$slots:{default:!0}});var P=g(m,2);L(P,{size:1,class:"c-field-label",children:(a,f)=>{var p=M("Trigger:");l(a,p)},$$slots:{default:!0}});var ce=g(P,2);_e(ce,{onSave:K,get rule(){return e(te)}});var pe=g(F,2);Se(pe,{size:1,get path(){return e(i)},onOpenLocalFile:async()=>(re.openFile({repoRoot:"",pathName:e(i)}),"success"),$$slots:{text:(a,f)=>{L(a,{slot:"text",size:1,children:(p,w)=>{var R=M("Open file");l(p,R)},$$slots:{default:!0}})}}});var de=g(E,2),ve=a=>{var f=Ae(),p=n(f),w=n(p);L(w,{size:1,class:"c-field-label",children:(T,Oe)=>{var ue=M("Description");l(T,ue)},$$slots:{default:!0}});var R=g(w,2);Ce(R,{placeholder:"When should this rules file be fetched by the Agent?",size:1,get value(){return e(c)},set value(T){C(c,T)},$$events:{input:()=>oe(e(d),e(c))}}),l(a,f)};Q(de,a=>{e(d)===Ne.AGENT_REQUESTED&&a(ve)}),l(r,k)},$$slots:{header:!0}}),l(t,O),j()})(s,{get rule(){return N()}})},ee=s=>{var t=Ie();l(s,t)};Q(H,s=>{N()!==null?s(J):s(ee,!1)}),l(V,A),j(),X()},{target:document.getElementById("app")});
