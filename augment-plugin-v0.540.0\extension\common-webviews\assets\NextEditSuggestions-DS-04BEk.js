import{v as j0,aW as M0,Y as E0,Z as F0,X as R0,a4 as J3,x as s1,y as q,N as G,a1 as U1,b as S,G as l1,B as s3,E as _,z as a,A as K,S as y1,a3 as F,a2 as p1,P as E,as as Q3,m as n1,I as X1,J as L3,K as l3,L as q2,D as T1,a as o2,u as U,C as O0,o as f,a7 as o3,a8 as P1,Q as u1,f as o1,F as $0,av as H2,a0 as P0,T as m3,O as Z3,R as v1,c$ as z0,a_ as B0,a6 as J2,W as N0,ag as Z0,M as D0}from"./legacy-AoIeRrIA.js";import{p as C,k as c3,a as S1,b as f1,l as a2,g as a3,i as Y1,s as I0,q as D1,S as X3,f as K0,n as T0,o as E1}from"./SpinnerAugment-mywmfXFR.js";import{S as e3,C as s2}from"./next-edit-types-904A5ehg.js";import{e as u3,i as Q2,W as Q,h as C1}from"./host-qgbK079d.js";import{a as x1,g as W0,b as U0,f as X2,S as V0,c as W1,I as Y2,d as l2,i as G0,e as t0,n as G1,A as e0,h as q0,s as D3,j as H0,k as c2,l as J0,m as u2}from"./IconFilePath-DsS2-quS.js";import{A as Q0}from"./async-messaging-Dmg2N9Pf.js";import{r as X0,D as Y0}from"./Drawer-KXH87plr.js";import{a as t3}from"./IconButtonAugment-DZyIKjh7.js";import{o as x3}from"./keypress-DD1aQVr0.js";import{t as n0}from"./index-Dtf_gCqL.js";import{V as t4}from"./VSCodeCodicon-Bh752rpS.js";import{c as e4,a as n4,g as d2,M as r4}from"./index-BdF7sLLk.js";import{a as i4}from"./monaco-render-utils-DfwV7QLY.js";import{e as o4}from"./toggleHighContrast-Cb9MCs64.js";import{r as a4}from"./LanguageIcon-D_oFq7vE.js";import{i as r0,_ as _3,a as B1,b as g2,c as b3,d as s4}from"./isObjectLike-CnrzNkq5.js";import{B as l4}from"./ButtonAugment-D7YBjBq5.js";import"./ellipsis-5yZhsJie.js";import"./preload-helper-Dv6uf1Os.js";const i0=Symbol("code-roll-selection-context");function n3(){let t=R0(i0);return t||(t=o0({})),t}function o0(t){return E0(i0,F0(t))}function I1(t){return t.activeSuggestion??t.selectedSuggestion??t.nextSuggestion}function v3(t){return t.activeSuggestion?"active":t.selectedSuggestion?"select":"next"}function K1(t,e,n){return e.activeSuggestion?x1(e.activeSuggestion,t)?n?"select":"active":"none":e.selectedSuggestion?x1(e.selectedSuggestion,t)?n?"active":"select":"none":e.nextSuggestion&&x1(e.nextSuggestion,t)?"next":"none"}function a0(t){return["",`--augment-code-roll-selection-background: var(--augment-code-roll-item-background-${t})`,`--augment-code-roll-selection-color: var(--augment-code-roll-item-color-${t})`,"--augment-code-roll-selection-border: var(--augment-code-roll-selection-background)",""].join(";")}const O3={scrollContainer:document.documentElement,scrollIntoView:{behavior:"smooth",block:"nearest"},scrollDelayMS:100,useSmartBlockAlignment:!0,doScroll:!0};function k1(t,e=O3){let n,r=Object.assign({},O3,e);function i(o){let{doScroll:s,scrollIntoView:l,scrollDelayMS:g,useSmartBlockAlignment:u,scrollContainer:d}=Object.assign({},r,o);s&&(u&&t.getBoundingClientRect().height>((d==null?void 0:d.getBoundingClientRect().height)??1/0)&&(l=Object.assign({},l,{block:"start"})),n=setTimeout(()=>{const y=t.getBoundingClientRect();if(y.bottom===0&&y.top===0&&y.height===0&&y.width===0)return;const x=function(L,m,h){const M=L.getBoundingClientRect(),A=m.getBoundingClientRect();if(h==="nearest")if(M.bottom>A.bottom)h="end";else{if(!(M.top<A.top))return m.scrollTop;h="start"}return M.height>A.height||h==="start"?m.scrollTop+M.top:h==="end"?m.scrollTop+M.bottom-A.height:m.scrollTop+M.top-(A.height-M.height)/2}(t,d,(l==null?void 0:l.block)??O3.scrollIntoView.block);d.scrollTo({top:x,behavior:l==null?void 0:l.behavior})},g))}return i(r),{update:i,destroy(){clearTimeout(n)}}}var c4=(t,e,n)=>e.onCodeAction("select",a(n)),u4=q('<div role="button" tabindex="0"></div>'),d4=q('<div class="c-suggestion-tree__file-divider svelte-ffrg54"></div> <!>',1),g4=q("<div></div>");function h4(t,e){s1(e,!0);const[n,r]=S1(),i=()=>f1(l,"$ctx",n);let o=C(e,"sortedPathSuggestionsMap",19,()=>new Map),s=C(e,"show",3,!0);const l=n3();let g=y1(void 0);var u=g4();let d;u3(u,21,()=>o().entries(),([y,x])=>y,(y,x)=>{var L=K(()=>Q3(a(x),2)),m=d4(),h=s3(m),M=_(h,2);u3(M,17,()=>a(L)[1],Q2,(A,p)=>{var O=u4();const c=K(()=>K1(a(p),i(),!0));O.__click=[c4,e,p];var b=K(()=>x3("Space",()=>e.onCodeAction("select",a(p))));O.__keydown=function(...$){var k;(k=a(b))==null||k.apply(this,$)},t3(O,($,k)=>k1==null?void 0:k1($,k),()=>({scrollContainer:a(g),doScroll:x1(a(p),I1(i())),scrollIntoView:{behavior:"smooth",block:"nearest"}})),G($=>{F(O,"title",`${a(p).lineRange.start+1}: ${a(p).result.changeDescription??""}`),U1(O,1,`c-suggestion-tree__tick-mark ${a(c)??""}`,"svelte-ffrg54"),p1(O,$)},[()=>function($){let k="var(--ds-color-neutral-7, currentColor)";return $==="active"&&(k="var(--augment-code-roll-item-background-active, currentColor)"),$==="select"&&(k="currentColor"),$==="next"&&(k="var(--ds-color-neutral-10, currentColor)"),`--augment-code-roll-selection-background: ${k}`}(a(c))]),S(A,O)}),G(()=>F(h,"title",a(L)[0])),S(y,m)}),c3(u,y=>E(g,y),()=>a(g)),G(y=>d=U1(u,1,"c-suggestion-tree__minimized svelte-ffrg54",null,d,y),[()=>({hidden:!s()})]),S(t,u),l1(),r()}J3(["click","keydown"]);const $3={duration:300,easing:"ease-out"},P3=(t,e=$3)=>{const n=t.querySelector("summary"),r=t.querySelector(".c-detail__content");if(!n||!r)return;e={...$3,...e};const i=/^(tb|vertical)/.test(getComputedStyle(r).writingMode);let o=!1;const s=d=>{o=!0,d&&(t.open=!0),t.dispatchEvent(new CustomEvent(d?"open-start":"close-start",{detail:t}));const y=r[i?"clientWidth":"clientHeight"],x=r.animate({blockSize:d?["0",`${y}px`]:[`${y}px`,"0"]},e);x.oncancel=x.onfinish=x.onremove=()=>{t.dispatchEvent(new CustomEvent(d?"open-end":"close-end",{detail:t})),d||(t.open=!1),o=!1}},l=new MutationObserver(d=>{for(const y of d)if(y.type==="attributes"&&y.attributeName==="open"){if(o)return;t.open&&s(!0)}});l.observe(t,{attributes:!0});const g=d=>{d.preventDefault(),o||s(!t.open)},u=x3("Enter",g);return n.addEventListener("click",g),n.addEventListener("keypress",u),{destroy(){l.disconnect(),n.removeEventListener("click",g),n.removeEventListener("keypress",u)},update(d=$3){e={...e,...d}}}};var C4=q('<details><summary class="c-detail__summary svelte-hwtbr4"><!> <!></summary> <div class="c-detail__content svelte-hwtbr4"><!></div></details>'),v4=q('<div><div class="c-detail__summary svelte-hwtbr4"><!></div> <div class="c-detail__content svelte-hwtbr4"><!></div></div>');function s0(t,e){const n=a2(e,["children","$$slots","$$events","$$legacy"]),r=a2(n,["open","duration","expandable","onChangeOpen","class"]);s1(e,!1);let i=C(e,"open",12,!0),o=C(e,"duration",8,300),s=C(e,"expandable",8,!0),l=C(e,"onChangeOpen",24,()=>{}),g=C(e,"class",8,"");const u=typeof o()=="number"?{duration:o()}:o();let d=n1(i());const y=A=>()=>{var p;E(d,A!=="close"),(p=l())==null||p(A==="open")},x=A=>()=>{E(d,A==="open")};X1(()=>u1(i()),()=>{E(d,i())}),L3(),l3();var L=q2(),m=s3(L),h=A=>{var p=C4(),O=K(()=>x("close")),c=K(()=>x("open")),b=K(()=>y("close")),$=K(()=>y("open"));o2(p,R=>({...r,style:`--au-detail-duration: ${U(()=>u.duration)??""}ms`,class:`c-detail ${g()??""}`,[O0]:R}),[()=>({"c-detail__rotate":a(d)})],"svelte-hwtbr4");var k=f(p),v=f(k);t4(v,{icon:"chevron-down",class:"c-detail__chevron"});var I=_(v,2);a3(I,e,"summary",{},null);var D=_(k,2),a1=f(D);a3(a1,e,"default",{},null),o3(()=>function(R,N,H,J,Y){var w=()=>{J(H[R])};H.addEventListener(N,w),Y?j0(()=>{H[R]=Y()}):w(),H!==document.body&&H!==window&&H!==document||M0(()=>{H.removeEventListener(N,w)})}("open","toggle",p,i,i)),t3(p,(R,N)=>P3==null?void 0:P3(R,N),()=>u),o3(()=>P1("close-start",p,function(...R){var N;(N=a(O))==null||N.apply(this,R)})),o3(()=>P1("open-start",p,function(...R){var N;(N=a(c))==null||N.apply(this,R)})),o3(()=>P1("close-end",p,function(...R){var N;(N=a(b))==null||N.apply(this,R)})),o3(()=>P1("open-end",p,function(...R){var N;(N=a($))==null||N.apply(this,R)})),S(A,p)},M=A=>{var p=v4();o2(p,()=>({...r,class:`c-detail ${g()??""}`}),void 0,"svelte-hwtbr4");var O=f(p),c=f(O);a3(c,e,"summary",{},null);var b=_(O,2),$=f(b);a3($,e,"default",{},null),S(A,p)};T1(m,A=>{s()?A(h):A(M,!1)}),S(t,L),l1()}const A1=Symbol.for("@ts-pattern/matcher"),p4=Symbol.for("@ts-pattern/isVariadic"),w3="@ts-pattern/anonymous-select-key",I3=t=>!!(t&&typeof t=="object"),p3=t=>t&&!!t[A1],L1=(t,e,n)=>{if(p3(t)){const r=t[A1](),{matched:i,selections:o}=r.match(e);return i&&o&&Object.keys(o).forEach(s=>n(s,o[s])),i}if(I3(t)){if(!I3(e))return!1;if(Array.isArray(t)){if(!Array.isArray(e))return!1;let r=[],i=[],o=[];for(const s of t.keys()){const l=t[s];p3(l)&&l[p4]?o.push(l):o.length?i.push(l):r.push(l)}if(o.length){if(o.length>1)throw new Error("Pattern error: Using `...P.array(...)` several times in a single pattern is not allowed.");if(e.length<r.length+i.length)return!1;const s=e.slice(0,r.length),l=i.length===0?[]:e.slice(-i.length),g=e.slice(r.length,i.length===0?1/0:-i.length);return r.every((u,d)=>L1(u,s[d],n))&&i.every((u,d)=>L1(u,l[d],n))&&(o.length===0||L1(o[0],g,n))}return t.length===e.length&&t.every((s,l)=>L1(s,e[l],n))}return Reflect.ownKeys(t).every(r=>{const i=t[r];return(r in e||p3(o=i)&&o[A1]().matcherType==="optional")&&L1(i,e[r],n);var o})}return Object.is(e,t)},z1=t=>{var e,n,r;return I3(t)?p3(t)?(e=(n=(r=t[A1]()).getSelectionKeys)==null?void 0:n.call(r))!=null?e:[]:Array.isArray(t)?d3(t,z1):d3(Object.values(t),z1):[]},d3=(t,e)=>t.reduce((n,r)=>n.concat(e(r)),[]);function d1(t){return Object.assign(t,{optional:()=>f4(t),and:e=>V(t,e),or:e=>m4(t,e),select:e=>e===void 0?h2(t):h2(e,t)})}function f4(t){return d1({[A1]:()=>({match:e=>{let n={};const r=(i,o)=>{n[i]=o};return e===void 0?(z1(t).forEach(i=>r(i,void 0)),{matched:!0,selections:n}):{matched:L1(t,e,r),selections:n}},getSelectionKeys:()=>z1(t),matcherType:"optional"})})}function V(...t){return d1({[A1]:()=>({match:e=>{let n={};const r=(i,o)=>{n[i]=o};return{matched:t.every(i=>L1(i,e,r)),selections:n}},getSelectionKeys:()=>d3(t,z1),matcherType:"and"})})}function m4(...t){return d1({[A1]:()=>({match:e=>{let n={};const r=(i,o)=>{n[i]=o};return d3(t,z1).forEach(i=>r(i,void 0)),{matched:t.some(i=>L1(i,e,r)),selections:n}},getSelectionKeys:()=>d3(t,z1),matcherType:"or"})})}function B(t){return{[A1]:()=>({match:e=>({matched:!!t(e)})})}}function h2(...t){const e=typeof t[0]=="string"?t[0]:void 0,n=t.length===2?t[1]:typeof t[0]=="string"?void 0:t[0];return d1({[A1]:()=>({match:r=>{let i={[e??w3]:r};return{matched:n===void 0||L1(n,r,(o,s)=>{i[o]=s}),selections:i}},getSelectionKeys:()=>[e??w3].concat(n===void 0?[]:z1(n))})})}function m1(t){return typeof t=="number"}function F1(t){return typeof t=="string"}function R1(t){return typeof t=="bigint"}d1(B(function(t){return!0}));const O1=t=>Object.assign(d1(t),{startsWith:e=>{return O1(V(t,(n=e,B(r=>F1(r)&&r.startsWith(n)))));var n},endsWith:e=>{return O1(V(t,(n=e,B(r=>F1(r)&&r.endsWith(n)))));var n},minLength:e=>O1(V(t,(n=>B(r=>F1(r)&&r.length>=n))(e))),length:e=>O1(V(t,(n=>B(r=>F1(r)&&r.length===n))(e))),maxLength:e=>O1(V(t,(n=>B(r=>F1(r)&&r.length<=n))(e))),includes:e=>{return O1(V(t,(n=e,B(r=>F1(r)&&r.includes(n)))));var n},regex:e=>{return O1(V(t,(n=e,B(r=>F1(r)&&!!r.match(n)))));var n}});O1(B(F1));const w1=t=>Object.assign(d1(t),{between:(e,n)=>w1(V(t,((r,i)=>B(o=>m1(o)&&r<=o&&i>=o))(e,n))),lt:e=>w1(V(t,(n=>B(r=>m1(r)&&r<n))(e))),gt:e=>w1(V(t,(n=>B(r=>m1(r)&&r>n))(e))),lte:e=>w1(V(t,(n=>B(r=>m1(r)&&r<=n))(e))),gte:e=>w1(V(t,(n=>B(r=>m1(r)&&r>=n))(e))),int:()=>w1(V(t,B(e=>m1(e)&&Number.isInteger(e)))),finite:()=>w1(V(t,B(e=>m1(e)&&Number.isFinite(e)))),positive:()=>w1(V(t,B(e=>m1(e)&&e>0))),negative:()=>w1(V(t,B(e=>m1(e)&&e<0)))});w1(B(m1));const $1=t=>Object.assign(d1(t),{between:(e,n)=>$1(V(t,((r,i)=>B(o=>R1(o)&&r<=o&&i>=o))(e,n))),lt:e=>$1(V(t,(n=>B(r=>R1(r)&&r<n))(e))),gt:e=>$1(V(t,(n=>B(r=>R1(r)&&r>n))(e))),lte:e=>$1(V(t,(n=>B(r=>R1(r)&&r<=n))(e))),gte:e=>$1(V(t,(n=>B(r=>R1(r)&&r>=n))(e))),positive:()=>$1(V(t,B(e=>R1(e)&&e>0))),negative:()=>$1(V(t,B(e=>R1(e)&&e<0)))});$1(B(R1)),d1(B(function(t){return typeof t=="boolean"})),d1(B(function(t){return typeof t=="symbol"})),d1(B(function(t){return t==null})),d1(B(function(t){return t!=null}));class w4 extends Error{constructor(e){let n;try{n=JSON.stringify(e)}catch{n=e}super(`Pattern matching error: no pattern matches value ${n}`),this.input=void 0,this.input=e}}const K3={matched:!1,value:void 0};class y3{constructor(e,n){this.input=void 0,this.state=void 0,this.input=e,this.state=n}with(...e){if(this.state.matched)return this;const n=e[e.length-1],r=[e[0]];let i;e.length===3&&typeof e[1]=="function"?i=e[1]:e.length>2&&r.push(...e.slice(1,e.length-1));let o=!1,s={};const l=(u,d)=>{o=!0,s[u]=d},g=!r.some(u=>L1(u,this.input,l))||i&&!i(this.input)?K3:{matched:!0,value:n(o?w3 in s?s[w3]:s:this.input,this.input)};return new y3(this.input,g)}when(e,n){if(this.state.matched)return this;const r=!!e(this.input);return new y3(this.input,r?{matched:!0,value:n(this.input,this.input)}:K3)}otherwise(e){return this.state.matched?this.state.value:e(this.input)}exhaustive(){if(this.state.matched)return this.state.value;throw new w4(this.input)}run(){return this.exhaustive()}returnType(){return this}}var y4=o1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_addition_light</title><g id="nextedit_addition_light" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16" rx="2"></rect></g><g id="Group" transform="translate(9, 1)"><rect id="Rectangle" x="2.25" y="0" width="1.5" height="6" rx="0.75"></rect><rect id="Rectangle-Copy" transform="translate(3, 3) rotate(90) translate(-3, -3)" x="2.25" y="1.13686838e-13" width="1.5" height="6" rx="0.75"></rect></g></g></svg>');function L4(t,e){let n=C(e,"mask",3,!1),r=C(e,"maskColor",3,"white");var i=y4(),o=_(f(i)),s=f(o),l=_(s);G(()=>{F(s,"fill",n()?r():"#0A84FF"),F(l,"fill",n()?r():"#34C759")}),S(t,i)}var x4=o1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_addition_dark</title><g id="nextedit_addition_dark" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16" rx="2"></rect></g><g id="Group" transform="translate(9, 1)"><rect id="Rectangle" x="2.25" y="0" width="1.5" height="6" rx="0.75"></rect><rect id="Rectangle-Copy" transform="translate(3, 3) rotate(90) translate(-3, -3)" x="2.25" y="1.13686838e-13" width="1.5" height="6" rx="0.75"></rect></g></g></svg>');function _4(t,e){let n=C(e,"mask",3,!1),r=C(e,"maskColor",3,"white");var i=x4(),o=_(f(i)),s=f(o),l=_(s);G(()=>{F(s,"fill",n()?r():"#168AFF"),F(l,"fill",n()?r():"#30D158")}),S(t,i)}var b4=o1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_deletion_light</title><g id="nextedit_deletion_light" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16" rx="2"></rect></g><g id="Group" transform="translate(9, 3.25)"><rect id="Rectangle-Copy" transform="translate(3, 0.75) rotate(90) translate(-3, -0.75)" x="2.25" y="-2.25" width="1.5" height="6" rx="0.75"></rect></g></g></svg>');function k4(t,e){let n=C(e,"mask",3,!1),r=C(e,"maskColor",3,"white");var i=b4(),o=_(f(i)),s=f(o),l=_(s);G(()=>{F(s,"fill",n()?r():"#0A84FF"),F(l,"fill",n()?r():"#FF5D4E")}),S(t,i)}var S4=o1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_deletion_dark</title><g id="nextedit_deletion_dark" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16" rx="2"></rect></g><g id="Group" transform="translate(9, 3.25)"><rect id="Rectangle-Copy" transform="translate(3, 0.75) rotate(90) translate(-3, -0.75)" x="2.25" y="-2.25" width="1.5" height="6" rx="0.75"></rect></g></g></svg>');function A4(t,e){let n=C(e,"mask",3,!1),r=C(e,"maskColor",3,"white");var i=S4(),o=_(f(i)),s=f(o),l=_(s);G(()=>{F(s,"fill",n()?r():"#168AFF"),F(l,"fill",n()?r():"#FF7E72")}),S(t,i)}var j4=o1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_change_light</title><g id="nextedit_change_light" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16" rx="2"></rect></g><g id="Group-2" transform="translate(10.5, 1.5)"><g id="Group" transform="translate(0, 1.5)"><rect id="Rectangle-Copy" transform="translate(2, 0.5) rotate(90) translate(-2, -0.5)" x="1.5" y="-1.5" width="1" height="4" rx="0.5"></rect><rect id="Rectangle-Copy" transform="translate(2, 4.5) rotate(90) translate(-2, -4.5)" x="1.5" y="2.5" width="1" height="4" rx="0.5"></rect></g><g id="Group" transform="translate(2, 2) rotate(90) translate(-2, -2)translate(0, 1.5)"><path d="M2,-1.5 C2.27614237,-1.5 2.5,-1.27614237 2.5,-1 L2.5,2 C2.5,2.27614237 2.27614237,2.5 2,2.5 C1.72385763,2.5 1.5,2.27614237 1.5,2 L1.5,-1 C1.5,-1.27614237 1.72385763,-1.5 2,-1.5 Z" id="Rectangle-Copy" transform="translate(2, 0.5) rotate(90) translate(-2, -0.5)"></path></g></g></g></svg>');function C2(t,e){let n=C(e,"mask",3,!1),r=C(e,"maskColor",3,"white");var i=j4(),o=_(f(i)),s=f(o),l=_(s);G(()=>{F(s,"fill",n()?r():"#0A84FF"),F(l,"fill",n()?r():"#F4A414")}),S(t,i)}var M4=o1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_change_dark</title><g id="nextedit_change_dark" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16" rx="2"></rect></g><g id="Group-2" transform="translate(10.5, 1.5)"><g id="Group" transform="translate(0, 1.5)"><rect id="Rectangle-Copy" transform="translate(2, 0.5) rotate(90) translate(-2, -0.5)" x="1.5" y="-1.5" width="1" height="4" rx="0.5"></rect><rect id="Rectangle-Copy" transform="translate(2, 4.5) rotate(90) translate(-2, -4.5)" x="1.5" y="2.5" width="1" height="4" rx="0.5"></rect></g><g id="Group" transform="translate(2, 2) rotate(90) translate(-2, -2)translate(0, 1.5)"><path d="M2,-1.5 C2.27614237,-1.5 2.5,-1.27614237 2.5,-1 L2.5,2 C2.5,2.27614237 2.27614237,2.5 2,2.5 C1.72385763,2.5 1.5,2.27614237 1.5,2 L1.5,-1 C1.5,-1.27614237 1.72385763,-1.5 2,-1.5 Z" id="Rectangle-Copy" transform="translate(2, 0.5) rotate(90) translate(-2, -0.5)"></path></g></g></g></svg>');function v2(t,e){let n=C(e,"mask",3,!1),r=C(e,"maskColor",3,"white");var i=M4(),o=_(f(i)),s=f(o),l=_(s);G(()=>{F(s,"fill",n()?r():"#168AFF"),F(l,"fill",n()?r():"#FFC255")}),S(t,i)}var E4=o1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_applied_light</title><g id="nextedit_applied_light" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="􀆅" transform="translate(8.5216, 0.8311)" fill-rule="nonzero"><path d="M2.68994141,6.32666016 C3.01578776,6.32666016 3.26074219,6.2047526 3.42480469,5.9609375 L6.55908203,1.30566406 C6.61832682,1.21907552 6.66162109,1.13191732 6.68896484,1.04418945 C6.71630859,0.956461589 6.72998047,0.872721354 6.72998047,0.79296875 C6.72998047,0.567382812 6.65079753,0.37882487 6.49243164,0.227294922 C6.33406576,0.075764974 6.13867188,0 5.90625,0 C5.74902344,0 5.61572266,0.0313313802 5.50634766,0.0939941406 C5.39697266,0.156656901 5.29329427,0.263183594 5.1953125,0.413574219 L2.67626953,4.34765625 L1.42871094,2.91210938 C1.26692708,2.72753906 1.06184896,2.63525391 0.813476562,2.63525391 C0.578776042,2.63525391 0.384521484,2.71101888 0.230712891,2.86254883 C0.0769042969,3.01407878 0,3.20377604 0,3.43164063 C0,3.53417969 0.0165201823,3.62988281 0.0495605469,3.71875 C0.0826009115,3.80761719 0.143554688,3.90218099 0.232421875,4.00244141 L1.98925781,6.01904297 C2.16927083,6.22412109 2.40283203,6.32666016 2.68994141,6.32666016 Z" id="Path"></path></g><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16"></rect></g></g></svg>');function F4(t,e){let n=C(e,"mask",3,!1),r=C(e,"maskColor",3,"white");var i=E4(),o=_(f(i)),s=f(o),l=_(s);let g;G(u=>{F(s,"fill",n()?r():"#34C759"),F(l,"fill",n()?r():"#000000"),g=p1(l,"",g,u)},[()=>({opacity:n()?"1":"0.2"})]),S(t,i)}var R4=o1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_applied_dark</title><g id="nextedit_applied_dark" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="􀆅" transform="translate(8.5167, 0.8311)" fill-rule="nonzero"><path d="M2.68994141,6.32666016 C3.01578776,6.32666016 3.26074219,6.2047526 3.42480469,5.9609375 L6.55908203,1.30566406 C6.61832682,1.21907552 6.66162109,1.13191732 6.68896484,1.04418945 C6.71630859,0.956461589 6.72998047,0.872721354 6.72998047,0.79296875 C6.72998047,0.567382812 6.65079753,0.37882487 6.49243164,0.227294922 C6.33406576,0.075764974 6.13867188,0 5.90625,0 C5.74902344,0 5.61572266,0.0313313802 5.50634766,0.0939941406 C5.39697266,0.156656901 5.29329427,0.263183594 5.1953125,0.413574219 L2.67626953,4.34765625 L1.42871094,2.91210938 C1.26692708,2.72753906 1.06184896,2.63525391 0.813476562,2.63525391 C0.578776042,2.63525391 0.384521484,2.71101888 0.230712891,2.86254883 C0.0769042969,3.01407878 0,3.20377604 0,3.43164063 C0,3.53417969 0.0165201823,3.62988281 0.0495605469,3.71875 C0.0826009115,3.80761719 0.143554688,3.90218099 0.232421875,4.00244141 L1.98925781,6.01904297 C2.16927083,6.22412109 2.40283203,6.32666016 2.68994141,6.32666016 Z" id="Path"></path></g><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16"></rect></g></g></svg>');function O4(t,e){let n=C(e,"mask",3,!1),r=C(e,"maskColor",3,"white");var i=R4(),o=_(f(i)),s=f(o),l=_(s);let g;G(u=>{F(s,"fill",n()?r():"#30D158"),F(l,"fill",n()?r():"#FFFFFF"),g=p1(l,"",g,u)},[()=>({opacity:n()?"1":"0.4"})]),S(t,i)}var $4=o1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_rejected_light</title><g id="nextedit_rejected_light" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16" rx="2"></rect></g><g id="Group" transform="translate(12, 4) rotate(45) translate(-12, -4)translate(9, 1)"><rect id="Rectangle" x="2.25" y="0" width="1.5" height="6" rx="0.75"></rect><rect id="Rectangle-Copy" transform="translate(3, 3) rotate(90) translate(-3, -3)" x="2.25" y="1.13686838e-13" width="1.5" height="6" rx="0.75"></rect></g></g></svg>');function P4(t,e){let n=C(e,"mask",3,!0),r=C(e,"maskColor",3,"white");var i=$4(),o=_(f(i)),s=f(o),l=_(s);G(()=>{F(s,"fill",n()?r():"#0A84FF"),F(l,"fill",n()?r():"#FF5D4E")}),S(t,i)}var z4=o1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_rejected_dark</title><g id="nextedit_rejected_dark" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16" rx="2"></rect></g><g id="Group" transform="translate(12, 4) rotate(45) translate(-12, -4)translate(9, 1)"><rect id="Rectangle" x="2.25" y="0" width="1.5" height="6" rx="0.75"></rect><rect id="Rectangle-Copy" transform="translate(3, 3) rotate(90) translate(-3, -3)" x="2.25" y="1.13686838e-13" width="1.5" height="6" rx="0.75"></rect></g></g></svg>');function B4(t,e){let n=C(e,"mask",3,!0),r=C(e,"maskColor",3,"white");var i=z4(),o=_(f(i)),s=f(o),l=_(s);G(()=>{F(s,"fill",n()?r():"#168AFF"),F(l,"fill",n()?r():"#FF7E72")}),S(t,i)}var N4=o1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>Option 2_light</title><g id="Option-2_light" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><path d="M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z" id="Path"></path><g id="Pencil" transform="translate(0.172, 2.224)"><path d="M4.81096943,2.24362978 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03645345,8.60325107 L2.24362978,4.81096943 L4.81096943,2.24362978 Z M2.40551485,0.605057322 C2.66486762,0.603004601 2.92459099,0.65617863 3.16550077,0.764612139 L0.782130647,3.14739928 C0.671050497,2.89397491 0.616248167,2.62929203 0.618310403,2.36812709 C0.621891891,1.91456136 0.798131359,1.47507103 1.13660119,1.13660119 C1.48731739,0.785884996 1.94585368,0.608695442 2.40551485,0.605057322 Z" id="Combined-Shape" stroke-width="1.21"></path><path d="M10.506,8.164 L11.3762654,10.1836291 C11.5746952,10.6438741 11.3624521,11.1778356 10.9022072,11.3762654 C10.6728839,11.4751357 10.4129523,11.4751357 10.1836291,11.3762654 L8.164,10.506 L10.506,8.164 Z M4.13119841,0.708801589 L9.03108125,5.60868442 C9.11487834,5.69248152 9.19545077,5.77920876 9.27266509,5.86866949 L5.86866949,9.27266509 C5.77920876,9.19545077 5.69248152,9.11487834 5.60868442,9.03108125 L0.708801589,4.13119841 C-0.236267196,3.18612962 -0.236267196,1.65387038 0.708801589,0.708801589 C1.65387038,-0.236267196 3.18612962,-0.236267196 4.13119841,0.708801589 Z" id="Combined-Shape"></path></g></g></svg>');function Z4(t,e){let n=C(e,"mask",3,!1),r=C(e,"maskColor",3,"white");var i=N4(),o=_(f(i)),s=f(o),l=_(s),g=f(l),u=_(g);G(()=>{F(s,"fill",n()?r():"#007AFF"),F(g,"stroke",n()?r():"#007AFF"),F(u,"fill",n()?r():"#007AFF")}),S(t,i)}var D4=o1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>Option 2_dark</title><g id="Option-2_dark" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><path d="M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z" id="Path"></path><g id="Pencil" transform="translate(0.172, 2.224)"><path d="M4.81096943,2.24362978 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03645345,8.60325107 L2.24362978,4.81096943 L4.81096943,2.24362978 Z M2.40551485,0.605057322 C2.66486762,0.603004601 2.92459099,0.65617863 3.16550077,0.764612139 L0.782130647,3.14739928 C0.671050497,2.89397491 0.616248167,2.62929203 0.618310403,2.36812709 C0.621891891,1.91456136 0.798131359,1.47507103 1.13660119,1.13660119 C1.48731739,0.785884996 1.94585368,0.608695442 2.40551485,0.605057322 Z" id="Combined-Shape" stroke-width="1.21"></path><path d="M10.506,8.164 L11.3762654,10.1836291 C11.5746952,10.6438741 11.3624521,11.1778356 10.9022072,11.3762654 C10.6728839,11.4751357 10.4129523,11.4751357 10.1836291,11.3762654 L8.164,10.506 L10.506,8.164 Z M4.13119841,0.708801589 L9.03108125,5.60868442 C9.11487834,5.69248152 9.19545077,5.77920876 9.27266509,5.86866949 L5.86866949,9.27266509 C5.77920876,9.19545077 5.69248152,9.11487834 5.60868442,9.03108125 L0.708801589,4.13119841 C-0.236267196,3.18612962 -0.236267196,1.65387038 0.708801589,0.708801589 C1.65387038,-0.236267196 3.18612962,-0.236267196 4.13119841,0.708801589 Z" id="Combined-Shape"></path></g></g></svg>');function I4(t,e){let n=C(e,"mask",3,!1),r=C(e,"maskColor",3,"white");var i=D4(),o=_(f(i)),s=f(o),l=_(s),g=f(l),u=_(g);G(()=>{F(s,"fill",n()?r():"#BF5AF2"),F(g,"stroke",n()?r():"#389BFF"),F(u,"fill",n()?r():"#389BFF")}),S(t,i)}var K4=o1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>Option 2_Inactive_light</title><g id="Option-2_Inactive_light" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><path d="M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z" id="Path"></path><g id="Pencil" transform="translate(0.172, 2.224)" stroke-width="1.21"><path d="M2.42,0.605 C2.88449901,0.605 3.34899801,0.782200397 3.70339881,1.13660119 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03648403,8.60328164 L1.13660119,3.70339881 C0.782200397,3.34899801 0.605,2.88449901 0.605,2.42 C0.605,1.95550099 0.782200397,1.49100199 1.13660119,1.13660119 C1.49100199,0.782200397 1.95550099,0.605 2.42,0.605 Z" id="Combined-Shape"></path></g></g></svg>');function T4(t,e){let n=C(e,"mask",3,!1),r=C(e,"maskColor",3,"white");var i=K4(),o=_(f(i)),s=f(o),l=_(s);G(()=>{F(s,"fill",n()?r():"#BF5AF2"),F(l,"stroke",n()?r():"#007AFF")}),S(t,i)}var W4=o1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>Option 2_inactive_dark</title><g id="Option-2_inactive_dark" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><path d="M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z" id="Path"></path><g id="Pencil" transform="translate(0.172, 2.224)" stroke-width="1.21"><path d="M2.42,0.605 C2.88449901,0.605 3.34899801,0.782200397 3.70339881,1.13660119 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03648403,8.60328164 L1.13660119,3.70339881 C0.782200397,3.34899801 0.605,2.88449901 0.605,2.42 C0.605,1.95550099 0.782200397,1.49100199 1.13660119,1.13660119 C1.49100199,0.782200397 1.95550099,0.605 2.42,0.605 Z" id="Combined-Shape"></path></g></g></svg>');function U4(t,e){let n=C(e,"mask",3,!1),r=C(e,"maskColor",3,"white");var i=W4(),o=_(f(i)),s=f(o),l=_(s);G(()=>{F(s,"fill",n()?r():"#BF5AF2"),F(l,"stroke",n()?r():"#389BFF")}),S(t,i)}var V4=q('<span class="c-pencil-icon svelte-6fsamc"><!> <!></span>');function l0(t,e){s1(e,!0);const[n,r]=S1(),i=()=>f1(H2,"$themeStore",n),o={insertion:{light:L4,dark:_4},deletion:{light:k4,dark:A4},modification:{light:C2,dark:v2},noop:{light:C2,dark:v2},active:{light:Z4,dark:I4},inactive:{light:T4,dark:U4},accepted:{light:F4,dark:O4},rejected:{light:P4,dark:B4}};let s=C(e,"mask",3,!1),l=C(e,"maskColor",3,"currentColor"),g=C(e,"themeCategory",15);Y1(()=>{var h;(h=i())!=null&&h.category&&g(g()??i().category)});let u=K(()=>function(h){return new y3(h,K3)}(e.suggestion).with({state:e3.stale},{state:e3.accepted},()=>"accepted").otherwise(({changeType:h})=>h)),d=K(()=>o[a(u)]??o.active),y=K(()=>a(d)[g()??P0.light]);var x=V4(),L=f(x);e4(L,()=>a(y),(h,M)=>{M(h,{get mask(){return s()},get maskColor(){return l()}})});var m=_(L,2);I0(m,()=>e.children??$0),S(t,x),l1(),r()}function G4(t,e,n,r){x1(e.suggestion,n().activeSuggestion)?e.onCodeAction("dismiss"):e.onCodeAction("active",e.suggestion)}function q4(t,e){e.onCodeAction("select",e.suggestion)}var H4=q('<button><!> <span class="c-suggestion-tree-item-button__description svelte-hekzdv"><span class="c-suggestion-tree-item__description__linenumber"> </span> <span class="c-suggestion-tree-item__description__path svelte-hekzdv"> </span></span></button>');J3(["click","dblclick","keydown"]);var J4=q('<li class="c-suggestion-tree-item__suggestion svelte-3abz9e"><!> <!></li>');function Q4(t,e){s1(e,!0);const[n,r]=S1(),i=()=>f1(s,"$ctx",n);let o=y1(!1),s=n3(),l=C(e,"shouldScrollIntoView",3,!1),g=C(e,"currentSelectionState",31,()=>m3(K1(e.suggestion,i(),!0)));Y1(()=>{g(K1(e.suggestion,i(),!0))});let u=K(()=>e.suggestion.state===e3.accepted?W1("reject","undo"):W1("reject","accept"));var d=J4(),y=f(d);(function(m,h){s1(h,!0);const[M,A]=S1(),p=()=>f1(O,"$ctx",M),O=n3();let c=y1(m3(K1(h.suggestion,p(),!0)));Y1(()=>{E(c,K1(h.suggestion,p(),!0),!0)});var b=H4();b.__click=[q4,h],b.__dblclick=[G4,h,p,O];var $=K(()=>x3("Space",()=>h.onCodeAction("select",h.suggestion)));b.__keydown=function(...H){var J;(J=a($))==null||J.apply(this,H)},F(b,"tabindex",0);var k=f(b);const v=K(()=>a(c)!=="none");l0(k,{get mask(){return a(v)},get suggestion(){return h.suggestion}});var I=_(k,2),D=f(I),a1=f(D),R=_(D,2),N=f(R);G((H,J)=>{F(b,"title",`${H??""}:${J??""}`),U1(b,1,`c-suggestion-tree-item-button ${a(c)??""}`,"svelte-hekzdv"),Z3(a1,`${h.suggestion.lineRange.start+1}:`),Z3(N,h.suggestion.result.changeDescription)},[()=>W0(h.suggestion,!0),()=>U0(h.suggestion)]),S(m,b),l1(),A()})(y,{get suggestion(){return e.suggestion},get onCodeAction(){return e.onCodeAction}});var x=_(y,2),L=m=>{V0(m,{get onCodeAction(){return e.onCodeAction},get codeActions(){return a(u)},get value(){return e.suggestion}})};T1(x,m=>{a(o)&&m(L)}),t3(d,(m,h)=>k1==null?void 0:k1(m,h),()=>({scrollContainer:e.scrollContainer,doScroll:l(),scrollIntoView:{behavior:"smooth",block:"nearest"}})),G(m=>p1(d,m),[()=>a0(g())]),P1("mouseenter",d,()=>E(o,!0)),P1("mouseleave",d,()=>E(o,!1)),n0(3,d,()=>X2),S(t,d),l1(),r()}var X4=q('<ul class="c-suggestion-tree-item__inner svelte-bnynfs"></ul>'),Y4=q('<li class="svelte-bnynfs"><!></li>'),t5=q("<ul></ul>"),e5=q("<!> <!> <div><!></div>",1);function n5(t,e){s1(e,!1);const[n,r]=S1(),i=()=>f1(u,"$ctx",n);let o=C(e,"onCodeAction",8),s=C(e,"sortedPathSuggestionsMap",24,()=>new Map),l=C(e,"minimized",8,!1),g=n1();const u=n3();function d(c,b=!0){c!=null&&c.qualifiedPathName.relPath&&(D1(u,U(i).nextSuggestion=c,U(i)),D1(u,U(i).isOpen={...i().isOpen,[c.qualifiedPathName.relPath]:b},U(i)))}function y(c,b){c.preventDefault(),c.stopPropagation();const $=I1(i()),k=G0(s(),$),v=t0(s(),k+(b?-1:1));v&&v!==$&&(d(v),v3(i())==="active"?o()("active",v):o()("select",v))}X1(()=>(i(),a(g)),()=>{i().nextSuggestion&&v3(i())==="next"&&!x1(i().nextSuggestion,a(g))&&(E(g,i().nextSuggestion),d(i().nextSuggestion))}),L3(),l3();var x=e5();P1("keydown",z0.body,function(c){switch(c.code){case"KeyZ":if(!c.metaKey&&!c.ctrlKey||(c.preventDefault(),c.stopPropagation(),!i().nextSuggestion))return;d(i().nextSuggestion),o()("undo",i().nextSuggestion);break;case"KeyK":case"ArrowUp":if(c.metaKey||c.altKey||c.ctrlKey||c.shiftKey)return;y(c,!0);break;case"KeyJ":case"ArrowDown":if(c.metaKey||c.altKey||c.ctrlKey||c.shiftKey)return;y(c);break;case"ArrowRight":case"Space":{if(c.metaKey||c.altKey||c.ctrlKey||c.shiftKey)return;c.preventDefault(),c.stopPropagation();const b=I1(i());d(b),o()(v3(i())==="select"?"active":"select",b);break}case"ArrowLeft":case"Escape":if(c.metaKey||c.altKey||c.ctrlKey||c.shiftKey)return;c.preventDefault(),c.stopPropagation(),o()("dismiss");break;case"Enter":if(c.metaKey||c.altKey||c.ctrlKey||c.shiftKey||!i().nextSuggestion)return;c.preventDefault(),c.stopPropagation(),o()("accept",i().nextSuggestion);break;case"Backspace":if(c.metaKey||c.altKey||c.ctrlKey||c.shiftKey||!i().nextSuggestion)return;c.preventDefault(),c.stopPropagation(),o()("reject",i().nextSuggestion)}});var L=s3(x);const m=v1(()=>(u1(l()),u1(s()),U(()=>l()&&!!s().size)));h4(L,{get sortedPathSuggestionsMap(){return s()},get onCodeAction(){return o()},get show(){return a(m)}});var h=_(L,2);const M=v1(()=>(u1(l()),u1(s()),U(()=>!l()&&!!s().size)));(function(c,b){s1(b,!1);const[$,k]=S1(),v=()=>f1(N,"$ctx",$);let I=C(b,"onCodeAction",8),D=C(b,"sortedPathSuggestionsMap",24,()=>new Map),a1=C(b,"show",8,!0),R=n1();const N=n3();l3();var H=t5();let J;u3(H,5,D,([Y,w])=>Y,(Y,w)=>{var P=K(()=>Q3(a(w),2));let z=()=>a(P)[0],Z=()=>a(P)[1];var T=Y4(),X=f(T);const c1=v1(()=>(u1(l2),Z(),U(()=>l2(Z().length)))),j1=v1(()=>(v(),z(),U(()=>{var j;return((j=v().isOpen)==null?void 0:j[z()])??!0})));s0(X,{get duration(){return a(c1)},class:"c-suggestion-tree-item",get open(){return a(j1)},onChangeOpen:j=>D1(N,U(v).isOpen={...v().isOpen,[z()]:j},U(v)),children:(j,r1)=>{var t1=X4();u3(t1,5,Z,i1=>i1.result.suggestionId,(i1,_1)=>{const e1=v1(()=>(u1(x1),a(_1),u1(I1),v(),U(()=>x1(a(_1),I1(v())))));Q4(i1,{get onCodeAction(){return I()},get suggestion(){return a(_1)},get shouldScrollIntoView(){return a(e1)},get scrollContainer(){return a(R)}})}),S(j,t1)},$$slots:{default:!0,summary:(j,r1)=>{const t1=v1(()=>(Z(),u1(W1),U(()=>Z().every(i1=>i1.state==="accepted")?W1("rejectAllInFile"):W1("rejectAllInFile","acceptAllInFile"))));Y2(j,{get filepath(){return z()},slot:"summary",class:"c-suggestion-tree-item__lang-summary",get value(){return Z()},get onCodeAction(){return I()},get codeActions(){return a(t1)}})}}}),n0(3,T,()=>X2),S(Y,T)}),c3(H,Y=>E(R,Y),()=>a(R)),G(Y=>J=U1(H,1,"c-suggestion-tree__maximized svelte-bnynfs",null,J,Y),[()=>({hidden:!a1()})],v1),S(c,H),l1(),k()})(h,{get sortedPathSuggestionsMap(){return s()},get onCodeAction(){return o()},get show(){return a(M)}});var A=_(h,2);let p;var O=f(A);a3(O,e,"no-suggestions",{},null),G(c=>p=U1(A,1,"c-suggestion-tree__no-suggestions svelte-l320gs",null,p,c),[()=>({hidden:s().size})],v1),S(t,x),l1(),r()}var r5=q('<div class="c-code-roll-item-header svelte-3zqetr"><!> <!></div>');function p2(t,e,n,r){const i=t.split(""),o=e.toSorted(({lineRange:{start:s}},{lineRange:{start:l}})=>l-s);for(const s of o){const l=[s.result.charStart,n(s)],g=r(s);g&&l.push(...g.split("")),i.splice(...l)}return i.join("")}const f2=((t=0)=>()=>Date.now()+"-"+t++)(),i5=(t,e,n,r,i,o)=>{var u,d,y,x;if(!t||!n)return[];n=function(L,m){return p2(L,m,h=>h.result.suggestedCode.split("").length,h=>h.result.existingCode)}(n,e.filter(L=>L.state===e3.accepted));const s=function(L,m){return p2(L,m,h=>h.result.charEnd-h.result.charStart,h=>h.result.suggestedCode)}(n,e);(d=(u=t.getModel())==null?void 0:u.original)==null||d.dispose(),(x=(y=t.getModel())==null?void 0:y.modified)==null||x.dispose();const l=o.editor.createModel(n,r,o.Uri.parse("file://"+i+`#${f2()}`)),g=o.editor.createModel(s,r,o.Uri.parse("file://"+i+`#${f2()}`));return t.setModel({original:l,modified:g}),[l,g]};function o5(t){var e;return`${t.requestId}#${(e=t.result)==null?void 0:e.suggestionId}`}function m2(t){return t.map(o5).join(":")}function w2(t){const e=t.toSorted((i,o)=>i.start-o.start),n=[];let r=e.shift();for(;e.length;){const i=e.shift();i.start<=r.end+1?r.end=Math.max(r.end,i.end):(n.push(r),r=i)}return n.push(r),n}function y2(t){return t.reduce((e,n)=>e+=n.end-n.start+1,0)}function L2(t){return t.reduce((e,n,r)=>r===0?e:e+=n.start-t[r-1].end-1,0)}function x2(t){var r;let e,n;if(t.modifiedEndLineNumber===0)n=t.originalEndLineNumber-t.originalStartLineNumber+1,e=t.originalStartLineNumber-1;else if((r=t.charChanges)!=null&&r.length){const i=w2(t.charChanges.map(d=>({start:d.originalStartLineNumber,end:d.originalEndLineNumber}))),o=w2(t.charChanges.map(d=>({start:d.modifiedStartLineNumber,end:d.modifiedEndLineNumber}))),s=y2(i),l=y2(o),g=L2(i),u=L2(o);n=s+l+Math.max(g,u),e=t.modifiedStartLineNumber-1}else{if(t.originalEndLineNumber!==0)throw new Error("Unexpected line change");n=t.modifiedEndLineNumber-t.modifiedStartLineNumber+1,e=t.modifiedStartLineNumber-1}return{lineCount:n,afterLineNumber:e}}function a5(...t){return t.reduce((e,n)=>({...s5(e,n),charChanges:[...e.charChanges??[],...n.charChanges??[]]}))}function s5(...t){return t.reduce((e,n)=>({originalStartLineNumber:Math.min(e.originalStartLineNumber,n.originalStartLineNumber),originalEndLineNumber:Math.max(e.originalEndLineNumber,n.originalEndLineNumber),modifiedStartLineNumber:Math.min(e.modifiedStartLineNumber,n.modifiedStartLineNumber),modifiedEndLineNumber:Math.max(e.modifiedEndLineNumber,n.modifiedEndLineNumber)}))}function l5(t,e){if(e.originalStartLineNumber===t.lineRange.start+1)return!0;const n=Math.min(e.originalStartLineNumber,e.modifiedStartLineNumber),r=Math.max(e.originalEndLineNumber,e.modifiedEndLineNumber);return n>=t.lineRange.start&&n<=t.lineRange.stop||r>=t.lineRange.start&&r<=t.lineRange.stop||n<=t.lineRange.start&&r>=t.lineRange.stop}function _2(t,e){const n=new Map,r=t.toSorted(({lineRange:{start:i}},{lineRange:{start:o}})=>i-o);t:for(const i of e.toSorted(({modifiedStartLineNumber:o,originalStartLineNumber:s},{modifiedStartLineNumber:l,originalStartLineNumber:g})=>o-l||s-g))for(const o of r)if(l5(o,i)){const s=n.get(o);n.set(o,s?a5(s,i):i);continue t}return n}var c5=q('<div class="c-code-roll-suggestion-window__view-zone"></div> <div class="c-code-roll-suggestion-window svelte-1ci0na9"><div class="c-code-roll-suggestion-window__item svelte-1ci0na9"><div class="c-code-roll-suggestion-window__item-title svelte-1ci0na9"><button class="c-code-roll-suggestion-window__item-title-text svelte-1ci0na9" tabindex="0"><!> </button> <!></div> <div class="c-code-roll-suggestion-window__border svelte-1ci0na9"><div class="c-code-roll-suggestion-window__window svelte-1ci0na9"></div></div></div></div>',1);J3(["keydown","click"]);var u5=q('<div class="c-diff-view__loading"><!></div>'),d5=q('<div><div class="c-diff-view svelte-syu9kz"></div> <!></div>');function g5(t,e){s1(e,!0);const[n,r]=S1(),i=()=>f1(L,"$monaco",n),o=()=>f1(H2,"$themeStore",n);let s=C(e,"height",15,500),l=C(e,"codeActions",19,()=>[]),g=C(e,"onCodeAction",3,G1),u=C(e,"busy",15,!0),d=C(e,"expanded",3,!1),y=C(e,"scrollContainer",19,()=>{}),x=C(e,"options",19,()=>({enableSplitViewResizing:!1,automaticLayout:!0,readOnly:!0,overviewRulerLanes:0,lineHeight:20,renderLineHighlight:"none",contextmenu:!1,renderSideBySide:!1,renderIndicators:!0,renderMarginRevertIcon:!1,originalEditable:!1,diffCodeLens:!1,renderOverviewRuler:!1,ignoreTrimWhitespace:!1,maxComputationTime:3e3,scrollBeyondLastColumn:0,scrollBeyondLastLine:!1,scrollPredominantAxis:!1,scrollbar:{alwaysConsumeMouseWheel:!1,vertical:"hidden",horizontal:"hidden"},cursorSurroundingLines:0,cursorSurroundingLinesStyle:"all",hideUnchangedRegions:{enabled:!d(),revealLineCount:5,minimumLineCount:3,contextLineCount:5},lineNumbers:String,hover:{enabled:!1}}));const L=n4.getContext().monaco;let m=y1(m3(m2(e.suggestions))),h=y1(""),M=[],A=y1(void 0),p=document.createElement("div");p.classList.add("c-diff-view__editor");let O,c=y1(void 0),b=y1(m3([])),$=y1(!1);function k(w){const P=function(z,{enabled:Z=!0,revealLineCount:T=5,minimumLineCount:X=3,contextLineCount:c1=5}={enabled:!0,revealLineCount:5,minimumLineCount:3,contextLineCount:5},j1,j){const r1={lines:0,decorations:0,hasTopDecorations:!1};if(!z.length||!Z)return r1;let t1=0,i1=!1;const _1=[...z].sort((M1,V1)=>M1.lineRange.start-V1.lineRange.start);let e1=1;for(let M1=0,V1=1;M1<_1.length;M1++,V1++){const g1=_1[M1],N1=_1[V1];if(e1+=Math.min(g1.lineRange.start+1,T),e1+=j!=null&&j.get(g1)?x2(j.get(g1)).lineCount:q0(g1),g1.lineRange.start-T>1?(i1=!0,t1++):g1.lineRange.start-T==1&&e1++,N1){const g3=N1.lineRange.start-g1.lineRange.start;g3>c1+T?(t1++,e1+=c1,e1+=T):e1+=g3}else g1.lineRange.stop<j1?(t1++,e1+=Math.min(j1-g1.lineRange.stop,T)):(e1+=c1,e1+=T)}return{lines:Math.max(e1,X),decorations:t1,hasTopDecorations:i1}}(e.suggestions,x().hideUnchangedRegions,H0(e.originalCode),a(b).length>0?_2(e.suggestions,a(b)):void 0);s(d()&&w?w.getModifiedEditor().getContentHeight():P.lines*(x().lineHeight??20)+24*P.decorations),E($,P.hasTopDecorations,!0)}function v(){var w,P,z,Z,T,X;(z=(P=(w=a(c))==null?void 0:w.getModel())==null?void 0:P.original)==null||z.dispose(),(X=(T=(Z=a(c))==null?void 0:Z.getModel())==null?void 0:T.modified)==null||X.dispose(),i()&&(a(c)||(E(c,i().editor.createDiffEditor(p,a(I)),!0),M.push(a(c))),a(c).onDidDispose(()=>E(c,void 0)),M.push(...i5(a(c),e.suggestions,e.originalCode,e.language,e.path,i())),k(a(c)),O==null||O.dispose(),O=a(c).onDidUpdateDiff(()=>{var c1;u(!1),E(b,((c1=a(c))==null?void 0:c1.getLineChanges())??[],!0),k(a(c))}))}J2(()=>{a(A).appendChild(p),v()}),N0(()=>{M.forEach(w=>{var P;return(P=w==null?void 0:w.dispose)==null?void 0:P.call(w)}),p.remove()});let I=K(()=>{var w,P;return{...x(),theme:d2((w=o())==null?void 0:w.category,(P=o())==null?void 0:P.intensity)}});Y1(()=>{var w;(w=a(c))==null||w.updateOptions({hideUnchangedRegions:{enabled:!d(),revealLineCount:5,minimumLineCount:3,contextLineCount:5}})}),Y1(()=>{var z,Z,T,X;const w=o(),P=d2(w==null?void 0:w.category,w==null?void 0:w.intensity);(z=i())==null||z.editor.setTheme(P),(Z=a(c))==null||Z.getModifiedEditor().updateOptions({theme:P}),(T=a(c))==null||T.getOriginalEditor().updateOptions({theme:P}),(X=a(c))==null||X.layout()}),Y1(()=>{const w=m2(e.suggestions);a(m)===w&&e.originalCode===a(h)||(E(m,w,!0),E(h,e.originalCode,!0),v())});var D=d5();let a1;var R=f(D);let N;t3(R,(w,P)=>{var z;return(z=X0)==null?void 0:z(w,P)},()=>({onResize:()=>{var w;return(w=a(c))==null?void 0:w.layout()}})),c3(R,w=>E(A,w),()=>a(A));var H=_(R,2),J=w=>{var P=u5(),z=f(P);X3(z,{}),S(w,P)},Y=w=>{var P=q2(),z=s3(P);u3(z,17,()=>_2(e.suggestions,a(b)),Q2,(Z,T)=>{var X=K(()=>Q3(a(T),2)),c1=K(()=>x2(a(X)[1]));(function(j1,j){s1(j,!0);const[r1,t1]=S1(),i1=()=>f1(V1,"$ctx",r1);let _1=C(j,"codeActions",19,()=>[]),e1=C(j,"lineCount",3,0),M1=y1(0);const V1=n3();let g1=K(()=>K1(j.suggestion,i1(),!1)),N1=K(()=>j.diffEditor.getModifiedEditor().getOption(o4.EditorOption.lineHeight)),g3=K(()=>e1()*a(N1)),w0=K(()=>j.suggestion.changeType===s2.insertion||j.suggestion.changeType===s2.modification),y0=K(()=>a(w0)?j.afterLineNumber+e1():j.afterLineNumber),e2=K(()=>a(y0)>999?0:5);var n2=c5(),M3=s3(n2);t3(M3,(h1,b1)=>{var i2;return(i2=i4)==null?void 0:i2(h1,b1)},()=>({editor:j.diffEditor,afterLineNumber:j.afterLineNumber,heightInPx:a(N1),onDomNodeTop(h1){E(M1,h1,!0)}}));var E3=_(M3,2),F3=f(E3),R3=f(F3),h3=f(R3),L0=K(()=>x3("Enter",D3(j.onCodeAction,"active",j.suggestion)));h3.__keydown=function(...h1){var b1;(b1=a(L0))==null||b1.apply(this,h1)};var x0=K(()=>D3(j.onCodeAction,"active",j.suggestion));h3.__click=function(...h1){var b1;(b1=a(x0))==null||b1.apply(this,h1)};var r2=f(h3);const _0=K(()=>a(g1)!=="none");l0(r2,{get mask(){return a(_0)},get suggestion(){return j.suggestion}});var b0=_(r2),k0=_(h3,2);e0(k0,{compact:!0,get actions(){return _1()},get onAction(){return j.onCodeAction},get value(){return j.suggestion}});var S0=_(R3,2),A0=f(S0);t3(F3,(h1,b1)=>k1==null?void 0:k1(h1,b1),()=>({scrollContainer:j.scrollContainer,doScroll:x1(j.suggestion,I1(i1())),scrollIntoView:{behavior:"smooth",block:"center"},useSmartBlockAlignment:!0})),G(h1=>{p1(M3,`--augment-code-roll-left-alignment:${a(e2)??""}px`),p1(E3,`
    --augment-code-roll-left-alignment:${a(e2)??""}px;
    --augment-code-roll-suggestion-window-line-height:${a(N1)??""}px;
    top:${a(M1)??""}px;
  `),F(E3,"data-result-id",`${j.suggestion.result.suggestionId}:${j.suggestion.requestId}`),p1(F3,h1),p1(R3,`height:${a(N1)??""}px`),Z3(b0,` ${j.suggestion.result.changeDescription??""}`),p1(A0,`height:${a(g3)??""}px;`)},[()=>a0(K1(j.suggestion,i1(),!1))]),S(j1,n2),l1(),t1()})(Z,K0({get diffEditor(){return a(c)},get suggestion(){return a(X)[0]},get codeActions(){return l()},get onCodeAction(){return g()}},()=>a(c1),{get scrollContainer(){return y()}}))}),S(w,P)};T1(H,w=>{u()||!a(c)?w(J):w(Y,!1)}),G((w,P)=>{a1=U1(D,1,"c-diff-view__container svelte-syu9kz",null,a1,w),p1(D,`--augment-codeblock-min-height:${s()??""}px;`),N=p1(R,"",N,P)},[()=>({"has-top-decorations":a($)}),()=>({display:u()?"none":"flex"})]),S(t,D),l1(),r()}var h5=q('<div class="c-code-roll-item__loading svelte-1i59d33"><!></div>'),C5=q('<div class="c-code-roll-item-diff svelte-1i59d33"><!></div>'),v5=function(){this.__data__=[],this.size=0},c0=function(t,e){return t===e||t!=t&&e!=e},p5=c0,k3=function(t,e){for(var n=t.length;n--;)if(p5(t[n][0],e))return n;return-1},f5=k3,m5=Array.prototype.splice,w5=function(t){var e=this.__data__,n=f5(e,t);return!(n<0)&&(n==e.length-1?e.pop():m5.call(e,n,1),--this.size,!0)},y5=k3,L5=function(t){var e=this.__data__,n=y5(e,t);return n<0?void 0:e[n][1]},x5=k3,_5=function(t){return x5(this.__data__,t)>-1},b5=k3,k5=function(t,e){var n=this.__data__,r=b5(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this},S5=v5,A5=w5,j5=L5,M5=_5,E5=k5;function q1(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}q1.prototype.clear=S5,q1.prototype.delete=A5,q1.prototype.get=j5,q1.prototype.has=M5,q1.prototype.set=E5;var S3=q1,F5=S3,R5=function(){this.__data__=new F5,this.size=0},O5=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},$5=function(t){return this.__data__.get(t)},P5=function(t){return this.__data__.has(t)},z5=_3,B5=r0,b2,u0=function(t){if(!B5(t))return!1;var e=z5(t);return e=="[object Function]"||e=="[object GeneratorFunction]"||e=="[object AsyncFunction]"||e=="[object Proxy]"},z3=B1["__core-js_shared__"],k2=(b2=/[^.]+$/.exec(z3&&z3.keys&&z3.keys.IE_PROTO||""))?"Symbol(src)_1."+b2:"",N5=function(t){return!!k2&&k2 in t},Z5=Function.prototype.toString,d0=function(t){if(t!=null){try{return Z5.call(t)}catch{}try{return t+""}catch{}}return""},D5=u0,I5=N5,K5=r0,T5=d0,W5=/^\[object .+?Constructor\]$/,U5=Function.prototype,V5=Object.prototype,G5=U5.toString,q5=V5.hasOwnProperty,H5=RegExp("^"+G5.call(q5).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),J5=function(t,e){return t==null?void 0:t[e]},Q5=function(t){return!(!K5(t)||I5(t))&&(D5(t)?H5:W5).test(T5(t))},X5=J5,r3=function(t,e){var n=X5(t,e);return Q5(n)?n:void 0},Y3=r3(B1,"Map"),A3=r3(Object,"create"),S2=A3,Y5=function(){this.__data__=S2?S2(null):{},this.size=0},t9=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},e9=A3,n9=Object.prototype.hasOwnProperty,r9=function(t){var e=this.__data__;if(e9){var n=e[t];return n==="__lodash_hash_undefined__"?void 0:n}return n9.call(e,t)?e[t]:void 0},i9=A3,o9=Object.prototype.hasOwnProperty,a9=function(t){var e=this.__data__;return i9?e[t]!==void 0:o9.call(e,t)},s9=A3,l9=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=s9&&e===void 0?"__lodash_hash_undefined__":e,this},c9=Y5,u9=t9,d9=r9,g9=a9,h9=l9;function H1(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}H1.prototype.clear=c9,H1.prototype.delete=u9,H1.prototype.get=d9,H1.prototype.has=g9,H1.prototype.set=h9;var A2=H1,C9=S3,v9=Y3,p9=function(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null},j3=function(t,e){var n=t.__data__;return p9(e)?n[typeof e=="string"?"string":"hash"]:n.map},f9=j3,m9=function(t){var e=f9(this,t).delete(t);return this.size-=e?1:0,e},w9=j3,y9=function(t){return w9(this,t).get(t)},L9=j3,x9=function(t){return L9(this,t).has(t)},_9=j3,b9=function(t,e){var n=_9(this,t),r=n.size;return n.set(t,e),this.size+=n.size==r?0:1,this},k9=function(){this.size=0,this.__data__={hash:new A2,map:new(v9||C9),string:new A2}},S9=m9,A9=y9,j9=x9,M9=b9;function J1(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}J1.prototype.clear=k9,J1.prototype.delete=S9,J1.prototype.get=A9,J1.prototype.has=j9,J1.prototype.set=M9;var g0=J1,E9=S3,F9=Y3,R9=g0,O9=function(t,e){var n=this.__data__;if(n instanceof E9){var r=n.__data__;if(!F9||r.length<199)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new R9(r)}return n.set(t,e),this.size=n.size,this},$9=S3,P9=R5,z9=O5,B9=$5,N9=P5,Z9=O9;function Q1(t){var e=this.__data__=new $9(t);this.size=e.size}Q1.prototype.clear=P9,Q1.prototype.delete=z9,Q1.prototype.get=B9,Q1.prototype.has=N9,Q1.prototype.set=Z9;var D9=Q1,I9=g0,K9=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this},T9=function(t){return this.__data__.has(t)};function f3(t){var e=-1,n=t==null?0:t.length;for(this.__data__=new I9;++e<n;)this.add(t[e])}f3.prototype.add=f3.prototype.push=K9,f3.prototype.has=T9;var W9=function(t,e){for(var n=-1,r=t==null?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1},U9=function(t,e){return t.has(e)},V9=f3,G9=W9,q9=U9,h0=function(t,e,n,r,i,o){var s=1&n,l=t.length,g=e.length;if(l!=g&&!(s&&g>l))return!1;var u=o.get(t),d=o.get(e);if(u&&d)return u==e&&d==t;var y=-1,x=!0,L=2&n?new V9:void 0;for(o.set(t,e),o.set(e,t);++y<l;){var m=t[y],h=e[y];if(r)var M=s?r(h,m,y,e,t,o):r(m,h,y,t,e,o);if(M!==void 0){if(M)continue;x=!1;break}if(L){if(!G9(e,function(A,p){if(!q9(L,p)&&(m===A||i(m,A,n,r,o)))return L.push(p)})){x=!1;break}}else if(m!==h&&!i(m,h,n,r,o)){x=!1;break}}return o.delete(t),o.delete(e),x},H9=function(t){var e=-1,n=Array(t.size);return t.forEach(function(r,i){n[++e]=[i,r]}),n},J9=function(t){var e=-1,n=Array(t.size);return t.forEach(function(r){n[++e]=r}),n},j2=B1.Uint8Array,Q9=c0,X9=h0,Y9=H9,t8=J9,M2=g2?g2.prototype:void 0,B3=M2?M2.valueOf:void 0,e8=function(t,e,n,r,i,o,s){switch(n){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=e.byteLength||!o(new j2(t),new j2(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return Q9(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var l=Y9;case"[object Set]":var g=1&r;if(l||(l=t8),t.size!=e.size&&!g)return!1;var u=s.get(t);if(u)return u==e;r|=2,s.set(t,e);var d=X9(l(t),l(e),r,i,o,s);return s.delete(t),d;case"[object Symbol]":if(B3)return B3.call(t)==B3.call(e)}return!1},n8=function(t,e){for(var n=-1,r=e.length,i=t.length;++n<r;)t[i+n]=e[n];return t},t2=Array.isArray,r8=n8,i8=t2,o8=function(t,e,n){var r=e(t);return i8(t)?r:r8(r,n(t))},a8=function(t,e){for(var n=-1,r=t==null?0:t.length,i=0,o=[];++n<r;){var s=t[n];e(s,n,t)&&(o[i++]=s)}return o},s8=a8,l8=function(){return[]},c8=Object.prototype.propertyIsEnumerable,E2=Object.getOwnPropertySymbols,u8=E2?function(t){return t==null?[]:(t=Object(t),s8(E2(t),function(e){return c8.call(t,e)}))}:l8,d8=function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r},g8=_3,h8=b3,F2=function(t){return h8(t)&&g8(t)=="[object Arguments]"},C8=b3,C0=Object.prototype,v8=C0.hasOwnProperty,p8=C0.propertyIsEnumerable,f8=F2(function(){return arguments}())?F2:function(t){return C8(t)&&v8.call(t,"callee")&&!p8.call(t,"callee")},T3={exports:{}},m8=function(){return!1};(function(t,e){var n=B1,r=m8,i=e&&!e.nodeType&&e,o=i&&t&&!t.nodeType&&t,s=o&&o.exports===i?n.Buffer:void 0,l=(s?s.isBuffer:void 0)||r;t.exports=l})(T3,T3.exports);var v0=T3.exports,w8=/^(?:0|[1-9]\d*)$/,y8=function(t,e){var n=typeof t;return!!(e=e??9007199254740991)&&(n=="number"||n!="symbol"&&w8.test(t))&&t>-1&&t%1==0&&t<e},p0=function(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=9007199254740991},L8=_3,x8=p0,_8=b3,W={};W["[object Float32Array]"]=W["[object Float64Array]"]=W["[object Int8Array]"]=W["[object Int16Array]"]=W["[object Int32Array]"]=W["[object Uint8Array]"]=W["[object Uint8ClampedArray]"]=W["[object Uint16Array]"]=W["[object Uint32Array]"]=!0,W["[object Arguments]"]=W["[object Array]"]=W["[object ArrayBuffer]"]=W["[object Boolean]"]=W["[object DataView]"]=W["[object Date]"]=W["[object Error]"]=W["[object Function]"]=W["[object Map]"]=W["[object Number]"]=W["[object Object]"]=W["[object RegExp]"]=W["[object Set]"]=W["[object String]"]=W["[object WeakMap]"]=!1;var b8=function(t){return _8(t)&&x8(t.length)&&!!W[L8(t)]},k8=function(t){return function(e){return t(e)}},W3={exports:{}};(function(t,e){var n=s4,r=e&&!e.nodeType&&e,i=r&&t&&!t.nodeType&&t,o=i&&i.exports===r&&n.process,s=function(){try{var l=i&&i.require&&i.require("util").types;return l||o&&o.binding&&o.binding("util")}catch{}}();t.exports=s})(W3,W3.exports);var R2=W3.exports,S8=b8,A8=k8,O2=R2&&R2.isTypedArray,f0=O2?A8(O2):S8,j8=d8,M8=f8,E8=t2,F8=v0,R8=y8,O8=f0,$8=Object.prototype.hasOwnProperty,P8=function(t,e){var n=E8(t),r=!n&&M8(t),i=!n&&!r&&F8(t),o=!n&&!r&&!i&&O8(t),s=n||r||i||o,l=s?j8(t.length,String):[],g=l.length;for(var u in t)!e&&!$8.call(t,u)||s&&(u=="length"||i&&(u=="offset"||u=="parent")||o&&(u=="buffer"||u=="byteLength"||u=="byteOffset")||R8(u,g))||l.push(u);return l},z8=Object.prototype,B8=function(t){var e=t&&t.constructor;return t===(typeof e=="function"&&e.prototype||z8)},N8=function(t,e){return function(n){return t(e(n))}}(Object.keys,Object),Z8=B8,D8=N8,I8=Object.prototype.hasOwnProperty,K8=function(t){if(!Z8(t))return D8(t);var e=[];for(var n in Object(t))I8.call(t,n)&&n!="constructor"&&e.push(n);return e},T8=u0,W8=p0,U8=P8,V8=K8,G8=function(t){return t!=null&&W8(t.length)&&!T8(t)},q8=o8,H8=u8,J8=function(t){return G8(t)?U8(t):V8(t)},$2=function(t){return q8(t,J8,H8)},Q8=Object.prototype.hasOwnProperty,X8=function(t,e,n,r,i,o){var s=1&n,l=$2(t),g=l.length;if(g!=$2(e).length&&!s)return!1;for(var u=g;u--;){var d=l[u];if(!(s?d in e:Q8.call(e,d)))return!1}var y=o.get(t),x=o.get(e);if(y&&x)return y==e&&x==t;var L=!0;o.set(t,e),o.set(e,t);for(var m=s;++u<g;){var h=t[d=l[u]],M=e[d];if(r)var A=s?r(M,h,d,e,t,o):r(h,M,d,t,e,o);if(!(A===void 0?h===M||i(h,M,n,r,o):A)){L=!1;break}m||(m=d=="constructor")}if(L&&!m){var p=t.constructor,O=e.constructor;p==O||!("constructor"in t)||!("constructor"in e)||typeof p=="function"&&p instanceof p&&typeof O=="function"&&O instanceof O||(L=!1)}return o.delete(t),o.delete(e),L},U3=r3(B1,"DataView"),V3=Y3,G3=r3(B1,"Promise"),q3=r3(B1,"Set"),H3=r3(B1,"WeakMap"),m0=_3,i3=d0,P2="[object Map]",z2="[object Promise]",B2="[object Set]",N2="[object WeakMap]",Z2="[object DataView]",Y8=i3(U3),t6=i3(V3),e6=i3(G3),n6=i3(q3),r6=i3(H3),Z1=m0;(U3&&Z1(new U3(new ArrayBuffer(1)))!=Z2||V3&&Z1(new V3)!=P2||G3&&Z1(G3.resolve())!=z2||q3&&Z1(new q3)!=B2||H3&&Z1(new H3)!=N2)&&(Z1=function(t){var e=m0(t),n=e=="[object Object]"?t.constructor:void 0,r=n?i3(n):"";if(r)switch(r){case Y8:return Z2;case t6:return P2;case e6:return z2;case n6:return B2;case r6:return N2}return e});var N3=D9,i6=h0,o6=e8,a6=X8,D2=Z1,I2=t2,K2=v0,s6=f0,T2="[object Arguments]",W2="[object Array]",C3="[object Object]",U2=Object.prototype.hasOwnProperty,l6=function(t,e,n,r,i,o){var s=I2(t),l=I2(e),g=s?W2:D2(t),u=l?W2:D2(e),d=(g=g==T2?C3:g)==C3,y=(u=u==T2?C3:u)==C3,x=g==u;if(x&&K2(t)){if(!K2(e))return!1;s=!0,d=!1}if(x&&!d)return o||(o=new N3),s||s6(t)?i6(t,e,n,r,i,o):o6(t,e,g,n,r,i,o);if(!(1&n)){var L=d&&U2.call(t,"__wrapped__"),m=y&&U2.call(e,"__wrapped__");if(L||m){var h=L?t.value():t,M=m?e.value():e;return o||(o=new N3),i(h,M,n,r,o)}}return!!x&&(o||(o=new N3),a6(t,e,n,r,i,o))},V2=b3,c6=function t(e,n,r,i,o){return e===n||(e==null||n==null||!V2(e)&&!V2(n)?e!=e&&n!=n:l6(e,n,r,i,t,o))},u6=c6;const d6=T0(function(t,e){return u6(t,e)});function g6(t,e){s1(e,!1);let n=C(e,"suggestions",8),r=C(e,"filepath",8),i=C(e,"readFile",8),o=C(e,"onFileAction",8,G1),s=C(e,"onCodeAction",8,G1),l=C(e,"fileActions",24,()=>[]),g=C(e,"codeActions",24,()=>[]),u=C(e,"expandable",8,!0),d=C(e,"scrollContainer",24,()=>{}),y=n1(""),x=n1(!1),L=n1(null),m=n1(null);X1(()=>(u1(n()),a(L),a(m),u1(r())),()=>{d6(n(),a(L))&&a(m)!==null&&c2(r(),a(m))||(a(m)!==null&&c2(r(),a(m))||E(x,!1),E(m,r()),E(L,n()),async function(h){E(y,await i()(h)),E(x,!0)}(r()))}),L3(),l3(),s0(t,{open:!0,class:"c-code-roll-item ",get expandable(){return u()},children:(h,M)=>{(function(A,p){s1(p,!0);let O=C(p,"loaded",3,!1),c=C(p,"language",19,()=>a4(p.filepath.relPath)),b=C(p,"onCodeAction",3,G1),$=C(p,"codeActions",19,()=>[]),k=C(p,"scrollContainer",19,()=>{});var v=C5(),I=f(v),D=R=>{g5(R,{get onCodeAction(){return b()},get codeActions(){return $()},get suggestions(){return p.suggestions},get originalCode(){return p.originalCode},get language(){return c()},get path(){return p.filepath.relPath},get scrollContainer(){return k()}})},a1=R=>{var N=h5(),H=f(N);X3(H,{}),S(R,N)};T1(I,R=>{O()?R(D):R(a1,!1)}),S(A,v),l1()})(h,{get codeActions(){return g()},get loaded(){return a(x)},get suggestions(){return n()},get filepath(){return r()},get originalCode(){return a(y)},get onCodeAction(){return s()},get scrollContainer(){return d()}})},$$slots:{default:!0,summary:(h,M)=>{(function(A,p){s1(p,!0);let O=C(p,"onFileAction",3,G1),c=C(p,"fileActions",19,()=>[]);var b=r5(),$=f(b);Y2($,{get filepath(){return p.filepath.relPath},get onCodeAction(){return G1},get value(){return p.suggestions}});var k=_($,2);e0(k,{get actions(){return c()},get onAction(){return O()},get value(){return p.filepath}}),S(A,b),l1()})(h,{get filepath(){return r()},get onFileAction(){return o()},get fileActions(){return l()},slot:"summary",get suggestions(){return n()}})}}}),l1()}function G2(t,e,n){return t.addEventListener(e,n),()=>{t.removeEventListener(e,n)}}function h6(t){t()}var C6=q('<div class="c-next-edit-suggestions--empty svelte-xgtx0g"><p>No Suggestions</p> <!></div>'),v6=q('<div class="c-next-edit-suggestions--empty svelte-xgtx0g"><!></div>'),p6=q('<div slot="left" class="c-next-edit-suggestions__left svelte-xgtx0g"><!></div>'),f6=q('<div class="c-next-edit-suggestions__right--empty svelte-xgtx0g"></div>'),m6=q('<div class="c-next-edit-suggestions__right svelte-xgtx0g" slot="right"><!></div>'),w6=q('<main><div class="c-next-edit-suggestions__container svelte-xgtx0g" tabindex="0" role="button"><!></div></main>');function Z6(t,e){s1(e,!1);const[n,r]=S1(),i=()=>f1(u,"$ctx",n),o=n1(),s=function(k){return async function(v){const I=await k.send({type:Q.readFileRequest,data:{pathName:v}});if("error"in I.data)throw new Error(I.data.error);return I.data.content}}(new Q0(C1.postMessage,3e3)),l=(g="(min-width: 500px)",B0(!1,k=>{const v=window.matchMedia(g);k((v==null?void 0:v.matches)??!1);const I=D=>k(D.matches);return v.addEventListener("change",I),()=>{v.removeEventListener("change",I)}}));var g;const u=o0({}),d=W1("active","|","reject","accept"),y=W1("active","|","reject","undo");let x=n1(new Map),L=n1([]),m=n1(!0),h=n1(!1),M=n1();function A(k){const v=function(I){if(I===-1)return-1;let D=I;do D--;while(D>=0&&a(L)[I].state==="stale");if(D!==-1)return D;D=I;do D++;while(D<a(L).length&&a(L)[I].state==="stale");return D===a(L).length?-1:D}(a(L).findIndex(x1.bind(null,k)));return t0(a(x),v)}const p=(k,v)=>{switch(k){case"acceptAllInFile":return Array.isArray(v)?void C1.postMessage({type:Q.nextEditSuggestionsAction,data:{acceptAllInFile:v}}):void 0;case"rejectAllInFile":return Array.isArray(v)?void C1.postMessage({type:Q.nextEditSuggestionsAction,data:{rejectAllInFile:v}}):void 0;case"undoAllInFile":return Array.isArray(v)?void C1.postMessage({type:Q.nextEditSuggestionsAction,data:{undoAllInFile:v}}):void 0;case"refresh":return E(m,!0),void C1.postMessage({type:Q.nextEditRefreshStarted,data:"refresh"});case"accept":return!v||Array.isArray(v)?void 0:(D1(u,U(i).selectedSuggestion=A(v),U(i)),void C1.postMessage({type:Q.nextEditSuggestionsAction,data:{accept:v}}));case"reject":return!v||Array.isArray(v)?void 0:void C1.postMessage({type:Q.nextEditSuggestionsAction,data:{reject:v}});case"active":return!v||Array.isArray(v)?void 0:(C1.postMessage({type:Q.nextEditOpenSuggestion,data:v}),void E1(u,{...i(),activeSuggestion:v,selectedSuggestion:v}));case"select":return!v||Array.isArray(v)?void 0:void E1(u,{...i(),activeSuggestion:void 0,selectedSuggestion:v});case"dismiss":return v3(i())==="active"?(E1(u,{...i(),activeSuggestion:void 0}),void C1.postMessage({type:Q.nextEditDismiss})):void 0;case"undo":return!v||Array.isArray(v)?void 0:void C1.postMessage({type:Q.nextEditSuggestionsAction,data:{undo:v}})}};function O(k){E($,!0)}function c(k){D1(u,U(i).selectedSuggestion=void 0,U(i)),E($,!1)}J2(()=>(C1.postMessage({type:Q.nextEditLoaded}),function(...k){return function(){k.forEach(h6)}}(G2(window,"focus",O),G2(window,"blur",c))));let b=n1(),$=n1(!1);X1(()=>a(b),()=>{a(b)&&a(b).focus()}),X1(()=>i(),()=>{E(o,I1(i()))}),X1(()=>(a($),i()),()=>{a($)&&i().nextSuggestion&&i().selectedSuggestion===void 0&&i().nextSuggestion!==i().selectedSuggestion&&D1(u,U(i).selectedSuggestion=i().nextSuggestion,U(i))}),L3(),l3(),P1("message",Z0,function(k){const v=k.data;switch(v.type){case Q.nextEditPreviewActive:E1(u,{...i(),activeSuggestion:v.data,nextSuggestion:v.data});break;case Q.nextEditDismiss:E1(u,{...i(),activeSuggestion:void 0});break;case Q.nextEditActiveSuggestionChanged:D1(u,U(i).activeSuggestion=v.data,U(i));break;case Q.nextEditToggleSuggestionTree:E(h,!a(h));break;case Q.nextEditRefreshStarted:E(m,!0);break;case Q.nextEditRefreshFinished:E(m,!1);break;case Q.nextEditSuggestionsChanged:E(m,!1),E(x,new Map(J0(v.data.suggestions??[]))),E(L,[...a(x).values()].flat()),u2(a(L),i().nextSuggestion)||E1(u,{...i(),nextSuggestion:void 0}),u2(a(L),i().activeSuggestion)||E1(u,{...i(),activeSuggestion:void 0});break;case Q.nextEditNextSuggestionChanged:E1(u,{...i(),nextSuggestion:v.data});break;case Q.nextEditPanelFocus:a(b)&&a(b).focus()}}),r4.Root(t,{children:(k,v)=>{var I=w6();let D;var a1=f(I),R=f(a1),N=J=>{var Y=C6(),w=_(f(Y),2),P=K(()=>D3(p,"refresh"));l4(w,{get loading(){return a(m)},$$events:{click(...z){var Z;(Z=a(P))==null||Z.apply(this,z)}},children:(z,Z)=>{var T=D0("Refresh");S(z,T)},$$slots:{default:!0}}),S(J,Y)},H=(J,Y)=>{var w=z=>{var Z=v6(),T=f(Z);X3(T,{}),S(z,Z)},P=z=>{Y0(z,{showButton:!1,class:"c-next-edit-suggestions__drawer",initialWidth:300,expandedMinWidth:150,deadzone:50,minimizedWidth:40,get minimized(){return a(h)},set minimized(Z){E(h,Z)},$$slots:{left:(Z,T)=>{var X=p6();n5(f(X),{get sortedPathSuggestionsMap(){return a(x)},onCodeAction:p,get minimized(){return a(h)}}),S(Z,X)},right:(Z,T)=>{var X=m6(),c1=f(X),j1=r1=>{const t1=v1(()=>[a(o)]),i1=v1(()=>(a(o),u1(e3),U(()=>a(o).state===e3.accepted?y:d)));g6(r1,{get filepath(){return a(o),U(()=>a(o).qualifiedPathName)},get suggestions(){return a(t1)},onCodeAction:p,get codeActions(){return a(i1)},get readFile(){return s},expandable:!1,get scrollContainer(){return a(M)}})},j=r1=>{var t1=f6();S(r1,t1)};T1(c1,r1=>{a(o)?r1(j1):r1(j,!1)}),c3(X,r1=>E(M,r1),()=>a(M)),S(Z,X)}},$$legacy:!0})};T1(J,z=>{a(m)?z(w):z(P,!1)},Y)};T1(R,J=>{a(L),U(()=>a(L).length===0)?J(N):J(H,!1)}),c3(a1,J=>E(b,J),()=>a(b)),G(J=>D=U1(I,1,"c-next-edit-suggestions svelte-xgtx0g",null,D,J),[()=>({"c-next-edit-suggestions__narrow":!f1(l,"$query",n)})],v1),S(k,I)},$$slots:{default:!0}}),l1(),r()}export{Z6 as default};
