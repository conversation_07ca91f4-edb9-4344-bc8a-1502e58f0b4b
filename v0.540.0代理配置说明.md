# Augment v0.540.0 代理配置说明

## 🚨 重要说明

由于v0.540.0版本**未修改extension.js**，插件不会自动应用代理配置。用户需要**手动配置**VSCode的代理设置。

## 📋 手动配置步骤

### 1. 打开VSCode设置

- 按 `Ctrl+,` 打开设置
- 或者 `文件` → `首选项` → `设置`

### 2. 切换到JSON模式

- 点击右上角的 `{}` 图标切换到JSON模式
- 或者按 `Ctrl+Shift+P` 输入 `Preferences: Open Settings (JSON)`

### 3. 添加代理配置

在settings.json中添加以下配置：

```json
{
  "augment.advanced.proxyConfig": {
    "enabled": true,
    "host": "iepl01.tube-cat.com",
    "port": 20040,
    "auth": {
      "username": "新加坡-IEPL 03",
      "password": "7bc365fe-4733-4ced-a0e1-5c2c75842b4a"
    }
  },
  "augment.advanced.networkOptimization": {
    "connectionTimeout": 30000,
    "requestTimeout": 60000,
    "retryAttempts": 5,
    "retryDelay": 1000,
    "keepAlive": true,
    "maxConcurrentRequests": 3,
    "enableRequestQueue": true,
    "excludeLocalRequests": true
  }
}
```

### 4. 保存并重启VSCode

- 保存settings.json文件
- 重启VSCode使配置生效

## 🔍 验证配置

1. 重启VSCode后，打开任意代码文件
2. 尝试使用Augment的代码补全功能
3. 检查网络请求是否通过代理服务器

## ⚠️ 注意事项

1. **用户名格式**：必须使用"新加坡-IEPL 03"，不是UUID
2. **配置位置**：必须在VSCode的settings.json中配置，不是插件目录
3. **重启要求**：修改配置后需要重启VSCode
4. **网络测试**：建议先测试代理连接是否正常

## 🆚 与自动配置版本的区别

- **v0.531.0-enhanced**：自动配置，安装即用
- **v0.540.0-enhanced**：手动配置，需要用户操作

## 🔍 API地址信息

根据分析，Augment插件可能使用以下API地址模式：
- `d1.augmentcode.com` 到 `d20.augmentcode.com`
- 这些是随机分配的API服务器地址
- 代理配置将影响对这些地址的访问

## 🔧 故障排除

如果代理不生效：

1. 检查代理服务器是否可用
2. 确认用户名密码是否正确
3. 重启VSCode
4. 检查VSCode的网络设置
5. 验证是否能访问augmentcode.com相关域名
