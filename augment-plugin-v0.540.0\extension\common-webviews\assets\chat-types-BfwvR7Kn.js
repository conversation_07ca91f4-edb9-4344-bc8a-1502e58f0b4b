var s=(t=>(t[t.TEXT=0]="TEXT",t[t.TOOL_RESULT=1]="TOOL_RESULT",t[t.IMAGE=2]="IMAGE",t[t.IMAGE_ID=3]="IMAGE_ID",t[t.IDE_STATE=4]="IDE_STATE",t[t.EDIT_EVENTS=5]="EDIT_EVENTS",t[t.CHECKPOINT_REF=6]="CHECKPOINT_REF",t[t.CHANGE_PERSONALITY=7]="CHANGE_PERSONALITY",t[t.FILE=8]="FILE",t[t.FILE_ID=9]="FILE_ID",t))(s||{}),T=(t=>(t[t.IMAGE_FORMAT_UNSPECIFIED=0]="IMAGE_FORMAT_UNSPECIFIED",t[t.PNG=1]="PNG",t[t.JPEG=2]="JPEG",t[t.GIF=3]="GIF",t[t.WEBP=4]="WEBP",t))(T||{}),r=(t=>(t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.USER_EDIT=1]="USER_EDIT",t[t.CHECKPOINT_REVERT=2]="CHECKPOINT_REVERT",t))(r||{}),i=(t=>(t[t.CONTENT_TYPE_UNSPECIFIED=0]="CONTENT_TYPE_UNSPECIFIED",t[t.CONTENT_TEXT=1]="CONTENT_TEXT",t[t.CONTENT_IMAGE=2]="CONTENT_IMAGE",t))(i||{}),c=(t=>(t[t.RAW_RESPONSE=0]="RAW_RESPONSE",t[t.SUGGESTED_QUESTIONS=1]="SUGGESTED_QUESTIONS",t[t.MAIN_TEXT_FINISHED=2]="MAIN_TEXT_FINISHED",t[t.TOOL_USE=5]="TOOL_USE",t[t.AGENT_MEMORY=6]="AGENT_MEMORY",t[t.TOOL_USE_START=7]="TOOL_USE_START",t[t.THINKING=8]="THINKING",t))(c||{}),u=(t=>(t.chat="CHAT",t.agent="AGENT",t.remoteAgent="REMOTE_AGENT",t.memories="MEMORIES",t.orientation="ORIENTATION",t.memoriesCompression="MEMORIES_COMPRESSION",t.cliAgent="CLI_AGENT",t))(u||{}),e=(t=>(t[t.DEFAULT=0]="DEFAULT",t[t.PROTOTYPER=1]="PROTOTYPER",t[t.BRAINSTORM=2]="BRAINSTORM",t[t.REVIEWER=3]="REVIEWER",t))(e||{}),I=(t=>(t[t.ALWAYS_ATTACHED=0]="ALWAYS_ATTACHED",t[t.MANUAL=1]="MANUAL",t[t.AGENT_REQUESTED=2]="AGENT_REQUESTED",t))(I||{});const O="augment-welcome";var o=(t=>(t.draft="draft",t.sent="sent",t.failed="failed",t.success="success",t.cancelled="cancelled",t))(o||{}),_=(t=>(t.running="running",t.awaitingUserAction="awaiting-user-action",t.notRunning="not-running",t))(_||{}),m=(t=>(t.seen="seen",t.unseen="unseen",t))(m||{}),N=(t=>(t.signInWelcome="sign-in-welcome",t.generateCommitMessage="generate-commit-message",t.summaryResponse="summary-response",t.summaryTitle="summary-title",t.educateFeatures="educate-features",t.agentOnboarding="agent-onboarding",t.agenticTurnDelimiter="agentic-turn-delimiter",t.agenticRevertDelimiter="agentic-revert-delimiter",t.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",t.exchange="exchange",t.exchangePointer="exchange-pointer",t.historySummary="history-summary",t))(N||{});function R(t){return a(t)||A(t)||S(t)}function a(t){return!!t&&(t.chatItemType===void 0||t.chatItemType==="agent-onboarding")}function g(t){return a(t)&&t.status==="success"}function C(t){return!!t&&t.chatItemType==="exchange-pointer"}function d(t){return t.chatItemType==="sign-in-welcome"}function A(t){return t.chatItemType==="generate-commit-message"}function D(t){return t.chatItemType==="summary-response"}function G(t){return t.chatItemType==="educate-features"}function S(t){return t.chatItemType==="agent-onboarding"}function P(t){return t.chatItemType==="agentic-turn-delimiter"}function f(t){return t.chatItemType==="agentic-checkpoint-delimiter"}function p(t){return t.chatItemType==="history-summary"}function M(t){return t.revertTarget!==void 0}function y(t){var n;return((n=t.structured_output_nodes)==null?void 0:n.some(E=>E.type===5))??!1}function l(t){var n;return((n=t.structured_request_nodes)==null?void 0:n.some(E=>E.type===1))??!1}function U(t){return!(!t||typeof t!="object")&&(!("request_id"in t)||typeof t.request_id=="string")&&(!("seen_state"in t)||t.seen_state==="seen"||t.seen_state==="unseen")}function h(t){return(t==null?void 0:t.status)==="success"||(t==null?void 0:t.status)==="failed"||(t==null?void 0:t.status)==="cancelled"}export{_ as A,c as C,o as E,T as I,e as P,I as R,m as S,s as a,i as b,p as c,N as d,C as e,g as f,f as g,l as h,a as i,r as j,D as k,u as l,P as m,d as n,A as o,G as p,S as q,y as r,O as s,M as t,U as u,R as v,h as w};
