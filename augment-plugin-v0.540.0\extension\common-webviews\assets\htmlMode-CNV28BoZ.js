import{m as cn}from"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";var dn=Object.defineProperty,ln=Object.getOwnPropertyDescriptor,gn=Object.getOwnPropertyNames,fn=Object.prototype.hasOwnProperty,u={};((t,e,n,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let o of gn(e))fn.call(t,o)||o===n||dn(t,o,{get:()=>e[o],enumerable:!(r=ln(e,o))||r.enumerable})})(u,cn,"default");var ce,X,de,R,j,L,k,le,m,ge,N,fe,me,he,z,pe,ve,be,_e,ke,P,$,we,ye,B,xe,C,U,Ie,Ee,<PERSON>,V,Ae,E,Ce,S,O,q,<PERSON>,<PERSON>,Le,W,Q,Te,G,Me,J,Pe,Y,De,Z,<PERSON>,je,Ne,Ue,ee,Ve,Oe,We,te,T,M,h,g,se,He,Ke,Xe,ze,$e,Be,qe,Qe,Ge,H,Je,Ye,Ze,et,D,ne,tt,p,l,nt,rt,it,ot,at,st,y,K,ut,ct,dt,lt,gt,ft,mt,ht,pt,vt,bt,_t,re,kt,f,wt,w,yt,xt,It,Et,St,At,Ct,Rt,Lt,ie,oe,ae,Tt,Mt,Pt,Dt,Ft,jt,Nt,Ut,Vt,Ot,Wt,Ht,zt=class{constructor(t){this._defaults=t,this._worker=null,this._client=null,this._idleCheckInterval=window.setInterval(()=>this._checkIfIdle(),3e4),this._lastUsedTime=0,this._configChangeListener=this._defaults.onDidChange(()=>this._stopWorker())}_stopWorker(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null}dispose(){clearInterval(this._idleCheckInterval),this._configChangeListener.dispose(),this._stopWorker()}_checkIfIdle(){this._worker&&Date.now()-this._lastUsedTime>12e4&&this._stopWorker()}_getClient(){return this._lastUsedTime=Date.now(),this._client||(this._worker=u.editor.createWebWorker({moduleId:"vs/language/html/htmlWorker",createData:{languageSettings:this._defaults.options,languageId:this._defaults.languageId},label:this._defaults.languageId}),this._client=this._worker.getProxy()),this._client}getLanguageServiceWorker(...t){let e;return this._getClient().then(n=>{e=n}).then(n=>{if(this._worker)return this._worker.withSyncedResources(t)}).then(n=>e)}};(ce||(ce={})).is=function(t){return typeof t=="string"},(X||(X={})).is=function(t){return typeof t=="string"},(R=de||(de={})).MIN_VALUE=-2147483648,R.MAX_VALUE=2147483647,R.is=function(t){return typeof t=="number"&&R.MIN_VALUE<=t&&t<=R.MAX_VALUE},(L=j||(j={})).MIN_VALUE=0,L.MAX_VALUE=2147483647,L.is=function(t){return typeof t=="number"&&L.MIN_VALUE<=t&&t<=L.MAX_VALUE},(le=k||(k={})).create=function(t,e){return t===Number.MAX_VALUE&&(t=j.MAX_VALUE),e===Number.MAX_VALUE&&(e=j.MAX_VALUE),{line:t,character:e}},le.is=function(t){let e=t;return i.objectLiteral(e)&&i.uinteger(e.line)&&i.uinteger(e.character)},(ge=m||(m={})).create=function(t,e,n,r){if(i.uinteger(t)&&i.uinteger(e)&&i.uinteger(n)&&i.uinteger(r))return{start:k.create(t,e),end:k.create(n,r)};if(k.is(t)&&k.is(e))return{start:t,end:e};throw new Error(`Range#create called with invalid arguments[${t}, ${e}, ${n}, ${r}]`)},ge.is=function(t){let e=t;return i.objectLiteral(e)&&k.is(e.start)&&k.is(e.end)},(fe=N||(N={})).create=function(t,e){return{uri:t,range:e}},fe.is=function(t){let e=t;return i.objectLiteral(e)&&m.is(e.range)&&(i.string(e.uri)||i.undefined(e.uri))},(he=me||(me={})).create=function(t,e,n,r){return{targetUri:t,targetRange:e,targetSelectionRange:n,originSelectionRange:r}},he.is=function(t){let e=t;return i.objectLiteral(e)&&m.is(e.targetRange)&&i.string(e.targetUri)&&m.is(e.targetSelectionRange)&&(m.is(e.originSelectionRange)||i.undefined(e.originSelectionRange))},(pe=z||(z={})).create=function(t,e,n,r){return{red:t,green:e,blue:n,alpha:r}},pe.is=function(t){const e=t;return i.objectLiteral(e)&&i.numberRange(e.red,0,1)&&i.numberRange(e.green,0,1)&&i.numberRange(e.blue,0,1)&&i.numberRange(e.alpha,0,1)},(be=ve||(ve={})).create=function(t,e){return{range:t,color:e}},be.is=function(t){const e=t;return i.objectLiteral(e)&&m.is(e.range)&&z.is(e.color)},(ke=_e||(_e={})).create=function(t,e,n){return{label:t,textEdit:e,additionalTextEdits:n}},ke.is=function(t){const e=t;return i.objectLiteral(e)&&i.string(e.label)&&(i.undefined(e.textEdit)||S.is(e))&&(i.undefined(e.additionalTextEdits)||i.typedArray(e.additionalTextEdits,S.is))},($=P||(P={})).Comment="comment",$.Imports="imports",$.Region="region",(ye=we||(we={})).create=function(t,e,n,r,o,a){const s={startLine:t,endLine:e};return i.defined(n)&&(s.startCharacter=n),i.defined(r)&&(s.endCharacter=r),i.defined(o)&&(s.kind=o),i.defined(a)&&(s.collapsedText=a),s},ye.is=function(t){const e=t;return i.objectLiteral(e)&&i.uinteger(e.startLine)&&i.uinteger(e.startLine)&&(i.undefined(e.startCharacter)||i.uinteger(e.startCharacter))&&(i.undefined(e.endCharacter)||i.uinteger(e.endCharacter))&&(i.undefined(e.kind)||i.string(e.kind))},(xe=B||(B={})).create=function(t,e){return{location:t,message:e}},xe.is=function(t){let e=t;return i.defined(e)&&N.is(e.location)&&i.string(e.message)},(U=C||(C={})).Error=1,U.Warning=2,U.Information=3,U.Hint=4,(Ee=Ie||(Ie={})).Unnecessary=1,Ee.Deprecated=2,(Se||(Se={})).is=function(t){const e=t;return i.objectLiteral(e)&&i.string(e.href)},(Ae=V||(V={})).create=function(t,e,n,r,o,a){let s={range:t,message:e};return i.defined(n)&&(s.severity=n),i.defined(r)&&(s.code=r),i.defined(o)&&(s.source=o),i.defined(a)&&(s.relatedInformation=a),s},Ae.is=function(t){var e;let n=t;return i.defined(n)&&m.is(n.range)&&i.string(n.message)&&(i.number(n.severity)||i.undefined(n.severity))&&(i.integer(n.code)||i.string(n.code)||i.undefined(n.code))&&(i.undefined(n.codeDescription)||i.string((e=n.codeDescription)===null||e===void 0?void 0:e.href))&&(i.string(n.source)||i.undefined(n.source))&&(i.undefined(n.relatedInformation)||i.typedArray(n.relatedInformation,B.is))},(Ce=E||(E={})).create=function(t,e,...n){let r={title:t,command:e};return i.defined(n)&&n.length>0&&(r.arguments=n),r},Ce.is=function(t){let e=t;return i.defined(e)&&i.string(e.title)&&i.string(e.command)},(O=S||(S={})).replace=function(t,e){return{range:t,newText:e}},O.insert=function(t,e){return{range:{start:t,end:t},newText:e}},O.del=function(t){return{range:t,newText:""}},O.is=function(t){const e=t;return i.objectLiteral(e)&&i.string(e.newText)&&m.is(e.range)},(Re=q||(q={})).create=function(t,e,n){const r={label:t};return e!==void 0&&(r.needsConfirmation=e),n!==void 0&&(r.description=n),r},Re.is=function(t){const e=t;return i.objectLiteral(e)&&i.string(e.label)&&(i.boolean(e.needsConfirmation)||e.needsConfirmation===void 0)&&(i.string(e.description)||e.description===void 0)},(A||(A={})).is=function(t){const e=t;return i.string(e)},(W=Le||(Le={})).replace=function(t,e,n){return{range:t,newText:e,annotationId:n}},W.insert=function(t,e,n){return{range:{start:t,end:t},newText:e,annotationId:n}},W.del=function(t,e){return{range:t,newText:"",annotationId:e}},W.is=function(t){const e=t;return S.is(e)&&(q.is(e.annotationId)||A.is(e.annotationId))},(Te=Q||(Q={})).create=function(t,e){return{textDocument:t,edits:e}},Te.is=function(t){let e=t;return i.defined(e)&&ee.is(e.textDocument)&&Array.isArray(e.edits)},(Me=G||(G={})).create=function(t,e,n){let r={kind:"create",uri:t};return e===void 0||e.overwrite===void 0&&e.ignoreIfExists===void 0||(r.options=e),n!==void 0&&(r.annotationId=n),r},Me.is=function(t){let e=t;return e&&e.kind==="create"&&i.string(e.uri)&&(e.options===void 0||(e.options.overwrite===void 0||i.boolean(e.options.overwrite))&&(e.options.ignoreIfExists===void 0||i.boolean(e.options.ignoreIfExists)))&&(e.annotationId===void 0||A.is(e.annotationId))},(Pe=J||(J={})).create=function(t,e,n,r){let o={kind:"rename",oldUri:t,newUri:e};return n===void 0||n.overwrite===void 0&&n.ignoreIfExists===void 0||(o.options=n),r!==void 0&&(o.annotationId=r),o},Pe.is=function(t){let e=t;return e&&e.kind==="rename"&&i.string(e.oldUri)&&i.string(e.newUri)&&(e.options===void 0||(e.options.overwrite===void 0||i.boolean(e.options.overwrite))&&(e.options.ignoreIfExists===void 0||i.boolean(e.options.ignoreIfExists)))&&(e.annotationId===void 0||A.is(e.annotationId))},(De=Y||(Y={})).create=function(t,e,n){let r={kind:"delete",uri:t};return e===void 0||e.recursive===void 0&&e.ignoreIfNotExists===void 0||(r.options=e),n!==void 0&&(r.annotationId=n),r},De.is=function(t){let e=t;return e&&e.kind==="delete"&&i.string(e.uri)&&(e.options===void 0||(e.options.recursive===void 0||i.boolean(e.options.recursive))&&(e.options.ignoreIfNotExists===void 0||i.boolean(e.options.ignoreIfNotExists)))&&(e.annotationId===void 0||A.is(e.annotationId))},(Z||(Z={})).is=function(t){let e=t;return e&&(e.changes!==void 0||e.documentChanges!==void 0)&&(e.documentChanges===void 0||e.documentChanges.every(n=>i.string(n.kind)?G.is(n)||J.is(n)||Y.is(n):Q.is(n)))},(je=Fe||(Fe={})).create=function(t){return{uri:t}},je.is=function(t){let e=t;return i.defined(e)&&i.string(e.uri)},(Ue=Ne||(Ne={})).create=function(t,e){return{uri:t,version:e}},Ue.is=function(t){let e=t;return i.defined(e)&&i.string(e.uri)&&i.integer(e.version)},(Ve=ee||(ee={})).create=function(t,e){return{uri:t,version:e}},Ve.is=function(t){let e=t;return i.defined(e)&&i.string(e.uri)&&(e.version===null||i.integer(e.version))},(We=Oe||(Oe={})).create=function(t,e,n,r){return{uri:t,languageId:e,version:n,text:r}},We.is=function(t){let e=t;return i.defined(e)&&i.string(e.uri)&&i.string(e.languageId)&&i.integer(e.version)&&i.string(e.text)},(T=te||(te={})).PlainText="plaintext",T.Markdown="markdown",T.is=function(t){const e=t;return e===T.PlainText||e===T.Markdown},(M||(M={})).is=function(t){const e=t;return i.objectLiteral(t)&&te.is(e.kind)&&i.string(e.value)},(g=h||(h={})).Text=1,g.Method=2,g.Function=3,g.Constructor=4,g.Field=5,g.Variable=6,g.Class=7,g.Interface=8,g.Module=9,g.Property=10,g.Unit=11,g.Value=12,g.Enum=13,g.Keyword=14,g.Snippet=15,g.Color=16,g.File=17,g.Reference=18,g.Folder=19,g.EnumMember=20,g.Constant=21,g.Struct=22,g.Event=23,g.Operator=24,g.TypeParameter=25,(He=se||(se={})).PlainText=1,He.Snippet=2,(Ke||(Ke={})).Deprecated=1,(ze=Xe||(Xe={})).create=function(t,e,n){return{newText:t,insert:e,replace:n}},ze.is=function(t){const e=t;return e&&i.string(e.newText)&&m.is(e.insert)&&m.is(e.replace)},(Be=$e||($e={})).asIs=1,Be.adjustIndentation=2,(qe||(qe={})).is=function(t){const e=t;return e&&(i.string(e.detail)||e.detail===void 0)&&(i.string(e.description)||e.description===void 0)},(Qe||(Qe={})).create=function(t){return{label:t}},(Ge||(Ge={})).create=function(t,e){return{items:t||[],isIncomplete:!!e}},(Je=H||(H={})).fromPlainText=function(t){return t.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},Je.is=function(t){const e=t;return i.string(e)||i.objectLiteral(e)&&i.string(e.language)&&i.string(e.value)},(Ye||(Ye={})).is=function(t){let e=t;return!!e&&i.objectLiteral(e)&&(M.is(e.contents)||H.is(e.contents)||i.typedArray(e.contents,H.is))&&(t.range===void 0||m.is(t.range))},(Ze||(Ze={})).create=function(t,e){return e?{label:t,documentation:e}:{label:t}},(et||(et={})).create=function(t,e,...n){let r={label:t};return i.defined(e)&&(r.documentation=e),i.defined(n)?r.parameters=n:r.parameters=[],r},(ne=D||(D={})).Text=1,ne.Read=2,ne.Write=3,(tt||(tt={})).create=function(t,e){let n={range:t};return i.number(e)&&(n.kind=e),n},(l=p||(p={})).File=1,l.Module=2,l.Namespace=3,l.Package=4,l.Class=5,l.Method=6,l.Property=7,l.Field=8,l.Constructor=9,l.Enum=10,l.Interface=11,l.Function=12,l.Variable=13,l.Constant=14,l.String=15,l.Number=16,l.Boolean=17,l.Array=18,l.Object=19,l.Key=20,l.Null=21,l.EnumMember=22,l.Struct=23,l.Event=24,l.Operator=25,l.TypeParameter=26,(nt||(nt={})).Deprecated=1,(rt||(rt={})).create=function(t,e,n,r,o){let a={name:t,kind:e,location:{uri:r,range:n}};return o&&(a.containerName=o),a},(it||(it={})).create=function(t,e,n,r){return r!==void 0?{name:t,kind:e,location:{uri:n,range:r}}:{name:t,kind:e,location:{uri:n}}},(at=ot||(ot={})).create=function(t,e,n,r,o,a){let s={name:t,detail:e,kind:n,range:r,selectionRange:o};return a!==void 0&&(s.children=a),s},at.is=function(t){let e=t;return e&&i.string(e.name)&&i.number(e.kind)&&m.is(e.range)&&m.is(e.selectionRange)&&(e.detail===void 0||i.string(e.detail))&&(e.deprecated===void 0||i.boolean(e.deprecated))&&(e.children===void 0||Array.isArray(e.children))&&(e.tags===void 0||Array.isArray(e.tags))},(y=st||(st={})).Empty="",y.QuickFix="quickfix",y.Refactor="refactor",y.RefactorExtract="refactor.extract",y.RefactorInline="refactor.inline",y.RefactorRewrite="refactor.rewrite",y.Source="source",y.SourceOrganizeImports="source.organizeImports",y.SourceFixAll="source.fixAll",(ut=K||(K={})).Invoked=1,ut.Automatic=2,(dt=ct||(ct={})).create=function(t,e,n){let r={diagnostics:t};return e!=null&&(r.only=e),n!=null&&(r.triggerKind=n),r},dt.is=function(t){let e=t;return i.defined(e)&&i.typedArray(e.diagnostics,V.is)&&(e.only===void 0||i.typedArray(e.only,i.string))&&(e.triggerKind===void 0||e.triggerKind===K.Invoked||e.triggerKind===K.Automatic)},(gt=lt||(lt={})).create=function(t,e,n){let r={title:t},o=!0;return typeof e=="string"?(o=!1,r.kind=e):E.is(e)?r.command=e:r.edit=e,o&&n!==void 0&&(r.kind=n),r},gt.is=function(t){let e=t;return e&&i.string(e.title)&&(e.diagnostics===void 0||i.typedArray(e.diagnostics,V.is))&&(e.kind===void 0||i.string(e.kind))&&(e.edit!==void 0||e.command!==void 0)&&(e.command===void 0||E.is(e.command))&&(e.isPreferred===void 0||i.boolean(e.isPreferred))&&(e.edit===void 0||Z.is(e.edit))},(mt=ft||(ft={})).create=function(t,e){let n={range:t};return i.defined(e)&&(n.data=e),n},mt.is=function(t){let e=t;return i.defined(e)&&m.is(e.range)&&(i.undefined(e.command)||E.is(e.command))},(pt=ht||(ht={})).create=function(t,e){return{tabSize:t,insertSpaces:e}},pt.is=function(t){let e=t;return i.defined(e)&&i.uinteger(e.tabSize)&&i.boolean(e.insertSpaces)},(bt=vt||(vt={})).create=function(t,e,n){return{range:t,target:e,data:n}},bt.is=function(t){let e=t;return i.defined(e)&&m.is(e.range)&&(i.undefined(e.target)||i.string(e.target))},(re=_t||(_t={})).create=function(t,e){return{range:t,parent:e}},re.is=function(t){let e=t;return i.objectLiteral(e)&&m.is(e.range)&&(e.parent===void 0||re.is(e.parent))},(f=kt||(kt={})).namespace="namespace",f.type="type",f.class="class",f.enum="enum",f.interface="interface",f.struct="struct",f.typeParameter="typeParameter",f.parameter="parameter",f.variable="variable",f.property="property",f.enumMember="enumMember",f.event="event",f.function="function",f.method="method",f.macro="macro",f.keyword="keyword",f.modifier="modifier",f.comment="comment",f.string="string",f.number="number",f.regexp="regexp",f.operator="operator",f.decorator="decorator",(w=wt||(wt={})).declaration="declaration",w.definition="definition",w.readonly="readonly",w.static="static",w.deprecated="deprecated",w.abstract="abstract",w.async="async",w.modification="modification",w.documentation="documentation",w.defaultLibrary="defaultLibrary",(yt||(yt={})).is=function(t){const e=t;return i.objectLiteral(e)&&(e.resultId===void 0||typeof e.resultId=="string")&&Array.isArray(e.data)&&(e.data.length===0||typeof e.data[0]=="number")},(It=xt||(xt={})).create=function(t,e){return{range:t,text:e}},It.is=function(t){const e=t;return e!=null&&m.is(e.range)&&i.string(e.text)},(St=Et||(Et={})).create=function(t,e,n){return{range:t,variableName:e,caseSensitiveLookup:n}},St.is=function(t){const e=t;return e!=null&&m.is(e.range)&&i.boolean(e.caseSensitiveLookup)&&(i.string(e.variableName)||e.variableName===void 0)},(Ct=At||(At={})).create=function(t,e){return{range:t,expression:e}},Ct.is=function(t){const e=t;return e!=null&&m.is(e.range)&&(i.string(e.expression)||e.expression===void 0)},(Lt=Rt||(Rt={})).create=function(t,e){return{frameId:t,stoppedLocation:e}},Lt.is=function(t){const e=t;return i.defined(e)&&m.is(t.stoppedLocation)},(oe=ie||(ie={})).Type=1,oe.Parameter=2,oe.is=function(t){return t===1||t===2},(Tt=ae||(ae={})).create=function(t){return{value:t}},Tt.is=function(t){const e=t;return i.objectLiteral(e)&&(e.tooltip===void 0||i.string(e.tooltip)||M.is(e.tooltip))&&(e.location===void 0||N.is(e.location))&&(e.command===void 0||E.is(e.command))},(Pt=Mt||(Mt={})).create=function(t,e,n){const r={position:t,label:e};return n!==void 0&&(r.kind=n),r},Pt.is=function(t){const e=t;return i.objectLiteral(e)&&k.is(e.position)&&(i.string(e.label)||i.typedArray(e.label,ae.is))&&(e.kind===void 0||ie.is(e.kind))&&e.textEdits===void 0||i.typedArray(e.textEdits,S.is)&&(e.tooltip===void 0||i.string(e.tooltip)||M.is(e.tooltip))&&(e.paddingLeft===void 0||i.boolean(e.paddingLeft))&&(e.paddingRight===void 0||i.boolean(e.paddingRight))},(Dt||(Dt={})).createSnippet=function(t){return{kind:"snippet",value:t}},(Ft||(Ft={})).create=function(t,e,n,r){return{insertText:t,filterText:e,range:n,command:r}},(jt||(jt={})).create=function(t){return{items:t}},(Ut=Nt||(Nt={})).Invoked=0,Ut.Automatic=1,(Vt||(Vt={})).create=function(t,e){return{range:t,text:e}},(Ot||(Ot={})).create=function(t,e){return{triggerKind:t,selectedCompletionInfo:e}},(Wt||(Wt={})).is=function(t){const e=t;return i.objectLiteral(e)&&X.is(e.uri)&&i.string(e.name)},function(t){function e(n,r){if(n.length<=1)return n;const o=n.length/2|0,a=n.slice(0,o),s=n.slice(o);e(a,r),e(s,r);let c=0,v=0,d=0;for(;c<a.length&&v<s.length;){let _=r(a[c],s[v]);n[d++]=_<=0?a[c++]:s[v++]}for(;c<a.length;)n[d++]=a[c++];for(;v<s.length;)n[d++]=s[v++];return n}t.create=function(n,r,o,a){return new mn(n,r,o,a)},t.is=function(n){let r=n;return!!(i.defined(r)&&i.string(r.uri)&&(i.undefined(r.languageId)||i.string(r.languageId))&&i.uinteger(r.lineCount)&&i.func(r.getText)&&i.func(r.positionAt)&&i.func(r.offsetAt))},t.applyEdits=function(n,r){let o=n.getText(),a=e(r,(c,v)=>{let d=c.range.start.line-v.range.start.line;return d===0?c.range.start.character-v.range.start.character:d}),s=o.length;for(let c=a.length-1;c>=0;c--){let v=a[c],d=n.offsetAt(v.range.start),_=n.offsetAt(v.range.end);if(!(_<=s))throw new Error("Overlapping edit");o=o.substring(0,d)+v.newText+o.substring(_,o.length),s=d}return o}}(Ht||(Ht={}));var i,mn=class{constructor(t,e,n,r){this._uri=t,this._languageId=e,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(t){if(t){let e=this.offsetAt(t.start),n=this.offsetAt(t.end);return this._content.substring(e,n)}return this._content}update(t,e){this._content=t.text,this._version=e,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let t=[],e=this._content,n=!0;for(let r=0;r<e.length;r++){n&&(t.push(r),n=!1);let o=e.charAt(r);n=o==="\r"||o===`
`,o==="\r"&&r+1<e.length&&e.charAt(r+1)===`
`&&r++}n&&e.length>0&&t.push(e.length),this._lineOffsets=t}return this._lineOffsets}positionAt(t){t=Math.max(Math.min(t,this._content.length),0);let e=this.getLineOffsets(),n=0,r=e.length;if(r===0)return k.create(0,t);for(;n<r;){let a=Math.floor((n+r)/2);e[a]>t?r=a:n=a+1}let o=n-1;return k.create(o,t-e[o])}offsetAt(t){let e=this.getLineOffsets();if(t.line>=e.length)return this._content.length;if(t.line<0)return 0;let n=e[t.line],r=t.line+1<e.length?e[t.line+1]:this._content.length;return Math.max(Math.min(n+t.character,r),n)}get lineCount(){return this.getLineOffsets().length}};(function(t){const e=Object.prototype.toString;t.defined=function(n){return n!==void 0},t.undefined=function(n){return n===void 0},t.boolean=function(n){return n===!0||n===!1},t.string=function(n){return e.call(n)==="[object String]"},t.number=function(n){return e.call(n)==="[object Number]"},t.numberRange=function(n,r,o){return e.call(n)==="[object Number]"&&r<=n&&n<=o},t.integer=function(n){return e.call(n)==="[object Number]"&&-2147483648<=n&&n<=2147483647},t.uinteger=function(n){return e.call(n)==="[object Number]"&&0<=n&&n<=2147483647},t.func=function(n){return e.call(n)==="[object Function]"},t.objectLiteral=function(n){return n!==null&&typeof n=="object"},t.typedArray=function(n,r){return Array.isArray(n)&&n.every(r)}})(i||(i={}));var yn=class{constructor(t,e,n){this._languageId=t,this._worker=e,this._disposables=[],this._listener=Object.create(null);const r=a=>{let s,c=a.getLanguageId();c===this._languageId&&(this._listener[a.uri.toString()]=a.onDidChangeContent(()=>{window.clearTimeout(s),s=window.setTimeout(()=>this._doValidate(a.uri,c),500)}),this._doValidate(a.uri,c))},o=a=>{u.editor.setModelMarkers(a,this._languageId,[]);let s=a.uri.toString(),c=this._listener[s];c&&(c.dispose(),delete this._listener[s])};this._disposables.push(u.editor.onDidCreateModel(r)),this._disposables.push(u.editor.onWillDisposeModel(o)),this._disposables.push(u.editor.onDidChangeModelLanguage(a=>{o(a.model),r(a.model)})),this._disposables.push(n(a=>{u.editor.getModels().forEach(s=>{s.getLanguageId()===this._languageId&&(o(s),r(s))})})),this._disposables.push({dispose:()=>{u.editor.getModels().forEach(o);for(let a in this._listener)this._listener[a].dispose()}}),u.editor.getModels().forEach(r)}dispose(){this._disposables.forEach(t=>t&&t.dispose()),this._disposables.length=0}_doValidate(t,e){this._worker(t).then(n=>n.doValidation(t.toString())).then(n=>{const r=n.map(a=>function(s,c){let v=typeof c.code=="number"?String(c.code):c.code;return{severity:hn(c.severity),startLineNumber:c.range.start.line+1,startColumn:c.range.start.character+1,endLineNumber:c.range.end.line+1,endColumn:c.range.end.character+1,message:c.message,code:v,source:c.source}}(0,a));let o=u.editor.getModel(t);o&&o.getLanguageId()===e&&u.editor.setModelMarkers(o,e,r)}).then(void 0,n=>{console.error(n)})}};function hn(t){switch(t){case C.Error:return u.MarkerSeverity.Error;case C.Warning:return u.MarkerSeverity.Warning;case C.Information:return u.MarkerSeverity.Info;case C.Hint:return u.MarkerSeverity.Hint;default:return u.MarkerSeverity.Info}}var pn=class{constructor(t,e){this._worker=t,this._triggerCharacters=e}get triggerCharacters(){return this._triggerCharacters}provideCompletionItems(t,e,n,r){const o=t.uri;return this._worker(o).then(a=>a.doComplete(o.toString(),x(e))).then(a=>{if(!a)return;const s=t.getWordUntilPosition(e),c=new u.Range(e.lineNumber,s.startColumn,e.lineNumber,s.endColumn),v=a.items.map(d=>{const _={label:d.label,insertText:d.insertText||d.label,sortText:d.sortText,filterText:d.filterText,documentation:d.documentation,detail:d.detail,command:(I=d.command,I&&I.command==="editor.action.triggerSuggest"?{id:I.command,title:I.title,arguments:I.arguments}:void 0),range:c,kind:vn(d.kind)};var I,ue;return d.textEdit&&((ue=d.textEdit).insert!==void 0&&ue.replace!==void 0?_.range={insert:b(d.textEdit.insert),replace:b(d.textEdit.replace)}:_.range=b(d.textEdit.range),_.insertText=d.textEdit.newText),d.additionalTextEdits&&(_.additionalTextEdits=d.additionalTextEdits.map(F)),d.insertTextFormat===se.Snippet&&(_.insertTextRules=u.languages.CompletionItemInsertTextRule.InsertAsSnippet),_});return{isIncomplete:a.isIncomplete,suggestions:v}})}};function x(t){if(t)return{character:t.column-1,line:t.lineNumber-1}}function $t(t){if(t)return{start:{line:t.startLineNumber-1,character:t.startColumn-1},end:{line:t.endLineNumber-1,character:t.endColumn-1}}}function b(t){if(t)return new u.Range(t.start.line+1,t.start.character+1,t.end.line+1,t.end.character+1)}function vn(t){const e=u.languages.CompletionItemKind;switch(t){case h.Text:return e.Text;case h.Method:return e.Method;case h.Function:return e.Function;case h.Constructor:return e.Constructor;case h.Field:return e.Field;case h.Variable:return e.Variable;case h.Class:return e.Class;case h.Interface:return e.Interface;case h.Module:return e.Module;case h.Property:return e.Property;case h.Unit:return e.Unit;case h.Value:return e.Value;case h.Enum:return e.Enum;case h.Keyword:return e.Keyword;case h.Snippet:return e.Snippet;case h.Color:return e.Color;case h.File:return e.File;case h.Reference:return e.Reference}return e.Property}function F(t){if(t)return{range:b(t.range),text:t.newText}}var Bt=class{constructor(t){this._worker=t}provideHover(t,e,n){let r=t.uri;return this._worker(r).then(o=>o.doHover(r.toString(),x(e))).then(o=>{if(o)return{range:b(o.range),contents:bn(o.contents)}})}};function Kt(t){return typeof t=="string"?{value:t}:(e=t)&&typeof e=="object"&&typeof e.kind=="string"?t.kind==="plaintext"?{value:t.value.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}:{value:t.value}:{value:"```"+t.language+`
`+t.value+"\n```\n"};var e}function bn(t){if(t)return Array.isArray(t)?t.map(Kt):[Kt(t)]}var qt=class{constructor(t){this._worker=t}provideDocumentHighlights(t,e,n){const r=t.uri;return this._worker(r).then(o=>o.findDocumentHighlights(r.toString(),x(e))).then(o=>{if(o)return o.map(a=>({range:b(a.range),kind:_n(a.kind)}))})}};function _n(t){switch(t){case D.Read:return u.languages.DocumentHighlightKind.Read;case D.Write:return u.languages.DocumentHighlightKind.Write;case D.Text:return u.languages.DocumentHighlightKind.Text}return u.languages.DocumentHighlightKind.Text}var xn=class{constructor(t){this._worker=t}provideDefinition(t,e,n){const r=t.uri;return this._worker(r).then(o=>o.findDefinition(r.toString(),x(e))).then(o=>{if(o)return[Qt(o)]})}};function Qt(t){return{uri:u.Uri.parse(t.uri),range:b(t.range)}}var In=class{constructor(t){this._worker=t}provideReferences(t,e,n,r){const o=t.uri;return this._worker(o).then(a=>a.findReferences(o.toString(),x(e))).then(a=>{if(a)return a.map(Qt)})}},Gt=class{constructor(t){this._worker=t}provideRenameEdits(t,e,n,r){const o=t.uri;return this._worker(o).then(a=>a.doRename(o.toString(),x(e),n)).then(a=>function(s){if(!s||!s.changes)return;let c=[];for(let v in s.changes){const d=u.Uri.parse(v);for(let _ of s.changes[v])c.push({resource:d,versionId:void 0,textEdit:{range:b(_.range),text:_.newText}})}return{edits:c}}(a))}},Jt=class{constructor(t){this._worker=t}provideDocumentSymbols(t,e){const n=t.uri;return this._worker(n).then(r=>r.findDocumentSymbols(n.toString())).then(r=>{if(r)return r.map(o=>"children"in o?Yt(o):{name:o.name,detail:"",containerName:o.containerName,kind:Zt(o.kind),range:b(o.location.range),selectionRange:b(o.location.range),tags:[]})})}};function Yt(t){return{name:t.name,detail:t.detail??"",kind:Zt(t.kind),range:b(t.range),selectionRange:b(t.selectionRange),tags:t.tags??[],children:(t.children??[]).map(e=>Yt(e))}}function Zt(t){let e=u.languages.SymbolKind;switch(t){case p.File:return e.File;case p.Module:return e.Module;case p.Namespace:return e.Namespace;case p.Package:return e.Package;case p.Class:return e.Class;case p.Method:return e.Method;case p.Property:return e.Property;case p.Field:return e.Field;case p.Constructor:return e.Constructor;case p.Enum:return e.Enum;case p.Interface:return e.Interface;case p.Function:return e.Function;case p.Variable:return e.Variable;case p.Constant:return e.Constant;case p.String:return e.String;case p.Number:return e.Number;case p.Boolean:return e.Boolean;case p.Array:return e.Array}return e.Function}var en=class{constructor(t){this._worker=t}provideLinks(t,e){const n=t.uri;return this._worker(n).then(r=>r.findDocumentLinks(n.toString())).then(r=>{if(r)return{links:r.map(o=>({range:b(o.range),url:o.target}))}})}},tn=class{constructor(t){this._worker=t}provideDocumentFormattingEdits(t,e,n){const r=t.uri;return this._worker(r).then(o=>o.format(r.toString(),null,rn(e)).then(a=>{if(a&&a.length!==0)return a.map(F)}))}},nn=class{constructor(t){this._worker=t,this.canFormatMultipleRanges=!1}provideDocumentRangeFormattingEdits(t,e,n,r){const o=t.uri;return this._worker(o).then(a=>a.format(o.toString(),$t(e),rn(n)).then(s=>{if(s&&s.length!==0)return s.map(F)}))}};function rn(t){return{tabSize:t.tabSize,insertSpaces:t.insertSpaces}}var En=class{constructor(t){this._worker=t}provideDocumentColors(t,e){const n=t.uri;return this._worker(n).then(r=>r.findDocumentColors(n.toString())).then(r=>{if(r)return r.map(o=>({color:o.color,range:b(o.range)}))})}provideColorPresentations(t,e,n){const r=t.uri;return this._worker(r).then(o=>o.getColorPresentations(r.toString(),e.color,$t(e.range))).then(o=>{if(o)return o.map(a=>{let s={label:a.label};return a.textEdit&&(s.textEdit=F(a.textEdit)),a.additionalTextEdits&&(s.additionalTextEdits=a.additionalTextEdits.map(F)),s})})}},on=class{constructor(t){this._worker=t}provideFoldingRanges(t,e,n){const r=t.uri;return this._worker(r).then(o=>o.getFoldingRanges(r.toString(),e)).then(o=>{if(o)return o.map(a=>{const s={start:a.startLine+1,end:a.endLine+1};return a.kind!==void 0&&(s.kind=function(c){switch(c){case P.Comment:return u.languages.FoldingRangeKind.Comment;case P.Imports:return u.languages.FoldingRangeKind.Imports;case P.Region:return u.languages.FoldingRangeKind.Region}}(a.kind)),s})})}},an=class{constructor(t){this._worker=t}provideSelectionRanges(t,e,n){const r=t.uri;return this._worker(r).then(o=>o.getSelectionRanges(r.toString(),e.map(x))).then(o=>{if(o)return o.map(a=>{const s=[];for(;a;)s.push({range:b(a.range)}),a=a.parent;return s})})}},sn=class extends pn{constructor(t){super(t,[".",":","<",'"',"=","/"])}};function Sn(t){const e=new zt(t),n=(...o)=>e.getLanguageServiceWorker(...o);let r=t.languageId;u.languages.registerCompletionItemProvider(r,new sn(n)),u.languages.registerHoverProvider(r,new Bt(n)),u.languages.registerDocumentHighlightProvider(r,new qt(n)),u.languages.registerLinkProvider(r,new en(n)),u.languages.registerFoldingRangeProvider(r,new on(n)),u.languages.registerDocumentSymbolProvider(r,new Jt(n)),u.languages.registerSelectionRangeProvider(r,new an(n)),u.languages.registerRenameProvider(r,new Gt(n)),r==="html"&&(u.languages.registerDocumentFormattingEditProvider(r,new tn(n)),u.languages.registerDocumentRangeFormattingEditProvider(r,new nn(n)))}function An(t){const e=[],n=[],r=new zt(t);e.push(r);const o=(...a)=>r.getLanguageServiceWorker(...a);return function(){const{languageId:a,modeConfiguration:s}=t;un(n),s.completionItems&&n.push(u.languages.registerCompletionItemProvider(a,new sn(o))),s.hovers&&n.push(u.languages.registerHoverProvider(a,new Bt(o))),s.documentHighlights&&n.push(u.languages.registerDocumentHighlightProvider(a,new qt(o))),s.links&&n.push(u.languages.registerLinkProvider(a,new en(o))),s.documentSymbols&&n.push(u.languages.registerDocumentSymbolProvider(a,new Jt(o))),s.rename&&n.push(u.languages.registerRenameProvider(a,new Gt(o))),s.foldingRanges&&n.push(u.languages.registerFoldingRangeProvider(a,new on(o))),s.selectionRanges&&n.push(u.languages.registerSelectionRangeProvider(a,new an(o))),s.documentFormattingEdits&&n.push(u.languages.registerDocumentFormattingEditProvider(a,new tn(o))),s.documentRangeFormattingEdits&&n.push(u.languages.registerDocumentRangeFormattingEditProvider(a,new nn(o)))}(),e.push(Xt(n)),Xt(e)}function Xt(t){return{dispose:()=>un(t)}}function un(t){for(;t.length;)t.pop().dispose()}export{pn as CompletionAdapter,xn as DefinitionAdapter,yn as DiagnosticsAdapter,En as DocumentColorAdapter,tn as DocumentFormattingEditProvider,qt as DocumentHighlightAdapter,en as DocumentLinkAdapter,nn as DocumentRangeFormattingEditProvider,Jt as DocumentSymbolAdapter,on as FoldingRangeAdapter,Bt as HoverAdapter,In as ReferenceAdapter,Gt as RenameAdapter,an as SelectionRangeAdapter,zt as WorkerManager,x as fromPosition,$t as fromRange,An as setupMode,Sn as setupMode1,b as toRange,F as toTextEdit};
