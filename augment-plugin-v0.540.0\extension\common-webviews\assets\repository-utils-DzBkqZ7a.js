import{f as c,b as u,w as a}from"./legacy-AoIeRrIA.js";var s=c('<svg width="15" height="15" viewBox="-1 0 15 15" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M11.45 7.25C11.9167 7.68333 12.15 8.21667 12.15 8.85C12.15 9.45 11.95 9.98333 11.55 10.45C11.15 10.8833 10.65 11.1333 10.05 11.2C9.45 11.2333 8.9 11.0833 8.4 10.75C7.93333 10.3833 7.63333 9.9 7.5 9.3C6.83333 9.26667 6.18333 9.08333 5.55 8.75C4.81667 8.38333 4.2 7.85 3.7 7.15V9.4C4.06667 9.46667 4.4 9.63333 4.7 9.9C5 10.1333 5.21667 10.4167 5.35 10.75C5.51667 11.0833 5.58333 11.4 5.55 11.7C5.55 12.2 5.41667 12.65 5.15 13.05C4.91667 13.4167 4.56667 13.7 4.1 13.9C3.66667 14.0667 3.21667 14.1 2.75 14C2.28333 13.9 1.88333 13.6833 1.55 13.35C1.21667 13.0167 1 12.6333 0.9 12.2C0.833333 11.7333 0.883333 11.2833 1.05 10.85C1.25 10.3833 1.53333 10.0167 1.9 9.75C2.16667 9.58333 2.45 9.46667 2.75 9.4V4.65C2.28333 4.55 1.88333 4.33333 1.55 4C1.21667 3.66667 1 3.26667 0.9 2.8C0.833333 2.33333 0.883333 1.88333 1.05 1.45C1.25 1.01667 1.53333 0.666666 1.9 0.4C2.3 0.133333 2.73333 0 3.2 0C3.53333 0 3.85 0.0666666 4.15 0.2C4.75 0.433333 5.16667 0.849999 5.4 1.45C5.53333 1.75 5.6 2.06667 5.6 2.4C5.6 2.86667 5.46667 3.3 5.2 3.7C4.93333 4.06667 4.58333 4.35 4.15 4.55C4.01667 4.61667 3.88333 4.65 3.75 4.65C3.85 5.65 4.25 6.5 4.95 7.2C5.65 7.9 6.5 8.3 7.5 8.4C7.53333 8.26667 7.56667 8.13333 7.6 8C7.8 7.56667 8.1 7.21667 8.5 6.95C8.96667 6.65 9.46667 6.53333 10 6.6C10.5667 6.63333 11.05 6.85 11.45 7.25ZM3.75 10.4C3.48333 10.3 3.21667 10.2833 2.95 10.35C2.68333 10.3833 2.43333 10.5167 2.2 10.75C2 10.95 1.86667 11.1833 1.8 11.45C1.76667 11.7167 1.8 11.9833 1.9 12.25C2.03333 12.5167 2.21667 12.7333 2.45 12.9C2.71667 13.0667 3.01667 13.1333 3.35 13.1C3.68333 13.0667 3.96667 12.9333 4.2 12.7C4.43333 12.4667 4.56667 12.1833 4.6 11.85C4.63333 11.5167 4.56667 11.2333 4.4 11C4.23333 10.7333 4.01667 10.5333 3.75 10.4ZM3.2 3.75C3.56667 3.75 3.86667 3.65 4.1 3.45C4.36667 3.21667 4.53333 2.93333 4.6 2.6C4.66667 2.26667 4.61667 1.96667 4.45 1.7C4.28333 1.4 4.05 1.2 3.75 1.1C3.48333 0.966667 3.21667 0.933333 2.95 1C2.68333 1.03333 2.43333 1.15 2.2 1.35C2 1.55 1.86667 1.8 1.8 2.1C1.76667 2.36667 1.8 2.63333 1.9 2.9C2.03333 3.13333 2.21667 3.33333 2.45 3.5C2.68333 3.66667 2.93333 3.75 3.2 3.75ZM10.8 9.9C10.9667 9.73333 11.0833 9.53333 11.15 9.3C11.2167 9.06667 11.2167 8.85 11.15 8.65C11.1167 8.41667 11.0167 8.21667 10.85 8.05C10.7167 7.85 10.5333 7.7 10.3 7.6C10.0667 7.5 9.8 7.48333 9.5 7.55C9.23333 7.58333 9 7.7 8.8 7.9C8.6 8.1 8.46667 8.35 8.4 8.65C8.33333 8.91667 8.35 9.16667 8.45 9.4C8.55 9.63333 8.68333 9.83333 8.85 10C9.05 10.1333 9.26667 10.2333 9.5 10.3C9.73333 10.3333 9.96667 10.3167 10.2 10.25C10.4333 10.1833 10.6333 10.0667 10.8 9.9Z"></path></svg>');function p(C){var t=s();u(C,t)}function o(C){return C.replace(/\.git$/,"")}function l(C){if(!C)return"";const t=C.match(/github\.com\/([^/]+)\/([^/]+)/);if(t)return t[2].replace(/\.git$/,"");const n=C.split("/").filter(Boolean);return(n.length>0?n[n.length-1]:"").replace(/\.git$/,"")}function f(C){if(!C)return"";const t=C.match(/github\.com\/([^/]+)\/([^/]+)/);if(t)return`${t[1]}/${t[2].replace(/\.git$/,"")}`;const n=C.split("/").filter(Boolean);return n.length>1?`${n[n.length-2]}/${n[n.length-1].replace(/\.git$/,"")}`:n.length>0?n[n.length-1].replace(/\.git$/,""):""}function r(C){var t,n,e;return((e=(n=(t=C.workspace_setup)==null?void 0:t.starting_files)==null?void 0:n.github_commit_ref)==null?void 0:e.repository_url)||""}function h(C){if(!C||typeof C!="object"||!("workspace_setup"in C))return{name:"Unknown",fullPath:"Unknown"};const t=r(C);return{name:l(t),fullPath:f(t)}}function w(C,t){if(!C||typeof C!="object"||!("workspace_setup"in C))return!1;const n=r(C);return!!n&&n!==t}function m(C){return a(C,t=>n=>{if(!t)return!0;const e=o(t),i=o(r(n));return!!i&&!!e&&i!==e})}export{p as B,f as a,w as b,h as c,l as g,m as i};
