var eu=Object.defineProperty;var tu=(e,t,n)=>t in e?eu(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var f=(e,t,n)=>tu(e,typeof t!="symbol"?t+"":t,n);import{a as nu}from"./async-messaging-Dmg2N9Pf.js";import{W as k,S as ru,b as Ze}from"./host-qgbK079d.js";import{E as pe,S as De,R as Oe,P as Se,C as su}from"./chat-types-BfwvR7Kn.js";import{n as kr,p as F,g as ae,b as Le,a as et,e as au,h as Eo,l as X,f as gt,T as To,t as Aa,s as iu,k as wo}from"./SpinnerAugment-mywmfXFR.js";import{n as ou,F as xa}from"./focusTrapStack-CaEmYw0i.js";import{Z as Qn,f as ko,b as U,aT as cs,x as ve,Y as ta,X as se,I as Fe,J as Pt,K as we,a8 as Me,ag as lu,y as oe,o as he,N as Lt,a1 as Dt,G as be,Q as st,P as $e,m as Ue,z as q,B as Ne,D as en,u as yt,E as tn,L as We,A as Ra,R as ds,H as No,a as Io,aU as Oa,V as uu,W as cu,F as du,w as pu,a5 as hu,a3 as mu}from"./legacy-AoIeRrIA.js";import{a as fu,c as gu,_ as yu,i as na}from"./isObjectLike-CnrzNkq5.js";import{a as ps,B as _u,h as vu}from"./IconButtonAugment-DZyIKjh7.js";import{c as bu,e as Gn,f as Su,C as Eu,R as Tu,b as It,a as qn,g as Ma}from"./CardAugment-DwIptXof.js";import{b as wu}from"./input-DCBQtNgo.js";import{B as ku}from"./BaseTextInput-D2MYbf3a.js";function ra(e,t){return!(e===null||typeof e!="object"||!("$typeName"in e)||typeof e.$typeName!="string")&&(t===void 0||t.typeName===e.$typeName)}var _;function Nu(){let e=0,t=0;for(let r=0;r<28;r+=7){let s=this.buf[this.pos++];if(e|=(127&s)<<r,!(128&s))return this.assertBounds(),[e,t]}let n=this.buf[this.pos++];if(e|=(15&n)<<28,t=(112&n)>>4,!(128&n))return this.assertBounds(),[e,t];for(let r=3;r<=31;r+=7){let s=this.buf[this.pos++];if(t|=(127&s)<<r,!(128&s))return this.assertBounds(),[e,t]}throw new Error("invalid varint")}function Rr(e,t,n){for(let a=0;a<28;a+=7){const i=e>>>a,o=!(!(i>>>7)&&t==0),u=255&(o?128|i:i);if(n.push(u),!o)return}const r=e>>>28&15|(7&t)<<4,s=!!(t>>3);if(n.push(255&(s?128|r:r)),s){for(let a=3;a<31;a+=7){const i=t>>>a,o=!!(i>>>7),u=255&(o?128|i:i);if(n.push(u),!o)return}n.push(t>>>31&1)}}(function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"})(_||(_={}));const Zn=4294967296;function Pa(e){const t=e[0]==="-";t&&(e=e.slice(1));const n=1e6;let r=0,s=0;function a(i,o){const u=Number(e.slice(i,o));s*=n,r=r*n+u,r>=Zn&&(s+=r/Zn|0,r%=Zn)}return a(-24,-18),a(-18,-12),a(-12,-6),a(-6),t?Co(r,s):sa(r,s)}function La(e,t){if({lo:e,hi:t}=function(u,l){return{lo:u>>>0,hi:l>>>0}}(e,t),t<=2097151)return String(Zn*t+e);const n=16777215&(e>>>24|t<<8),r=t>>16&65535;let s=(16777215&e)+6777216*n+6710656*r,a=n+8147497*r,i=2*r;const o=1e7;return s>=o&&(a+=Math.floor(s/o),s%=o),a>=o&&(i+=Math.floor(a/o),a%=o),i.toString()+Da(a)+Da(s)}function sa(e,t){return{lo:0|e,hi:0|t}}function Co(e,t){return t=~t,e?e=1+~e:t+=1,sa(e,t)}const Da=e=>{const t=String(e);return"0000000".slice(t.length)+t};function Fa(e,t){if(e>=0){for(;e>127;)t.push(127&e|128),e>>>=7;t.push(e)}else{for(let n=0;n<9;n++)t.push(127&e|128),e>>=7;t.push(1)}}function Iu(){let e=this.buf[this.pos++],t=127&e;if(!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<7,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<14,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<21,!(128&e))return this.assertBounds(),t;e=this.buf[this.pos++],t|=(15&e)<<28;for(let n=5;128&e&&n<10;n++)e=this.buf[this.pos++];if(128&e)throw new Error("invalid varint");return this.assertBounds(),t>>>0}var $a={};const V=Cu();function Cu(){const e=new DataView(new ArrayBuffer(8));if(typeof BigInt=="function"&&typeof e.getBigInt64=="function"&&typeof e.getBigUint64=="function"&&typeof e.setBigInt64=="function"&&typeof e.setBigUint64=="function"&&(typeof process!="object"||typeof $a!="object"||$a.BUF_BIGINT_DISABLE!=="1")){const t=BigInt("-9223372036854775808"),n=BigInt("9223372036854775807"),r=BigInt("0"),s=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(a){const i=typeof a=="bigint"?a:BigInt(a);if(i>n||i<t)throw new Error(`invalid int64: ${a}`);return i},uParse(a){const i=typeof a=="bigint"?a:BigInt(a);if(i>s||i<r)throw new Error(`invalid uint64: ${a}`);return i},enc(a){return e.setBigInt64(0,this.parse(a),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},uEnc(a){return e.setBigInt64(0,this.uParse(a),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},dec:(a,i)=>(e.setInt32(0,a,!0),e.setInt32(4,i,!0),e.getBigInt64(0,!0)),uDec:(a,i)=>(e.setInt32(0,a,!0),e.setInt32(4,i,!0),e.getBigUint64(0,!0))}}return{zero:"0",supported:!1,parse:t=>(typeof t!="string"&&(t=t.toString()),Ua(t),t),uParse:t=>(typeof t!="string"&&(t=t.toString()),Ba(t),t),enc:t=>(typeof t!="string"&&(t=t.toString()),Ua(t),Pa(t)),uEnc:t=>(typeof t!="string"&&(t=t.toString()),Ba(t),Pa(t)),dec:(t,n)=>function(r,s){let a=sa(r,s);const i=2147483648&a.hi;i&&(a=Co(a.lo,a.hi));const o=La(a.lo,a.hi);return i?"-"+o:o}(t,n),uDec:(t,n)=>La(t,n)}}function Ua(e){if(!/^-?[0-9]+$/.test(e))throw new Error("invalid int64: "+e)}function Ba(e){if(!/^[0-9]+$/.test(e))throw new Error("invalid uint64: "+e)}function vt(e,t){switch(e){case _.STRING:return"";case _.BOOL:return!1;case _.DOUBLE:case _.FLOAT:return 0;case _.INT64:case _.UINT64:case _.SFIXED64:case _.FIXED64:case _.SINT64:return t?"0":V.zero;case _.BYTES:return new Uint8Array(0);default:return 0}}const ze=Symbol.for("reflect unsafe local");function Ao(e,t){const n=e[t.localName].case;return n===void 0?n:t.fields.find(r=>r.localName===n)}function Au(e,t){const n=t.localName;if(t.oneof)return e[t.oneof.localName].case===n;if(t.presence!=2)return e[n]!==void 0&&Object.prototype.hasOwnProperty.call(e,n);switch(t.fieldKind){case"list":return e[n].length>0;case"map":return Object.keys(e[n]).length>0;case"scalar":return!function(r,s){switch(r){case _.BOOL:return s===!1;case _.STRING:return s==="";case _.BYTES:return s instanceof Uint8Array&&!s.byteLength;default:return s==0}}(t.scalar,e[n]);case"enum":return e[n]!==t.enum.values[0].number}throw new Error("message field with implicit presence")}function nn(e,t){return Object.prototype.hasOwnProperty.call(e,t)&&e[t]!==void 0}function xo(e,t){if(t.oneof){const n=e[t.oneof.localName];return n.case===t.localName?n.value:void 0}return e[t.localName]}function Ro(e,t,n){t.oneof?e[t.oneof.localName]={case:t.localName,value:n}:e[t.localName]=n}function ot(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)}function aa(e,t){var n,r,s,a;if(ot(e)&&ze in e&&"add"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const i=t,o=e.field();return i.listKind==o.listKind&&i.scalar===o.scalar&&((n=i.message)===null||n===void 0?void 0:n.typeName)===((r=o.message)===null||r===void 0?void 0:r.typeName)&&((s=i.enum)===null||s===void 0?void 0:s.typeName)===((a=o.enum)===null||a===void 0?void 0:a.typeName)}return!0}return!1}function ia(e,t){var n,r,s,a;if(ot(e)&&ze in e&&"has"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const i=t,o=e.field();return i.mapKey===o.mapKey&&i.mapKind==o.mapKind&&i.scalar===o.scalar&&((n=i.message)===null||n===void 0?void 0:n.typeName)===((r=o.message)===null||r===void 0?void 0:r.typeName)&&((s=i.enum)===null||s===void 0?void 0:s.typeName)===((a=o.enum)===null||a===void 0?void 0:a.typeName)}return!0}return!1}function oa(e,t){return ot(e)&&ze in e&&"desc"in e&&ot(e.desc)&&e.desc.kind==="message"&&(t===void 0||e.desc.typeName==t.typeName)}function Cn(e){const t=e.fields[0];return Oo(e.typeName)&&t!==void 0&&t.fieldKind=="scalar"&&t.name=="value"&&t.number==1}function Oo(e){return e.startsWith("google.protobuf.")&&["DoubleValue","FloatValue","Int64Value","UInt64Value","Int32Value","UInt32Value","BoolValue","StringValue","BytesValue"].includes(e.substring(16))}const xu=999,Ru=998,On=2;function xe(e,t){if(ra(t,e))return t;const n=function(r){let s;if(function(a){switch(a.file.edition){case xu:return!1;case Ru:return!0;default:return a.fields.some(i=>i.presence!=On&&i.fieldKind!="message"&&!i.oneof)}}(r)){const a=Va.get(r);let i,o;if(a)({prototype:i,members:o}=a);else{i={},o=new Set;for(const u of r.members)u.kind!="oneof"&&(u.fieldKind!="scalar"&&u.fieldKind!="enum"||u.presence!=On&&(o.add(u),i[u.localName]=Or(u)));Va.set(r,{prototype:i,members:o})}s=Object.create(i),s.$typeName=r.typeName;for(const u of r.members)if(!o.has(u)){if(u.kind=="field"&&(u.fieldKind=="message"||(u.fieldKind=="scalar"||u.fieldKind=="enum")&&u.presence!=On))continue;s[u.localName]=Or(u)}}else{s={$typeName:r.typeName};for(const a of r.members)a.kind!="oneof"&&a.presence!=On||(s[a.localName]=Or(a))}return s}(e);return t!==void 0&&function(r,s,a){for(const i of r.members){let o,u=a[i.localName];if(u!=null){if(i.kind=="oneof"){const l=Ao(a,i);if(!l)continue;o=l,u=xo(a,l)}else o=i;switch(o.fieldKind){case"message":u=la(o,u);break;case"scalar":u=Mo(o,u);break;case"list":u=Mu(o,u);break;case"map":u=Ou(o,u)}Ro(s,o,u)}}}(e,n,t),n}function Mo(e,t){return e.scalar==_.BYTES?ua(t):t}function Ou(e,t){if(ot(t)){if(e.scalar==_.BYTES)return ja(t,ua);if(e.mapKind=="message")return ja(t,n=>la(e,n))}return t}function Mu(e,t){if(Array.isArray(t)){if(e.scalar==_.BYTES)return t.map(ua);if(e.listKind=="message")return t.map(n=>la(e,n))}return t}function la(e,t){if(e.fieldKind=="message"&&!e.oneof&&Cn(e.message))return Mo(e.message.fields[0],t);if(ot(t)){if(e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!=="google.protobuf.Value")return t;if(!ra(t,e.message))return xe(e.message,t)}return t}function ua(e){return Array.isArray(e)?new Uint8Array(e):e}function ja(e,t){const n={};for(const r of Object.entries(e))n[r[0]]=t(r[1]);return n}const Pu=Symbol(),Va=new WeakMap;function Or(e){if(e.kind=="oneof")return{case:void 0};if(e.fieldKind=="list")return[];if(e.fieldKind=="map")return{};if(e.fieldKind=="message")return Pu;const t=e.getDefaultValue();return t!==void 0?e.fieldKind=="scalar"&&e.longAsString?t.toString():t:e.fieldKind=="scalar"?vt(e.scalar,e.longAsString):e.enum.values[0].number}const Lu=["FieldValueInvalidError","FieldListRangeError","ForeignFieldError"];class ue extends Error{constructor(t,n,r="FieldValueInvalidError"){super(n),this.name=r,this.field=()=>t}}const Mr=Symbol.for("@bufbuild/protobuf/text-encoding");function ca(){if(globalThis[Mr]==null){const e=new globalThis.TextEncoder,t=new globalThis.TextDecoder;globalThis[Mr]={encodeUtf8:n=>e.encode(n),decodeUtf8:n=>t.decode(n),checkUtf8(n){try{return encodeURIComponent(n),!0}catch{return!1}}}}return globalThis[Mr]}var K;(function(e){e[e.Varint=0]="Varint",e[e.Bit64=1]="Bit64",e[e.LengthDelimited=2]="LengthDelimited",e[e.StartGroup=3]="StartGroup",e[e.EndGroup=4]="EndGroup",e[e.Bit32=5]="Bit32"})(K||(K={}));const Po=34028234663852886e22,Lo=-34028234663852886e22,Do=4294967295,Fo=2147483647,$o=-2147483648;class da{constructor(t=ca().encodeUtf8){this.encodeUtf8=t,this.stack=[],this.chunks=[],this.buf=[]}finish(){this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]);let t=0;for(let s=0;s<this.chunks.length;s++)t+=this.chunks[s].length;let n=new Uint8Array(t),r=0;for(let s=0;s<this.chunks.length;s++)n.set(this.chunks[s],r),r+=this.chunks[s].length;return this.chunks=[],n}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let t=this.finish(),n=this.stack.pop();if(!n)throw new Error("invalid state, fork stack empty");return this.chunks=n.chunks,this.buf=n.buf,this.uint32(t.byteLength),this.raw(t)}tag(t,n){return this.uint32((t<<3|n)>>>0)}raw(t){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(t),this}uint32(t){for(Ga(t);t>127;)this.buf.push(127&t|128),t>>>=7;return this.buf.push(t),this}int32(t){return Pr(t),Fa(t,this.buf),this}bool(t){return this.buf.push(t?1:0),this}bytes(t){return this.uint32(t.byteLength),this.raw(t)}string(t){let n=this.encodeUtf8(t);return this.uint32(n.byteLength),this.raw(n)}float(t){(function(r){if(typeof r=="string"){const s=r;if(r=Number(r),Number.isNaN(r)&&s!=="NaN")throw new Error("invalid float32: "+s)}else if(typeof r!="number")throw new Error("invalid float32: "+typeof r);if(Number.isFinite(r)&&(r>Po||r<Lo))throw new Error("invalid float32: "+r)})(t);let n=new Uint8Array(4);return new DataView(n.buffer).setFloat32(0,t,!0),this.raw(n)}double(t){let n=new Uint8Array(8);return new DataView(n.buffer).setFloat64(0,t,!0),this.raw(n)}fixed32(t){Ga(t);let n=new Uint8Array(4);return new DataView(n.buffer).setUint32(0,t,!0),this.raw(n)}sfixed32(t){Pr(t);let n=new Uint8Array(4);return new DataView(n.buffer).setInt32(0,t,!0),this.raw(n)}sint32(t){return Pr(t),Fa(t=(t<<1^t>>31)>>>0,this.buf),this}sfixed64(t){let n=new Uint8Array(8),r=new DataView(n.buffer),s=V.enc(t);return r.setInt32(0,s.lo,!0),r.setInt32(4,s.hi,!0),this.raw(n)}fixed64(t){let n=new Uint8Array(8),r=new DataView(n.buffer),s=V.uEnc(t);return r.setInt32(0,s.lo,!0),r.setInt32(4,s.hi,!0),this.raw(n)}int64(t){let n=V.enc(t);return Rr(n.lo,n.hi,this.buf),this}sint64(t){const n=V.enc(t),r=n.hi>>31;return Rr(n.lo<<1^r,(n.hi<<1|n.lo>>>31)^r,this.buf),this}uint64(t){const n=V.uEnc(t);return Rr(n.lo,n.hi,this.buf),this}}class pa{constructor(t,n=ca().decodeUtf8){this.decodeUtf8=n,this.varint64=Nu,this.uint32=Iu,this.buf=t,this.len=t.length,this.pos=0,this.view=new DataView(t.buffer,t.byteOffset,t.byteLength)}tag(){let t=this.uint32(),n=t>>>3,r=7&t;if(n<=0||r<0||r>5)throw new Error("illegal tag: field no "+n+" wire type "+r);return[n,r]}skip(t,n){let r=this.pos;switch(t){case K.Varint:for(;128&this.buf[this.pos++];);break;case K.Bit64:this.pos+=4;case K.Bit32:this.pos+=4;break;case K.LengthDelimited:let s=this.uint32();this.pos+=s;break;case K.StartGroup:for(;;){const[a,i]=this.tag();if(i===K.EndGroup){if(n!==void 0&&a!==n)throw new Error("invalid end group tag");break}this.skip(i,a)}break;default:throw new Error("cant skip wire type "+t)}return this.assertBounds(),this.buf.subarray(r,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let t=this.uint32();return t>>>1^-(1&t)}int64(){return V.dec(...this.varint64())}uint64(){return V.uDec(...this.varint64())}sint64(){let[t,n]=this.varint64(),r=-(1&t);return t=(t>>>1|(1&n)<<31)^r,n=n>>>1^r,V.dec(t,n)}bool(){let[t,n]=this.varint64();return t!==0||n!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return V.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return V.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let t=this.uint32(),n=this.pos;return this.pos+=t,this.assertBounds(),this.buf.subarray(n,n+t)}string(){return this.decodeUtf8(this.bytes())}}function Pr(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid int32: "+typeof e);if(!Number.isInteger(e)||e>Fo||e<$o)throw new Error("invalid int32: "+e)}function Ga(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid uint32: "+typeof e);if(!Number.isInteger(e)||e>Do||e<0)throw new Error("invalid uint32: "+e)}function ht(e,t){const n=e.fieldKind=="list"?aa(t,e):e.fieldKind=="map"?ia(t,e):ha(e,t);if(n===!0)return;let r;switch(e.fieldKind){case"list":r=`expected ${jo(e)}, got ${Y(t)}`;break;case"map":r=`expected ${Vo(e)}, got ${Y(t)}`;break;default:r=er(e,t,n)}return new ue(e,r)}function qa(e,t,n){const r=ha(e,n);if(r!==!0)return new ue(e,`list item #${t+1}: ${er(e,n,r)}`)}function ha(e,t){return e.scalar!==void 0?Uo(t,e.scalar):e.enum!==void 0?e.enum.open?Number.isInteger(t):e.enum.values.some(n=>n.number===t):oa(t,e.message)}function Uo(e,t){switch(t){case _.DOUBLE:return typeof e=="number";case _.FLOAT:return typeof e=="number"&&(!(!Number.isNaN(e)&&Number.isFinite(e))||!(e>Po||e<Lo)||`${e.toFixed()} out of range`);case _.INT32:case _.SFIXED32:case _.SINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>Fo||e<$o)||`${e.toFixed()} out of range`);case _.FIXED32:case _.UINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>Do||e<0)||`${e.toFixed()} out of range`);case _.BOOL:return typeof e=="boolean";case _.STRING:return typeof e=="string"&&(ca().checkUtf8(e)||"invalid UTF8");case _.BYTES:return e instanceof Uint8Array;case _.INT64:case _.SFIXED64:case _.SINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return V.parse(e),!0}catch{return`${e} out of range`}return!1;case _.FIXED64:case _.UINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return V.uParse(e),!0}catch{return`${e} out of range`}return!1}}function er(e,t,n){return n=typeof n=="string"?`: ${n}`:`, got ${Y(t)}`,e.scalar!==void 0?`expected ${function(r){switch(r){case _.STRING:return"string";case _.BOOL:return"boolean";case _.INT64:case _.SINT64:case _.SFIXED64:return"bigint (int64)";case _.UINT64:case _.FIXED64:return"bigint (uint64)";case _.BYTES:return"Uint8Array";case _.DOUBLE:return"number (float64)";case _.FLOAT:return"number (float32)";case _.FIXED32:case _.UINT32:return"number (uint32)";case _.INT32:case _.SFIXED32:case _.SINT32:return"number (int32)"}}(e.scalar)}`+n:e.enum!==void 0?`expected ${e.enum.toString()}`+n:`expected ${Bo(e.message)}`+n}function Y(e){switch(typeof e){case"object":return e===null?"null":e instanceof Uint8Array?`Uint8Array(${e.length})`:Array.isArray(e)?`Array(${e.length})`:aa(e)?jo(e.field()):ia(e)?Vo(e.field()):oa(e)?Bo(e.desc):ra(e)?`message ${e.$typeName}`:"object";case"string":return e.length>30?"string":`"${e.split('"').join('\\"')}"`;case"boolean":case"number":return String(e);case"bigint":return String(e)+"n";default:return typeof e}}function Bo(e){return`ReflectMessage (${e.typeName})`}function jo(e){switch(e.listKind){case"message":return`ReflectList (${e.message.toString()})`;case"enum":return`ReflectList (${e.enum.toString()})`;case"scalar":return`ReflectList (${_[e.scalar]})`}}function Vo(e){switch(e.mapKind){case"message":return`ReflectMap (${_[e.mapKey]}, ${e.message.toString()})`;case"enum":return`ReflectMap (${_[e.mapKey]}, ${e.enum.toString()})`;case"scalar":return`ReflectMap (${_[e.mapKey]}, ${_[e.scalar]})`}}function Ee(e,t,n=!0){return new Go(e,t,n)}class Go{get sortedFields(){var t;return(t=this._sortedFields)!==null&&t!==void 0?t:this._sortedFields=this.desc.fields.concat().sort((n,r)=>n.number-r.number)}constructor(t,n,r=!0){this.lists=new Map,this.maps=new Map,this.check=r,this.desc=t,this.message=this[ze]=n??xe(t),this.fields=t.fields,this.oneofs=t.oneofs,this.members=t.members}findNumber(t){return this._fieldsByNumber||(this._fieldsByNumber=new Map(this.desc.fields.map(n=>[n.number,n]))),this._fieldsByNumber.get(t)}oneofCase(t){return Zt(this.message,t),Ao(this.message,t)}isSet(t){return Zt(this.message,t),Au(this.message,t)}clear(t){Zt(this.message,t),function(n,r){const s=r.localName;if(r.oneof){const a=r.oneof.localName;n[a].case===s&&(n[a]={case:void 0})}else if(r.presence!=2)delete n[s];else switch(r.fieldKind){case"map":n[s]={};break;case"list":n[s]=[];break;case"enum":n[s]=r.enum.values[0].number;break;case"scalar":n[s]=vt(r.scalar,r.longAsString)}}(this.message,t)}get(t){Zt(this.message,t);const n=xo(this.message,t);switch(t.fieldKind){case"list":let r=this.lists.get(t);return r&&r[ze]===n||this.lists.set(t,r=new Du(t,n,this.check)),r;case"map":let s=this.maps.get(t);return s&&s[ze]===n||this.maps.set(t,s=new Fu(t,n,this.check)),s;case"message":return fa(t,n,this.check);case"scalar":return n===void 0?vt(t.scalar,!1):ga(t,n);case"enum":return n??t.enum.values[0].number}}set(t,n){if(Zt(this.message,t),this.check){const s=ht(t,n);if(s)throw s}let r;r=t.fieldKind=="message"?ma(t,n):ia(n)||aa(n)?n[ze]:ya(t,n),Ro(this.message,t,r)}getUnknown(){return this.message.$unknown}setUnknown(t){this.message.$unknown=t}}function Zt(e,t){if(t.parent.typeName!==e.$typeName)throw new ue(t,`cannot use ${t.toString()} with message ${e.$typeName}`,"ForeignFieldError")}class Du{field(){return this._field}get size(){return this._arr.length}constructor(t,n,r){this._field=t,this._arr=this[ze]=n,this.check=r}get(t){const n=this._arr[t];return n===void 0?void 0:Lr(this._field,n,this.check)}set(t,n){if(t<0||t>=this._arr.length)throw new ue(this._field,`list item #${t+1}: out of range`);if(this.check){const r=qa(this._field,t,n);if(r)throw r}this._arr[t]=Za(this._field,n)}add(t){if(this.check){const n=qa(this._field,this._arr.length,t);if(n)throw n}this._arr.push(Za(this._field,t))}clear(){this._arr.splice(0,this._arr.length)}[Symbol.iterator](){return this.values()}keys(){return this._arr.keys()}*values(){for(const t of this._arr)yield Lr(this._field,t,this.check)}*entries(){for(let t=0;t<this._arr.length;t++)yield[t,Lr(this._field,this._arr[t],this.check)]}}class Fu{constructor(t,n,r=!0){this.obj=this[ze]=n??{},this.check=r,this._field=t}field(){return this._field}set(t,n){if(this.check){const r=function(s,a,i){const o=Uo(a,s.mapKey);if(o!==!0)return new ue(s,`invalid map key: ${er({scalar:s.mapKey},a,o)}`);const u=ha(s,i);return u!==!0?new ue(s,`map entry ${Y(a)}: ${er(s,i,u)}`):void 0}(this._field,t,n);if(r)throw r}return this.obj[Mn(t)]=function(r,s){return r.mapKind=="message"?ma(r,s):ya(r,s)}(this._field,n),this}delete(t){const n=Mn(t),r=Object.prototype.hasOwnProperty.call(this.obj,n);return r&&delete this.obj[n],r}clear(){for(const t of Object.keys(this.obj))delete this.obj[t]}get(t){let n=this.obj[Mn(t)];return n!==void 0&&(n=Dr(this._field,n,this.check)),n}has(t){return Object.prototype.hasOwnProperty.call(this.obj,Mn(t))}*keys(){for(const t of Object.keys(this.obj))yield Ka(t,this._field.mapKey)}*entries(){for(const t of Object.entries(this.obj))yield[Ka(t[0],this._field.mapKey),Dr(this._field,t[1],this.check)]}[Symbol.iterator](){return this.entries()}get size(){return Object.keys(this.obj).length}*values(){for(const t of Object.values(this.obj))yield Dr(this._field,t,this.check)}forEach(t,n){for(const r of this.entries())t.call(n,r[1],r[0],this)}}function ma(e,t){return oa(t)?Oo(t.message.$typeName)&&!e.oneof&&e.fieldKind=="message"?t.message.value:t.desc.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"?Zo(t.message):t.message:t}function fa(e,t,n){return t!==void 0&&(Cn(e.message)&&!e.oneof&&e.fieldKind=="message"?t={$typeName:e.message.typeName,value:ga(e.message.fields[0],t)}:e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"&&ot(t)&&(t=qo(t))),new Go(e.message,t,n)}function Za(e,t){return e.listKind=="message"?ma(e,t):ya(e,t)}function Lr(e,t,n){return e.listKind=="message"?fa(e,t,n):ga(e,t)}function Dr(e,t,n){return e.mapKind=="message"?fa(e,t,n):t}function Mn(e){return typeof e=="string"||typeof e=="number"?e:String(e)}function Ka(e,t){switch(t){case _.STRING:return e;case _.INT32:case _.FIXED32:case _.UINT32:case _.SFIXED32:case _.SINT32:{const n=Number.parseInt(e);if(Number.isFinite(n))return n;break}case _.BOOL:switch(e){case"true":return!0;case"false":return!1}break;case _.UINT64:case _.FIXED64:try{return V.uParse(e)}catch{}break;default:try{return V.parse(e)}catch{}}return e}function ga(e,t){switch(e.scalar){case _.INT64:case _.SFIXED64:case _.SINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=V.parse(t));break;case _.FIXED64:case _.UINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=V.uParse(t))}return t}function ya(e,t){switch(e.scalar){case _.INT64:case _.SFIXED64:case _.SINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=V.parse(t));break;case _.FIXED64:case _.UINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=V.uParse(t))}return t}function qo(e){const t={$typeName:"google.protobuf.Struct",fields:{}};if(ot(e))for(const[n,r]of Object.entries(e))t.fields[n]=Yo(r);return t}function Zo(e){const t={};for(const[n,r]of Object.entries(e.fields))t[n]=Ko(r);return t}function Ko(e){switch(e.kind.case){case"structValue":return Zo(e.kind.value);case"listValue":return e.kind.value.values.map(Ko);case"nullValue":case void 0:return null;default:return e.kind.value}}function Yo(e){const t={$typeName:"google.protobuf.Value",kind:{case:void 0}};switch(typeof e){case"number":t.kind={case:"numberValue",value:e};break;case"string":t.kind={case:"stringValue",value:e};break;case"boolean":t.kind={case:"boolValue",value:e};break;case"object":if(e===null)t.kind={case:"nullValue",value:0};else if(Array.isArray(e)){const n={$typeName:"google.protobuf.ListValue",values:[]};if(Array.isArray(e))for(const r of e)n.values.push(Yo(r));t.kind={case:"listValue",value:n}}else t.kind={case:"structValue",value:qo(e)}}return t}function Ho(e){const t=function(){if(!Ct){Ct=[];const u=Wo("std");for(let l=0;l<u.length;l++)Ct[u[l].charCodeAt(0)]=l;Ct[45]=u.indexOf("+"),Ct[95]=u.indexOf("/")}return Ct}();let n=3*e.length/4;e[e.length-2]=="="?n-=2:e[e.length-1]=="="&&(n-=1);let r,s=new Uint8Array(n),a=0,i=0,o=0;for(let u=0;u<e.length;u++){if(r=t[e.charCodeAt(u)],r===void 0)switch(e[u]){case"=":i=0;case`
`:case"\r":case"	":case" ":continue;default:throw Error("invalid base64 string")}switch(i){case 0:o=r,i=1;break;case 1:s[a++]=o<<2|(48&r)>>4,o=r,i=2;break;case 2:s[a++]=(15&o)<<4|(60&r)>>2,o=r,i=3;break;case 3:s[a++]=(3&o)<<6|r,i=0}}if(i==1)throw Error("invalid base64 string");return s.subarray(0,a)}let Pn,Ya,Ct;function Wo(e){return Pn||(Pn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),Ya=Pn.slice(0,-2).concat("-","_")),e=="url"?Ya:Pn}function cn(e){let t=!1;const n=[];for(let r=0;r<e.length;r++){let s=e.charAt(r);switch(s){case"_":t=!0;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n.push(s),t=!1;break;default:t&&(t=!1,s=s.toUpperCase()),n.push(s)}}return n.join("")}const $u=new Set(["constructor","toString","toJSON","valueOf"]);function dn(e){return $u.has(e)?e+"$":e}function _a(e){for(const t of e.field)nn(t,"jsonName")||(t.jsonName=cn(t.name));e.nestedType.forEach(_a)}function Uu(e,t){switch(e){case _.STRING:return t;case _.BYTES:{const n=function(r){const s=[],a={tail:r,c:"",next(){return this.tail.length!=0&&(this.c=this.tail[0],this.tail=this.tail.substring(1),!0)},take(i){if(this.tail.length>=i){const o=this.tail.substring(0,i);return this.tail=this.tail.substring(i),o}return!1}};for(;a.next();)if(a.c==="\\"){if(a.next())switch(a.c){case"\\":s.push(a.c.charCodeAt(0));break;case"b":s.push(8);break;case"f":s.push(12);break;case"n":s.push(10);break;case"r":s.push(13);break;case"t":s.push(9);break;case"v":s.push(11);break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":{const i=a.c,o=a.take(2);if(o===!1)return!1;const u=parseInt(i+o,8);if(Number.isNaN(u))return!1;s.push(u);break}case"x":{const i=a.c,o=a.take(2);if(o===!1)return!1;const u=parseInt(i+o,16);if(Number.isNaN(u))return!1;s.push(u);break}case"u":{const i=a.c,o=a.take(4);if(o===!1)return!1;const u=parseInt(i+o,16);if(Number.isNaN(u))return!1;const l=new Uint8Array(4);new DataView(l.buffer).setInt32(0,u,!0),s.push(l[0],l[1],l[2],l[3]);break}case"U":{const i=a.c,o=a.take(8);if(o===!1)return!1;const u=V.uEnc(i+o),l=new Uint8Array(8),d=new DataView(l.buffer);d.setInt32(0,u.lo,!0),d.setInt32(4,u.hi,!0),s.push(l[0],l[1],l[2],l[3],l[4],l[5],l[6],l[7]);break}}}else s.push(a.c.charCodeAt(0));return new Uint8Array(s)}(t);if(n===!1)throw new Error(`cannot parse ${_[e]} default value: ${t}`);return n}case _.INT64:case _.SFIXED64:case _.SINT64:return V.parse(t);case _.UINT64:case _.FIXED64:return V.uParse(t);case _.DOUBLE:case _.FLOAT:switch(t){case"inf":return Number.POSITIVE_INFINITY;case"-inf":return Number.NEGATIVE_INFINITY;case"nan":return Number.NaN;default:return parseFloat(t)}case _.BOOL:return t==="true";case _.INT32:case _.UINT32:case _.SINT32:case _.FIXED32:case _.SFIXED32:return parseInt(t,10)}}function*hs(e){switch(e.kind){case"file":for(const t of e.messages)yield t,yield*hs(t);yield*e.enums,yield*e.services,yield*e.extensions;break;case"message":for(const t of e.nestedMessages)yield t,yield*hs(t);yield*e.nestedEnums,yield*e.nestedExtensions}}function zo(...e){const t=function(){const n=new Map,r=new Map,s=new Map;return{kind:"registry",types:n,extendees:r,[Symbol.iterator]:()=>n.values(),get files(){return s.values()},addFile(a,i,o){if(s.set(a.proto.name,a),!i)for(const u of hs(a))this.add(u);if(o)for(const u of a.dependencies)this.addFile(u,i,o)},add(a){if(a.kind=="extension"){let i=r.get(a.extendee.typeName);i||r.set(a.extendee.typeName,i=new Map),i.set(a.number,a)}n.set(a.typeName,a)},get:a=>n.get(a),getFile:a=>s.get(a),getMessage(a){const i=n.get(a);return(i==null?void 0:i.kind)=="message"?i:void 0},getEnum(a){const i=n.get(a);return(i==null?void 0:i.kind)=="enum"?i:void 0},getExtension(a){const i=n.get(a);return(i==null?void 0:i.kind)=="extension"?i:void 0},getExtensionFor(a,i){var o;return(o=r.get(a.typeName))===null||o===void 0?void 0:o.get(i)},getService(a){const i=n.get(a);return(i==null?void 0:i.kind)=="service"?i:void 0}}}();if(!e.length)return t;if("$typeName"in e[0]&&e[0].$typeName=="google.protobuf.FileDescriptorSet"){for(const n of e[0].file)Xa(n,t);return t}if("$typeName"in e[0]){let a=function(i){const o=[];for(const u of i.dependency){if(t.getFile(u)!=null||s.has(u))continue;const l=r(u);if(!l)throw new Error(`Unable to resolve ${u}, imported by ${i.name}`);"kind"in l?t.addFile(l,!1,!0):(s.add(l.name),o.push(l))}return o.concat(...o.map(a))};const n=e[0],r=e[1],s=new Set;for(const i of[n,...a(n)].reverse())Xa(i,t)}else for(const n of e)for(const r of n.files)t.addFile(r);return t}const Bu=998,ju=999,Vu=9,rn=10,Wt=11,Gu=12,Ha=14,ms=3,qu=2,Wa=1,Zu=0,Fr=1,za=2,Ku=3,Yu=1,Hu=2,Wu=1,Xo={998:{fieldPresence:1,enumType:2,repeatedFieldEncoding:2,utf8Validation:3,messageEncoding:1,jsonFormat:2,enforceNamingStyle:2,defaultSymbolVisibility:1},999:{fieldPresence:2,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2,defaultSymbolVisibility:1},1e3:{fieldPresence:1,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2,defaultSymbolVisibility:1}};function Xa(e,t){var n,r;const s={kind:"file",proto:e,deprecated:(r=(n=e.options)===null||n===void 0?void 0:n.deprecated)!==null&&r!==void 0&&r,edition:Ju(e),name:e.name.replace(/\.proto$/,""),dependencies:Qu(e,t),enums:[],messages:[],extensions:[],services:[],toString:()=>`file ${e.name}`},a=new Map,i={get:o=>a.get(o),add(o){var u;ke(((u=o.proto.options)===null||u===void 0?void 0:u.mapEntry)===!0),a.set(o.typeName,o)}};for(const o of e.enumType)Jo(o,s,void 0,t);for(const o of e.messageType)Qo(o,s,void 0,t,i);for(const o of e.service)zu(o,s,t);fs(s,t);for(const o of a.values())gs(o,t,i);for(const o of s.messages)gs(o,t,i),fs(o,t);t.addFile(s,!0)}function fs(e,t){switch(e.kind){case"file":for(const n of e.proto.extension){const r=ys(n,e,t);e.extensions.push(r),t.add(r)}break;case"message":for(const n of e.proto.extension){const r=ys(n,e,t);e.nestedExtensions.push(r),t.add(r)}for(const n of e.nestedMessages)fs(n,t)}}function gs(e,t,n){const r=e.proto.oneofDecl.map(a=>function(i,o){return{kind:"oneof",proto:i,deprecated:!1,parent:o,fields:[],name:i.name,localName:dn(cn(i.name)),toString(){return`oneof ${o.typeName}.${this.name}`}}}(a,e)),s=new Set;for(const a of e.proto.field){const i=ec(a,r),o=ys(a,e,t,i,n);e.fields.push(o),e.field[o.localName]=o,i===void 0?e.members.push(o):(i.fields.push(o),s.has(i)||(s.add(i),e.members.push(i)))}for(const a of r.filter(i=>s.has(i)))e.oneofs.push(a);for(const a of e.nestedMessages)gs(a,t,n)}function Jo(e,t,n,r){var s,a,i,o,u;const l=function(c,p){const h=(m=c,(m.substring(0,1)+m.substring(1).replace(/[A-Z]/g,y=>"_"+y)).toLowerCase()+"_");var m;for(const y of p){if(!y.name.toLowerCase().startsWith(h))return;const g=y.name.substring(h.length);if(g.length==0||/^\d/.test(g))return}return h}(e.name,e.value),d={kind:"enum",proto:e,deprecated:(a=(s=e.options)===null||s===void 0?void 0:s.deprecated)!==null&&a!==void 0&&a,file:t,parent:n,open:!0,name:e.name,typeName:Nr(e,n,t),value:{},values:[],sharedPrefix:l,toString(){return`enum ${this.typeName}`}};d.open=function(c){var p;return Wu==Ut("enumType",{proto:c.proto,parent:(p=c.parent)!==null&&p!==void 0?p:c.file})}(d),r.add(d);for(const c of e.value){const p=c.name;d.values.push(d.value[c.number]={kind:"enum_value",proto:c,deprecated:(o=(i=c.options)===null||i===void 0?void 0:i.deprecated)!==null&&o!==void 0&&o,parent:d,name:p,localName:dn(l==null?p:p.substring(l.length)),number:c.number,toString:()=>`enum value ${d.typeName}.${p}`})}((u=n==null?void 0:n.nestedEnums)!==null&&u!==void 0?u:t.enums).push(d)}function Qo(e,t,n,r,s){var a,i,o,u;const l={kind:"message",proto:e,deprecated:(i=(a=e.options)===null||a===void 0?void 0:a.deprecated)!==null&&i!==void 0&&i,file:t,parent:n,name:e.name,typeName:Nr(e,n,t),fields:[],field:{},oneofs:[],members:[],nestedEnums:[],nestedMessages:[],nestedExtensions:[],toString(){return`message ${this.typeName}`}};((o=e.options)===null||o===void 0?void 0:o.mapEntry)===!0?s.add(l):(((u=n==null?void 0:n.nestedMessages)!==null&&u!==void 0?u:t.messages).push(l),r.add(l));for(const d of e.enumType)Jo(d,t,l,r);for(const d of e.nestedType)Qo(d,t,l,r,s)}function zu(e,t,n){var r,s;const a={kind:"service",proto:e,deprecated:(s=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&s!==void 0&&s,file:t,name:e.name,typeName:Nr(e,void 0,t),methods:[],method:{},toString(){return`service ${this.typeName}`}};t.services.push(a),n.add(a);for(const i of e.method){const o=Xu(i,a,n);a.methods.push(o),a.method[o.localName]=o}}function Xu(e,t,n){var r,s,a,i;let o;o=e.clientStreaming&&e.serverStreaming?"bidi_streaming":e.clientStreaming?"client_streaming":e.serverStreaming?"server_streaming":"unary";const u=n.getMessage(Ye(e.inputType)),l=n.getMessage(Ye(e.outputType));ke(u,`invalid MethodDescriptorProto: input_type ${e.inputType} not found`),ke(l,`invalid MethodDescriptorProto: output_type ${e.inputType} not found`);const d=e.name;return{kind:"rpc",proto:e,deprecated:(s=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&s!==void 0&&s,parent:t,name:d,localName:dn(d.length?dn(d[0].toLowerCase()+d.substring(1)):d),methodKind:o,input:u,output:l,idempotency:(i=(a=e.options)===null||a===void 0?void 0:a.idempotencyLevel)!==null&&i!==void 0?i:Zu,toString:()=>`rpc ${t.typeName}.${d}`}}function ys(e,t,n,r,s){var a,i,o;const u=s===void 0,l={kind:"field",proto:e,deprecated:(i=(a=e.options)===null||a===void 0?void 0:a.deprecated)!==null&&i!==void 0&&i,name:e.name,number:e.number,scalar:void 0,message:void 0,enum:void 0,presence:tc(e,r,u,t),listKind:void 0,mapKind:void 0,mapKey:void 0,delimitedEncoding:void 0,packed:void 0,longAsString:!1,getDefaultValue:void 0};if(u){const h=t.kind=="file"?t:t.file,m=t.kind=="file"?void 0:t,y=Nr(e,m,h);l.kind="extension",l.file=h,l.parent=m,l.oneof=void 0,l.typeName=y,l.jsonName=`[${y}]`,l.toString=()=>`extension ${y}`;const g=n.getMessage(Ye(e.extendee));ke(g,`invalid FieldDescriptorProto: extendee ${e.extendee} not found`),l.extendee=g}else{const h=t;ke(h.kind=="message"),l.parent=h,l.oneof=r,l.localName=r?cn(e.name):dn(cn(e.name)),l.jsonName=e.jsonName,l.toString=()=>`field ${h.typeName}.${e.name}`}const d=e.label,c=e.type,p=(o=e.options)===null||o===void 0?void 0:o.jstype;if(d===ms){const h=c==Wt?s==null?void 0:s.get(Ye(e.typeName)):void 0;if(h){l.fieldKind="map";const{key:m,value:y}=function(g){const v=g.fields.find(b=>b.number===1),E=g.fields.find(b=>b.number===2);return ke(v&&v.fieldKind=="scalar"&&v.scalar!=_.BYTES&&v.scalar!=_.FLOAT&&v.scalar!=_.DOUBLE&&E&&E.fieldKind!="list"&&E.fieldKind!="map"),{key:v,value:E}}(h);return l.mapKey=m.scalar,l.mapKind=y.fieldKind,l.message=y.message,l.delimitedEncoding=!1,l.enum=y.enum,l.scalar=y.scalar,l}switch(l.fieldKind="list",c){case Wt:case rn:l.listKind="message",l.message=n.getMessage(Ye(e.typeName)),ke(l.message),l.delimitedEncoding=Ja(e,t);break;case Ha:l.listKind="enum",l.enum=n.getEnum(Ye(e.typeName)),ke(l.enum);break;default:l.listKind="scalar",l.scalar=c,l.longAsString=p==Wa}return l.packed=function(m,y){if(m.label!=ms)return!1;switch(m.type){case Vu:case Gu:case rn:case Wt:return!1}const g=m.options;return g&&nn(g,"packed")?g.packed:Yu==Ut("repeatedFieldEncoding",{proto:m,parent:y})}(e,t),l}switch(c){case Wt:case rn:l.fieldKind="message",l.message=n.getMessage(Ye(e.typeName)),ke(l.message,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),l.delimitedEncoding=Ja(e,t),l.getDefaultValue=()=>{};break;case Ha:{const h=n.getEnum(Ye(e.typeName));ke(h!==void 0,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),l.fieldKind="enum",l.enum=n.getEnum(Ye(e.typeName)),l.getDefaultValue=()=>nn(e,"defaultValue")?function(m,y){const g=m.values.find(v=>v.name===y);if(!g)throw new Error(`cannot parse ${m} default value: ${y}`);return g.number}(h,e.defaultValue):void 0;break}default:l.fieldKind="scalar",l.scalar=c,l.longAsString=p==Wa,l.getDefaultValue=()=>nn(e,"defaultValue")?Uu(c,e.defaultValue):void 0}return l}function Ju(e){switch(e.syntax){case"":case"proto2":return Bu;case"proto3":return ju;case"editions":if(e.edition in Xo)return e.edition;throw new Error(`${e.name}: unsupported edition`);default:throw new Error(`${e.name}: unsupported syntax "${e.syntax}"`)}}function Qu(e,t){return e.dependency.map(n=>{const r=t.getFile(n);if(!r)throw new Error(`Cannot find ${n}, imported by ${e.name}`);return r})}function Nr(e,t,n){let r;return r=t?`${t.typeName}.${e.name}`:n.proto.package.length>0?`${n.proto.package}.${e.name}`:`${e.name}`,r}function Ye(e){return e.startsWith(".")?e.substring(1):e}function ec(e,t){if(!nn(e,"oneofIndex")||e.proto3Optional)return;const n=t[e.oneofIndex];return ke(n,`invalid FieldDescriptorProto: oneof #${e.oneofIndex} for field #${e.number} not found`),n}function tc(e,t,n,r){if(e.label==qu)return Ku;if(e.label==ms)return za;if(t||e.proto3Optional||n)return Fr;const s=Ut("fieldPresence",{proto:e,parent:r});return s!=za||e.type!=Wt&&e.type!=rn?s:Fr}function Ja(e,t){return e.type==rn||Hu==Ut("messageEncoding",{proto:e,parent:t})}function Ut(e,t){var n,r;const s=(n=t.proto.options)===null||n===void 0?void 0:n.features;if(s){const a=s[e];if(a!=0)return a}if("kind"in t){if(t.kind=="message")return Ut(e,(r=t.parent)!==null&&r!==void 0?r:t.file);const a=Xo[t.edition];if(!a)throw new Error(`feature default for edition ${t.edition} not found`);return a[e]}return Ut(e,t.parent)}function ke(e,t){if(!e)throw new Error(t)}function nc(e){const t=function(n){return Object.assign(Object.create({syntax:"",edition:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FileDescriptorProto",dependency:[],publicDependency:[],weakDependency:[],optionDependency:[],service:[],extension:[]},n),{messageType:n.messageType.map(el),enumType:n.enumType.map(tl)}))}(e);return t.messageType.forEach(_a),zo(t,()=>{}).getFile(t.name)}function el(e){var t,n,r,s,a,i,o,u;return Object.assign(Object.create({visibility:0}),{$typeName:"google.protobuf.DescriptorProto",name:e.name,field:(n=(t=e.field)===null||t===void 0?void 0:t.map(rc))!==null&&n!==void 0?n:[],extension:[],nestedType:(s=(r=e.nestedType)===null||r===void 0?void 0:r.map(el))!==null&&s!==void 0?s:[],enumType:(i=(a=e.enumType)===null||a===void 0?void 0:a.map(tl))!==null&&i!==void 0?i:[],extensionRange:(u=(o=e.extensionRange)===null||o===void 0?void 0:o.map(d=>Object.assign({$typeName:"google.protobuf.DescriptorProto.ExtensionRange"},d)))!==null&&u!==void 0?u:[],oneofDecl:[],reservedRange:[],reservedName:[]})}function rc(e){return Object.assign(Object.create({label:1,typeName:"",extendee:"",defaultValue:"",oneofIndex:0,jsonName:"",proto3Optional:!1}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldDescriptorProto"},e),{options:e.options?sc(e.options):void 0}))}function sc(e){var t,n,r;return Object.assign(Object.create({ctype:0,packed:!1,jstype:0,lazy:!1,unverifiedLazy:!1,deprecated:!1,weak:!1,debugRedact:!1,retention:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldOptions"},e),{targets:(t=e.targets)!==null&&t!==void 0?t:[],editionDefaults:(r=(n=e.editionDefaults)===null||n===void 0?void 0:n.map(a=>Object.assign({$typeName:"google.protobuf.FieldOptions.EditionDefault"},a)))!==null&&r!==void 0?r:[],uninterpretedOption:[]}))}function tl(e){return Object.assign(Object.create({visibility:0}),{$typeName:"google.protobuf.EnumDescriptorProto",name:e.name,reservedName:[],reservedRange:[],value:e.value.map(n=>Object.assign({$typeName:"google.protobuf.EnumValueDescriptorProto"},n))})}function An(e,t,...n){return n.reduce((r,s)=>r.nestedMessages[s],e.messages[t])}const ac=An(nc({name:"google/protobuf/descriptor.proto",package:"google.protobuf",messageType:[{name:"FileDescriptorSet",field:[{name:"file",number:1,type:11,label:3,typeName:".google.protobuf.FileDescriptorProto"}],extensionRange:[{start:536e6,end:536000001}]},{name:"FileDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"package",number:2,type:9,label:1},{name:"dependency",number:3,type:9,label:3},{name:"public_dependency",number:10,type:5,label:3},{name:"weak_dependency",number:11,type:5,label:3},{name:"option_dependency",number:15,type:9,label:3},{name:"message_type",number:4,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:5,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"service",number:6,type:11,label:3,typeName:".google.protobuf.ServiceDescriptorProto"},{name:"extension",number:7,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FileOptions"},{name:"source_code_info",number:9,type:11,label:1,typeName:".google.protobuf.SourceCodeInfo"},{name:"syntax",number:12,type:9,label:1},{name:"edition",number:14,type:14,label:1,typeName:".google.protobuf.Edition"}]},{name:"DescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"field",number:2,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"extension",number:6,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"nested_type",number:3,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"extension_range",number:5,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ExtensionRange"},{name:"oneof_decl",number:8,type:11,label:3,typeName:".google.protobuf.OneofDescriptorProto"},{name:"options",number:7,type:11,label:1,typeName:".google.protobuf.MessageOptions"},{name:"reserved_range",number:9,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ReservedRange"},{name:"reserved_name",number:10,type:9,label:3},{name:"visibility",number:11,type:14,label:1,typeName:".google.protobuf.SymbolVisibility"}],nestedType:[{name:"ExtensionRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ExtensionRangeOptions"}]},{name:"ReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"ExtensionRangeOptions",field:[{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"},{name:"declaration",number:2,type:11,label:3,typeName:".google.protobuf.ExtensionRangeOptions.Declaration",options:{retention:2}},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"verification",number:3,type:14,label:1,typeName:".google.protobuf.ExtensionRangeOptions.VerificationState",defaultValue:"UNVERIFIED",options:{retention:2}}],nestedType:[{name:"Declaration",field:[{name:"number",number:1,type:5,label:1},{name:"full_name",number:2,type:9,label:1},{name:"type",number:3,type:9,label:1},{name:"reserved",number:5,type:8,label:1},{name:"repeated",number:6,type:8,label:1}]}],enumType:[{name:"VerificationState",value:[{name:"DECLARATION",number:0},{name:"UNVERIFIED",number:1}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:3,type:5,label:1},{name:"label",number:4,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Label"},{name:"type",number:5,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Type"},{name:"type_name",number:6,type:9,label:1},{name:"extendee",number:2,type:9,label:1},{name:"default_value",number:7,type:9,label:1},{name:"oneof_index",number:9,type:5,label:1},{name:"json_name",number:10,type:9,label:1},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FieldOptions"},{name:"proto3_optional",number:17,type:8,label:1}],enumType:[{name:"Type",value:[{name:"TYPE_DOUBLE",number:1},{name:"TYPE_FLOAT",number:2},{name:"TYPE_INT64",number:3},{name:"TYPE_UINT64",number:4},{name:"TYPE_INT32",number:5},{name:"TYPE_FIXED64",number:6},{name:"TYPE_FIXED32",number:7},{name:"TYPE_BOOL",number:8},{name:"TYPE_STRING",number:9},{name:"TYPE_GROUP",number:10},{name:"TYPE_MESSAGE",number:11},{name:"TYPE_BYTES",number:12},{name:"TYPE_UINT32",number:13},{name:"TYPE_ENUM",number:14},{name:"TYPE_SFIXED32",number:15},{name:"TYPE_SFIXED64",number:16},{name:"TYPE_SINT32",number:17},{name:"TYPE_SINT64",number:18}]},{name:"Label",value:[{name:"LABEL_OPTIONAL",number:1},{name:"LABEL_REPEATED",number:3},{name:"LABEL_REQUIRED",number:2}]}]},{name:"OneofDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"options",number:2,type:11,label:1,typeName:".google.protobuf.OneofOptions"}]},{name:"EnumDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"value",number:2,type:11,label:3,typeName:".google.protobuf.EnumValueDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumOptions"},{name:"reserved_range",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto.EnumReservedRange"},{name:"reserved_name",number:5,type:9,label:3},{name:"visibility",number:6,type:14,label:1,typeName:".google.protobuf.SymbolVisibility"}],nestedType:[{name:"EnumReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"EnumValueDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumValueOptions"}]},{name:"ServiceDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"method",number:2,type:11,label:3,typeName:".google.protobuf.MethodDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ServiceOptions"}]},{name:"MethodDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"input_type",number:2,type:9,label:1},{name:"output_type",number:3,type:9,label:1},{name:"options",number:4,type:11,label:1,typeName:".google.protobuf.MethodOptions"},{name:"client_streaming",number:5,type:8,label:1,defaultValue:"false"},{name:"server_streaming",number:6,type:8,label:1,defaultValue:"false"}]},{name:"FileOptions",field:[{name:"java_package",number:1,type:9,label:1},{name:"java_outer_classname",number:8,type:9,label:1},{name:"java_multiple_files",number:10,type:8,label:1,defaultValue:"false"},{name:"java_generate_equals_and_hash",number:20,type:8,label:1,options:{deprecated:!0}},{name:"java_string_check_utf8",number:27,type:8,label:1,defaultValue:"false"},{name:"optimize_for",number:9,type:14,label:1,typeName:".google.protobuf.FileOptions.OptimizeMode",defaultValue:"SPEED"},{name:"go_package",number:11,type:9,label:1},{name:"cc_generic_services",number:16,type:8,label:1,defaultValue:"false"},{name:"java_generic_services",number:17,type:8,label:1,defaultValue:"false"},{name:"py_generic_services",number:18,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:23,type:8,label:1,defaultValue:"false"},{name:"cc_enable_arenas",number:31,type:8,label:1,defaultValue:"true"},{name:"objc_class_prefix",number:36,type:9,label:1},{name:"csharp_namespace",number:37,type:9,label:1},{name:"swift_prefix",number:39,type:9,label:1},{name:"php_class_prefix",number:40,type:9,label:1},{name:"php_namespace",number:41,type:9,label:1},{name:"php_metadata_namespace",number:44,type:9,label:1},{name:"ruby_package",number:45,type:9,label:1},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"OptimizeMode",value:[{name:"SPEED",number:1},{name:"CODE_SIZE",number:2},{name:"LITE_RUNTIME",number:3}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"MessageOptions",field:[{name:"message_set_wire_format",number:1,type:8,label:1,defaultValue:"false"},{name:"no_standard_descriptor_accessor",number:2,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"map_entry",number:7,type:8,label:1},{name:"deprecated_legacy_json_field_conflicts",number:11,type:8,label:1,options:{deprecated:!0}},{name:"features",number:12,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldOptions",field:[{name:"ctype",number:1,type:14,label:1,typeName:".google.protobuf.FieldOptions.CType",defaultValue:"STRING"},{name:"packed",number:2,type:8,label:1},{name:"jstype",number:6,type:14,label:1,typeName:".google.protobuf.FieldOptions.JSType",defaultValue:"JS_NORMAL"},{name:"lazy",number:5,type:8,label:1,defaultValue:"false"},{name:"unverified_lazy",number:15,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"weak",number:10,type:8,label:1,defaultValue:"false"},{name:"debug_redact",number:16,type:8,label:1,defaultValue:"false"},{name:"retention",number:17,type:14,label:1,typeName:".google.protobuf.FieldOptions.OptionRetention"},{name:"targets",number:19,type:14,label:3,typeName:".google.protobuf.FieldOptions.OptionTargetType"},{name:"edition_defaults",number:20,type:11,label:3,typeName:".google.protobuf.FieldOptions.EditionDefault"},{name:"features",number:21,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"feature_support",number:22,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],nestedType:[{name:"EditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"value",number:2,type:9,label:1}]},{name:"FeatureSupport",field:[{name:"edition_introduced",number:1,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"edition_deprecated",number:2,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"deprecation_warning",number:3,type:9,label:1},{name:"edition_removed",number:4,type:14,label:1,typeName:".google.protobuf.Edition"}]}],enumType:[{name:"CType",value:[{name:"STRING",number:0},{name:"CORD",number:1},{name:"STRING_PIECE",number:2}]},{name:"JSType",value:[{name:"JS_NORMAL",number:0},{name:"JS_STRING",number:1},{name:"JS_NUMBER",number:2}]},{name:"OptionRetention",value:[{name:"RETENTION_UNKNOWN",number:0},{name:"RETENTION_RUNTIME",number:1},{name:"RETENTION_SOURCE",number:2}]},{name:"OptionTargetType",value:[{name:"TARGET_TYPE_UNKNOWN",number:0},{name:"TARGET_TYPE_FILE",number:1},{name:"TARGET_TYPE_EXTENSION_RANGE",number:2},{name:"TARGET_TYPE_MESSAGE",number:3},{name:"TARGET_TYPE_FIELD",number:4},{name:"TARGET_TYPE_ONEOF",number:5},{name:"TARGET_TYPE_ENUM",number:6},{name:"TARGET_TYPE_ENUM_ENTRY",number:7},{name:"TARGET_TYPE_SERVICE",number:8},{name:"TARGET_TYPE_METHOD",number:9}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"OneofOptions",field:[{name:"features",number:1,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumOptions",field:[{name:"allow_alias",number:2,type:8,label:1},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"deprecated_legacy_json_field_conflicts",number:6,type:8,label:1,options:{deprecated:!0}},{name:"features",number:7,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumValueOptions",field:[{name:"deprecated",number:1,type:8,label:1,defaultValue:"false"},{name:"features",number:2,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"debug_redact",number:3,type:8,label:1,defaultValue:"false"},{name:"feature_support",number:4,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"ServiceOptions",field:[{name:"features",number:34,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"MethodOptions",field:[{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"idempotency_level",number:34,type:14,label:1,typeName:".google.protobuf.MethodOptions.IdempotencyLevel",defaultValue:"IDEMPOTENCY_UNKNOWN"},{name:"features",number:35,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"IdempotencyLevel",value:[{name:"IDEMPOTENCY_UNKNOWN",number:0},{name:"NO_SIDE_EFFECTS",number:1},{name:"IDEMPOTENT",number:2}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"UninterpretedOption",field:[{name:"name",number:2,type:11,label:3,typeName:".google.protobuf.UninterpretedOption.NamePart"},{name:"identifier_value",number:3,type:9,label:1},{name:"positive_int_value",number:4,type:4,label:1},{name:"negative_int_value",number:5,type:3,label:1},{name:"double_value",number:6,type:1,label:1},{name:"string_value",number:7,type:12,label:1},{name:"aggregate_value",number:8,type:9,label:1}],nestedType:[{name:"NamePart",field:[{name:"name_part",number:1,type:9,label:2},{name:"is_extension",number:2,type:8,label:2}]}]},{name:"FeatureSet",field:[{name:"field_presence",number:1,type:14,label:1,typeName:".google.protobuf.FeatureSet.FieldPresence",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPLICIT",edition:900},{value:"IMPLICIT",edition:999},{value:"EXPLICIT",edition:1e3}]}},{name:"enum_type",number:2,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnumType",options:{retention:1,targets:[6,1],editionDefaults:[{value:"CLOSED",edition:900},{value:"OPEN",edition:999}]}},{name:"repeated_field_encoding",number:3,type:14,label:1,typeName:".google.protobuf.FeatureSet.RepeatedFieldEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPANDED",edition:900},{value:"PACKED",edition:999}]}},{name:"utf8_validation",number:4,type:14,label:1,typeName:".google.protobuf.FeatureSet.Utf8Validation",options:{retention:1,targets:[4,1],editionDefaults:[{value:"NONE",edition:900},{value:"VERIFY",edition:999}]}},{name:"message_encoding",number:5,type:14,label:1,typeName:".google.protobuf.FeatureSet.MessageEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"LENGTH_PREFIXED",edition:900}]}},{name:"json_format",number:6,type:14,label:1,typeName:".google.protobuf.FeatureSet.JsonFormat",options:{retention:1,targets:[3,6,1],editionDefaults:[{value:"LEGACY_BEST_EFFORT",edition:900},{value:"ALLOW",edition:999}]}},{name:"enforce_naming_style",number:7,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnforceNamingStyle",options:{retention:2,targets:[1,2,3,4,5,6,7,8,9],editionDefaults:[{value:"STYLE_LEGACY",edition:900},{value:"STYLE2024",edition:1001}]}},{name:"default_symbol_visibility",number:8,type:14,label:1,typeName:".google.protobuf.FeatureSet.VisibilityFeature.DefaultSymbolVisibility",options:{retention:2,targets:[1],editionDefaults:[{value:"EXPORT_ALL",edition:900},{value:"EXPORT_TOP_LEVEL",edition:1001}]}}],nestedType:[{name:"VisibilityFeature",enumType:[{name:"DefaultSymbolVisibility",value:[{name:"DEFAULT_SYMBOL_VISIBILITY_UNKNOWN",number:0},{name:"EXPORT_ALL",number:1},{name:"EXPORT_TOP_LEVEL",number:2},{name:"LOCAL_ALL",number:3},{name:"STRICT",number:4}]}]}],enumType:[{name:"FieldPresence",value:[{name:"FIELD_PRESENCE_UNKNOWN",number:0},{name:"EXPLICIT",number:1},{name:"IMPLICIT",number:2},{name:"LEGACY_REQUIRED",number:3}]},{name:"EnumType",value:[{name:"ENUM_TYPE_UNKNOWN",number:0},{name:"OPEN",number:1},{name:"CLOSED",number:2}]},{name:"RepeatedFieldEncoding",value:[{name:"REPEATED_FIELD_ENCODING_UNKNOWN",number:0},{name:"PACKED",number:1},{name:"EXPANDED",number:2}]},{name:"Utf8Validation",value:[{name:"UTF8_VALIDATION_UNKNOWN",number:0},{name:"VERIFY",number:2},{name:"NONE",number:3}]},{name:"MessageEncoding",value:[{name:"MESSAGE_ENCODING_UNKNOWN",number:0},{name:"LENGTH_PREFIXED",number:1},{name:"DELIMITED",number:2}]},{name:"JsonFormat",value:[{name:"JSON_FORMAT_UNKNOWN",number:0},{name:"ALLOW",number:1},{name:"LEGACY_BEST_EFFORT",number:2}]},{name:"EnforceNamingStyle",value:[{name:"ENFORCE_NAMING_STYLE_UNKNOWN",number:0},{name:"STYLE2024",number:1},{name:"STYLE_LEGACY",number:2}]}],extensionRange:[{start:1e3,end:9995},{start:9995,end:1e4},{start:1e4,end:10001}]},{name:"FeatureSetDefaults",field:[{name:"defaults",number:1,type:11,label:3,typeName:".google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault"},{name:"minimum_edition",number:4,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"maximum_edition",number:5,type:14,label:1,typeName:".google.protobuf.Edition"}],nestedType:[{name:"FeatureSetEditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"overridable_features",number:4,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"fixed_features",number:5,type:11,label:1,typeName:".google.protobuf.FeatureSet"}]}]},{name:"SourceCodeInfo",field:[{name:"location",number:1,type:11,label:3,typeName:".google.protobuf.SourceCodeInfo.Location"}],nestedType:[{name:"Location",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"span",number:2,type:5,label:3,options:{packed:!0}},{name:"leading_comments",number:3,type:9,label:1},{name:"trailing_comments",number:4,type:9,label:1},{name:"leading_detached_comments",number:6,type:9,label:3}]}],extensionRange:[{start:536e6,end:536000001}]},{name:"GeneratedCodeInfo",field:[{name:"annotation",number:1,type:11,label:3,typeName:".google.protobuf.GeneratedCodeInfo.Annotation"}],nestedType:[{name:"Annotation",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"source_file",number:2,type:9,label:1},{name:"begin",number:3,type:5,label:1},{name:"end",number:4,type:5,label:1},{name:"semantic",number:5,type:14,label:1,typeName:".google.protobuf.GeneratedCodeInfo.Annotation.Semantic"}],enumType:[{name:"Semantic",value:[{name:"NONE",number:0},{name:"SET",number:1},{name:"ALIAS",number:2}]}]}]}],enumType:[{name:"Edition",value:[{name:"EDITION_UNKNOWN",number:0},{name:"EDITION_LEGACY",number:900},{name:"EDITION_PROTO2",number:998},{name:"EDITION_PROTO3",number:999},{name:"EDITION_2023",number:1e3},{name:"EDITION_2024",number:1001},{name:"EDITION_1_TEST_ONLY",number:1},{name:"EDITION_2_TEST_ONLY",number:2},{name:"EDITION_99997_TEST_ONLY",number:99997},{name:"EDITION_99998_TEST_ONLY",number:99998},{name:"EDITION_99999_TEST_ONLY",number:99999},{name:"EDITION_MAX",number:2147483647}]},{name:"SymbolVisibility",value:[{name:"VISIBILITY_UNSET",number:0},{name:"VISIBILITY_LOCAL",number:1},{name:"VISIBILITY_EXPORT",number:2}]}]}),1);var Qa,ei,ti,ni,ri,si,ai,ii,oi,li,ui,ci,di,pi,hi,mi,fi,gi,yi,_i;(function(e){e[e.DECLARATION=0]="DECLARATION",e[e.UNVERIFIED=1]="UNVERIFIED"})(Qa||(Qa={})),function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.GROUP=10]="GROUP",e[e.MESSAGE=11]="MESSAGE",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.ENUM=14]="ENUM",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"}(ei||(ei={})),function(e){e[e.OPTIONAL=1]="OPTIONAL",e[e.REPEATED=3]="REPEATED",e[e.REQUIRED=2]="REQUIRED"}(ti||(ti={})),function(e){e[e.SPEED=1]="SPEED",e[e.CODE_SIZE=2]="CODE_SIZE",e[e.LITE_RUNTIME=3]="LITE_RUNTIME"}(ni||(ni={})),function(e){e[e.STRING=0]="STRING",e[e.CORD=1]="CORD",e[e.STRING_PIECE=2]="STRING_PIECE"}(ri||(ri={})),function(e){e[e.JS_NORMAL=0]="JS_NORMAL",e[e.JS_STRING=1]="JS_STRING",e[e.JS_NUMBER=2]="JS_NUMBER"}(si||(si={})),function(e){e[e.RETENTION_UNKNOWN=0]="RETENTION_UNKNOWN",e[e.RETENTION_RUNTIME=1]="RETENTION_RUNTIME",e[e.RETENTION_SOURCE=2]="RETENTION_SOURCE"}(ai||(ai={})),function(e){e[e.TARGET_TYPE_UNKNOWN=0]="TARGET_TYPE_UNKNOWN",e[e.TARGET_TYPE_FILE=1]="TARGET_TYPE_FILE",e[e.TARGET_TYPE_EXTENSION_RANGE=2]="TARGET_TYPE_EXTENSION_RANGE",e[e.TARGET_TYPE_MESSAGE=3]="TARGET_TYPE_MESSAGE",e[e.TARGET_TYPE_FIELD=4]="TARGET_TYPE_FIELD",e[e.TARGET_TYPE_ONEOF=5]="TARGET_TYPE_ONEOF",e[e.TARGET_TYPE_ENUM=6]="TARGET_TYPE_ENUM",e[e.TARGET_TYPE_ENUM_ENTRY=7]="TARGET_TYPE_ENUM_ENTRY",e[e.TARGET_TYPE_SERVICE=8]="TARGET_TYPE_SERVICE",e[e.TARGET_TYPE_METHOD=9]="TARGET_TYPE_METHOD"}(ii||(ii={})),function(e){e[e.IDEMPOTENCY_UNKNOWN=0]="IDEMPOTENCY_UNKNOWN",e[e.NO_SIDE_EFFECTS=1]="NO_SIDE_EFFECTS",e[e.IDEMPOTENT=2]="IDEMPOTENT"}(oi||(oi={})),function(e){e[e.DEFAULT_SYMBOL_VISIBILITY_UNKNOWN=0]="DEFAULT_SYMBOL_VISIBILITY_UNKNOWN",e[e.EXPORT_ALL=1]="EXPORT_ALL",e[e.EXPORT_TOP_LEVEL=2]="EXPORT_TOP_LEVEL",e[e.LOCAL_ALL=3]="LOCAL_ALL",e[e.STRICT=4]="STRICT"}(li||(li={})),function(e){e[e.FIELD_PRESENCE_UNKNOWN=0]="FIELD_PRESENCE_UNKNOWN",e[e.EXPLICIT=1]="EXPLICIT",e[e.IMPLICIT=2]="IMPLICIT",e[e.LEGACY_REQUIRED=3]="LEGACY_REQUIRED"}(ui||(ui={})),function(e){e[e.ENUM_TYPE_UNKNOWN=0]="ENUM_TYPE_UNKNOWN",e[e.OPEN=1]="OPEN",e[e.CLOSED=2]="CLOSED"}(ci||(ci={})),function(e){e[e.REPEATED_FIELD_ENCODING_UNKNOWN=0]="REPEATED_FIELD_ENCODING_UNKNOWN",e[e.PACKED=1]="PACKED",e[e.EXPANDED=2]="EXPANDED"}(di||(di={})),function(e){e[e.UTF8_VALIDATION_UNKNOWN=0]="UTF8_VALIDATION_UNKNOWN",e[e.VERIFY=2]="VERIFY",e[e.NONE=3]="NONE"}(pi||(pi={})),function(e){e[e.MESSAGE_ENCODING_UNKNOWN=0]="MESSAGE_ENCODING_UNKNOWN",e[e.LENGTH_PREFIXED=1]="LENGTH_PREFIXED",e[e.DELIMITED=2]="DELIMITED"}(hi||(hi={})),function(e){e[e.JSON_FORMAT_UNKNOWN=0]="JSON_FORMAT_UNKNOWN",e[e.ALLOW=1]="ALLOW",e[e.LEGACY_BEST_EFFORT=2]="LEGACY_BEST_EFFORT"}(mi||(mi={})),function(e){e[e.ENFORCE_NAMING_STYLE_UNKNOWN=0]="ENFORCE_NAMING_STYLE_UNKNOWN",e[e.STYLE2024=1]="STYLE2024",e[e.STYLE_LEGACY=2]="STYLE_LEGACY"}(fi||(fi={})),function(e){e[e.NONE=0]="NONE",e[e.SET=1]="SET",e[e.ALIAS=2]="ALIAS"}(gi||(gi={})),function(e){e[e.EDITION_UNKNOWN=0]="EDITION_UNKNOWN",e[e.EDITION_LEGACY=900]="EDITION_LEGACY",e[e.EDITION_PROTO2=998]="EDITION_PROTO2",e[e.EDITION_PROTO3=999]="EDITION_PROTO3",e[e.EDITION_2023=1e3]="EDITION_2023",e[e.EDITION_2024=1001]="EDITION_2024",e[e.EDITION_1_TEST_ONLY=1]="EDITION_1_TEST_ONLY",e[e.EDITION_2_TEST_ONLY=2]="EDITION_2_TEST_ONLY",e[e.EDITION_99997_TEST_ONLY=99997]="EDITION_99997_TEST_ONLY",e[e.EDITION_99998_TEST_ONLY=99998]="EDITION_99998_TEST_ONLY",e[e.EDITION_99999_TEST_ONLY=99999]="EDITION_99999_TEST_ONLY",e[e.EDITION_MAX=2147483647]="EDITION_MAX"}(yi||(yi={})),function(e){e[e.VISIBILITY_UNSET=0]="VISIBILITY_UNSET",e[e.VISIBILITY_LOCAL=1]="VISIBILITY_LOCAL",e[e.VISIBILITY_EXPORT=2]="VISIBILITY_EXPORT"}(_i||(_i={}));const ic={readUnknownFields:!0};function va(e,t,n){const r=Ee(e,void 0,!1);return nl(r,new pa(t),ic,!1,t.byteLength),r.message}function nl(e,t,n,r,s){var a;const i=r?t.len:t.pos+s;let o,u;const l=(a=e.getUnknown())!==null&&a!==void 0?a:[];for(;t.pos<i&&([o,u]=t.tag(),!r||u!=K.EndGroup);){const d=e.findNumber(o);if(d)rl(e,t,d,u,n);else{const c=t.skip(u,o);n.readUnknownFields&&l.push({no:o,wireType:u,data:c})}}if(r&&(u!=K.EndGroup||o!==s))throw new Error("invalid end group tag");l.length>0&&e.setUnknown(l)}function rl(e,t,n,r,s){var a;switch(n.fieldKind){case"scalar":e.set(n,At(t,n.scalar));break;case"enum":const i=At(t,_.INT32);if(n.enum.open)e.set(n,i);else if(n.enum.values.some(o=>o.number===i))e.set(n,i);else if(s.readUnknownFields){const o=new da().int32(i).finish(),u=(a=e.getUnknown())!==null&&a!==void 0?a:[];u.push({no:n.number,wireType:r,data:o}),e.setUnknown(u)}break;case"message":e.set(n,$r(t,s,n,e.get(n)));break;case"list":(function(o,u,l,d){var c;const p=l.field();if(p.listKind==="message")return void l.add($r(o,d,p));const h=(c=p.scalar)!==null&&c!==void 0?c:_.INT32;if(!(u==K.LengthDelimited&&h!=_.STRING&&h!=_.BYTES))return void l.add(At(o,h));const y=o.uint32()+o.pos;for(;o.pos<y;)l.add(At(o,h))})(t,r,e.get(n),s);break;case"map":(function(o,u,l){const d=u.field();let c,p;const h=o.pos+o.uint32();for(;o.pos<h;){const[m]=o.tag();switch(m){case 1:c=At(o,d.mapKey);break;case 2:switch(d.mapKind){case"scalar":p=At(o,d.scalar);break;case"enum":p=o.int32();break;case"message":p=$r(o,l,d)}}}if(c===void 0&&(c=vt(d.mapKey,!1)),p===void 0)switch(d.mapKind){case"scalar":p=vt(d.scalar,!1);break;case"enum":p=d.enum.values[0].number;break;case"message":p=Ee(d.message,void 0,!1)}u.set(c,p)})(t,e.get(n),s)}}function $r(e,t,n,r){const s=n.delimitedEncoding,a=r??Ee(n.message,void 0,!1);return nl(a,e,t,s,s?n.number:e.uint32()),a}function At(e,t){switch(t){case _.STRING:return e.string();case _.BOOL:return e.bool();case _.DOUBLE:return e.double();case _.FLOAT:return e.float();case _.INT32:return e.int32();case _.INT64:return e.int64();case _.UINT64:return e.uint64();case _.FIXED64:return e.fixed64();case _.BYTES:return e.bytes();case _.FIXED32:return e.fixed32();case _.SFIXED32:return e.sfixed32();case _.SFIXED64:return e.sfixed64();case _.SINT64:return e.sint64();case _.UINT32:return e.uint32();case _.SINT32:return e.sint32()}}function ba(e,t){const n=va(ac,Ho(e));return n.messageType.forEach(_a),n.dependency=[],zo(n,r=>{}).getFile(n.name)}const oc=An(ba("Chlnb29nbGUvcHJvdG9idWYvYW55LnByb3RvEg9nb29nbGUucHJvdG9idWYiJgoDQW55EhAKCHR5cGVfdXJsGAEgASgJEg0KBXZhbHVlGAIgASgMQnYKE2NvbS5nb29nbGUucHJvdG9idWZCCEFueVByb3RvUAFaLGdvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL2FueXBiogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),0),lc=3,vi={writeUnknownFields:!0};function uc(e,t,n){return tr(new da,function(r){return r?Object.assign(Object.assign({},vi),r):vi}(n),Ee(e,t)).finish()}function tr(e,t,n){var r;for(const s of n.sortedFields)if(n.isSet(s))sl(e,t,n,s);else if(s.presence==lc)throw new Error(`cannot encode ${s} to binary: required field not set`);if(t.writeUnknownFields)for(const{no:s,wireType:a,data:i}of(r=n.getUnknown())!==null&&r!==void 0?r:[])e.tag(s,a).raw(i);return e}function sl(e,t,n,r){var s;switch(r.fieldKind){case"scalar":case"enum":nr(e,n.desc.typeName,r.name,(s=r.scalar)!==null&&s!==void 0?s:_.INT32,r.number,n.get(r));break;case"list":(function(a,i,o,u){var l;if(o.listKind=="message"){for(const c of u)bi(a,i,o,c);return}const d=(l=o.scalar)!==null&&l!==void 0?l:_.INT32;if(o.packed){if(!u.size)return;a.tag(o.number,K.LengthDelimited).fork();for(const c of u)al(a,o.parent.typeName,o.name,d,c);return void a.join()}for(const c of u)nr(a,o.parent.typeName,o.name,d,o.number,c)})(e,t,r,n.get(r));break;case"message":bi(e,t,r,n.get(r));break;case"map":for(const[a,i]of n.get(r))cc(e,t,r,a,i)}}function nr(e,t,n,r,s,a){al(e.tag(s,function(i){switch(i){case _.BYTES:case _.STRING:return K.LengthDelimited;case _.DOUBLE:case _.FIXED64:case _.SFIXED64:return K.Bit64;case _.FIXED32:case _.SFIXED32:case _.FLOAT:return K.Bit32;default:return K.Varint}}(r)),t,n,r,a)}function bi(e,t,n,r){n.delimitedEncoding?tr(e.tag(n.number,K.StartGroup),t,r).tag(n.number,K.EndGroup):tr(e.tag(n.number,K.LengthDelimited).fork(),t,r).join()}function cc(e,t,n,r,s){var a;switch(e.tag(n.number,K.LengthDelimited).fork(),nr(e,n.parent.typeName,n.name,n.mapKey,1,r),n.mapKind){case"scalar":case"enum":nr(e,n.parent.typeName,n.name,(a=n.scalar)!==null&&a!==void 0?a:_.INT32,2,s);break;case"message":tr(e.tag(2,K.LengthDelimited).fork(),t,s).join()}e.join()}function al(e,t,n,r,s){try{switch(r){case _.STRING:e.string(s);break;case _.BOOL:e.bool(s);break;case _.DOUBLE:e.double(s);break;case _.FLOAT:e.float(s);break;case _.INT32:e.int32(s);break;case _.INT64:e.int64(s);break;case _.UINT64:e.uint64(s);break;case _.FIXED64:e.fixed64(s);break;case _.BYTES:e.bytes(s);break;case _.FIXED32:e.fixed32(s);break;case _.SFIXED32:e.sfixed32(s);break;case _.SFIXED64:e.sfixed64(s);break;case _.SINT64:e.sint64(s);break;case _.UINT32:e.uint32(s);break;case _.SINT32:e.sint32(s)}}catch(a){throw a instanceof Error?new Error(`cannot encode field ${t}.${n} to binary: ${a.message}`):a}}function dc(e,t){if(e.typeUrl==="")return;const n=t.kind=="message"?t:t.getMessage(Si(e.typeUrl));return n&&function(r,s){return r.typeUrl!==""&&(typeof s=="string"?s:s.typeName)===Si(r.typeUrl)}(e,n)?va(n,e.value):void 0}function Si(e){const t=e.lastIndexOf("/"),n=t>=0?e.substring(t+1):e;if(!n.length)throw new Error(`invalid type url: ${e}`);return n}const Sa=ba("Chxnb29nbGUvcHJvdG9idWYvc3RydWN0LnByb3RvEg9nb29nbGUucHJvdG9idWYihAEKBlN0cnVjdBIzCgZmaWVsZHMYASADKAsyIy5nb29nbGUucHJvdG9idWYuU3RydWN0LkZpZWxkc0VudHJ5GkUKC0ZpZWxkc0VudHJ5EgsKA2tleRgBIAEoCRIlCgV2YWx1ZRgCIAEoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZToCOAEi6gEKBVZhbHVlEjAKCm51bGxfdmFsdWUYASABKA4yGi5nb29nbGUucHJvdG9idWYuTnVsbFZhbHVlSAASFgoMbnVtYmVyX3ZhbHVlGAIgASgBSAASFgoMc3RyaW5nX3ZhbHVlGAMgASgJSAASFAoKYm9vbF92YWx1ZRgEIAEoCEgAEi8KDHN0cnVjdF92YWx1ZRgFIAEoCzIXLmdvb2dsZS5wcm90b2J1Zi5TdHJ1Y3RIABIwCgpsaXN0X3ZhbHVlGAYgASgLMhouZ29vZ2xlLnByb3RvYnVmLkxpc3RWYWx1ZUgAQgYKBGtpbmQiMwoJTGlzdFZhbHVlEiYKBnZhbHVlcxgBIAMoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZSobCglOdWxsVmFsdWUSDgoKTlVMTF9WQUxVRRAAQn8KE2NvbS5nb29nbGUucHJvdG9idWZCC1N0cnVjdFByb3RvUAFaL2dvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL3N0cnVjdHBi+AEBogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),pc=An(Sa,0),il=An(Sa,1),hc=An(Sa,2);var _s;function mc(e,t){ol(t,e);const n=function(i,o){if(i===void 0)return[];if(o.fieldKind==="enum"||o.fieldKind==="scalar"){for(let u=i.length-1;u>=0;--u)if(i[u].no==o.number)return[i[u]];return[]}return i.filter(u=>u.no===o.number)}(e.$unknown,t),[r,s,a]=Ir(t);for(const i of n)rl(r,new pa(i.data),s,i.wireType,{readUnknownFields:!0});return a()}function fc(e,t,n){var r;ol(t,e);const s=((r=e.$unknown)!==null&&r!==void 0?r:[]).filter(l=>l.no!==t.number),[a,i]=Ir(t,n),o=new da;sl(o,{writeUnknownFields:!0},a,i);const u=new pa(o.finish());for(;u.pos<u.len;){const[l,d]=u.tag(),c=u.skip(d,l);s.push({no:l,wireType:d,data:c})}e.$unknown=s}function Ir(e,t){const n=e.typeName,r=Object.assign(Object.assign({},e),{kind:"field",parent:e.extendee,localName:n}),s=Object.assign(Object.assign({},e.extendee),{fields:[r],members:[r],oneofs:[]}),a=xe(s,t!==void 0?{[n]:t}:void 0);return[Ee(s,a),r,()=>{const i=a[n];if(i===void 0){const o=e.message;return Cn(o)?vt(o.fields[0].scalar,o.fields[0].longAsString):xe(o)}return i}]}function ol(e,t){if(e.extendee.typeName!=t.$typeName)throw new Error(`extension ${e.typeName} can only be applied to message ${e.extendee.typeName}`)}(function(e){e[e.NULL_VALUE=0]="NULL_VALUE"})(_s||(_s={}));const gc=3,yc=2,Ei={alwaysEmitImplicit:!1,enumAsInteger:!1,useProtoFieldName:!1};function _c(e,t,n){return sn(Ee(e,t),function(r){return r?Object.assign(Object.assign({},Ei),r):Ei}(n))}function sn(e,t){var n;const r=function(a,i){if(a.desc.typeName.startsWith("google.protobuf.")){switch(a.desc.typeName){case"google.protobuf.Any":return function(u,l){if(u.typeUrl==="")return{};const{registry:d}=l;let c,p;if(d&&(c=dc(u,d),c&&(p=d.getMessage(c.$typeName))),!p||!c)throw new Error(`cannot encode message ${u.$typeName} to JSON: "${u.typeUrl}" is not in the type registry`);let h=sn(Ee(p,c),l);return(p.typeName.startsWith("google.protobuf.")||h===null||Array.isArray(h)||typeof h!="object")&&(h={value:h}),h["@type"]=u.typeUrl,h}(a.message,i);case"google.protobuf.Timestamp":return function(u){const l=1e3*Number(u.seconds);if(l<Date.parse("0001-01-01T00:00:00Z")||l>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot encode message ${u.$typeName} to JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);if(u.nanos<0)throw new Error(`cannot encode message ${u.$typeName} to JSON: nanos must not be negative`);let d="Z";if(u.nanos>0){const c=(u.nanos+1e9).toString().substring(1);d=c.substring(3)==="000000"?"."+c.substring(0,3)+"Z":c.substring(6)==="000"?"."+c.substring(0,6)+"Z":"."+c+"Z"}return new Date(l).toISOString().replace(".000Z",d)}(a.message);case"google.protobuf.Duration":return function(u){if(Number(u.seconds)>315576e6||Number(u.seconds)<-315576e6)throw new Error(`cannot encode message ${u.$typeName} to JSON: value out of range`);let l=u.seconds.toString();if(u.nanos!==0){let d=Math.abs(u.nanos).toString();d="0".repeat(9-d.length)+d,d.substring(3)==="000000"?d=d.substring(0,3):d.substring(6)==="000"&&(d=d.substring(0,6)),l+="."+d,u.nanos<0&&Number(u.seconds)==0&&(l="-"+l)}return l+"s"}(a.message);case"google.protobuf.FieldMask":return(o=a.message).paths.map(u=>{if(u.match(/_[0-9]?_/g)||u.match(/[A-Z]/g))throw new Error(`cannot encode message ${o.$typeName} to JSON: lowerCamelCase of path name "`+u+'" is irreversible');return cn(u)}).join(",");case"google.protobuf.Struct":return ll(a.message);case"google.protobuf.Value":return Ea(a.message);case"google.protobuf.ListValue":return ul(a.message);default:if(Cn(a.desc)){const u=a.desc.fields[0];return Kn(u,a.get(u))}return}var o}}(e,t);if(r!==void 0)return r;const s={};for(const a of e.sortedFields){if(!e.isSet(a)){if(a.presence==gc)throw new Error(`cannot encode ${a} to JSON: required field not set`);if(!t.alwaysEmitImplicit||a.presence!==yc)continue}const i=Ti(a,e.get(a),t);i!==void 0&&(s[vc(a,t)]=i)}if(t.registry){const a=new Set;for(const{no:i}of(n=e.getUnknown())!==null&&n!==void 0?n:[])if(!a.has(i)){a.add(i);const o=t.registry.getExtensionFor(e.desc,i);if(!o)continue;const u=mc(e.message,o),[l,d]=Ir(o,u),c=Ti(d,l.get(d),t);c!==void 0&&(s[o.jsonName]=c)}}return s}function Ti(e,t,n){switch(e.fieldKind){case"scalar":return Kn(e,t);case"message":return sn(t,n);case"enum":return Ur(e.enum,t,n.enumAsInteger);case"list":return function(r,s){const a=r.field(),i=[];switch(a.listKind){case"scalar":for(const o of r)i.push(Kn(a,o));break;case"enum":for(const o of r)i.push(Ur(a.enum,o,s.enumAsInteger));break;case"message":for(const o of r)i.push(sn(o,s))}return s.alwaysEmitImplicit||i.length>0?i:void 0}(t,n);case"map":return function(r,s){const a=r.field(),i={};switch(a.mapKind){case"scalar":for(const[o,u]of r)i[o]=Kn(a,u);break;case"message":for(const[o,u]of r)i[o]=sn(u,s);break;case"enum":for(const[o,u]of r)i[o]=Ur(a.enum,u,s.enumAsInteger)}return s.alwaysEmitImplicit||r.size>0?i:void 0}(t,n)}}function Ur(e,t,n){var r;if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: expected number, got ${Y(t)}`);if(e.typeName=="google.protobuf.NullValue")return null;if(n)return t;const s=e.value[t];return(r=s==null?void 0:s.name)!==null&&r!==void 0?r:t}function Kn(e,t){var n,r,s,a,i,o;switch(e.scalar){case _.INT32:case _.SFIXED32:case _.SINT32:case _.FIXED32:case _.UINT32:if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: ${(n=ht(e,t))===null||n===void 0?void 0:n.message}`);return t;case _.FLOAT:case _.DOUBLE:if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: ${(r=ht(e,t))===null||r===void 0?void 0:r.message}`);return Number.isNaN(t)?"NaN":t===Number.POSITIVE_INFINITY?"Infinity":t===Number.NEGATIVE_INFINITY?"-Infinity":t;case _.STRING:if(typeof t!="string")throw new Error(`cannot encode ${e} to JSON: ${(s=ht(e,t))===null||s===void 0?void 0:s.message}`);return t;case _.BOOL:if(typeof t!="boolean")throw new Error(`cannot encode ${e} to JSON: ${(a=ht(e,t))===null||a===void 0?void 0:a.message}`);return t;case _.UINT64:case _.FIXED64:case _.INT64:case _.SFIXED64:case _.SINT64:if(typeof t!="bigint"&&typeof t!="string")throw new Error(`cannot encode ${e} to JSON: ${(i=ht(e,t))===null||i===void 0?void 0:i.message}`);return t.toString();case _.BYTES:if(t instanceof Uint8Array)return function(u,l="std"){const d=Wo(l),c=l=="std";let p,h="",m=0,y=0;for(let g=0;g<u.length;g++)switch(p=u[g],m){case 0:h+=d[p>>2],y=(3&p)<<4,m=1;break;case 1:h+=d[y|p>>4],y=(15&p)<<2,m=2;break;case 2:h+=d[y|p>>6],h+=d[63&p],m=0}return m&&(h+=d[y],c&&(h+="=",m==1&&(h+="="))),h}(t);throw new Error(`cannot encode ${e} to JSON: ${(o=ht(e,t))===null||o===void 0?void 0:o.message}`)}}function vc(e,t){return t.useProtoFieldName?e.name:e.jsonName}function ll(e){const t={};for(const[n,r]of Object.entries(e.fields))t[n]=Ea(r);return t}function Ea(e){switch(e.kind.case){case"nullValue":return null;case"numberValue":if(!Number.isFinite(e.kind.value))throw new Error(`${e.$typeName} cannot be NaN or Infinity`);return e.kind.value;case"boolValue":case"stringValue":return e.kind.value;case"structValue":return ll(e.kind.value);case"listValue":return ul(e.kind.value);default:throw new Error(`${e.$typeName} must have a value`)}}function ul(e){return e.values.map(Ea)}const wi={ignoreUnknownFields:!1};function bc(e,t,n){const r=Ee(e);try{Ft(r,t,function(a){return a?Object.assign(Object.assign({},wi),a):wi}(n))}catch(a){throw(s=a)instanceof Error&&Lu.includes(s.name)&&"field"in s&&typeof s.field=="function"?new Error(`cannot decode ${a.field()} from JSON: ${a.message}`,{cause:a}):a}var s;return r.message}function Ft(e,t,n){var r;if(function(i,o,u){if(!i.desc.typeName.startsWith("google.protobuf."))return!1;switch(i.desc.typeName){case"google.protobuf.Any":return function(l,d,c){var p;if(d===null||Array.isArray(d)||typeof d!="object")throw new Error(`cannot decode message ${l.$typeName} from JSON: expected object but got ${Y(d)}`);if(Object.keys(d).length==0)return;const h=d["@type"];if(typeof h!="string"||h=="")throw new Error(`cannot decode message ${l.$typeName} from JSON: "@type" is empty`);const m=h.includes("/")?h.substring(h.lastIndexOf("/")+1):h;if(!m.length)throw new Error(`cannot decode message ${l.$typeName} from JSON: "@type" is invalid`);const y=(p=c.registry)===null||p===void 0?void 0:p.getMessage(m);if(!y)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${h} is not in the type registry`);const g=Ee(y);if(m.startsWith("google.protobuf.")&&Object.prototype.hasOwnProperty.call(d,"value"))Ft(g,d.value,c);else{const v=Object.assign({},d);delete v["@type"],Ft(g,v,c)}(function(v,E,b){let I=!1;b||(b=xe(oc),I=!0),b.value=uc(v,E),b.typeUrl=`type.googleapis.com/${E.$typeName}`})(g.desc,g.message,l)}(i.message,o,u),!0;case"google.protobuf.Timestamp":return function(l,d){if(typeof d!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${Y(d)}`);const c=d.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:\.([0-9]{1,9}))?(?:Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!c)throw new Error(`cannot decode message ${l.$typeName} from JSON: invalid RFC 3339 string`);const p=Date.parse(c[1]+"-"+c[2]+"-"+c[3]+"T"+c[4]+":"+c[5]+":"+c[6]+(c[8]?c[8]:"Z"));if(Number.isNaN(p))throw new Error(`cannot decode message ${l.$typeName} from JSON: invalid RFC 3339 string`);if(p<Date.parse("0001-01-01T00:00:00Z")||p>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot decode message ${l.$typeName} from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);l.seconds=V.parse(p/1e3),l.nanos=0,c[7]&&(l.nanos=parseInt("1"+c[7]+"0".repeat(9-c[7].length))-1e9)}(i.message,o),!0;case"google.protobuf.Duration":return function(l,d){if(typeof d!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${Y(d)}`);const c=d.match(/^(-?[0-9]+)(?:\.([0-9]+))?s/);if(c===null)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${Y(d)}`);const p=Number(c[1]);if(p>315576e6||p<-315576e6)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${Y(d)}`);if(l.seconds=V.parse(p),typeof c[2]!="string")return;const h=c[2]+"0".repeat(9-c[2].length);l.nanos=parseInt(h),(p<0||Object.is(p,-0))&&(l.nanos=-l.nanos)}(i.message,o),!0;case"google.protobuf.FieldMask":return function(l,d){if(typeof d!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${Y(d)}`);if(d==="")return;function c(p){if(p.includes("_"))throw new Error(`cannot decode message ${l.$typeName} from JSON: path names must be lowerCamelCase`);const h=p.replace(/[A-Z]/g,m=>"_"+m.toLowerCase());return h[0]==="_"?h.substring(1):h}l.paths=d.split(",").map(c)}(i.message,o),!0;case"google.protobuf.Struct":return dl(i.message,o),!0;case"google.protobuf.Value":return Ta(i.message,o),!0;case"google.protobuf.ListValue":return pl(i.message,o),!0;default:if(Cn(i.desc)){const l=i.desc.fields[0];return o===null?i.clear(l):i.set(l,Hn(l,o,!0)),!0}return!1}}(e,t,n))return;if(t==null||Array.isArray(t)||typeof t!="object")throw new Error(`cannot decode ${e.desc} from JSON: ${Y(t)}`);const s=new Map,a=new Map;for(const i of e.desc.fields)a.set(i.name,i).set(i.jsonName,i);for(const[i,o]of Object.entries(t)){const u=a.get(i);if(u){if(u.oneof){if(o===null&&u.fieldKind=="scalar")continue;const l=s.get(u.oneof);if(l!==void 0)throw new ue(u.oneof,`oneof set multiple times by ${l.name} and ${u.name}`);s.set(u.oneof,u)}ki(e,u,o,n)}else{let l;if(i.startsWith("[")&&i.endsWith("]")&&(l=(r=n.registry)===null||r===void 0?void 0:r.getExtension(i.substring(1,i.length-1)))&&l.extendee.typeName===e.desc.typeName){const[d,c,p]=Ir(l);ki(d,c,o,n),fc(e.message,l,p())}if(!l&&!n.ignoreUnknownFields)throw new Error(`cannot decode ${e.desc} from JSON: key "${i}" is unknown`)}}}function ki(e,t,n,r){switch(t.fieldKind){case"scalar":(function(s,a,i){const o=Hn(a,i,!1);o===rr?s.clear(a):s.set(a,o)})(e,t,n);break;case"enum":(function(s,a,i,o){const u=Br(a.enum,i,o.ignoreUnknownFields,!1);u===rr?s.clear(a):u!==Yn&&s.set(a,u)})(e,t,n,r);break;case"message":(function(s,a,i,o){if(i===null&&a.message.typeName!="google.protobuf.Value")return void s.clear(a);const u=s.isSet(a)?s.get(a):Ee(a.message);Ft(u,i,o),s.set(a,u)})(e,t,n,r);break;case"list":(function(s,a,i){if(a===null)return;const o=s.field();if(!Array.isArray(a))throw new ue(o,"expected Array, got "+Y(a));for(const u of a){if(u===null)throw new ue(o,"list item must not be null");switch(o.listKind){case"message":const l=Ee(o.message);Ft(l,u,i),s.add(l);break;case"enum":const d=Br(o.enum,u,i.ignoreUnknownFields,!0);d!==Yn&&s.add(d);break;case"scalar":s.add(Hn(o,u,!0))}}})(e.get(t),n,r);break;case"map":(function(s,a,i){if(a===null)return;const o=s.field();if(typeof a!="object"||Array.isArray(a))throw new ue(o,"expected object, got "+Y(a));for(const[u,l]of Object.entries(a)){if(l===null)throw new ue(o,"map value must not be null");let d;switch(o.mapKind){case"message":const p=Ee(o.message);Ft(p,l,i),d=p;break;case"enum":if(d=Br(o.enum,l,i.ignoreUnknownFields,!0),d===Yn)return;break;case"scalar":d=Hn(o,l,!0)}const c=Sc(o.mapKey,u);s.set(c,d)}})(e.get(t),n,r)}}const Yn=Symbol();function Br(e,t,n,r){if(t===null)return e.typeName=="google.protobuf.NullValue"?0:r?e.values[0].number:rr;switch(typeof t){case"number":if(Number.isInteger(t))return t;break;case"string":const s=e.values.find(a=>a.name===t);if(s!==void 0)return s.number;if(n)return Yn}throw new Error(`cannot decode ${e} from JSON: ${Y(t)}`)}const rr=Symbol();function Hn(e,t,n){if(t===null)return n?vt(e.scalar,!1):rr;switch(e.scalar){case _.DOUBLE:case _.FLOAT:if(t==="NaN")return NaN;if(t==="Infinity")return Number.POSITIVE_INFINITY;if(t==="-Infinity")return Number.NEGATIVE_INFINITY;if(typeof t=="number"){if(Number.isNaN(t))throw new ue(e,"unexpected NaN number");if(!Number.isFinite(t))throw new ue(e,"unexpected infinite number");break}if(typeof t=="string"){if(t===""||t.trim().length!==t.length)break;const r=Number(t);if(!Number.isFinite(r))break;return r}break;case _.INT32:case _.FIXED32:case _.SFIXED32:case _.SINT32:case _.UINT32:return cl(t);case _.BYTES:if(typeof t=="string"){if(t==="")return new Uint8Array(0);try{return Ho(t)}catch(r){const s=r instanceof Error?r.message:String(r);throw new ue(e,s)}}}return t}function Sc(e,t){switch(e){case _.BOOL:switch(t){case"true":return!0;case"false":return!1}return t;case _.INT32:case _.FIXED32:case _.UINT32:case _.SFIXED32:case _.SINT32:return cl(t);default:return t}}function cl(e){if(typeof e=="string"){if(e===""||e.trim().length!==e.length)return e;const t=Number(e);return Number.isNaN(t)?e:t}return e}function dl(e,t){if(typeof t!="object"||t==null||Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${Y(t)}`);for(const[n,r]of Object.entries(t)){const s=xe(il);Ta(s,r),e.fields[n]=s}}function Ta(e,t){switch(typeof t){case"number":e.kind={case:"numberValue",value:t};break;case"string":e.kind={case:"stringValue",value:t};break;case"boolean":e.kind={case:"boolValue",value:t};break;case"object":if(t===null)e.kind={case:"nullValue",value:_s.NULL_VALUE};else if(Array.isArray(t)){const n=xe(hc);pl(n,t),e.kind={case:"listValue",value:n}}else{const n=xe(pc);dl(n,t),e.kind={case:"structValue",value:n}}break;default:throw new Error(`cannot decode message ${e.$typeName} from JSON ${Y(t)}`)}return e}function pl(e,t){if(!Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${Y(t)}`);for(const n of t){const r=xe(il);Ta(r,n),e.values.push(r)}}class hl{constructor(t){f(this,"target");f(this,"pendingRequests",new Map);f(this,"cleanup");f(this,"serviceRegistries",new Set);this.target=t,this.cleanup=this.target.onReceiveMessage(this.handleMessage.bind(this))}addServiceRegistry(t){this.serviceRegistries.add(t)}removeServiceRegistry(t){this.serviceRegistries.delete(t)}handleMessage(t){if(!t||typeof t!="object"||!this.isGrpcMessageLike(t))return;const n=t;n.type==="com.augmentcode.client.rpc.request"?this.handleRequest(n):n.type==="com.augmentcode.client.rpc.response"&&this.handleResponse(n)}isGrpcMessageLike(t){return"type"in t&&t.type==="com.augmentcode.client.rpc.request"||t.type==="com.augmentcode.client.rpc.response"}async handleRequest(t){for(const n of this.serviceRegistries)if(n.canHandle(t))try{return void await n.handleRequest(t,r=>{this.target.sendMessage(r)})}catch(r){Array.from(this.serviceRegistries).indexOf(n)===this.serviceRegistries.size-1&&this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:r instanceof Error?r.message:String(r)})}this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:`No handlers registered for service: ${t.serviceTypeName}`})}handleResponse(t){const n=this.pendingRequests.get(t.id);if(n)if(this.pendingRequests.delete(t.id),clearTimeout(n.timeout),t.error)n.reject(new Error(`gRPC server error for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${t.error}`));else try{if(!t.data&&t.data!==null&&t.data!=="")throw new Error(`gRPC response missing data field for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id})`);n.resolve(t)}catch(r){const s=r instanceof Error?r.message:String(r);n.reject(new Error(`Failed to process gRPC response for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${s}`))}}sendRequest(t,n){return new Promise((r,s)=>{let a;n&&(a=setTimeout(()=>{this.pendingRequests.delete(t.id),s(new Error(`gRPC request timed out after ${n}ms: ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}). This may indicate that the server is not responding or the message routing is broken.`))},n)),this.pendingRequests.set(t.id,{resolve:r,reject:s,timeout:a}),this.target.sendMessage(t)})}async unary(t,n,r,s,a,i){const o=crypto.randomUUID(),u=t.localName,l=t.parent.typeName;if(!l)throw new Error("Service name is required for unary calls");const d=a?_c(t.input,xe(t.input,a)):{};if(n!=null&&n.aborted)throw new Error(`gRPC request aborted before sending: ${l}.${u} (ID: ${o})`);let c;n&&(c=()=>{const h=this.pendingRequests.get(o);h&&(this.pendingRequests.delete(o),clearTimeout(h.timeout),h.reject(new Error(`gRPC request aborted during execution: ${l}.${u} (ID: ${o})`)))},n.addEventListener("abort",c));const p=await this.sendRequest({type:"com.augmentcode.client.rpc.request",id:o,methodLocalName:u,serviceTypeName:l,data:d,timeout:r},r);return n&&c&&n.removeEventListener("abort",c),{stream:!1,method:t,service:t.parent,header:new Headers(s),message:bc(t.output,p.data),trailer:new Headers}}stream(t,n,r,s,a,i){throw new Error("Streaming is not supported by this transport")}dispose(){this.cleanup();for(const{timeout:t}of this.pendingRequests.values())clearTimeout(t);this.pendingRequests.clear(),this.serviceRegistries.clear()}}f(hl,"PROTOCOL_NAME","com.augmentcode.client.rpc");var at;function Ni(e){const t=at[e];return typeof t!="string"?e.toString():t[0].toLowerCase()+t.substring(1).replace(/[A-Z]/g,n=>"_"+n.toLowerCase())}(function(e){e[e.Canceled=1]="Canceled",e[e.Unknown=2]="Unknown",e[e.InvalidArgument=3]="InvalidArgument",e[e.DeadlineExceeded=4]="DeadlineExceeded",e[e.NotFound=5]="NotFound",e[e.AlreadyExists=6]="AlreadyExists",e[e.PermissionDenied=7]="PermissionDenied",e[e.ResourceExhausted=8]="ResourceExhausted",e[e.FailedPrecondition=9]="FailedPrecondition",e[e.Aborted=10]="Aborted",e[e.OutOfRange=11]="OutOfRange",e[e.Unimplemented=12]="Unimplemented",e[e.Internal=13]="Internal",e[e.Unavailable=14]="Unavailable",e[e.DataLoss=15]="DataLoss",e[e.Unauthenticated=16]="Unauthenticated"})(at||(at={}));class tt extends Error{constructor(t,n=at.Unknown,r,s,a){super(function(i,o){return i.length?`[${Ni(o)}] ${i}`:`[${Ni(o)}]`}(t,n)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=t,this.code=n,this.metadata=new Headers(r??{}),this.details=s??[],this.cause=a}static from(t,n=at.Unknown){return t instanceof tt?t:t instanceof Error?t.name=="AbortError"?new tt(t.message,at.Canceled):new tt(t.message,n,void 0,void 0,t):new tt(String(t),n,void 0,void 0,t)}static[Symbol.hasInstance](t){return t instanceof Error&&(Object.getPrototypeOf(t)===tt.prototype||t.name==="ConnectError"&&"code"in t&&typeof t.code=="number"&&"metadata"in t&&"details"in t&&Array.isArray(t.details)&&"rawMessage"in t&&typeof t.rawMessage=="string"&&"cause"in t)}findDetails(t){const n=t.kind==="message"?{getMessage:s=>s===t.typeName?t:void 0}:t,r=[];for(const s of this.details){if("desc"in s){n.getMessage(s.desc.typeName)&&r.push(xe(s.desc,s.value));continue}const a=n.getMessage(s.type);if(a)try{r.push(va(a,s.value))}catch{}}return r}}var Ec=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(s){t[s]=e[s]&&function(a){return new Promise(function(i,o){(function(u,l,d,c){Promise.resolve(c).then(function(p){u({value:p,done:d})},l)})(i,o,(a=e[s](a)).done,a.value)})}}},pn=function(e){return this instanceof pn?(this.v=e,this):new pn(e)},Tc=function(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,s=n.apply(e,t||[]),a=[];return r=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),i("next"),i("throw"),i("return",function(c){return function(p){return Promise.resolve(p).then(c,l)}}),r[Symbol.asyncIterator]=function(){return this},r;function i(c,p){s[c]&&(r[c]=function(h){return new Promise(function(m,y){a.push([c,h,m,y])>1||o(c,h)})},p&&(r[c]=p(r[c])))}function o(c,p){try{(h=s[c](p)).value instanceof pn?Promise.resolve(h.value.v).then(u,l):d(a[0][2],h)}catch(m){d(a[0][3],m)}var h}function u(c){o("next",c)}function l(c){o("throw",c)}function d(c,p){c(p),a.shift(),a.length&&o(a[0][0],a[0][1])}},wc=function(e){var t,n;return t={},r("next"),r("throw",function(s){throw s}),r("return"),t[Symbol.iterator]=function(){return this},t;function r(s,a){t[s]=e[s]?function(i){return(n=!n)?{value:pn(e[s](i)),done:!1}:a?a(i):i}:a}},ml=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(s){t[s]=e[s]&&function(a){return new Promise(function(i,o){(function(u,l,d,c){Promise.resolve(c).then(function(p){u({value:p,done:d})},l)})(i,o,(a=e[s](a)).done,a.value)})}}},Bt=function(e){return this instanceof Bt?(this.v=e,this):new Bt(e)},kc=function(e){var t,n;return t={},r("next"),r("throw",function(s){throw s}),r("return"),t[Symbol.iterator]=function(){return this},t;function r(s,a){t[s]=e[s]?function(i){return(n=!n)?{value:Bt(e[s](i)),done:!1}:a?a(i):i}:a}},Nc=function(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,s=n.apply(e,t||[]),a=[];return r=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),i("next"),i("throw"),i("return",function(c){return function(p){return Promise.resolve(p).then(c,l)}}),r[Symbol.asyncIterator]=function(){return this},r;function i(c,p){s[c]&&(r[c]=function(h){return new Promise(function(m,y){a.push([c,h,m,y])>1||o(c,h)})},p&&(r[c]=p(r[c])))}function o(c,p){try{(h=s[c](p)).value instanceof Bt?Promise.resolve(h.value.v).then(u,l):d(a[0][2],h)}catch(m){d(a[0][3],m)}var h}function u(c){o("next",c)}function l(c){o("throw",c)}function d(c,p){c(p),a.shift(),a.length&&o(a[0][0],a[0][1])}};function Ic(e,t){return function(n,r){const s={};for(const a of n.methods){const i=r(a);i!=null&&(s[a.localName]=i)}return s}(e,n=>{switch(n.methodKind){case"unary":return function(r,s){return async function(a,i){var o,u;const l=await r.unary(s,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,a,i==null?void 0:i.contextValues);return(o=i==null?void 0:i.onHeader)===null||o===void 0||o.call(i,l.header),(u=i==null?void 0:i.onTrailer)===null||u===void 0||u.call(i,l.trailer),l.message}}(t,n);case"server_streaming":return function(r,s){return function(a,i){return Ii(r.stream(s,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,function(o){return Tc(this,arguments,function*(){yield pn(yield*wc(Ec(o)))})}([a]),i==null?void 0:i.contextValues),i)}}(t,n);case"client_streaming":return function(r,s){return async function(a,i){var o,u,l,d,c,p;const h=await r.stream(s,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,a,i==null?void 0:i.contextValues);let m;(c=i==null?void 0:i.onHeader)===null||c===void 0||c.call(i,h.header);let y=0;try{for(var g,v=!0,E=ml(h.message);!(o=(g=await E.next()).done);v=!0)d=g.value,v=!1,m=d,y++}catch(b){u={error:b}}finally{try{v||o||!(l=E.return)||await l.call(E)}finally{if(u)throw u.error}}if(!m)throw new tt("protocol error: missing response message",at.Unimplemented);if(y>1)throw new tt("protocol error: received extra messages for client streaming method",at.Unimplemented);return(p=i==null?void 0:i.onTrailer)===null||p===void 0||p.call(i,h.trailer),m}}(t,n);case"bidi_streaming":return function(r,s){return function(a,i){return Ii(r.stream(s,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,a,i==null?void 0:i.contextValues),i)}}(t,n);default:return null}})}function Ii(e,t){const n=function(){return Nc(this,arguments,function*(){var r,s;const a=yield Bt(e);(r=t==null?void 0:t.onHeader)===null||r===void 0||r.call(t,a.header),yield Bt(yield*kc(ml(a.message))),(s=t==null?void 0:t.onTrailer)===null||s===void 0||s.call(t,a.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>n.next()})}}async function Ci(e){const t=await crypto.subtle.digest("SHA-256",e);return Array.from(new Uint8Array(t)).map(n=>n.toString(16).padStart(2,"0")).join("")}for(var jr=256,Cc=[];jr--;)Cc[jr]=(jr+256).toString(16).substring(1);function Ac(e){throw new Error("Logger not initialized. Call setLibraryLogger() before using getLogger().")}const xc={EXPERIMENT_VIEWED:"experiment_viewed",THREAD_CREATION_ATTEMPTED:"thread_creation_attempted",SEND_ACTION_TRIGGERED:"send_action_triggered",CANCEL_ACTION_TRIGGERED:"cancel_action_triggered",RESEND_ACTION_TRIGGERED:"resend_action_triggered",AGENT_EXECUTION_MODE_TOGGLED:"agent_execution_mode_toggled",MESSAGE_SEND_ERROR_DISPLAYED:"message_send_error_displayed",MESSAGE_SEND_RETRY_CLICKED:"message_send_retry_clicked",MESSAGE_SEND_TIMING:"message_sent_timing",VSCODE_EXTENSION_STARTUP:"vscode_extension_started_up",NOTIFICATION_DISPLAYED:"notification_displayed",NOTIFICATION_DISMISSED:"notification_dismissed",TOOL_CONNECT_BUTTON_CLICKED:"tool_connect_button_clicked",TOOL_CONNECTED:"tool_connected",TURN_SUMMARY_BUTTON_CLICKED:"turn_summary_button_clicked",SUBSCRIPTION_WARNING_UPGRADE_CLICKED:"subscription_warning_upgrade_clicked"};var fl=(e=>(e.chat="chat",e))(fl||{}),Rc=(e=>(e.chatMentionFolder="chat-mention-folder",e.chatMentionFile="chat-mention-file",e.chatMentionExternalSource="chat-mention-external-source",e.chatClearContext="chat-clear-context",e.chatRestoreDefaultContext="chat-restore-default-context",e.chatUseActionFind="chat-use-action-find",e.chatUseActionExplain="chat-use-action-explain",e.chatUseActionWriteTest="chat-use-action-write-test",e.chatNewConversation="chat-new-conversation",e.chatEditConversationName="chat-edit-conversation-name",e.chatFailedSmartPasteResolveFile="chat-failed-smart-paste-resolve-file",e.chatPrecomputeSmartPaste="chat-precompute-smart-paste",e.chatSmartPaste="chat-smart-paste",e.chatCodeblockCopy="chat-codeblock-copy",e.chatCodeblockCreate="chat-codeblock-create",e.chatCodeblockGoToFile="chat-codeblock-go-to-file",e.chatCodespanGoToFile="chat-codespan-go-to-file",e.chatCodespanGoToSymbol="chat-codespan-go-to-symbol",e.chatMermaidblockInitialize="chat-mermaidblock-initialize",e.chatMermaidblockToggle="chat-mermaidblock-toggle",e.chatMermaidblockInteract="chat-mermaidblock-interact",e.chatMermaidBlockError="chat-mermaidblock-error",e.chatUseSuggestedQuestion="chat-use-suggested-question",e.chatDisplaySuggestedQuestions="chat-display-suggested-questions",e.setWorkspaceGuidelines="chat-set-workspace-guidelines",e.clearWorkspaceGuidelines="chat-clear-workspace-guidelines",e.setUserGuidelines="chat-set-user-guidelines",e.clearUserGuidelines="chat-clear-user-guidelines",e))(Rc||{});function Ai(e){return e.replace(/^data:.*?;base64,/,"")}async function Vr(e){return new Promise((t,n)=>{const r=new FileReader;r.onload=s=>{var a;return t((a=s.target)==null?void 0:a.result)},r.onerror=n,r.readAsDataURL(e)})}async function Gr(e){return e.length<1e4?Promise.resolve(function(t){const n=atob(t);return Uint8Array.from(n,r=>r.codePointAt(0)||0)}(e)):new Promise((t,n)=>{const r=new Worker(URL.createObjectURL(new Blob([`
            self.onmessage = function(e) {
              try {
                const base64 = e.data;
                const binString = atob(base64);
                const bytes = new Uint8Array(binString.length);
                for (let i = 0; i < binString.length; i++) {
                  bytes[i] = binString.charCodeAt(i);
                }
                self.postMessage(bytes, [bytes.buffer]);
              } catch (error) {
                self.postMessage({ error: error.message });
              }
            };
            `],{type:"application/javascript"})));r.onmessage=function(s){s.data.error?n(new Error(s.data.error)):t(s.data),r.terminate()},r.onerror=function(s){n(s.error),r.terminate()},r.postMessage(e)})}function Oc(e,t,...n){if(n.length>0)throw new Error;return e.services[t]}const Mc=Oc(ba("Ci5jbGllbnRzL3NpZGVjYXIvbGlicy9wcm90b3MvdGVzdF9zZXJ2aWNlLnByb3RvEgR0ZXN0IhoKC1Rlc3RSZXF1ZXN0EgsKA2ZvbxgBIAEoCSIeCgxUZXN0UmVzcG9uc2USDgoGcmVzdWx0GAEgASgJMngKC1Rlc3RTZXJ2aWNlEjMKClRlc3RNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2USNAoLRXJyb3JNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2ViBnByb3RvMw"),0);var W=(e=>(e.getEditListRequest="agent-get-edit-list-request",e.getEditListResponse="agent-get-edit-list-response",e.getEditChangesByRequestIdRequest="agent-get-edit-changes-by-request-id-request",e.getEditChangesByRequestIdResponse="agent-get-edit-changes-by-request-id-response",e.setCurrentConversation="agent-set-current-conversation",e.migrateConversationId="agent-migrate-conversation-id",e.revertToTimestamp="revert-to-timestamp",e.chatAgentEditAcceptAll="chat-agent-edit-accept-all",e.reportAgentSessionEvent="report-agent-session-event",e.reportAgentRequestEvent="report-agent-request-event",e.chatReviewAgentFile="chat-review-agent-file",e.getAgentEditContentsByRequestId="get-agent-edit-contents-by-request-id",e.getAgentEditContentsByRequestIdResponse="get-agent-edit-contents-by-request-id-response",e.checkHasEverUsedAgent="check-has-ever-used-agent",e.checkHasEverUsedAgentResponse="check-has-ever-used-agent-response",e.setHasEverUsedAgent="set-has-ever-used-agent",e.checkHasEverUsedRemoteAgent="check-has-ever-used-remote-agent",e.checkHasEverUsedRemoteAgentResponse="check-has-ever-used-remote-agent-response",e.setHasEverUsedRemoteAgent="set-has-ever-used-remote-agent",e.getSoundSettings="get-sound-settings",e.getSoundSettingsResponse="get-sound-settings-response",e.updateSoundSettings="update-sound-settings",e.soundSettingsBroadcast="sound-settings-broadcast",e.getSwarmModeSettings="get-swarm-mode-settings",e.getSwarmModeSettingsResponse="get-swarm-mode-settings-response",e.updateSwarmModeSettings="update-swarm-mode-settings",e.swarmModeSettingsBroadcast="swarm-mode-settings-broadcast",e.getChatModeRequest="get-chat-mode-request",e.getChatModeResponse="get-chat-mode-response",e))(W||{}),vs=(e=>(e.trackAnalyticsEvent="track-analytics-event",e.trackExperimentViewedEvent="track-experiment-viewed-event",e))(vs||{}),Wn=(e=>(e.checkToolCallSafeRequest="check-tool-call-safe-request",e.checkToolCallSafeResponse="check-tool-call-safe-response",e.closeAllToolProcesses="close-all-tool-processes",e.getToolIdentifierRequest="get-tool-identifier-request",e.getToolIdentifierResponse="get-tool-identifier-response",e))(Wn||{}),zn=(e=>(e.loadConversationExchangesRequest="load-conversation-exchanges-request",e.loadConversationExchangesResponse="load-conversation-exchanges-response",e.loadExchangesByUuidsRequest="load-exchanges-by-uuids-request",e.loadExchangesByUuidsResponse="load-exchanges-by-uuids-response",e.saveExchangesRequest="save-exchanges-request",e.saveExchangesResponse="save-exchanges-response",e.deleteExchangesRequest="delete-exchanges-request",e.deleteExchangesResponse="delete-exchanges-response",e.deleteConversationExchangesRequest="delete-conversation-exchanges-request",e.deleteConversationExchangesResponse="delete-conversation-exchanges-response",e.countExchangesRequest="count-exchanges-request",e.countExchangesResponse="count-exchanges-response",e))(zn||{});async function*Pc(e,t=1e3){for(;e>0;)yield e,await new Promise(n=>setTimeout(n,Math.min(t,e))),e-=t}class Lc{constructor(t,n,r,s=5,a=4e3,i){f(this,"_isCancelled",!1);this.requestId=t,this.chatMessage=n,this.startStreamFn=r,this.maxRetries=s,this.baseDelay=a,this.flags=i}cancel(){this._isCancelled=!0}async*getStream(){let t=0,n=0,r=!1;try{for(;!this._isCancelled;){const s=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let a,i,o=!1,u=!0;for await(const l of s){if(l.status===pe.failed){if(l.isRetriable!==!0||r)return yield l;o=!0,u=l.shouldBackoff??!0,a=l.display_error_message,i=l.request_id;break}r=!0,yield l}if(!o)return;if(this._isCancelled)return yield this.createCancelledStatus();if(t++,t>this.maxRetries)return console.error(`Failed after ${this.maxRetries} attempts: ${a}`),void(yield{request_id:i??this.requestId,seen_state:De.unseen,status:pe.failed,display_error_message:a,isRetriable:!1});if(u){const l=this.baseDelay*2**n;n++;for await(const d of Pc(l))yield{request_id:this.requestId,status:pe.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(d/1e3)} seconds... (Attempt ${t} of ${this.maxRetries})`,isRetriable:!0}}yield{request_id:this.requestId,status:pe.sent,display_error_message:`Generating response... (Attempt ${t+1})`,isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(s){console.error("Unexpected error in chat stream:",s),yield{request_id:this.requestId,seen_state:De.unseen,status:pe.failed,display_error_message:s instanceof Error?s.message:String(s)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:De.unseen,status:pe.cancelled}}}var xt=(e=>(e.getHydratedTaskRequest="get-hydrated-task-request",e.getHydratedTaskResponse="get-hydrated-task-response",e.setCurrentRootTaskUuid="set-current-root-task-uuid",e.createTaskRequest="create-task-request",e.createTaskResponse="create-task-response",e.updateTaskRequest="update-task-request",e.updateTaskResponse="update-task-response",e.updateHydratedTaskRequest="update-hydrated-task-request",e.updateHydratedTaskResponse="update-hydrated-task-response",e))(xt||{});class Dc{constructor(t){f(this,"getHydratedTask",async t=>{const n={type:xt.getHydratedTaskRequest,data:{uuid:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.task});f(this,"createTask",async(t,n,r)=>{const s={type:xt.createTaskRequest,data:{name:t,description:n,parentTaskUuid:r}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data.uuid});f(this,"updateTask",async(t,n,r)=>{const s={type:xt.updateTaskRequest,data:{uuid:t,updates:n,updatedBy:r}};await this._asyncMsgSender.sendToSidecar(s,3e4)});f(this,"setCurrentRootTaskUuid",t=>{const n={type:xt.setCurrentRootTaskUuid,data:{uuid:t}};this._asyncMsgSender.sendToSidecar(n)});f(this,"updateHydratedTask",async(t,n)=>{const r={type:xt.updateHydratedTaskRequest,data:{task:t,updatedBy:n}};return(await this._asyncMsgSender.sendToSidecar(r,3e4)).data});this._asyncMsgSender=t}}var me=(e=>(e.NOT_STARTED="NOT_STARTED",e.IN_PROGRESS="IN_PROGRESS",e.CANCELLED="CANCELLED",e.COMPLETE="COMPLETE",e))(me||{}),wa=(e=>(e.USER="USER",e.AGENT="AGENT",e))(wa||{}),gl={},sr={},ar={};let Ln;Object.defineProperty(ar,"__esModule",{value:!0}),ar.default=function(){if(!Ln&&(Ln=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Ln))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Ln(Fc)};const Fc=new Uint8Array(16);var it={},bt={},ir={};Object.defineProperty(ir,"__esModule",{value:!0}),ir.default=void 0;ir.default=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,Object.defineProperty(bt,"__esModule",{value:!0}),bt.default=void 0;var Dn,$c=(Dn=ir)&&Dn.__esModule?Dn:{default:Dn},Uc=function(e){return typeof e=="string"&&$c.default.test(e)};bt.default=Uc,Object.defineProperty(it,"__esModule",{value:!0}),it.default=void 0,it.unsafeStringify=yl;var Bc=function(e){return e&&e.__esModule?e:{default:e}}(bt);const z=[];for(let e=0;e<256;++e)z.push((e+256).toString(16).slice(1));function yl(e,t=0){return z[e[t+0]]+z[e[t+1]]+z[e[t+2]]+z[e[t+3]]+"-"+z[e[t+4]]+z[e[t+5]]+"-"+z[e[t+6]]+z[e[t+7]]+"-"+z[e[t+8]]+z[e[t+9]]+"-"+z[e[t+10]]+z[e[t+11]]+z[e[t+12]]+z[e[t+13]]+z[e[t+14]]+z[e[t+15]]}var jc=function(e,t=0){const n=yl(e,t);if(!(0,Bc.default)(n))throw TypeError("Stringified UUID is invalid");return n};it.default=jc,Object.defineProperty(sr,"__esModule",{value:!0}),sr.default=void 0;var Vc=function(e){return e&&e.__esModule?e:{default:e}}(ar),Gc=it;let xi,qr,Zr=0,Kr=0;var qc=function(e,t,n){let r=t&&n||0;const s=t||new Array(16);let a=(e=e||{}).node||xi,i=e.clockseq!==void 0?e.clockseq:qr;if(a==null||i==null){const p=e.random||(e.rng||Vc.default)();a==null&&(a=xi=[1|p[0],p[1],p[2],p[3],p[4],p[5]]),i==null&&(i=qr=16383&(p[6]<<8|p[7]))}let o=e.msecs!==void 0?e.msecs:Date.now(),u=e.nsecs!==void 0?e.nsecs:Kr+1;const l=o-Zr+(u-Kr)/1e4;if(l<0&&e.clockseq===void 0&&(i=i+1&16383),(l<0||o>Zr)&&e.nsecs===void 0&&(u=0),u>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");Zr=o,Kr=u,qr=i,o+=122192928e5;const d=(1e4*(268435455&o)+u)%4294967296;s[r++]=d>>>24&255,s[r++]=d>>>16&255,s[r++]=d>>>8&255,s[r++]=255&d;const c=o/4294967296*1e4&268435455;s[r++]=c>>>8&255,s[r++]=255&c,s[r++]=c>>>24&15|16,s[r++]=c>>>16&255,s[r++]=i>>>8|128,s[r++]=255&i;for(let p=0;p<6;++p)s[r+p]=a[p];return t||(0,Gc.unsafeStringify)(s)};sr.default=qc;var or={},nt={},hn={};Object.defineProperty(hn,"__esModule",{value:!0}),hn.default=void 0;var Zc=function(e){return e&&e.__esModule?e:{default:e}}(bt),Kc=function(e){if(!(0,Zc.default)(e))throw TypeError("Invalid UUID");let t;const n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n};hn.default=Kc,Object.defineProperty(nt,"__esModule",{value:!0}),nt.URL=nt.DNS=void 0,nt.default=function(e,t,n){function r(s,a,i,o){var u;if(typeof s=="string"&&(s=function(d){d=unescape(encodeURIComponent(d));const c=[];for(let p=0;p<d.length;++p)c.push(d.charCodeAt(p));return c}(s)),typeof a=="string"&&(a=(0,Hc.default)(a)),((u=a)===null||u===void 0?void 0:u.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let l=new Uint8Array(16+s.length);if(l.set(a),l.set(s,a.length),l=n(l),l[6]=15&l[6]|t,l[8]=63&l[8]|128,i){o=o||0;for(let d=0;d<16;++d)i[o+d]=l[d];return i}return(0,Yc.unsafeStringify)(l)}try{r.name=e}catch{}return r.DNS=_l,r.URL=vl,r};var Yc=it,Hc=function(e){return e&&e.__esModule?e:{default:e}}(hn);const _l="6ba7b810-9dad-11d1-80b4-00c04fd430c8";nt.DNS=_l;const vl="6ba7b811-9dad-11d1-80b4-00c04fd430c8";nt.URL=vl;var lr={};function Ri(e){return 14+(e+64>>>9<<4)+1}function rt(e,t){const n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function Cr(e,t,n,r,s,a){return rt((i=rt(rt(t,e),rt(r,a)))<<(o=s)|i>>>32-o,n);var i,o}function J(e,t,n,r,s,a,i){return Cr(t&n|~t&r,e,t,s,a,i)}function Q(e,t,n,r,s,a,i){return Cr(t&r|n&~r,e,t,s,a,i)}function ee(e,t,n,r,s,a,i){return Cr(t^n^r,e,t,s,a,i)}function te(e,t,n,r,s,a,i){return Cr(n^(t|~r),e,t,s,a,i)}Object.defineProperty(lr,"__esModule",{value:!0}),lr.default=void 0;var Wc=function(e){if(typeof e=="string"){const t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(let n=0;n<t.length;++n)e[n]=t.charCodeAt(n)}return function(t){const n=[],r=32*t.length,s="0123456789abcdef";for(let a=0;a<r;a+=8){const i=t[a>>5]>>>a%32&255,o=parseInt(s.charAt(i>>>4&15)+s.charAt(15&i),16);n.push(o)}return n}(function(t,n){t[n>>5]|=128<<n%32,t[Ri(n)-1]=n;let r=1732584193,s=-271733879,a=-1732584194,i=271733878;for(let o=0;o<t.length;o+=16){const u=r,l=s,d=a,c=i;r=J(r,s,a,i,t[o],7,-680876936),i=J(i,r,s,a,t[o+1],12,-389564586),a=J(a,i,r,s,t[o+2],17,606105819),s=J(s,a,i,r,t[o+3],22,-1044525330),r=J(r,s,a,i,t[o+4],7,-176418897),i=J(i,r,s,a,t[o+5],12,1200080426),a=J(a,i,r,s,t[o+6],17,-1473231341),s=J(s,a,i,r,t[o+7],22,-45705983),r=J(r,s,a,i,t[o+8],7,1770035416),i=J(i,r,s,a,t[o+9],12,-1958414417),a=J(a,i,r,s,t[o+10],17,-42063),s=J(s,a,i,r,t[o+11],22,-1990404162),r=J(r,s,a,i,t[o+12],7,1804603682),i=J(i,r,s,a,t[o+13],12,-40341101),a=J(a,i,r,s,t[o+14],17,-1502002290),s=J(s,a,i,r,t[o+15],22,1236535329),r=Q(r,s,a,i,t[o+1],5,-165796510),i=Q(i,r,s,a,t[o+6],9,-1069501632),a=Q(a,i,r,s,t[o+11],14,643717713),s=Q(s,a,i,r,t[o],20,-373897302),r=Q(r,s,a,i,t[o+5],5,-701558691),i=Q(i,r,s,a,t[o+10],9,38016083),a=Q(a,i,r,s,t[o+15],14,-660478335),s=Q(s,a,i,r,t[o+4],20,-405537848),r=Q(r,s,a,i,t[o+9],5,568446438),i=Q(i,r,s,a,t[o+14],9,-1019803690),a=Q(a,i,r,s,t[o+3],14,-187363961),s=Q(s,a,i,r,t[o+8],20,1163531501),r=Q(r,s,a,i,t[o+13],5,-1444681467),i=Q(i,r,s,a,t[o+2],9,-51403784),a=Q(a,i,r,s,t[o+7],14,1735328473),s=Q(s,a,i,r,t[o+12],20,-1926607734),r=ee(r,s,a,i,t[o+5],4,-378558),i=ee(i,r,s,a,t[o+8],11,-2022574463),a=ee(a,i,r,s,t[o+11],16,1839030562),s=ee(s,a,i,r,t[o+14],23,-35309556),r=ee(r,s,a,i,t[o+1],4,-1530992060),i=ee(i,r,s,a,t[o+4],11,1272893353),a=ee(a,i,r,s,t[o+7],16,-155497632),s=ee(s,a,i,r,t[o+10],23,-1094730640),r=ee(r,s,a,i,t[o+13],4,681279174),i=ee(i,r,s,a,t[o],11,-358537222),a=ee(a,i,r,s,t[o+3],16,-722521979),s=ee(s,a,i,r,t[o+6],23,76029189),r=ee(r,s,a,i,t[o+9],4,-640364487),i=ee(i,r,s,a,t[o+12],11,-421815835),a=ee(a,i,r,s,t[o+15],16,530742520),s=ee(s,a,i,r,t[o+2],23,-995338651),r=te(r,s,a,i,t[o],6,-198630844),i=te(i,r,s,a,t[o+7],10,1126891415),a=te(a,i,r,s,t[o+14],15,-1416354905),s=te(s,a,i,r,t[o+5],21,-57434055),r=te(r,s,a,i,t[o+12],6,1700485571),i=te(i,r,s,a,t[o+3],10,-1894986606),a=te(a,i,r,s,t[o+10],15,-1051523),s=te(s,a,i,r,t[o+1],21,-2054922799),r=te(r,s,a,i,t[o+8],6,1873313359),i=te(i,r,s,a,t[o+15],10,-30611744),a=te(a,i,r,s,t[o+6],15,-1560198380),s=te(s,a,i,r,t[o+13],21,1309151649),r=te(r,s,a,i,t[o+4],6,-145523070),i=te(i,r,s,a,t[o+11],10,-1120210379),a=te(a,i,r,s,t[o+2],15,718787259),s=te(s,a,i,r,t[o+9],21,-343485551),r=rt(r,u),s=rt(s,l),a=rt(a,d),i=rt(i,c)}return[r,s,a,i]}(function(t){if(t.length===0)return[];const n=8*t.length,r=new Uint32Array(Ri(n));for(let s=0;s<n;s+=8)r[s>>5]|=(255&t[s/8])<<s%32;return r}(e),8*e.length))};lr.default=Wc,Object.defineProperty(or,"__esModule",{value:!0}),or.default=void 0;var zc=bl(nt),Xc=bl(lr);function bl(e){return e&&e.__esModule?e:{default:e}}var Jc=(0,zc.default)("v3",48,Xc.default);or.default=Jc;var ur={},cr={};Object.defineProperty(cr,"__esModule",{value:!0}),cr.default=void 0;var Qc={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};cr.default=Qc,Object.defineProperty(ur,"__esModule",{value:!0}),ur.default=void 0;var Oi=Sl(cr),ed=Sl(ar),td=it;function Sl(e){return e&&e.__esModule?e:{default:e}}var nd=function(e,t,n){if(Oi.default.randomUUID&&!t&&!e)return Oi.default.randomUUID();const r=(e=e||{}).random||(e.rng||ed.default)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(let s=0;s<16;++s)t[n+s]=r[s];return t}return(0,td.unsafeStringify)(r)};ur.default=nd;var dr={},pr={};function rd(e,t,n,r){switch(e){case 0:return t&n^~t&r;case 1:case 3:return t^n^r;case 2:return t&n^t&r^n&r}}function Yr(e,t){return e<<t|e>>>32-t}Object.defineProperty(pr,"__esModule",{value:!0}),pr.default=void 0;var sd=function(e){const t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof e=="string"){const i=unescape(encodeURIComponent(e));e=[];for(let o=0;o<i.length;++o)e.push(i.charCodeAt(o))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);const r=e.length/4+2,s=Math.ceil(r/16),a=new Array(s);for(let i=0;i<s;++i){const o=new Uint32Array(16);for(let u=0;u<16;++u)o[u]=e[64*i+4*u]<<24|e[64*i+4*u+1]<<16|e[64*i+4*u+2]<<8|e[64*i+4*u+3];a[i]=o}a[s-1][14]=8*(e.length-1)/Math.pow(2,32),a[s-1][14]=Math.floor(a[s-1][14]),a[s-1][15]=8*(e.length-1)&4294967295;for(let i=0;i<s;++i){const o=new Uint32Array(80);for(let h=0;h<16;++h)o[h]=a[i][h];for(let h=16;h<80;++h)o[h]=Yr(o[h-3]^o[h-8]^o[h-14]^o[h-16],1);let u=n[0],l=n[1],d=n[2],c=n[3],p=n[4];for(let h=0;h<80;++h){const m=Math.floor(h/20),y=Yr(u,5)+rd(m,l,d,c)+p+t[m]+o[h]>>>0;p=c,c=d,d=Yr(l,30)>>>0,l=u,u=y}n[0]=n[0]+u>>>0,n[1]=n[1]+l>>>0,n[2]=n[2]+d>>>0,n[3]=n[3]+c>>>0,n[4]=n[4]+p>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]};pr.default=sd,Object.defineProperty(dr,"__esModule",{value:!0}),dr.default=void 0;var ad=El(nt),id=El(pr);function El(e){return e&&e.__esModule?e:{default:e}}var od=(0,ad.default)("v5",80,id.default);dr.default=od;var hr={};Object.defineProperty(hr,"__esModule",{value:!0}),hr.default=void 0;hr.default="00000000-0000-0000-0000-000000000000";var mr={};Object.defineProperty(mr,"__esModule",{value:!0}),mr.default=void 0;var ld=function(e){return e&&e.__esModule?e:{default:e}}(bt),ud=function(e){if(!(0,ld.default)(e))throw TypeError("Invalid UUID");return parseInt(e.slice(14,15),16)};function bs(e,t){if(!(e&&t&&e.length&&t.length))throw new Error("Bad alphabet");this.srcAlphabet=e,this.dstAlphabet=t}mr.default=ud,function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"NIL",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(e,"parse",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(e,"stringify",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(e,"v1",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"v3",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(e,"v4",{enumerable:!0,get:function(){return r.default}}),Object.defineProperty(e,"v5",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(e,"validate",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(e,"version",{enumerable:!0,get:function(){return i.default}});var t=d(sr),n=d(or),r=d(ur),s=d(dr),a=d(hr),i=d(mr),o=d(bt),u=d(it),l=d(hn);function d(c){return c&&c.__esModule?c:{default:c}}}(gl),bs.prototype.convert=function(e){var t,n,r,s={},a=this.srcAlphabet.length,i=this.dstAlphabet.length,o=e.length,u=typeof e=="string"?"":[];if(!this.isValid(e))throw new Error('Number "'+e+'" contains of non-alphabetic digits ('+this.srcAlphabet+")");if(this.srcAlphabet===this.dstAlphabet)return e;for(t=0;t<o;t++)s[t]=this.srcAlphabet.indexOf(e[t]);do{for(n=0,r=0,t=0;t<o;t++)(n=n*a+s[t])>=i?(s[r++]=parseInt(n/i,10),n%=i):r>0&&(s[r++]=0);o=r,u=this.dstAlphabet.slice(n,n+1).concat(u)}while(r!==0);return u},bs.prototype.isValid=function(e){for(var t=0;t<e.length;++t)if(this.srcAlphabet.indexOf(e[t])===-1)return!1;return!0};var cd=bs;function zt(e,t){var n=new cd(e,t);return function(r){return n.convert(r)}}zt.BIN="01",zt.OCT="01234567",zt.DEC="0123456789",zt.HEX="0123456789abcdef";var dd=zt;const{v4:Hr,validate:pd}=gl,Fn=dd,Wr={cookieBase90:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!#$%&'()*+-./:<=>?@[]^_`{|}~",flickrBase58:"123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ",uuid25Base36:"0123456789abcdefghijklmnopqrstuvwxyz"},hd={consistentLength:!0};let zr;const Mi=(e,t,n)=>{const r=t(e.toLowerCase().replace(/-/g,""));return n&&n.consistentLength?r.padStart(n.shortIdLength,n.paddingChar):r},Pi=(e,t)=>{const n=t(e).padStart(32,"0").match(/(\w{8})(\w{4})(\w{4})(\w{4})(\w{12})/);return[n[1],n[2],n[3],n[4],n[5]].join("-")};var md=(()=>{const e=(t,n)=>{const r=t||Wr.flickrBase58,s={...hd,...n};if([...new Set(Array.from(r))].length!==r.length)throw new Error("The provided Alphabet has duplicate characters resulting in unreliable results");const a=(i=r.length,Math.ceil(Math.log(2**128)/Math.log(i)));var i;const o={shortIdLength:a,consistentLength:s.consistentLength,paddingChar:r[0]},u=Fn(Fn.HEX,r),l=Fn(r,Fn.HEX),d=()=>Mi(Hr(),u,o),c={alphabet:r,fromUUID:p=>Mi(p,u,o),maxLength:a,generate:d,new:d,toUUID:p=>Pi(p,l),uuid:Hr,validate:(p,h=!1)=>{if(!p||typeof p!="string")return!1;const m=s.consistentLength?p.length===a:p.length<=a,y=p.split("").every(g=>r.includes(g));return h===!1?m&&y:m&&y&&pd(Pi(p,l))}};return Object.freeze(c),c};return e.constants=Wr,e.uuid=Hr,e.generate=()=>(zr||(zr=e(Wr.flickrBase58).generate),zr()),e})();const fd=kr(md),Tl={[me.NOT_STARTED]:"[ ]",[me.IN_PROGRESS]:"[/]",[me.COMPLETE]:"[x]",[me.CANCELLED]:"[-]"},wl=fd(void 0,{consistentLength:!0});function gd(e,t){if(e.uuid===t)return e;if(e.subTasksData)for(const n of e.subTasksData){const r=gd(n,t);if(r)return r}}function kl(e,t={}){const{shallow:n=!1,excludeUuid:r=!1,shortUuid:s=!0}=t;return Nl(e,{shallow:n,excludeUuid:r,shortUuid:s}).join(`
`)}function Nl(e,t={}){const{shallow:n=!1,excludeUuid:r=!1,shortUuid:s=!0}=t;let a="";r||(a=`UUID:${s?function(o){try{return wl.fromUUID(o)}catch{return o}}(e.uuid):e.uuid} `);const i=`${Tl[e.state]} ${a}NAME:${e.name} DESCRIPTION:${e.description}`;return n||!e.subTasksData||e.subTasksData.length===0?[i]:[i,...(e.subTasksData||[]).map(o=>Nl(o,t).map(u=>`-${u}`)).flat()]}function yd(e,t){var r;const n=(r=e.subTasksData)==null?void 0:r.map(s=>yd(s,t));return{...e,uuid:t!=null&&t.keepUuid?e.uuid:crypto.randomUUID(),subTasks:(n==null?void 0:n.map(s=>s.uuid))||[],subTasksData:n}}function Jm(e,t={}){if(!e.trim())throw new Error("Empty markdown");const n=e.split(`
`);let r=0;for(const l of n)if(l.trim()&&Li(l)===0)try{Ss(l,t),r++}catch{}if(r===0)throw new Error("No root task found");if(r>1)throw new Error(`Multiple root tasks found (${r}). There can only be one root task per conversation. All other tasks must be subtasks (indented with dashes). Root task format: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (no dashes). Subtask format: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (with dashes).`);const s=e.split(`
`);function a(){for(;s.length>0;){const l=s.shift(),d=Li(l);try{return{task:Ss(l,t),level:d}}catch{}}}const i=a();if(!i)throw new Error("No root task found");const o=[i.task];let u;for(;u=a();){const l=o[u.level-1];if(!l)throw new Error(`Invalid markdown: level ${u.level+1} has no parent
Line: ${u.task.name} is missing a parent
Current tasks: 
${kl(i.task)}`);l.subTasksData&&l.subTasks||(l.subTasks=[],l.subTasksData=[]),l.subTasksData.push(u.task),l.subTasks.push(u.task.uuid),o[u.level]=u.task,o.splice(u.level+1)}return i.task}function Li(e){let t=0,n=0;for(;n<e.length&&(e[n]===" "||e[n]==="	");)e[n]===" "?t+=.5:e[n]==="	"&&(t+=1),n++;for(;n<e.length&&e[n]==="-";)t+=1,n++;return Math.floor(t)}function Ss(e,t={}){const{excludeUuid:n=!1,shortUuid:r=!0}=t;let s=0;for(;s<e.length&&(e[s]===" "||e[s]==="	"||e[s]==="-");)s++;const a=e.substring(s),i=a.match(/^\s*\[([ x\-/?])\]/);if(!i)throw new Error(`Invalid task line: ${e} (missing state)`);const o=i[1],u=Object.entries(Tl).reduce((h,[m,y])=>(h[y.substring(1,2)]=m,h),{})[o]||me.NOT_STARTED,l=a.substring(i.index+i[0].length).trim();let d,c,p;if(n){const h=/(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,m=l.match(h);if(!m){const y=/\b(?:name|NAME):/i.test(l),g=/\b(?:description|DESCRIPTION):/i.test(l);throw!y||!g?new Error(`Invalid task line: ${e} (missing required fields)`):l.toLowerCase().indexOf("name:")<l.toLowerCase().indexOf("description:")?new Error(`Invalid task line: ${e} (invalid format)`):new Error(`Invalid task line: ${e} (incorrect field order)`)}if(c=m[1].trim(),p=m[2].trim(),!c)throw new Error(`Invalid task line: ${e} (missing required fields)`);d=crypto.randomUUID()}else{const h=/(?:uuid|UUID):([^]*?)(?=(?:name|NAME):)(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,m=l.match(h);if(!m){const y=/\b(?:uuid|UUID):/i.test(l),g=/\b(?:name|NAME):/i.test(l),v=/\b(?:description|DESCRIPTION):/i.test(l);if(!y||!g||!v)throw new Error(`Invalid task line: ${e} (missing required fields)`);const E=l.toLowerCase().indexOf("uuid:"),b=l.toLowerCase().indexOf("name:"),I=l.toLowerCase().indexOf("description:");throw E<b&&b<I?new Error(`Invalid task line: ${e} (invalid format)`):new Error(`Invalid task line: ${e} (incorrect field order)`)}if(d=m[1].trim(),c=m[2].trim(),p=m[3].trim(),!d||!c)throw new Error(`Invalid task line: ${e} (missing required fields)`);if(d==="NEW_UUID")d=crypto.randomUUID();else if(r)try{d=function(y){try{return wl.toUUID(y)}catch{return y}}(d)}catch{}}return{uuid:d,name:c,description:p,state:u,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:wa.USER}}const qt=e=>({uuid:crypto.randomUUID(),name:"New Task",description:"New task description",state:me.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:wa.USER,...e}),Di=qt({name:"Task 1.1",description:"This is the first sub task",state:me.IN_PROGRESS}),Fi=qt({name:"Task 1.2.1",description:"This is a nested sub task, child of Task 1.2",state:me.NOT_STARTED}),$i=qt({name:"Task 1.2.2",description:"This is another nested sub task, child of Task 1.2",state:me.IN_PROGRESS}),Ui=qt({name:"Task 1.2",description:"This is the second sub task",state:me.COMPLETE,subTasks:[Fi.uuid,$i.uuid],subTasksData:[Fi,$i]}),Bi=qt({name:"Task 1.3",description:"This is the third sub task",state:me.CANCELLED}),Qm=kl(qt({name:"Task 1",description:"This is the first task",state:me.NOT_STARTED,subTasks:[Di.uuid,Ui.uuid,Bi.uuid],subTasksData:[Di,Ui,Bi]}));function Il(e){const t=e.split(`
`);let n=null;const r={created:[],updated:[],deleted:[]};for(const s of t){const a=s.trim();if(a!=="## Created Tasks")if(a!=="## Updated Tasks")if(a!=="## Deleted Tasks"){if(n&&(a.startsWith("[ ]")||a.startsWith("[/]")||a.startsWith("[x]")||a.startsWith("[-]")))try{const i=Ss(a,{excludeUuid:!1,shortUuid:!0});i&&r[n].push(i)}catch{}}else n="deleted";else n="updated";else n="created"}return r}function ef(e){const t=e.match(/Created: (\d+), Updated: (\d+), Deleted: (\d+)/);if(t)return{created:parseInt(t[1],10),updated:parseInt(t[2],10),deleted:parseInt(t[3],10)};const n=Il(Cl(e));return{created:n.created.length,updated:n.updated.length,deleted:n.deleted.length}}function Cl(e){const t=e.indexOf("# Task Changes");if(t===-1)return"";const n=e.substring(t),r=[`
New and Updated Tasks:`,`
Remember:`,`

---`];let s=n.length;for(const o of r){const u=n.indexOf(o);u!==-1&&u<s&&(s=u)}const a=n.substring(0,s),i=a.indexOf(`
`);return i===-1?"":a.substring(i+1).trim()}function tf(e){return Il(Cl(e))}class nf{static getTaskOrchestratorPrompt(t){const{taskTree:n,surroundingContext:r}=t,s=this.buildTaskContext(n,r);return`Please utilize sub-agents to complete the following task tree.
Here are the details, along with a suggestion prompt.
You may use 1 or more sub-agents in to complete the below task.
For each sub-agent, please give it the relevant context and breakdown of the below task.

## Task Details
**Name:** ${n.name}
${n.description?`**Description:** ${n.description}`:""}
**Status:** ${n.state}

## Task Context
${s}

## Instructions
Please complete this task according to the requirements.
When you are done, report back on the completion status with a summary of changes made,
important context, and other relevant information for the supervisor.

Focus on this specific task tree while being aware of the broader context provided above.`}static getTaskMentionId(t){return`task:${t.taskUuid}:${t.taskTree.name.replace(/\s+/g,"_")}`}static getTaskMentionLabel(t){const{taskTree:n,surroundingContext:r}=t;return r.targetTaskPath.length>1?`${r.targetTaskPath.slice(0,-1).join(" → ")} → ${n.name}`:n.name}static buildTaskContext(t,n){const{rootTask:r,targetTaskPath:s}=n;let a=`This task is part of a larger project: "${r.name}"`;return r.description&&(a+=`

**Project Description:** ${r.description}`),s.length>1&&(a+=`

**Task Path:** ${s.join(" → ")}`),t.subTasksData&&t.subTasksData.length>0&&(a+=`

**Subtasks:**`,t.subTasksData.forEach((i,o)=>{a+=`
${o+1}. ${i.name} (${i.state})`,i.description&&(a+=` - ${i.description}`)})),a}static parseJsonTaskList(t){try{const n=JSON.parse(t);return Array.isArray(n)?n:this.flattenTaskTree(n)}catch{return[]}}static flattenTaskTree(t,n=[]){if(n.push({uuid:t.uuid,name:t.name,description:t.description,state:t.state,level:t.level}),t.subTasks)for(const r of t.subTasks)this.flattenTaskTree(r,n);return n}}const Ia=class Ia{static hasFrontmatter(t){return this.frontmatterRegex.test(t)}static extractFrontmatter(t){const n=t.match(this.frontmatterRegex);return n&&n[1]?n[1]:null}static extractContent(t){return t==null?"":t.replace(this.frontmatterRegex,"")}static parseBoolean(t,n,r=!0){const s=this.extractFrontmatter(t);if(s){const a=new RegExp(`${n}\\s*:\\s*(true|false)`,"i"),i=s.match(a);if(i&&i[1])return i[1].toLowerCase()==="true"}return r}static parseString(t,n,r=""){const s=this.extractFrontmatter(t);if(s){const a=new RegExp(`${n}\\s*:\\s*["']?([^"'
]*)["']?`,"i"),i=s.match(a);if(i&&i[1])return i[1].trim()}return r}static updateFrontmatter(t,n,r){const s=t.match(this.frontmatterRegex),a=typeof r!="string"||/^(true|false)$/.test(r.toLowerCase())?String(r):`"${r}"`;if(s){const i=s[1],o=new RegExp(`(${n}\\s*:\\s*)([^\\n]*)`,"i");if(i.match(o)){const u=i.replace(o,`$1${a}`);return t.replace(this.frontmatterRegex,`---
${u}---

`)}{const u=`${i.endsWith(`
`)?i:i+`
`}${n}: ${a}
`;return t.replace(this.frontmatterRegex,`---
${u}---

`)}}return`---
${n}: ${a}
---

${t}`}static createFrontmatter(t,n){let r=t;this.hasFrontmatter(r)&&(r=this.extractContent(r));for(const[s,a]of Object.entries(n))r=this.updateFrontmatter(r,s,a);return r}};Ia.frontmatterRegex=/^---\s*\n([\s\S]*?)\n---\s*\n/;let de=Ia;const ft=class ft{static parseRuleFile(t,n){const r=de.parseString(t,this.DESCRIPTION_FRONTMATTER_KEY,""),s=de.extractContent(t);return{type:this.getRuleTypeFromContent(t),path:n,content:s,description:r||void 0}}static formatRuleFileForMarkdown(t){let n=t.content;return n=de.updateFrontmatter(n,this.TYPE_FRONTMATTER_KEY,this.mapRuleTypeToString(t.type)),t.description&&(n=de.updateFrontmatter(n,this.DESCRIPTION_FRONTMATTER_KEY,t.description)),n}static getAlwaysApplyFrontmatterKey(t){return de.parseBoolean(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,!1)}static extractContent(t){return de.extractContent(t)}static updateAlwaysApplyFrontmatterKey(t,n){return de.updateFrontmatter(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,n)}static getDescriptionFrontmatterKey(t){return de.parseString(t,this.DESCRIPTION_FRONTMATTER_KEY,"")}static updateDescriptionFrontmatterKey(t,n){return de.updateFrontmatter(t,this.DESCRIPTION_FRONTMATTER_KEY,n)}static mapStringToRuleType(t){switch(t.toLowerCase()){case"always_apply":return Oe.ALWAYS_ATTACHED;case"manual":return Oe.MANUAL;case"agent_requested":return Oe.AGENT_REQUESTED;default:return this.DEFAULT_RULE_TYPE}}static mapRuleTypeToString(t){switch(t){case Oe.ALWAYS_ATTACHED:return"always_apply";case Oe.MANUAL:return"manual";case Oe.AGENT_REQUESTED:return"agent_requested";default:return"manual"}}static isValidTypeValue(t){return this.VALID_TYPE_VALUES.includes(t.toLowerCase())}static getTypeFrontmatterKey(t){return de.parseString(t,this.TYPE_FRONTMATTER_KEY,"")}static updateTypeFrontmatterKey(t,n){const r=this.mapRuleTypeToString(n);return de.updateFrontmatter(t,this.TYPE_FRONTMATTER_KEY,r)}static getRuleTypeFromContent(t){const n=this.getTypeFrontmatterKey(t);if(n&&this.isValidTypeValue(n))return this.mapStringToRuleType(n);const r=this.getAlwaysApplyFrontmatterKey(t),s=this.getDescriptionFrontmatterKey(t);return r?Oe.ALWAYS_ATTACHED:s&&s.trim()!==""?Oe.AGENT_REQUESTED:Oe.MANUAL}};ft.ALWAYS_APPLY_FRONTMATTER_KEY="alwaysApply",ft.DESCRIPTION_FRONTMATTER_KEY="description",ft.TYPE_FRONTMATTER_KEY="type",ft.VALID_TYPE_VALUES=["always_apply","manual","agent_requested"],ft.DEFAULT_RULE_TYPE=Oe.MANUAL;let Es=ft;const _d=".augment",vd="rules",rf=".augment-guidelines";var bd=(e=>(e[e.Unknown=0]="Unknown",e[e.File=1]="File",e[e.Directory=2]="Directory",e[e.SymbolicLink=64]="SymbolicLink",e))(bd||{});const Ca=class Ca{static setClientWorkspaces(t){this._instance===void 0?this._instance=t:Ac().warn("Attempting to initialize client workspaces when one is already configured. Keeping existing client workspaces.")}static getClientWorkspaces(){if(this._instance===void 0)throw new Error("ClientWorkspaces not set");return this._instance}static reset(){this._instance=void 0}};Ca._instance=void 0;let Ts=Ca;const sf=()=>Ts.getClientWorkspaces();var Sd=(e=>(e[e.unspecified=0]="unspecified",e[e.userGuidelines=1]="userGuidelines",e[e.augmentGuidelines=2]="augmentGuidelines",e[e.rules=3]="rules",e))(Sd||{}),Ed=(e=>(e[e.unspecified=0]="unspecified",e[e.manuallyCreated=1]="manuallyCreated",e[e.auto=2]="auto",e[e.selectedDirectory=3]="selectedDirectory",e[e.selectedFile=4]="selectedFile",e))(Ed||{});function Td(e){return e===void 0?{num_lines:-1,num_chars:-1}:{num_lines:e.split(`
`).length,num_chars:e.length}}class Al{constructor(){this.tracingData={flags:{},nums:{},string_stats:{},request_ids:{}}}setFlag(t,n=!0){this.tracingData.flags[t]={value:n,timestamp:new Date().toISOString()}}getFlag(t){const n=this.tracingData.flags[t];return n==null?void 0:n.value}setNum(t,n){this.tracingData.nums[t]={value:n,timestamp:new Date().toISOString()}}getNum(t){const n=this.tracingData.nums[t];return n==null?void 0:n.value}setStringStats(t,n){this.tracingData.string_stats[t]={value:Td(n),timestamp:new Date().toISOString()}}setRequestId(t,n){this.tracingData.request_ids[t]={value:n,timestamp:new Date().toISOString()}}}var wd=(e=>(e[e.unspecified=0]="unspecified",e[e.classify_and_distill=1]="classify_and_distill",e[e.orientation=2]="orientation",e[e.memory_acceptance=3]="memory_acceptance",e))(wd||{}),kd=(e=>(e.start="start",e.end="end",e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noMemoryData="noMemoryData",e.agenticTurnHasRememberToolCall="agenticTurnHasRememberToolCall",e.emptyMemory="emptyMemory",e.removeUserExchangeMemoryFailed="removeUserExchangeMemoryFailed",e))(kd||{});class xl extends Al{constructor(){super()}static create(){return new xl}}var Nd=(e=>(e.openedAgentConversation="opened-agent-conversation",e.revertCheckpoint="revert-checkpoint",e.agentInterruption="agent-interruption",e.sentUserMessage="sent-user-message",e.rememberToolCall="remember-tool-call",e.openedMemoriesFile="opened-memories-file",e.initialOrientation="initial-orientation",e.classifyAndDistill="classify-and-distill",e.flushMemories="flush-memories",e.vsCodeTerminalCwdNotAbsolute="vs-code-terminal-cwd-not-absolute",e.vsCodeTerminalCwdDoesNotExist="vs-code-terminal-cwd-does-not-exist",e.vsCodeTerminalShellIntegrationNotAvailable="vs-code-terminal-shell-integration-not-available",e.vsCodeTerminalReadingApproximateOutput="vs-code-terminal-reading-approximate-output",e.vsCodeTerminalTimedOutWaitingForNoopCommand="vs-code-terminal-timed-out-waiting-for-noop-command",e.vsCodeTerminalTimedOutWaitingForSetCwdCommand="vs-code-terminal-timed-out-waiting-for-set-cwd-command",e.vsCodeTerminalTimedOutWaitingForStartupCommand="vs-code-terminal-timed-out-waiting-for-startup-command",e.vsCodeTerminalFailedToUseShellIntegration="vs-code-terminal-failed-to-use-shell-integration",e.vsCodeTerminalLastCommandIsSameAsCurrent="vs-code-terminal-last-command-is-same-as-current",e.vsCodeTerminalPollingDeterminedProcessIsDone="vs-code-terminal-polling-determined-process-is-done",e.vsCodeScriptStrategyPollingDeterminedProcessIsDone="vs-code-script-strategy-polling-determined-process-is-done",e.vsCodeTerminalFailedToReadOutput="vs-code-terminal-failed-to-read-output",e.vsCodeTerminalReadEmptyOutput="vs-code-terminal-read-empty-output",e.vsCodeTerminalBuggyOutput="vs-code-terminal-buggy-output",e.vsCodeTerminalBuggyExecutionEvents="vs-code-terminal-buggy-execution-events",e.vsCodeTerminalUnsupportedVSCodeShell="vs-code-terminal-unsupported-vscode-shell",e.vsCodeTerminalFailedToFindGitBash="vs-code-terminal-failed-to-find-git-bash",e.vsCodeTerminalFailedToFindPowerShell="vs-code-terminal-failed-to-find-powershell",e.vsCodeTerminalNoSupportedShellsFound="vs-code-terminal-no-supported-shells-found",e.vsCodeTerminalSettingsChanged="vs-code-terminal-settings-changed",e.vsCodeTerminalWaitTimeout="vs-code-terminal-wait-timeout",e.vsCodeTerminalErrorLoadingSettings="vs-code-terminal-error-loading-settings",e.vsCodeTerminalErrorCheckingForShellUpdates="vs-code-terminal-error-checking-for-shell-updates",e.vsCodeTerminalErrorCleaningUpTempDir="vs-code-terminal-error-cleaning-up-temp-dir",e.vsCodeTerminalErrorInitializingShells="vs-code-terminal-error-initializing-shells",e.vsCodeTerminalErrorCheckingShellCapability="vs-code-terminal-error-checking-shell-capability",e.vsCodeTerminalErrorCreatingZshEnvironment="vs-code-terminal-error-creating-zsh-environment",e.vsCodeTerminalErrorCreatingBashEnvironment="vs-code-terminal-error-creating-bash-environment",e.vsCodeTerminalErrorAccessingConfiguration="vs-code-terminal-error-accessing-configuration",e.vsCodeTerminalErrorSavingSettings="vs-code-terminal-error-saving-settings",e.vsCodeTerminalMissedStartEvent="vs-code-terminal-missed-start-event",e.vsCodeTerminalReadStreamTimeoutWhenProcessIsComplete="vs-code-terminal-read-stream-timeout-when-process-is-complete",e.vsCodeTerminalScriptCommandNotAvailable="vs-code-terminal-script-command-not-available",e.vsCodeTerminalScriptCaptureFailToStart="vs-code-terminal-script-capture-fail-to-start",e.vsCodeTerminalScriptCaptureReadOutputFailure="vs-code-terminal-script-capture-read-output-failure",e.vsCodeTerminalScriptCaptureStartupScriptFailure="vs-code-terminal-script-capture-startup-script-failure",e.vsCodeTerminalScriptCaptureFailedAfterRetry="vs-code-terminal-script-capture-failed-after-retry",e.vsCodeTerminalScriptCaptureCommandFilteringFailed="vs-code-terminal-script-capture-command-filtering-failed",e.vsCodeTerminalScriptCapturePlatformCompatibility="vs-code-terminal-script-capture-platform-compatibility",e.vsCodeTerminalScriptCaptureCwdTrackingSetupFailure="vs-code-terminal-script-capture-cwd-tracking-setup-failure",e.vsCodeTerminalScriptCaptureCwdTrackingSetupEmpty="vs-code-terminal-script-capture-cwd-tracking-setup-empty",e.vsCodeTerminalScriptCaptureMarkerSetupFailure="vs-code-terminal-script-capture-marker-setup-failure",e.vsCodeTerminalScriptCaptureMarkerSetupEmpty="vs-code-terminal-script-capture-marker-setup-empty",e.vsCodeTerminalScriptCapturePidNull="vs-code-terminal-script-capture-pid-null",e.vsCodeTerminalScriptCapturePidDead="vs-code-terminal-script-capture-pid-dead",e.vsCodeTerminalScriptCaptureGetChildProcessException="vs-code-terminal-script-capture-get-child-process-exception",e.vsCodeTerminalScriptCaptureGetProcessStateException="vs-code-terminal-script-capture-get-process-state-exception",e.enhancedPrompt="enhanced-prompt",e.memoriesMove="memories-move",e.rulesImported="rules-imported",e.taskListUsage="task-list-usage",e.memoryUsage="memory-usage",e.contentTruncation="content-truncation",e.modelSelectionChange="model-selection-change",e))(Nd||{}),Id=(e=>(e.sentUserMessage="sent-user-message",e.chatHistorySummarization="chat-history-summarization",e.enhancedPrompt="enhanced-prompt",e.firstTokenReceived="first-token-received",e.chatHistoryTruncated="chat-history-truncated",e))(Id||{}),Cd=(e=>(e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.start="start",e.end="end",e.noPendingUserMessage="noPendingUserMessage",e.startSendSilentExchange="startSendSilentExchange",e.sendSilentExchangeRequestId="sendSilentExchangeRequestId",e.sendSilentExchangeResponseStats="sendSilentExchangeResponseStats",e.noRequestId="noRequestId",e.conversationChanged="conversationChanged",e.explanationStats="explanationStats",e.contentStats="contentStats",e.invalidResponse="invalidResponse",e.worthRemembering="worthRemembering",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noLastUserExchangeRequestId="noLastUserExchangeRequestId",e))(Cd||{});class Rl extends Al{constructor(){super()}static create(){return new Rl}}var Ad=(e=>(e.remoteAgentSetup="remote-agent-setup",e.setupScript="setup-script",e.sshInteraction="ssh-interaction",e.notificationBell="notification-bell",e.diffPanel="diff-panel",e.setupPageOpened="setup-page-opened",e.githubAPIFailure="github-api-failure",e.remoteAgentCreated="remote-agent-created",e.changesApplied="changes-applied",e.createdPR="created-pr",e.modeSelector="mode-selector",e.remoteAgentSetupWindow="remote-agent-setup-window",e.remoteAgentThreadList="remote-agent-thread-list",e.remoteAgentNewThreadButton="remote-agent-new-thread-button",e))(Ad||{}),xd=(e=>(e[e.unknownSourceControl=0]="unknownSourceControl",e[e.git=1]="git",e[e.github=2]="github",e))(xd||{}),Rd=(e=>(e[e.unknownMode=0]="unknownMode",e[e.chat=1]="chat",e[e.agent=2]="agent",e[e.remoteAgent=3]="remoteAgent",e))(Rd||{}),Od=(e=>(e[e.unknownModeSelectorAction=0]="unknownModeSelectorAction",e[e.open=1]="open",e[e.close=2]="close",e[e.select=3]="select",e[e.init=4]="init",e))(Od||{}),Md=(e=>(e[e.unknownSetupWindowAction=0]="unknownSetupWindowAction",e[e.open=1]="open",e[e.close=2]="close",e[e.selectRepo=3]="selectRepo",e[e.selectBranch=4]="selectBranch",e[e.selectSetupScript=5]="selectSetupScript",e[e.autoGenerateSetupScript=6]="autoGenerateSetupScript",e[e.manuallyCreateSetupScript=7]="manuallyCreateSetupScript",e[e.typeInPromptWindow=8]="typeInPromptWindow",e[e.clickRewritePrompt=9]="clickRewritePrompt",e[e.enableNotifications=10]="enableNotifications",e[e.disableNotifications=11]="disableNotifications",e[e.clickCreateAgent=12]="clickCreateAgent",e))(Md||{}),Pd=(e=>(e[e.unknownAgentListAction=0]="unknownAgentListAction",e[e.open=1]="open",e[e.close=2]="close",e[e.selectAgent=3]="selectAgent",e[e.deleteAgent=4]="deleteAgent",e[e.pinAgent=5]="pinAgent",e[e.unpinAgent=6]="unpinAgent",e))(Pd||{}),Ld=(e=>(e[e.unknown=0]="unknown",e[e.click=1]="click",e[e.open=2]="open",e[e.close=3]="close",e))(Ld||{}),Dd=(e=>(e[e.unknown=0]="unknown",e[e.chat=1]="chat",e[e.agent=2]="agent",e[e.remoteAgent=3]="remoteAgent",e))(Dd||{}),Fd=(e=>(e[e.unknown=0]="unknown",e[e.addTask=1]="addTask",e[e.addSubtask=2]="addSubtask",e[e.updateTaskStatus=3]="updateTaskStatus",e[e.updateTaskName=4]="updateTaskName",e[e.updateTaskDescription=5]="updateTaskDescription",e[e.reorganizeTaskList=6]="reorganizeTaskList",e[e.deleteTask=7]="deleteTask",e[e.runSingleTask=8]="runSingleTask",e[e.runAllTasks=9]="runAllTasks",e[e.viewTaskList=10]="viewTaskList",e[e.exportTaskList=11]="exportTaskList",e[e.importTaskList=12]="importTaskList",e[e.syncTaskList=13]="syncTaskList",e))(Fd||{}),$d=(e=>(e[e.unknown=0]="unknown",e[e.user=1]="user",e[e.agent=2]="agent",e))($d||{}),Ud=(e=>(e[e.unknown=0]="unknown",e[e.saveMemory=1]="saveMemory",e[e.discardMemory=2]="discardMemory",e[e.editMemory=3]="editMemory",e[e.viewMemories=4]="viewMemories",e[e.refreshMemories=5]="refreshMemories",e[e.filterByState=6]="filterByState",e[e.filterByVersion=7]="filterByVersion",e[e.openMemoriesFile=8]="openMemoriesFile",e[e.createMemory=9]="createMemory",e))(Ud||{}),Bd=(e=>(e[e.unknown=0]="unknown",e[e.user=1]="user",e[e.agent=2]="agent",e))(Bd||{}),jd=(e=>(e.longRunning="longRunning",e.running="running",e.done="done",e))(jd||{}),Vd=(e=>(e.initializing="initializing",e.enabled="enabled",e.disabled="disabled",e.partial="partial",e))(Vd||{});function le(e,t){return t in e&&e[t]!==void 0}function Gd(e){return le(e,"file")}function qd(e){return le(e,"recentFile")}function Zd(e){return le(e,"folder")}function Kd(e){return le(e,"sourceFolder")}function af(e){return le(e,"sourceFolderGroup")}function of(e){return le(e,"selection")}function Yd(e){return le(e,"externalSource")}function lf(e){return le(e,"allDefaultContext")}function uf(e){return le(e,"clearContext")}function cf(e){return le(e,"userGuidelines")}function df(e){return le(e,"agentMemories")}function Hd(e){return le(e,"personality")}function Wd(e){return le(e,"rule")}function pf(e){return le(e,"task")}const hf={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},mf={clearContext:!0,label:"Clear Context",id:"clearContext"},ff={userGuidelines:{overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},gf={agentMemories:{},label:"Agent Memories",id:"agentMemories"},ji=[{personality:{type:Se.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:Se.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:Se.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:Se.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function yf(e){return le(e,"group")}function _f(e){const t=new Map;return e.forEach(n=>{Gd(n)?t.set("file",[...t.get("file")??[],n]):qd(n)?t.set("recentFile",[...t.get("recentFile")??[],n]):Zd(n)?t.set("folder",[...t.get("folder")??[],n]):Yd(n)?t.set("externalSource",[...t.get("externalSource")??[],n]):Kd(n)?t.set("sourceFolder",[...t.get("sourceFolder")??[],n]):Hd(n)?t.set("personality",[...t.get("personality")??[],n]):Wd(n)&&t.set("rule",[...t.get("rule")??[],n])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:t.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:t.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:t.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:t.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:t.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:t.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:t.get("rule")??[]}}].filter(n=>n.group.items.length>0)}function zd(e){const t=(n={rootPath:e.repoRoot,relPath:e.pathName}).rootPath+"/"+n.relPath;var n;const r={label:ou(e.pathName).split("/").filter(s=>s.trim()!=="").pop()||"",name:t,id:t};if(e.fullRange){const s=`:L${e.fullRange.startLineNumber}-${e.fullRange.endLineNumber}`;r.label+=s,r.name+=s,r.id+=s}else if(e.range){const s=`:L${e.range.start}-${e.range.stop}`;r.label+=s,r.name+=s,r.id+=s}return r}function Xd(e){const t=e.path.split("/"),n=t[t.length-1],r=n.endsWith(".md")?n.slice(0,-3):n,s=`${_d}/${vd}/${e.path}`;return{label:r,name:s,id:s}}var L,ws;(function(e){e.assertEqual=t=>t,e.assertIs=function(t){},e.assertNever=function(t){throw new Error},e.arrayToEnum=t=>{const n={};for(const r of t)n[r]=r;return n},e.getValidEnumValues=t=>{const n=e.objectKeys(t).filter(s=>typeof t[t[s]]!="number"),r={};for(const s of n)r[s]=t[s];return e.objectValues(r)},e.objectValues=t=>e.objectKeys(t).map(function(n){return t[n]}),e.objectKeys=typeof Object.keys=="function"?t=>Object.keys(t):t=>{const n=[];for(const r in t)Object.prototype.hasOwnProperty.call(t,r)&&n.push(r);return n},e.find=(t,n)=>{for(const r of t)if(n(r))return r},e.isInteger=typeof Number.isInteger=="function"?t=>Number.isInteger(t):t=>typeof t=="number"&&isFinite(t)&&Math.floor(t)===t,e.joinValues=function(t,n=" | "){return t.map(r=>typeof r=="string"?`'${r}'`:r).join(n)},e.jsonStringifyReplacer=(t,n)=>typeof n=="bigint"?n.toString():n})(L||(L={})),function(e){e.mergeShapes=(t,n)=>({...t,...n})}(ws||(ws={}));const w=L.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),He=e=>{switch(typeof e){case"undefined":return w.undefined;case"string":return w.string;case"number":return isNaN(e)?w.nan:w.number;case"boolean":return w.boolean;case"function":return w.function;case"bigint":return w.bigint;case"symbol":return w.symbol;case"object":return Array.isArray(e)?w.array:e===null?w.null:e.then&&typeof e.then=="function"&&e.catch&&typeof e.catch=="function"?w.promise:typeof Map<"u"&&e instanceof Map?w.map:typeof Set<"u"&&e instanceof Set?w.set:typeof Date<"u"&&e instanceof Date?w.date:w.object;default:return w.unknown}},S=L.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class ge extends Error{get errors(){return this.issues}constructor(t){super(),this.issues=[],this.addIssue=r=>{this.issues=[...this.issues,r]},this.addIssues=(r=[])=>{this.issues=[...this.issues,...r]};const n=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,n):this.__proto__=n,this.name="ZodError",this.issues=t}format(t){const n=t||function(a){return a.message},r={_errors:[]},s=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(s);else if(i.code==="invalid_return_type")s(i.returnTypeError);else if(i.code==="invalid_arguments")s(i.argumentsError);else if(i.path.length===0)r._errors.push(n(i));else{let o=r,u=0;for(;u<i.path.length;){const l=i.path[u];u===i.path.length-1?(o[l]=o[l]||{_errors:[]},o[l]._errors.push(n(i))):o[l]=o[l]||{_errors:[]},o=o[l],u++}}};return s(this),r}static assert(t){if(!(t instanceof ge))throw new Error(`Not a ZodError: ${t}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,L.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(t=n=>n.message){const n={},r=[];for(const s of this.issues)s.path.length>0?(n[s.path[0]]=n[s.path[0]]||[],n[s.path[0]].push(t(s))):r.push(t(s));return{formErrors:r,fieldErrors:n}}get formErrors(){return this.flatten()}}ge.create=e=>new ge(e);const jt=(e,t)=>{let n;switch(e.code){case S.invalid_type:n=e.received===w.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case S.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,L.jsonStringifyReplacer)}`;break;case S.unrecognized_keys:n=`Unrecognized key(s) in object: ${L.joinValues(e.keys,", ")}`;break;case S.invalid_union:n="Invalid input";break;case S.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${L.joinValues(e.options)}`;break;case S.invalid_enum_value:n=`Invalid enum value. Expected ${L.joinValues(e.options)}, received '${e.received}'`;break;case S.invalid_arguments:n="Invalid function arguments";break;case S.invalid_return_type:n="Invalid function return type";break;case S.invalid_date:n="Invalid date";break;case S.invalid_string:typeof e.validation=="object"?"includes"in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,typeof e.validation.position=="number"&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:L.assertNever(e.validation):n=e.validation!=="regex"?`Invalid ${e.validation}`:"Invalid";break;case S.too_small:n=e.type==="array"?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:e.type==="string"?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:e.type==="number"?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:e.type==="date"?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case S.too_big:n=e.type==="array"?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:e.type==="string"?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:e.type==="number"?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="bigint"?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="date"?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case S.custom:n="Invalid input";break;case S.invalid_intersection_types:n="Intersection results could not be merged";break;case S.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case S.not_finite:n="Number must be finite";break;default:n=t.defaultError,L.assertNever(e)}return{message:n}};let Ol=jt;function fr(){return Ol}const gr=e=>{const{data:t,path:n,errorMaps:r,issueData:s}=e,a=[...n,...s.path||[]],i={...s,path:a};if(s.message!==void 0)return{...s,path:a,message:s.message};let o="";const u=r.filter(l=>!!l).slice().reverse();for(const l of u)o=l(i,{data:t,defaultError:o}).message;return{...s,path:a,message:o}};function T(e,t){const n=fr(),r=gr({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,n,n===jt?void 0:jt].filter(s=>!!s)});e.common.issues.push(r)}class ne{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(t,n){const r=[];for(const s of n){if(s.status==="aborted")return x;s.status==="dirty"&&t.dirty(),r.push(s.value)}return{status:t.value,value:r}}static async mergeObjectAsync(t,n){const r=[];for(const s of n){const a=await s.key,i=await s.value;r.push({key:a,value:i})}return ne.mergeObjectSync(t,r)}static mergeObjectSync(t,n){const r={};for(const s of n){const{key:a,value:i}=s;if(a.status==="aborted"||i.status==="aborted")return x;a.status==="dirty"&&t.dirty(),i.status==="dirty"&&t.dirty(),a.value==="__proto__"||i.value===void 0&&!s.alwaysSet||(r[a.value]=i.value)}return{status:t.value,value:r}}}const x=Object.freeze({status:"aborted"}),yr=e=>({status:"dirty",value:e}),ie=e=>({status:"valid",value:e}),ks=e=>e.status==="aborted",Ns=e=>e.status==="dirty",St=e=>e.status==="valid",mn=e=>typeof Promise<"u"&&e instanceof Promise;function _r(e,t,n,r){if(typeof t=="function"||!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t.get(e)}function Ml(e,t,n,r,s){if(typeof t=="function"||!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return t.set(e,n),n}var N,Xt,Jt;typeof SuppressedError=="function"&&SuppressedError,function(e){e.errToObj=t=>typeof t=="string"?{message:t}:t||{},e.toString=t=>typeof t=="string"?t:t==null?void 0:t.message}(N||(N={}));class je{constructor(t,n,r,s){this._cachedPath=[],this.parent=t,this.data=n,this._path=r,this._key=s}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Vi=(e,t)=>{if(St(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const n=new ge(e.common.issues);return this._error=n,this._error}}};function R(e){if(!e)return{};const{errorMap:t,invalid_type_error:n,required_error:r,description:s}=e;if(t&&(n||r))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return t?{errorMap:t,description:s}:{errorMap:(a,i)=>{var o,u;const{message:l}=e;return a.code==="invalid_enum_value"?{message:l??i.defaultError}:i.data===void 0?{message:(o=l??r)!==null&&o!==void 0?o:i.defaultError}:a.code!=="invalid_type"?{message:i.defaultError}:{message:(u=l??n)!==null&&u!==void 0?u:i.defaultError}},description:s}}class O{get description(){return this._def.description}_getType(t){return He(t.data)}_getOrReturnCtx(t,n){return n||{common:t.parent.common,data:t.data,parsedType:He(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}_processInputParams(t){return{status:new ne,ctx:{common:t.parent.common,data:t.data,parsedType:He(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}}_parseSync(t){const n=this._parse(t);if(mn(n))throw new Error("Synchronous parse encountered promise.");return n}_parseAsync(t){const n=this._parse(t);return Promise.resolve(n)}parse(t,n){const r=this.safeParse(t,n);if(r.success)return r.data;throw r.error}safeParse(t,n){var r;const s={common:{issues:[],async:(r=n==null?void 0:n.async)!==null&&r!==void 0&&r,contextualErrorMap:n==null?void 0:n.errorMap},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:He(t)},a=this._parseSync({data:t,path:s.path,parent:s});return Vi(s,a)}"~validate"(t){var n,r;const s={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:He(t)};if(!this["~standard"].async)try{const a=this._parseSync({data:t,path:[],parent:s});return St(a)?{value:a.value}:{issues:s.common.issues}}catch(a){!((r=(n=a==null?void 0:a.message)===null||n===void 0?void 0:n.toLowerCase())===null||r===void 0)&&r.includes("encountered")&&(this["~standard"].async=!0),s.common={issues:[],async:!0}}return this._parseAsync({data:t,path:[],parent:s}).then(a=>St(a)?{value:a.value}:{issues:s.common.issues})}async parseAsync(t,n){const r=await this.safeParseAsync(t,n);if(r.success)return r.data;throw r.error}async safeParseAsync(t,n){const r={common:{issues:[],contextualErrorMap:n==null?void 0:n.errorMap,async:!0},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:He(t)},s=this._parse({data:t,path:r.path,parent:r}),a=await(mn(s)?s:Promise.resolve(s));return Vi(r,a)}refine(t,n){const r=s=>typeof n=="string"||n===void 0?{message:n}:typeof n=="function"?n(s):n;return this._refinement((s,a)=>{const i=t(s),o=()=>a.addIssue({code:S.custom,...r(s)});return typeof Promise<"u"&&i instanceof Promise?i.then(u=>!!u||(o(),!1)):!!i||(o(),!1)})}refinement(t,n){return this._refinement((r,s)=>!!t(r)||(s.addIssue(typeof n=="function"?n(r,s):n),!1))}_refinement(t){return new Ce({schema:this,typeName:C.ZodEffects,effect:{type:"refinement",refinement:t}})}superRefine(t){return this._refinement(t)}constructor(t){this.spa=this.safeParseAsync,this._def=t,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:n=>this["~validate"](n)}}optional(){return Be.create(this,this._def)}nullable(){return dt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Ae.create(this)}promise(){return Gt.create(this,this._def)}or(t){return _n.create([this,t],this._def)}and(t){return vn.create(this,t,this._def)}transform(t){return new Ce({...R(this._def),schema:this,typeName:C.ZodEffects,effect:{type:"transform",transform:t}})}default(t){const n=typeof t=="function"?t:()=>t;return new Tn({...R(this._def),innerType:this,defaultValue:n,typeName:C.ZodDefault})}brand(){return new ka({typeName:C.ZodBranded,type:this,...R(this._def)})}catch(t){const n=typeof t=="function"?t:()=>t;return new wn({...R(this._def),innerType:this,catchValue:n,typeName:C.ZodCatch})}describe(t){return new this.constructor({...this._def,description:t})}pipe(t){return xn.create(this,t)}readonly(){return kn.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Jd=/^c[^\s-]{8,}$/i,Qd=/^[0-9a-z]+$/,ep=/^[0-9A-HJKMNP-TV-Z]{26}$/i,tp=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,np=/^[a-z0-9_-]{21}$/i,rp=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,sp=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,ap=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let Xr;const ip=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,op=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,lp=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,up=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,cp=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,dp=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Pl="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",pp=new RegExp(`^${Pl}$`);function Ll(e){let t="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return e.precision?t=`${t}\\.\\d{${e.precision}}`:e.precision==null&&(t=`${t}(\\.\\d+)?`),t}function Dl(e){let t=`${Pl}T${Ll(e)}`;const n=[];return n.push(e.local?"Z?":"Z"),e.offset&&n.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${n.join("|")})`,new RegExp(`^${t}$`)}function hp(e,t){return!(t!=="v4"&&t||!ip.test(e))||!(t!=="v6"&&t||!lp.test(e))}function mp(e,t){if(!rp.test(e))return!1;try{const[n]=e.split("."),r=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),s=JSON.parse(atob(r));return typeof s=="object"&&s!==null&&!(!s.typ||!s.alg)&&(!t||s.alg===t)}catch{return!1}}function fp(e,t){return!(t!=="v4"&&t||!op.test(e))||!(t!=="v6"&&t||!up.test(e))}class Ie extends O{_parse(t){if(this._def.coerce&&(t.data=String(t.data)),this._getType(t)!==w.string){const s=this._getOrReturnCtx(t);return T(s,{code:S.invalid_type,expected:w.string,received:s.parsedType}),x}const n=new ne;let r;for(const s of this._def.checks)if(s.kind==="min")t.data.length<s.value&&(r=this._getOrReturnCtx(t,r),T(r,{code:S.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="max")t.data.length>s.value&&(r=this._getOrReturnCtx(t,r),T(r,{code:S.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="length"){const a=t.data.length>s.value,i=t.data.length<s.value;(a||i)&&(r=this._getOrReturnCtx(t,r),a?T(r,{code:S.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}):i&&T(r,{code:S.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}),n.dirty())}else if(s.kind==="email")ap.test(t.data)||(r=this._getOrReturnCtx(t,r),T(r,{validation:"email",code:S.invalid_string,message:s.message}),n.dirty());else if(s.kind==="emoji")Xr||(Xr=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),Xr.test(t.data)||(r=this._getOrReturnCtx(t,r),T(r,{validation:"emoji",code:S.invalid_string,message:s.message}),n.dirty());else if(s.kind==="uuid")tp.test(t.data)||(r=this._getOrReturnCtx(t,r),T(r,{validation:"uuid",code:S.invalid_string,message:s.message}),n.dirty());else if(s.kind==="nanoid")np.test(t.data)||(r=this._getOrReturnCtx(t,r),T(r,{validation:"nanoid",code:S.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid")Jd.test(t.data)||(r=this._getOrReturnCtx(t,r),T(r,{validation:"cuid",code:S.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid2")Qd.test(t.data)||(r=this._getOrReturnCtx(t,r),T(r,{validation:"cuid2",code:S.invalid_string,message:s.message}),n.dirty());else if(s.kind==="ulid")ep.test(t.data)||(r=this._getOrReturnCtx(t,r),T(r,{validation:"ulid",code:S.invalid_string,message:s.message}),n.dirty());else if(s.kind==="url")try{new URL(t.data)}catch{r=this._getOrReturnCtx(t,r),T(r,{validation:"url",code:S.invalid_string,message:s.message}),n.dirty()}else s.kind==="regex"?(s.regex.lastIndex=0,s.regex.test(t.data)||(r=this._getOrReturnCtx(t,r),T(r,{validation:"regex",code:S.invalid_string,message:s.message}),n.dirty())):s.kind==="trim"?t.data=t.data.trim():s.kind==="includes"?t.data.includes(s.value,s.position)||(r=this._getOrReturnCtx(t,r),T(r,{code:S.invalid_string,validation:{includes:s.value,position:s.position},message:s.message}),n.dirty()):s.kind==="toLowerCase"?t.data=t.data.toLowerCase():s.kind==="toUpperCase"?t.data=t.data.toUpperCase():s.kind==="startsWith"?t.data.startsWith(s.value)||(r=this._getOrReturnCtx(t,r),T(r,{code:S.invalid_string,validation:{startsWith:s.value},message:s.message}),n.dirty()):s.kind==="endsWith"?t.data.endsWith(s.value)||(r=this._getOrReturnCtx(t,r),T(r,{code:S.invalid_string,validation:{endsWith:s.value},message:s.message}),n.dirty()):s.kind==="datetime"?Dl(s).test(t.data)||(r=this._getOrReturnCtx(t,r),T(r,{code:S.invalid_string,validation:"datetime",message:s.message}),n.dirty()):s.kind==="date"?pp.test(t.data)||(r=this._getOrReturnCtx(t,r),T(r,{code:S.invalid_string,validation:"date",message:s.message}),n.dirty()):s.kind==="time"?new RegExp(`^${Ll(s)}$`).test(t.data)||(r=this._getOrReturnCtx(t,r),T(r,{code:S.invalid_string,validation:"time",message:s.message}),n.dirty()):s.kind==="duration"?sp.test(t.data)||(r=this._getOrReturnCtx(t,r),T(r,{validation:"duration",code:S.invalid_string,message:s.message}),n.dirty()):s.kind==="ip"?hp(t.data,s.version)||(r=this._getOrReturnCtx(t,r),T(r,{validation:"ip",code:S.invalid_string,message:s.message}),n.dirty()):s.kind==="jwt"?mp(t.data,s.alg)||(r=this._getOrReturnCtx(t,r),T(r,{validation:"jwt",code:S.invalid_string,message:s.message}),n.dirty()):s.kind==="cidr"?fp(t.data,s.version)||(r=this._getOrReturnCtx(t,r),T(r,{validation:"cidr",code:S.invalid_string,message:s.message}),n.dirty()):s.kind==="base64"?cp.test(t.data)||(r=this._getOrReturnCtx(t,r),T(r,{validation:"base64",code:S.invalid_string,message:s.message}),n.dirty()):s.kind==="base64url"?dp.test(t.data)||(r=this._getOrReturnCtx(t,r),T(r,{validation:"base64url",code:S.invalid_string,message:s.message}),n.dirty()):L.assertNever(s);return{status:n.value,value:t.data}}_regex(t,n,r){return this.refinement(s=>t.test(s),{validation:n,code:S.invalid_string,...N.errToObj(r)})}_addCheck(t){return new Ie({...this._def,checks:[...this._def.checks,t]})}email(t){return this._addCheck({kind:"email",...N.errToObj(t)})}url(t){return this._addCheck({kind:"url",...N.errToObj(t)})}emoji(t){return this._addCheck({kind:"emoji",...N.errToObj(t)})}uuid(t){return this._addCheck({kind:"uuid",...N.errToObj(t)})}nanoid(t){return this._addCheck({kind:"nanoid",...N.errToObj(t)})}cuid(t){return this._addCheck({kind:"cuid",...N.errToObj(t)})}cuid2(t){return this._addCheck({kind:"cuid2",...N.errToObj(t)})}ulid(t){return this._addCheck({kind:"ulid",...N.errToObj(t)})}base64(t){return this._addCheck({kind:"base64",...N.errToObj(t)})}base64url(t){return this._addCheck({kind:"base64url",...N.errToObj(t)})}jwt(t){return this._addCheck({kind:"jwt",...N.errToObj(t)})}ip(t){return this._addCheck({kind:"ip",...N.errToObj(t)})}cidr(t){return this._addCheck({kind:"cidr",...N.errToObj(t)})}datetime(t){var n,r;return typeof t=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:t}):this._addCheck({kind:"datetime",precision:(t==null?void 0:t.precision)===void 0?null:t==null?void 0:t.precision,offset:(n=t==null?void 0:t.offset)!==null&&n!==void 0&&n,local:(r=t==null?void 0:t.local)!==null&&r!==void 0&&r,...N.errToObj(t==null?void 0:t.message)})}date(t){return this._addCheck({kind:"date",message:t})}time(t){return typeof t=="string"?this._addCheck({kind:"time",precision:null,message:t}):this._addCheck({kind:"time",precision:(t==null?void 0:t.precision)===void 0?null:t==null?void 0:t.precision,...N.errToObj(t==null?void 0:t.message)})}duration(t){return this._addCheck({kind:"duration",...N.errToObj(t)})}regex(t,n){return this._addCheck({kind:"regex",regex:t,...N.errToObj(n)})}includes(t,n){return this._addCheck({kind:"includes",value:t,position:n==null?void 0:n.position,...N.errToObj(n==null?void 0:n.message)})}startsWith(t,n){return this._addCheck({kind:"startsWith",value:t,...N.errToObj(n)})}endsWith(t,n){return this._addCheck({kind:"endsWith",value:t,...N.errToObj(n)})}min(t,n){return this._addCheck({kind:"min",value:t,...N.errToObj(n)})}max(t,n){return this._addCheck({kind:"max",value:t,...N.errToObj(n)})}length(t,n){return this._addCheck({kind:"length",value:t,...N.errToObj(n)})}nonempty(t){return this.min(1,N.errToObj(t))}trim(){return new Ie({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Ie({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Ie({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(t=>t.kind==="datetime")}get isDate(){return!!this._def.checks.find(t=>t.kind==="date")}get isTime(){return!!this._def.checks.find(t=>t.kind==="time")}get isDuration(){return!!this._def.checks.find(t=>t.kind==="duration")}get isEmail(){return!!this._def.checks.find(t=>t.kind==="email")}get isURL(){return!!this._def.checks.find(t=>t.kind==="url")}get isEmoji(){return!!this._def.checks.find(t=>t.kind==="emoji")}get isUUID(){return!!this._def.checks.find(t=>t.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(t=>t.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(t=>t.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(t=>t.kind==="cuid2")}get isULID(){return!!this._def.checks.find(t=>t.kind==="ulid")}get isIP(){return!!this._def.checks.find(t=>t.kind==="ip")}get isCIDR(){return!!this._def.checks.find(t=>t.kind==="cidr")}get isBase64(){return!!this._def.checks.find(t=>t.kind==="base64")}get isBase64url(){return!!this._def.checks.find(t=>t.kind==="base64url")}get minLength(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxLength(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}}function gp(e,t){const n=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,s=n>r?n:r;return parseInt(e.toFixed(s).replace(".",""))%parseInt(t.toFixed(s).replace(".",""))/Math.pow(10,s)}Ie.create=e=>{var t;return new Ie({checks:[],typeName:C.ZodString,coerce:(t=e==null?void 0:e.coerce)!==null&&t!==void 0&&t,...R(e)})};class lt extends O{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(t){if(this._def.coerce&&(t.data=Number(t.data)),this._getType(t)!==w.number){const s=this._getOrReturnCtx(t);return T(s,{code:S.invalid_type,expected:w.number,received:s.parsedType}),x}let n;const r=new ne;for(const s of this._def.checks)s.kind==="int"?L.isInteger(t.data)||(n=this._getOrReturnCtx(t,n),T(n,{code:S.invalid_type,expected:"integer",received:"float",message:s.message}),r.dirty()):s.kind==="min"?(s.inclusive?t.data<s.value:t.data<=s.value)&&(n=this._getOrReturnCtx(t,n),T(n,{code:S.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),r.dirty()):s.kind==="max"?(s.inclusive?t.data>s.value:t.data>=s.value)&&(n=this._getOrReturnCtx(t,n),T(n,{code:S.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),r.dirty()):s.kind==="multipleOf"?gp(t.data,s.value)!==0&&(n=this._getOrReturnCtx(t,n),T(n,{code:S.not_multiple_of,multipleOf:s.value,message:s.message}),r.dirty()):s.kind==="finite"?Number.isFinite(t.data)||(n=this._getOrReturnCtx(t,n),T(n,{code:S.not_finite,message:s.message}),r.dirty()):L.assertNever(s);return{status:r.value,value:t.data}}gte(t,n){return this.setLimit("min",t,!0,N.toString(n))}gt(t,n){return this.setLimit("min",t,!1,N.toString(n))}lte(t,n){return this.setLimit("max",t,!0,N.toString(n))}lt(t,n){return this.setLimit("max",t,!1,N.toString(n))}setLimit(t,n,r,s){return new lt({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:N.toString(s)}]})}_addCheck(t){return new lt({...this._def,checks:[...this._def.checks,t]})}int(t){return this._addCheck({kind:"int",message:N.toString(t)})}positive(t){return this._addCheck({kind:"min",value:0,inclusive:!1,message:N.toString(t)})}negative(t){return this._addCheck({kind:"max",value:0,inclusive:!1,message:N.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:0,inclusive:!0,message:N.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:0,inclusive:!0,message:N.toString(t)})}multipleOf(t,n){return this._addCheck({kind:"multipleOf",value:t,message:N.toString(n)})}finite(t){return this._addCheck({kind:"finite",message:N.toString(t)})}safe(t){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:N.toString(t)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:N.toString(t)})}get minValue(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxValue(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}get isInt(){return!!this._def.checks.find(t=>t.kind==="int"||t.kind==="multipleOf"&&L.isInteger(t.value))}get isFinite(){let t=null,n=null;for(const r of this._def.checks){if(r.kind==="finite"||r.kind==="int"||r.kind==="multipleOf")return!0;r.kind==="min"?(n===null||r.value>n)&&(n=r.value):r.kind==="max"&&(t===null||r.value<t)&&(t=r.value)}return Number.isFinite(n)&&Number.isFinite(t)}}lt.create=e=>new lt({checks:[],typeName:C.ZodNumber,coerce:(e==null?void 0:e.coerce)||!1,...R(e)});class ut extends O{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(t){if(this._def.coerce)try{t.data=BigInt(t.data)}catch{return this._getInvalidInput(t)}if(this._getType(t)!==w.bigint)return this._getInvalidInput(t);let n;const r=new ne;for(const s of this._def.checks)s.kind==="min"?(s.inclusive?t.data<s.value:t.data<=s.value)&&(n=this._getOrReturnCtx(t,n),T(n,{code:S.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),r.dirty()):s.kind==="max"?(s.inclusive?t.data>s.value:t.data>=s.value)&&(n=this._getOrReturnCtx(t,n),T(n,{code:S.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),r.dirty()):s.kind==="multipleOf"?t.data%s.value!==BigInt(0)&&(n=this._getOrReturnCtx(t,n),T(n,{code:S.not_multiple_of,multipleOf:s.value,message:s.message}),r.dirty()):L.assertNever(s);return{status:r.value,value:t.data}}_getInvalidInput(t){const n=this._getOrReturnCtx(t);return T(n,{code:S.invalid_type,expected:w.bigint,received:n.parsedType}),x}gte(t,n){return this.setLimit("min",t,!0,N.toString(n))}gt(t,n){return this.setLimit("min",t,!1,N.toString(n))}lte(t,n){return this.setLimit("max",t,!0,N.toString(n))}lt(t,n){return this.setLimit("max",t,!1,N.toString(n))}setLimit(t,n,r,s){return new ut({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:N.toString(s)}]})}_addCheck(t){return new ut({...this._def,checks:[...this._def.checks,t]})}positive(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:N.toString(t)})}negative(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:N.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:N.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:N.toString(t)})}multipleOf(t,n){return this._addCheck({kind:"multipleOf",value:t,message:N.toString(n)})}get minValue(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxValue(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}}ut.create=e=>{var t;return new ut({checks:[],typeName:C.ZodBigInt,coerce:(t=e==null?void 0:e.coerce)!==null&&t!==void 0&&t,...R(e)})};class fn extends O{_parse(t){if(this._def.coerce&&(t.data=!!t.data),this._getType(t)!==w.boolean){const n=this._getOrReturnCtx(t);return T(n,{code:S.invalid_type,expected:w.boolean,received:n.parsedType}),x}return ie(t.data)}}fn.create=e=>new fn({typeName:C.ZodBoolean,coerce:(e==null?void 0:e.coerce)||!1,...R(e)});class Et extends O{_parse(t){if(this._def.coerce&&(t.data=new Date(t.data)),this._getType(t)!==w.date){const s=this._getOrReturnCtx(t);return T(s,{code:S.invalid_type,expected:w.date,received:s.parsedType}),x}if(isNaN(t.data.getTime()))return T(this._getOrReturnCtx(t),{code:S.invalid_date}),x;const n=new ne;let r;for(const s of this._def.checks)s.kind==="min"?t.data.getTime()<s.value&&(r=this._getOrReturnCtx(t,r),T(r,{code:S.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),n.dirty()):s.kind==="max"?t.data.getTime()>s.value&&(r=this._getOrReturnCtx(t,r),T(r,{code:S.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),n.dirty()):L.assertNever(s);return{status:n.value,value:new Date(t.data.getTime())}}_addCheck(t){return new Et({...this._def,checks:[...this._def.checks,t]})}min(t,n){return this._addCheck({kind:"min",value:t.getTime(),message:N.toString(n)})}max(t,n){return this._addCheck({kind:"max",value:t.getTime(),message:N.toString(n)})}get minDate(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t!=null?new Date(t):null}get maxDate(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t!=null?new Date(t):null}}Et.create=e=>new Et({checks:[],coerce:(e==null?void 0:e.coerce)||!1,typeName:C.ZodDate,...R(e)});class vr extends O{_parse(t){if(this._getType(t)!==w.symbol){const n=this._getOrReturnCtx(t);return T(n,{code:S.invalid_type,expected:w.symbol,received:n.parsedType}),x}return ie(t.data)}}vr.create=e=>new vr({typeName:C.ZodSymbol,...R(e)});class gn extends O{_parse(t){if(this._getType(t)!==w.undefined){const n=this._getOrReturnCtx(t);return T(n,{code:S.invalid_type,expected:w.undefined,received:n.parsedType}),x}return ie(t.data)}}gn.create=e=>new gn({typeName:C.ZodUndefined,...R(e)});class yn extends O{_parse(t){if(this._getType(t)!==w.null){const n=this._getOrReturnCtx(t);return T(n,{code:S.invalid_type,expected:w.null,received:n.parsedType}),x}return ie(t.data)}}yn.create=e=>new yn({typeName:C.ZodNull,...R(e)});class Vt extends O{constructor(){super(...arguments),this._any=!0}_parse(t){return ie(t.data)}}Vt.create=e=>new Vt({typeName:C.ZodAny,...R(e)});class _t extends O{constructor(){super(...arguments),this._unknown=!0}_parse(t){return ie(t.data)}}_t.create=e=>new _t({typeName:C.ZodUnknown,...R(e)});class Xe extends O{_parse(t){const n=this._getOrReturnCtx(t);return T(n,{code:S.invalid_type,expected:w.never,received:n.parsedType}),x}}Xe.create=e=>new Xe({typeName:C.ZodNever,...R(e)});class br extends O{_parse(t){if(this._getType(t)!==w.undefined){const n=this._getOrReturnCtx(t);return T(n,{code:S.invalid_type,expected:w.void,received:n.parsedType}),x}return ie(t.data)}}br.create=e=>new br({typeName:C.ZodVoid,...R(e)});class Ae extends O{_parse(t){const{ctx:n,status:r}=this._processInputParams(t),s=this._def;if(n.parsedType!==w.array)return T(n,{code:S.invalid_type,expected:w.array,received:n.parsedType}),x;if(s.exactLength!==null){const i=n.data.length>s.exactLength.value,o=n.data.length<s.exactLength.value;(i||o)&&(T(n,{code:i?S.too_big:S.too_small,minimum:o?s.exactLength.value:void 0,maximum:i?s.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:s.exactLength.message}),r.dirty())}if(s.minLength!==null&&n.data.length<s.minLength.value&&(T(n,{code:S.too_small,minimum:s.minLength.value,type:"array",inclusive:!0,exact:!1,message:s.minLength.message}),r.dirty()),s.maxLength!==null&&n.data.length>s.maxLength.value&&(T(n,{code:S.too_big,maximum:s.maxLength.value,type:"array",inclusive:!0,exact:!1,message:s.maxLength.message}),r.dirty()),n.common.async)return Promise.all([...n.data].map((i,o)=>s.type._parseAsync(new je(n,i,n.path,o)))).then(i=>ne.mergeArray(r,i));const a=[...n.data].map((i,o)=>s.type._parseSync(new je(n,i,n.path,o)));return ne.mergeArray(r,a)}get element(){return this._def.type}min(t,n){return new Ae({...this._def,minLength:{value:t,message:N.toString(n)}})}max(t,n){return new Ae({...this._def,maxLength:{value:t,message:N.toString(n)}})}length(t,n){return new Ae({...this._def,exactLength:{value:t,message:N.toString(n)}})}nonempty(t){return this.min(1,t)}}function Rt(e){if(e instanceof Z){const t={};for(const n in e.shape){const r=e.shape[n];t[n]=Be.create(Rt(r))}return new Z({...e._def,shape:()=>t})}return e instanceof Ae?new Ae({...e._def,type:Rt(e.element)}):e instanceof Be?Be.create(Rt(e.unwrap())):e instanceof dt?dt.create(Rt(e.unwrap())):e instanceof Ve?Ve.create(e.items.map(t=>Rt(t))):e}Ae.create=(e,t)=>new Ae({type:e,minLength:null,maxLength:null,exactLength:null,typeName:C.ZodArray,...R(t)});class Z extends O{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const t=this._def.shape(),n=L.objectKeys(t);return this._cached={shape:t,keys:n}}_parse(t){if(this._getType(t)!==w.object){const u=this._getOrReturnCtx(t);return T(u,{code:S.invalid_type,expected:w.object,received:u.parsedType}),x}const{status:n,ctx:r}=this._processInputParams(t),{shape:s,keys:a}=this._getCached(),i=[];if(!(this._def.catchall instanceof Xe&&this._def.unknownKeys==="strip"))for(const u in r.data)a.includes(u)||i.push(u);const o=[];for(const u of a){const l=s[u],d=r.data[u];o.push({key:{status:"valid",value:u},value:l._parse(new je(r,d,r.path,u)),alwaysSet:u in r.data})}if(this._def.catchall instanceof Xe){const u=this._def.unknownKeys;if(u==="passthrough")for(const l of i)o.push({key:{status:"valid",value:l},value:{status:"valid",value:r.data[l]}});else if(u==="strict")i.length>0&&(T(r,{code:S.unrecognized_keys,keys:i}),n.dirty());else if(u!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const u=this._def.catchall;for(const l of i){const d=r.data[l];o.push({key:{status:"valid",value:l},value:u._parse(new je(r,d,r.path,l)),alwaysSet:l in r.data})}}return r.common.async?Promise.resolve().then(async()=>{const u=[];for(const l of o){const d=await l.key,c=await l.value;u.push({key:d,value:c,alwaysSet:l.alwaysSet})}return u}).then(u=>ne.mergeObjectSync(n,u)):ne.mergeObjectSync(n,o)}get shape(){return this._def.shape()}strict(t){return N.errToObj,new Z({...this._def,unknownKeys:"strict",...t!==void 0?{errorMap:(n,r)=>{var s,a,i,o;const u=(i=(a=(s=this._def).errorMap)===null||a===void 0?void 0:a.call(s,n,r).message)!==null&&i!==void 0?i:r.defaultError;return n.code==="unrecognized_keys"?{message:(o=N.errToObj(t).message)!==null&&o!==void 0?o:u}:{message:u}}}:{}})}strip(){return new Z({...this._def,unknownKeys:"strip"})}passthrough(){return new Z({...this._def,unknownKeys:"passthrough"})}extend(t){return new Z({...this._def,shape:()=>({...this._def.shape(),...t})})}merge(t){return new Z({unknownKeys:t._def.unknownKeys,catchall:t._def.catchall,shape:()=>({...this._def.shape(),...t._def.shape()}),typeName:C.ZodObject})}setKey(t,n){return this.augment({[t]:n})}catchall(t){return new Z({...this._def,catchall:t})}pick(t){const n={};return L.objectKeys(t).forEach(r=>{t[r]&&this.shape[r]&&(n[r]=this.shape[r])}),new Z({...this._def,shape:()=>n})}omit(t){const n={};return L.objectKeys(this.shape).forEach(r=>{t[r]||(n[r]=this.shape[r])}),new Z({...this._def,shape:()=>n})}deepPartial(){return Rt(this)}partial(t){const n={};return L.objectKeys(this.shape).forEach(r=>{const s=this.shape[r];t&&!t[r]?n[r]=s:n[r]=s.optional()}),new Z({...this._def,shape:()=>n})}required(t){const n={};return L.objectKeys(this.shape).forEach(r=>{if(t&&!t[r])n[r]=this.shape[r];else{let s=this.shape[r];for(;s instanceof Be;)s=s._def.innerType;n[r]=s}}),new Z({...this._def,shape:()=>n})}keyof(){return Fl(L.objectKeys(this.shape))}}Z.create=(e,t)=>new Z({shape:()=>e,unknownKeys:"strip",catchall:Xe.create(),typeName:C.ZodObject,...R(t)}),Z.strictCreate=(e,t)=>new Z({shape:()=>e,unknownKeys:"strict",catchall:Xe.create(),typeName:C.ZodObject,...R(t)}),Z.lazycreate=(e,t)=>new Z({shape:e,unknownKeys:"strip",catchall:Xe.create(),typeName:C.ZodObject,...R(t)});class _n extends O{_parse(t){const{ctx:n}=this._processInputParams(t),r=this._def.options;if(n.common.async)return Promise.all(r.map(async s=>{const a={...n,common:{...n.common,issues:[]},parent:null};return{result:await s._parseAsync({data:n.data,path:n.path,parent:a}),ctx:a}})).then(function(s){for(const i of s)if(i.result.status==="valid")return i.result;for(const i of s)if(i.result.status==="dirty")return n.common.issues.push(...i.ctx.common.issues),i.result;const a=s.map(i=>new ge(i.ctx.common.issues));return T(n,{code:S.invalid_union,unionErrors:a}),x});{let s;const a=[];for(const o of r){const u={...n,common:{...n.common,issues:[]},parent:null},l=o._parseSync({data:n.data,path:n.path,parent:u});if(l.status==="valid")return l;l.status!=="dirty"||s||(s={result:l,ctx:u}),u.common.issues.length&&a.push(u.common.issues)}if(s)return n.common.issues.push(...s.ctx.common.issues),s.result;const i=a.map(o=>new ge(o));return T(n,{code:S.invalid_union,unionErrors:i}),x}}get options(){return this._def.options}}_n.create=(e,t)=>new _n({options:e,typeName:C.ZodUnion,...R(t)});const Je=e=>e instanceof bn?Je(e.schema):e instanceof Ce?Je(e.innerType()):e instanceof Sn?[e.value]:e instanceof ct?e.options:e instanceof En?L.objectValues(e.enum):e instanceof Tn?Je(e._def.innerType):e instanceof gn?[void 0]:e instanceof yn?[null]:e instanceof Be?[void 0,...Je(e.unwrap())]:e instanceof dt?[null,...Je(e.unwrap())]:e instanceof ka||e instanceof kn?Je(e.unwrap()):e instanceof wn?Je(e._def.innerType):[];class Ar extends O{_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==w.object)return T(n,{code:S.invalid_type,expected:w.object,received:n.parsedType}),x;const r=this.discriminator,s=n.data[r],a=this.optionsMap.get(s);return a?n.common.async?a._parseAsync({data:n.data,path:n.path,parent:n}):a._parseSync({data:n.data,path:n.path,parent:n}):(T(n,{code:S.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),x)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(t,n,r){const s=new Map;for(const a of n){const i=Je(a.shape[t]);if(!i.length)throw new Error(`A discriminator value for key \`${t}\` could not be extracted from all schema options`);for(const o of i){if(s.has(o))throw new Error(`Discriminator property ${String(t)} has duplicate value ${String(o)}`);s.set(o,a)}}return new Ar({typeName:C.ZodDiscriminatedUnion,discriminator:t,options:n,optionsMap:s,...R(r)})}}function Is(e,t){const n=He(e),r=He(t);if(e===t)return{valid:!0,data:e};if(n===w.object&&r===w.object){const s=L.objectKeys(t),a=L.objectKeys(e).filter(o=>s.indexOf(o)!==-1),i={...e,...t};for(const o of a){const u=Is(e[o],t[o]);if(!u.valid)return{valid:!1};i[o]=u.data}return{valid:!0,data:i}}if(n===w.array&&r===w.array){if(e.length!==t.length)return{valid:!1};const s=[];for(let a=0;a<e.length;a++){const i=Is(e[a],t[a]);if(!i.valid)return{valid:!1};s.push(i.data)}return{valid:!0,data:s}}return n===w.date&&r===w.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}class vn extends O{_parse(t){const{status:n,ctx:r}=this._processInputParams(t),s=(a,i)=>{if(ks(a)||ks(i))return x;const o=Is(a.value,i.value);return o.valid?((Ns(a)||Ns(i))&&n.dirty(),{status:n.value,value:o.data}):(T(r,{code:S.invalid_intersection_types}),x)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([a,i])=>s(a,i)):s(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}vn.create=(e,t,n)=>new vn({left:e,right:t,typeName:C.ZodIntersection,...R(n)});class Ve extends O{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==w.array)return T(r,{code:S.invalid_type,expected:w.array,received:r.parsedType}),x;if(r.data.length<this._def.items.length)return T(r,{code:S.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),x;!this._def.rest&&r.data.length>this._def.items.length&&(T(r,{code:S.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),n.dirty());const s=[...r.data].map((a,i)=>{const o=this._def.items[i]||this._def.rest;return o?o._parse(new je(r,a,r.path,i)):null}).filter(a=>!!a);return r.common.async?Promise.all(s).then(a=>ne.mergeArray(n,a)):ne.mergeArray(n,s)}get items(){return this._def.items}rest(t){return new Ve({...this._def,rest:t})}}Ve.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Ve({items:e,typeName:C.ZodTuple,rest:null,...R(t)})};class xr extends O{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==w.object)return T(r,{code:S.invalid_type,expected:w.object,received:r.parsedType}),x;const s=[],a=this._def.keyType,i=this._def.valueType;for(const o in r.data)s.push({key:a._parse(new je(r,o,r.path,o)),value:i._parse(new je(r,r.data[o],r.path,o)),alwaysSet:o in r.data});return r.common.async?ne.mergeObjectAsync(n,s):ne.mergeObjectSync(n,s)}get element(){return this._def.valueType}static create(t,n,r){return new xr(n instanceof O?{keyType:t,valueType:n,typeName:C.ZodRecord,...R(r)}:{keyType:Ie.create(),valueType:t,typeName:C.ZodRecord,...R(n)})}}class Sr extends O{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==w.map)return T(r,{code:S.invalid_type,expected:w.map,received:r.parsedType}),x;const s=this._def.keyType,a=this._def.valueType,i=[...r.data.entries()].map(([o,u],l)=>({key:s._parse(new je(r,o,r.path,[l,"key"])),value:a._parse(new je(r,u,r.path,[l,"value"]))}));if(r.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const u of i){const l=await u.key,d=await u.value;if(l.status==="aborted"||d.status==="aborted")return x;l.status!=="dirty"&&d.status!=="dirty"||n.dirty(),o.set(l.value,d.value)}return{status:n.value,value:o}})}{const o=new Map;for(const u of i){const l=u.key,d=u.value;if(l.status==="aborted"||d.status==="aborted")return x;l.status!=="dirty"&&d.status!=="dirty"||n.dirty(),o.set(l.value,d.value)}return{status:n.value,value:o}}}}Sr.create=(e,t,n)=>new Sr({valueType:t,keyType:e,typeName:C.ZodMap,...R(n)});class Tt extends O{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==w.set)return T(r,{code:S.invalid_type,expected:w.set,received:r.parsedType}),x;const s=this._def;s.minSize!==null&&r.data.size<s.minSize.value&&(T(r,{code:S.too_small,minimum:s.minSize.value,type:"set",inclusive:!0,exact:!1,message:s.minSize.message}),n.dirty()),s.maxSize!==null&&r.data.size>s.maxSize.value&&(T(r,{code:S.too_big,maximum:s.maxSize.value,type:"set",inclusive:!0,exact:!1,message:s.maxSize.message}),n.dirty());const a=this._def.valueType;function i(u){const l=new Set;for(const d of u){if(d.status==="aborted")return x;d.status==="dirty"&&n.dirty(),l.add(d.value)}return{status:n.value,value:l}}const o=[...r.data.values()].map((u,l)=>a._parse(new je(r,u,r.path,l)));return r.common.async?Promise.all(o).then(u=>i(u)):i(o)}min(t,n){return new Tt({...this._def,minSize:{value:t,message:N.toString(n)}})}max(t,n){return new Tt({...this._def,maxSize:{value:t,message:N.toString(n)}})}size(t,n){return this.min(t,n).max(t,n)}nonempty(t){return this.min(1,t)}}Tt.create=(e,t)=>new Tt({valueType:e,minSize:null,maxSize:null,typeName:C.ZodSet,...R(t)});class $t extends O{constructor(){super(...arguments),this.validate=this.implement}_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==w.function)return T(n,{code:S.invalid_type,expected:w.function,received:n.parsedType}),x;function r(o,u){return gr({data:o,path:n.path,errorMaps:[n.common.contextualErrorMap,n.schemaErrorMap,fr(),jt].filter(l=>!!l),issueData:{code:S.invalid_arguments,argumentsError:u}})}function s(o,u){return gr({data:o,path:n.path,errorMaps:[n.common.contextualErrorMap,n.schemaErrorMap,fr(),jt].filter(l=>!!l),issueData:{code:S.invalid_return_type,returnTypeError:u}})}const a={errorMap:n.common.contextualErrorMap},i=n.data;if(this._def.returns instanceof Gt){const o=this;return ie(async function(...u){const l=new ge([]),d=await o._def.args.parseAsync(u,a).catch(p=>{throw l.addIssue(r(u,p)),l}),c=await Reflect.apply(i,this,d);return await o._def.returns._def.type.parseAsync(c,a).catch(p=>{throw l.addIssue(s(c,p)),l})})}{const o=this;return ie(function(...u){const l=o._def.args.safeParse(u,a);if(!l.success)throw new ge([r(u,l.error)]);const d=Reflect.apply(i,this,l.data),c=o._def.returns.safeParse(d,a);if(!c.success)throw new ge([s(d,c.error)]);return c.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...t){return new $t({...this._def,args:Ve.create(t).rest(_t.create())})}returns(t){return new $t({...this._def,returns:t})}implement(t){return this.parse(t)}strictImplement(t){return this.parse(t)}static create(t,n,r){return new $t({args:t||Ve.create([]).rest(_t.create()),returns:n||_t.create(),typeName:C.ZodFunction,...R(r)})}}class bn extends O{get schema(){return this._def.getter()}_parse(t){const{ctx:n}=this._processInputParams(t);return this._def.getter()._parse({data:n.data,path:n.path,parent:n})}}bn.create=(e,t)=>new bn({getter:e,typeName:C.ZodLazy,...R(t)});class Sn extends O{_parse(t){if(t.data!==this._def.value){const n=this._getOrReturnCtx(t);return T(n,{received:n.data,code:S.invalid_literal,expected:this._def.value}),x}return{status:"valid",value:t.data}}get value(){return this._def.value}}function Fl(e,t){return new ct({values:e,typeName:C.ZodEnum,...R(t)})}Sn.create=(e,t)=>new Sn({value:e,typeName:C.ZodLiteral,...R(t)});class ct extends O{constructor(){super(...arguments),Xt.set(this,void 0)}_parse(t){if(typeof t.data!="string"){const n=this._getOrReturnCtx(t),r=this._def.values;return T(n,{expected:L.joinValues(r),received:n.parsedType,code:S.invalid_type}),x}if(_r(this,Xt)||Ml(this,Xt,new Set(this._def.values)),!_r(this,Xt).has(t.data)){const n=this._getOrReturnCtx(t),r=this._def.values;return T(n,{received:n.data,code:S.invalid_enum_value,options:r}),x}return ie(t.data)}get options(){return this._def.values}get enum(){const t={};for(const n of this._def.values)t[n]=n;return t}get Values(){const t={};for(const n of this._def.values)t[n]=n;return t}get Enum(){const t={};for(const n of this._def.values)t[n]=n;return t}extract(t,n=this._def){return ct.create(t,{...this._def,...n})}exclude(t,n=this._def){return ct.create(this.options.filter(r=>!t.includes(r)),{...this._def,...n})}}Xt=new WeakMap,ct.create=Fl;class En extends O{constructor(){super(...arguments),Jt.set(this,void 0)}_parse(t){const n=L.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(t);if(r.parsedType!==w.string&&r.parsedType!==w.number){const s=L.objectValues(n);return T(r,{expected:L.joinValues(s),received:r.parsedType,code:S.invalid_type}),x}if(_r(this,Jt)||Ml(this,Jt,new Set(L.getValidEnumValues(this._def.values))),!_r(this,Jt).has(t.data)){const s=L.objectValues(n);return T(r,{received:r.data,code:S.invalid_enum_value,options:s}),x}return ie(t.data)}get enum(){return this._def.values}}Jt=new WeakMap,En.create=(e,t)=>new En({values:e,typeName:C.ZodNativeEnum,...R(t)});class Gt extends O{unwrap(){return this._def.type}_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==w.promise&&n.common.async===!1)return T(n,{code:S.invalid_type,expected:w.promise,received:n.parsedType}),x;const r=n.parsedType===w.promise?n.data:Promise.resolve(n.data);return ie(r.then(s=>this._def.type.parseAsync(s,{path:n.path,errorMap:n.common.contextualErrorMap})))}}Gt.create=(e,t)=>new Gt({type:e,typeName:C.ZodPromise,...R(t)});class Ce extends O{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===C.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(t){const{status:n,ctx:r}=this._processInputParams(t),s=this._def.effect||null,a={addIssue:i=>{T(r,i),i.fatal?n.abort():n.dirty()},get path(){return r.path}};if(a.addIssue=a.addIssue.bind(a),s.type==="preprocess"){const i=s.transform(r.data,a);if(r.common.async)return Promise.resolve(i).then(async o=>{if(n.value==="aborted")return x;const u=await this._def.schema._parseAsync({data:o,path:r.path,parent:r});return u.status==="aborted"?x:u.status==="dirty"||n.value==="dirty"?yr(u.value):u});{if(n.value==="aborted")return x;const o=this._def.schema._parseSync({data:i,path:r.path,parent:r});return o.status==="aborted"?x:o.status==="dirty"||n.value==="dirty"?yr(o.value):o}}if(s.type==="refinement"){const i=o=>{const u=s.refinement(o,a);if(r.common.async)return Promise.resolve(u);if(u instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(r.common.async===!1){const o=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return o.status==="aborted"?x:(o.status==="dirty"&&n.dirty(),i(o.value),{status:n.value,value:o.value})}return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(o=>o.status==="aborted"?x:(o.status==="dirty"&&n.dirty(),i(o.value).then(()=>({status:n.value,value:o.value}))))}if(s.type==="transform"){if(r.common.async===!1){const i=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!St(i))return i;const o=s.transform(i.value,a);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:n.value,value:o}}return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(i=>St(i)?Promise.resolve(s.transform(i.value,a)).then(o=>({status:n.value,value:o})):i)}L.assertNever(s)}}Ce.create=(e,t,n)=>new Ce({schema:e,typeName:C.ZodEffects,effect:t,...R(n)}),Ce.createWithPreprocess=(e,t,n)=>new Ce({schema:t,effect:{type:"preprocess",transform:e},typeName:C.ZodEffects,...R(n)});class Be extends O{_parse(t){return this._getType(t)===w.undefined?ie(void 0):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}Be.create=(e,t)=>new Be({innerType:e,typeName:C.ZodOptional,...R(t)});class dt extends O{_parse(t){return this._getType(t)===w.null?ie(null):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}dt.create=(e,t)=>new dt({innerType:e,typeName:C.ZodNullable,...R(t)});class Tn extends O{_parse(t){const{ctx:n}=this._processInputParams(t);let r=n.data;return n.parsedType===w.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:n.path,parent:n})}removeDefault(){return this._def.innerType}}Tn.create=(e,t)=>new Tn({innerType:e,typeName:C.ZodDefault,defaultValue:typeof t.default=="function"?t.default:()=>t.default,...R(t)});class wn extends O{_parse(t){const{ctx:n}=this._processInputParams(t),r={...n,common:{...n.common,issues:[]}},s=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return mn(s)?s.then(a=>({status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new ge(r.common.issues)},input:r.data})})):{status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new ge(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}wn.create=(e,t)=>new wn({innerType:e,typeName:C.ZodCatch,catchValue:typeof t.catch=="function"?t.catch:()=>t.catch,...R(t)});class Er extends O{_parse(t){if(this._getType(t)!==w.nan){const n=this._getOrReturnCtx(t);return T(n,{code:S.invalid_type,expected:w.nan,received:n.parsedType}),x}return{status:"valid",value:t.data}}}Er.create=e=>new Er({typeName:C.ZodNaN,...R(e)});const yp=Symbol("zod_brand");class ka extends O{_parse(t){const{ctx:n}=this._processInputParams(t),r=n.data;return this._def.type._parse({data:r,path:n.path,parent:n})}unwrap(){return this._def.type}}class xn extends O{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.common.async)return(async()=>{const s=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return s.status==="aborted"?x:s.status==="dirty"?(n.dirty(),yr(s.value)):this._def.out._parseAsync({data:s.value,path:r.path,parent:r})})();{const s=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return s.status==="aborted"?x:s.status==="dirty"?(n.dirty(),{status:"dirty",value:s.value}):this._def.out._parseSync({data:s.value,path:r.path,parent:r})}}static create(t,n){return new xn({in:t,out:n,typeName:C.ZodPipeline})}}class kn extends O{_parse(t){const n=this._def.innerType._parse(t),r=s=>(St(s)&&(s.value=Object.freeze(s.value)),s);return mn(n)?n.then(s=>r(s)):r(n)}unwrap(){return this._def.innerType}}function Gi(e,t={},n){return e?Vt.create().superRefine((r,s)=>{var a,i;if(!e(r)){const o=typeof t=="function"?t(r):typeof t=="string"?{message:t}:t,u=(i=(a=o.fatal)!==null&&a!==void 0?a:n)===null||i===void 0||i,l=typeof o=="string"?{message:o}:o;s.addIssue({code:"custom",...l,fatal:u})}}):Vt.create()}kn.create=(e,t)=>new kn({innerType:e,typeName:C.ZodReadonly,...R(t)});const _p={object:Z.lazycreate};var C;(function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"})(C||(C={}));const qi=Ie.create,Zi=lt.create,vp=Er.create,bp=ut.create,Ki=fn.create,Sp=Et.create,Ep=vr.create,Tp=gn.create,wp=yn.create,kp=Vt.create,Np=_t.create,Ip=Xe.create,Cp=br.create,Ap=Ae.create,xp=Z.create,Rp=Z.strictCreate,Op=_n.create,Mp=Ar.create,Pp=vn.create,Lp=Ve.create,Dp=xr.create,Fp=Sr.create,$p=Tt.create,Up=$t.create,Bp=bn.create,jp=Sn.create,Vp=ct.create,Gp=En.create,qp=Gt.create,Yi=Ce.create,Zp=Be.create,Kp=dt.create,Yp=Ce.createWithPreprocess,Hp=xn.create,Wp={string:e=>Ie.create({...e,coerce:!0}),number:e=>lt.create({...e,coerce:!0}),boolean:e=>fn.create({...e,coerce:!0}),bigint:e=>ut.create({...e,coerce:!0}),date:e=>Et.create({...e,coerce:!0})},zp=x;var re=Object.freeze({__proto__:null,defaultErrorMap:jt,setErrorMap:function(e){Ol=e},getErrorMap:fr,makeIssue:gr,EMPTY_PATH:[],addIssueToContext:T,ParseStatus:ne,INVALID:x,DIRTY:yr,OK:ie,isAborted:ks,isDirty:Ns,isValid:St,isAsync:mn,get util(){return L},get objectUtil(){return ws},ZodParsedType:w,getParsedType:He,ZodType:O,datetimeRegex:Dl,ZodString:Ie,ZodNumber:lt,ZodBigInt:ut,ZodBoolean:fn,ZodDate:Et,ZodSymbol:vr,ZodUndefined:gn,ZodNull:yn,ZodAny:Vt,ZodUnknown:_t,ZodNever:Xe,ZodVoid:br,ZodArray:Ae,ZodObject:Z,ZodUnion:_n,ZodDiscriminatedUnion:Ar,ZodIntersection:vn,ZodTuple:Ve,ZodRecord:xr,ZodMap:Sr,ZodSet:Tt,ZodFunction:$t,ZodLazy:bn,ZodLiteral:Sn,ZodEnum:ct,ZodNativeEnum:En,ZodPromise:Gt,ZodEffects:Ce,ZodTransformer:Ce,ZodOptional:Be,ZodNullable:dt,ZodDefault:Tn,ZodCatch:wn,ZodNaN:Er,BRAND:yp,ZodBranded:ka,ZodPipeline:xn,ZodReadonly:kn,custom:Gi,Schema:O,ZodSchema:O,late:_p,get ZodFirstPartyTypeKind(){return C},coerce:Wp,any:kp,array:Ap,bigint:bp,boolean:Ki,date:Sp,discriminatedUnion:Mp,effect:Yi,enum:Vp,function:Up,instanceof:(e,t={message:`Input not instance of ${e.name}`})=>Gi(n=>n instanceof e,t),intersection:Pp,lazy:Bp,literal:jp,map:Fp,nan:vp,nativeEnum:Gp,never:Ip,null:wp,nullable:Kp,number:Zi,object:xp,oboolean:()=>Ki().optional(),onumber:()=>Zi().optional(),optional:Zp,ostring:()=>qi().optional(),pipeline:Hp,preprocess:Yp,promise:qp,record:Dp,set:$p,strictObject:Rp,string:qi,symbol:Ep,transformer:Yi,tuple:Lp,undefined:Tp,union:Op,unknown:Np,void:Cp,NEVER:zp,ZodIssueCode:S,quotelessJson:e=>JSON.stringify(e,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:ge}),Xp=(e=>(e.readFile="read-file",e.saveFile="save-file",e.editFile="edit-file",e.clarify="clarify",e.onboardingSubAgent="onboarding-sub-agent",e.launchProcess="launch-process",e.killProcess="kill-process",e.readProcess="read-process",e.writeProcess="write-process",e.listProcesses="list-processes",e.waitProcess="wait-process",e.openBrowser="open-browser",e.strReplaceEditor="str-replace-editor",e.remember="remember",e.diagnostics="diagnostics",e.webFetch="web-fetch",e.setupScript="setup-script",e.readTerminal="read-terminal",e.gitCommitRetrieval="git-commit-retrieval",e.memoryRetrieval="memory-retrieval",e.startWorkerAgent="start_worker_agent",e.readWorkerState="read_worker_state",e.waitForWorkerAgent="wait_for_worker_agent",e.sendInstructionToWorkerAgent="send_instruction_to_worker_agent",e.stopWorkerAgent="stop_worker_agent",e.deleteWorkerAgent="delete_worker_agent",e.readWorkerAgentEdits="read_worker_agent_edits",e.applyWorkerAgentEdits="apply_worker_agent_edits",e.LocalSubAgent="local-sub-agent",e))(Xp||{}),Jp=(e=>(e.remoteToolHost="remoteToolHost",e.localToolHost="localToolHost",e.sidecarToolHost="sidecarToolHost",e.mcpHost="mcpHost",e))(Jp||{}),Qp=(e=>(e[e.ContentText=0]="ContentText",e[e.ContentImage=1]="ContentImage",e))(Qp||{}),eh=(e=>(e[e.Unsafe=0]="Unsafe",e[e.Safe=1]="Safe",e[e.Check=2]="Check",e))(eh||{}),th=(e=>(e[e.Unknown=0]="Unknown",e[e.WebSearch=1]="WebSearch",e[e.GitHubApi=8]="GitHubApi",e[e.Linear=12]="Linear",e[e.Jira=13]="Jira",e[e.Confluence=14]="Confluence",e[e.Notion=15]="Notion",e[e.Supabase=16]="Supabase",e[e.Glean=17]="Glean",e))(th||{});re.object({"tool-name":re.string().describe("Tool name to apply permission rule to. Can be any ToolType value (RemoteToolId, LocalToolType, SidecarToolType, or MCPToolType string)"),permission:re.discriminatedUnion("type",[re.object({type:re.literal("allow")}),re.object({type:re.literal("deny")}),re.object({type:re.literal("webhook-policy"),"webhook-url":re.string().describe("URL to send the request to")}),re.object({type:re.literal("script-policy"),script:re.string().describe("Path to the script file to execute")})]),"event-type":re.enum(["tool-call","tool-response"]).default("tool-call").optional(),"shell-input-regex":re.string().optional()});var Cs={exports:{}},As={exports:{}},Te={},B={__esModule:!0};B.extend=Hi,B.indexOf=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},B.escapeExpression=function(e){if(typeof e!="string"){if(e&&e.toHTML)return e.toHTML();if(e==null)return"";if(!e)return e+"";e=""+e}return sh.test(e)?e.replace(rh,ah):e},B.isEmpty=function(e){return!e&&e!==0||!(!$l(e)||e.length!==0)},B.createFrame=function(e){var t=Hi({},e);return t._parent=e,t},B.blockParams=function(e,t){return e.path=t,e},B.appendContextPath=function(e,t){return(e?e+".":"")+t};var nh={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;","=":"&#x3D;"},rh=/[&<>"'`=]/g,sh=/[&<>"'`=]/;function ah(e){return nh[e]}function Hi(e){for(var t=1;t<arguments.length;t++)for(var n in arguments[t])Object.prototype.hasOwnProperty.call(arguments[t],n)&&(e[n]=arguments[t][n]);return e}var Na=Object.prototype.toString;B.toString=Na;var Jr=function(e){return typeof e=="function"};Jr(/x/)&&(B.isFunction=Jr=function(e){return typeof e=="function"&&Na.call(e)==="[object Function]"}),B.isFunction=Jr;var $l=Array.isArray||function(e){return!(!e||typeof e!="object")&&Na.call(e)==="[object Array]"};B.isArray=$l;var xs={exports:{}};(function(e,t){t.__esModule=!0;var n=["description","fileName","lineNumber","endLineNumber","message","name","number","stack"];function r(s,a){var i=a&&a.loc,o=void 0,u=void 0,l=void 0,d=void 0;i&&(o=i.start.line,u=i.end.line,l=i.start.column,d=i.end.column,s+=" - "+o+":"+l);for(var c=Error.prototype.constructor.call(this,s),p=0;p<n.length;p++)this[n[p]]=c[n[p]];Error.captureStackTrace&&Error.captureStackTrace(this,r);try{i&&(this.lineNumber=o,this.endLineNumber=u,Object.defineProperty?(Object.defineProperty(this,"column",{value:l,enumerable:!0}),Object.defineProperty(this,"endColumn",{value:d,enumerable:!0})):(this.column=l,this.endColumn=d))}catch{}}r.prototype=new Error,t.default=r,e.exports=t.default})(xs,xs.exports);var Re=xs.exports,an={},Rs={exports:{}};(function(e,t){t.__esModule=!0;var n=B;t.default=function(r){r.registerHelper("blockHelperMissing",function(s,a){var i=a.inverse,o=a.fn;if(s===!0)return o(this);if(s===!1||s==null)return i(this);if(n.isArray(s))return s.length>0?(a.ids&&(a.ids=[a.name]),r.helpers.each(s,a)):i(this);if(a.data&&a.ids){var u=n.createFrame(a.data);u.contextPath=n.appendContextPath(a.data.contextPath,a.name),a={data:u}}return o(s,a)})},e.exports=t.default})(Rs,Rs.exports);var ih=Rs.exports,Os={exports:{}};(function(e,t){t.__esModule=!0;var n=B,r=function(s){return s&&s.__esModule?s:{default:s}}(Re);t.default=function(s){s.registerHelper("each",function(a,i){if(!i)throw new r.default("Must pass iterator to #each");var o,u=i.fn,l=i.inverse,d=0,c="",p=void 0,h=void 0;function m(b,I,A){p&&(p.key=b,p.index=I,p.first=I===0,p.last=!!A,h&&(p.contextPath=h+b)),c+=u(a[b],{data:p,blockParams:n.blockParams([a[b],b],[h+b,null])})}if(i.data&&i.ids&&(h=n.appendContextPath(i.data.contextPath,i.ids[0])+"."),n.isFunction(a)&&(a=a.call(this)),i.data&&(p=n.createFrame(i.data)),a&&typeof a=="object")if(n.isArray(a))for(var y=a.length;d<y;d++)d in a&&m(d,d,d===a.length-1);else if(typeof Symbol=="function"&&a[Symbol.iterator]){for(var g=[],v=a[Symbol.iterator](),E=v.next();!E.done;E=v.next())g.push(E.value);for(y=(a=g).length;d<y;d++)m(d,d,d===a.length-1)}else o=void 0,Object.keys(a).forEach(function(b){o!==void 0&&m(o,d-1),o=b,d++}),o!==void 0&&m(o,d-1,!0);return d===0&&(c=l(this)),c})},e.exports=t.default})(Os,Os.exports);var oh=Os.exports,Ms={exports:{}};(function(e,t){t.__esModule=!0;var n=function(r){return r&&r.__esModule?r:{default:r}}(Re);t.default=function(r){r.registerHelper("helperMissing",function(){if(arguments.length!==1)throw new n.default('Missing helper: "'+arguments[arguments.length-1].name+'"')})},e.exports=t.default})(Ms,Ms.exports);var lh=Ms.exports,Ps={exports:{}};(function(e,t){t.__esModule=!0;var n=B,r=function(s){return s&&s.__esModule?s:{default:s}}(Re);t.default=function(s){s.registerHelper("if",function(a,i){if(arguments.length!=2)throw new r.default("#if requires exactly one argument");return n.isFunction(a)&&(a=a.call(this)),!i.hash.includeZero&&!a||n.isEmpty(a)?i.inverse(this):i.fn(this)}),s.registerHelper("unless",function(a,i){if(arguments.length!=2)throw new r.default("#unless requires exactly one argument");return s.helpers.if.call(this,a,{fn:i.inverse,inverse:i.fn,hash:i.hash})})},e.exports=t.default})(Ps,Ps.exports);var Wi,Qr,uh=Ps.exports,Ls={exports:{}};Wi=Ls,(Qr=Ls.exports).__esModule=!0,Qr.default=function(e){e.registerHelper("log",function(){for(var t=[void 0],n=arguments[arguments.length-1],r=0;r<arguments.length-1;r++)t.push(arguments[r]);var s=1;n.hash.level!=null?s=n.hash.level:n.data&&n.data.level!=null&&(s=n.data.level),t[0]=s,e.log.apply(e,t)})},Wi.exports=Qr.default;var ch=Ls.exports,Ds={exports:{}};(function(e,t){t.__esModule=!0,t.default=function(n){n.registerHelper("lookup",function(r,s,a){return r&&a.lookupProperty(r,s)})},e.exports=t.default})(Ds,Ds.exports);var dh=Ds.exports,Fs={exports:{}};(function(e,t){t.__esModule=!0;var n=B,r=function(s){return s&&s.__esModule?s:{default:s}}(Re);t.default=function(s){s.registerHelper("with",function(a,i){if(arguments.length!=2)throw new r.default("#with requires exactly one argument");n.isFunction(a)&&(a=a.call(this));var o=i.fn;if(n.isEmpty(a))return i.inverse(this);var u=i.data;return i.data&&i.ids&&((u=n.createFrame(i.data)).contextPath=n.appendContextPath(i.data.contextPath,i.ids[0])),o(a,{data:u,blockParams:n.blockParams([a],[u&&u.contextPath])})})},e.exports=t.default})(Fs,Fs.exports);var ph=Fs.exports;function wt(e){return e&&e.__esModule?e:{default:e}}an.__esModule=!0,an.registerDefaultHelpers=function(e){hh.default(e),mh.default(e),fh.default(e),gh.default(e),yh.default(e),_h.default(e),vh.default(e)},an.moveHelperToHooks=function(e,t,n){e.helpers[t]&&(e.hooks[t]=e.helpers[t],n||delete e.helpers[t])};var hh=wt(ih),mh=wt(oh),fh=wt(lh),gh=wt(uh),yh=wt(ch),_h=wt(dh),vh=wt(ph),$s={},Us={exports:{}};(function(e,t){t.__esModule=!0;var n=B;t.default=function(r){r.registerDecorator("inline",function(s,a,i,o){var u=s;return a.partials||(a.partials={},u=function(l,d){var c=i.partials;i.partials=n.extend({},c,a.partials);var p=s(l,d);return i.partials=c,p}),a.partials[o.args[0]]=o.fn,u})},e.exports=t.default})(Us,Us.exports);var bh=Us.exports;$s.__esModule=!0,$s.registerDefaultDecorators=function(e){Sh.default(e)};var Sh=function(e){return e&&e.__esModule?e:{default:e}}(bh),Bs={exports:{}};(function(e,t){t.__esModule=!0;var n=B,r={methodMap:["debug","info","warn","error"],level:"info",lookupLevel:function(s){if(typeof s=="string"){var a=n.indexOf(r.methodMap,s.toLowerCase());s=a>=0?a:parseInt(s,10)}return s},log:function(s){if(s=r.lookupLevel(s),typeof console<"u"&&r.lookupLevel(r.level)<=s){var a=r.methodMap[s];console[a]||(a="log");for(var i=arguments.length,o=Array(i>1?i-1:0),u=1;u<i;u++)o[u-1]=arguments[u];console[a].apply(console,o)}}};t.default=r,e.exports=t.default})(Bs,Bs.exports);var Ul=Bs.exports,Ot={},Eh={__esModule:!0,createNewLookupObject:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return Th.extend.apply(void 0,[Object.create(null)].concat(t))}},Th=B;Ot.__esModule=!0,Ot.createProtoAccessControl=function(e){var t=Object.create(null);t.constructor=!1,t.__defineGetter__=!1,t.__defineSetter__=!1,t.__lookupGetter__=!1;var n=Object.create(null);return n.__proto__=!1,{properties:{whitelist:zi.createNewLookupObject(n,e.allowedProtoProperties),defaultValue:e.allowProtoPropertiesByDefault},methods:{whitelist:zi.createNewLookupObject(t,e.allowedProtoMethods),defaultValue:e.allowProtoMethodsByDefault}}},Ot.resultIsAllowed=function(e,t,n){return kh(typeof e=="function"?t.methods:t.properties,n)},Ot.resetLoggedProperties=function(){Object.keys(Tr).forEach(function(e){delete Tr[e]})};var zi=Eh,wh=function(e){return e&&e.__esModule?e:{default:e}}(Ul),Tr=Object.create(null);function kh(e,t){return e.whitelist[t]!==void 0?e.whitelist[t]===!0:e.defaultValue!==void 0?e.defaultValue:(function(n){Tr[n]!==!0&&(Tr[n]=!0,wh.default.log("error",'Handlebars: Access has been denied to resolve the property "'+n+`" because it is not an "own property" of its parent.
You can add a runtime option to disable the check or this warning:
See https://handlebarsjs.com/api-reference/runtime-options.html#options-to-control-prototype-access for details`))}(t),!1)}function Bl(e){return e&&e.__esModule?e:{default:e}}Te.__esModule=!0,Te.HandlebarsEnvironment=js;var mt=B,es=Bl(Re),Nh=an,Ih=$s,wr=Bl(Ul),Ch=Ot;Te.VERSION="4.7.8";Te.COMPILER_REVISION=8;Te.LAST_COMPATIBLE_COMPILER_REVISION=7;Te.REVISION_CHANGES={1:"<= 1.0.rc.2",2:"== 1.0.0-rc.3",3:"== 1.0.0-rc.4",4:"== 1.x.x",5:"== 2.0.0-alpha.x",6:">= 2.0.0-beta.1",7:">= 4.0.0 <4.3.0",8:">= 4.3.0"};var ts="[object Object]";function js(e,t,n){this.helpers=e||{},this.partials=t||{},this.decorators=n||{},Nh.registerDefaultHelpers(this),Ih.registerDefaultDecorators(this)}js.prototype={constructor:js,logger:wr.default,log:wr.default.log,registerHelper:function(e,t){if(mt.toString.call(e)===ts){if(t)throw new es.default("Arg not supported with multiple helpers");mt.extend(this.helpers,e)}else this.helpers[e]=t},unregisterHelper:function(e){delete this.helpers[e]},registerPartial:function(e,t){if(mt.toString.call(e)===ts)mt.extend(this.partials,e);else{if(t===void 0)throw new es.default('Attempting to register a partial called "'+e+'" as undefined');this.partials[e]=t}},unregisterPartial:function(e){delete this.partials[e]},registerDecorator:function(e,t){if(mt.toString.call(e)===ts){if(t)throw new es.default("Arg not supported with multiple decorators");mt.extend(this.decorators,e)}else this.decorators[e]=t},unregisterDecorator:function(e){delete this.decorators[e]},resetLoggedPropertyAccesses:function(){Ch.resetLoggedProperties()}};var Ah=wr.default.log;Te.log=Ah,Te.createFrame=mt.createFrame,Te.logger=wr.default;var Vs={exports:{}};(function(e,t){function n(r){this.string=r}t.__esModule=!0,n.prototype.toString=n.prototype.toHTML=function(){return""+this.string},t.default=n,e.exports=t.default})(Vs,Vs.exports);var xh=Vs.exports,Qe={},Gs={};Gs.__esModule=!0,Gs.wrapHelper=function(e,t){return typeof e!="function"?e:function(){return arguments[arguments.length-1]=t(arguments[arguments.length-1]),e.apply(this,arguments)}},Qe.__esModule=!0,Qe.checkRevision=function(e){var t=e&&e[0]||1,n=qe.COMPILER_REVISION;if(!(t>=qe.LAST_COMPATIBLE_COMPILER_REVISION&&t<=qe.COMPILER_REVISION)){if(t<qe.LAST_COMPATIBLE_COMPILER_REVISION){var r=qe.REVISION_CHANGES[n],s=qe.REVISION_CHANGES[t];throw new Ge.default("Template was precompiled with an older version of Handlebars than the current runtime. Please update your precompiler to a newer version ("+r+") or downgrade your runtime to an older version ("+s+").")}throw new Ge.default("Template was precompiled with a newer version of Handlebars than the current runtime. Please update your runtime to a newer version ("+e[1]+").")}},Qe.template=function(e,t){if(!t)throw new Ge.default("No environment passed to template");if(!e||!e.main)throw new Ge.default("Unknown template object: "+typeof e);e.main.decorator=e.main_d,t.VM.checkRevision(e.compiler);var n=e.compiler&&e.compiler[0]===7,r={strict:function(a,i,o){if(!a||!(i in a))throw new Ge.default('"'+i+'" not defined in '+a,{loc:o});return r.lookupProperty(a,i)},lookupProperty:function(a,i){var o=a[i];return o==null||Object.prototype.hasOwnProperty.call(a,i)||Ji.resultIsAllowed(o,r.protoAccessControl,i)?o:void 0},lookup:function(a,i){for(var o=a.length,u=0;u<o;u++)if((a[u]&&r.lookupProperty(a[u],i))!=null)return a[u][i]},lambda:function(a,i){return typeof a=="function"?a.call(i):a},escapeExpression:Ke.escapeExpression,invokePartial:function(a,i,o){o.hash&&(i=Ke.extend({},i,o.hash),o.ids&&(o.ids[0]=!0)),a=t.VM.resolvePartial.call(this,a,i,o);var u=Ke.extend({},o,{hooks:this.hooks,protoAccessControl:this.protoAccessControl}),l=t.VM.invokePartial.call(this,a,i,u);if(l==null&&t.compile&&(o.partials[o.name]=t.compile(a,e.compilerOptions,t),l=o.partials[o.name](i,u)),l!=null){if(o.indent){for(var d=l.split(`
`),c=0,p=d.length;c<p&&(d[c]||c+1!==p);c++)d[c]=o.indent+d[c];l=d.join(`
`)}return l}throw new Ge.default("The partial "+o.name+" could not be compiled when running in runtime-only mode")},fn:function(a){var i=e[a];return i.decorator=e[a+"_d"],i},programs:[],program:function(a,i,o,u,l){var d=this.programs[a],c=this.fn(a);return i||l||u||o?d=$n(this,a,c,i,o,u,l):d||(d=this.programs[a]=$n(this,a,c)),d},data:function(a,i){for(;a&&i--;)a=a._parent;return a},mergeIfNeeded:function(a,i){var o=a||i;return a&&i&&a!==i&&(o=Ke.extend({},i,a)),o},nullContext:Object.seal({}),noop:t.VM.noop,compilerInfo:e.compiler};function s(a){var i=arguments.length<=1||arguments[1]===void 0?{}:arguments[1],o=i.data;s._setup(i),!i.partial&&e.useData&&(o=function(c,p){return p&&"root"in p||((p=p?qe.createFrame(p):{}).root=c),p}(a,o));var u=void 0,l=e.useBlockParams?[]:void 0;function d(c){return""+e.main(r,c,r.helpers,r.partials,o,l,u)}return e.useDepths&&(u=i.depths?a!=i.depths[0]?[a].concat(i.depths):i.depths:[a]),(d=jl(e.main,d,r,i.depths||[],o,l))(a,i)}return s.isTop=!0,s._setup=function(a){if(a.partial)r.protoAccessControl=a.protoAccessControl,r.helpers=a.helpers,r.partials=a.partials,r.decorators=a.decorators,r.hooks=a.hooks;else{var i=Ke.extend({},t.helpers,a.helpers);(function(u,l){Object.keys(u).forEach(function(d){var c=u[d];u[d]=function(p,h){var m=h.lookupProperty;return Rh.wrapHelper(p,function(y){return Ke.extend({lookupProperty:m},y)})}(c,l)})})(i,r),r.helpers=i,e.usePartial&&(r.partials=r.mergeIfNeeded(a.partials,t.partials)),(e.usePartial||e.useDecorators)&&(r.decorators=Ke.extend({},t.decorators,a.decorators)),r.hooks={},r.protoAccessControl=Ji.createProtoAccessControl(a);var o=a.allowCallsToHelperMissing||n;Xi.moveHelperToHooks(r,"helperMissing",o),Xi.moveHelperToHooks(r,"blockHelperMissing",o)}},s._child=function(a,i,o,u){if(e.useBlockParams&&!o)throw new Ge.default("must pass block params");if(e.useDepths&&!u)throw new Ge.default("must pass parent depths");return $n(r,a,e[a],i,0,o,u)},s},Qe.wrapProgram=$n,Qe.resolvePartial=function(e,t,n){return e?e.call||n.name||(n.name=e,e=n.partials[e]):e=n.name==="@partial-block"?n.data["partial-block"]:n.partials[n.name],e},Qe.invokePartial=function(e,t,n){var r=n.data&&n.data["partial-block"];n.partial=!0,n.ids&&(n.data.contextPath=n.ids[0]||n.data.contextPath);var s=void 0;if(n.fn&&n.fn!==Qi&&function(){n.data=qe.createFrame(n.data);var a=n.fn;s=n.data["partial-block"]=function(i){var o=arguments.length<=1||arguments[1]===void 0?{}:arguments[1];return o.data=qe.createFrame(o.data),o.data["partial-block"]=r,a(i,o)},a.partials&&(n.partials=Ke.extend({},n.partials,a.partials))}(),e===void 0&&s&&(e=s),e===void 0)throw new Ge.default("The partial "+n.name+" could not be found");if(e instanceof Function)return e(t,n)},Qe.noop=Qi;var Ke=function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(B),Ge=function(e){return e&&e.__esModule?e:{default:e}}(Re),qe=Te,Xi=an,Rh=Gs,Ji=Ot;function $n(e,t,n,r,s,a,i){function o(u){var l=arguments.length<=1||arguments[1]===void 0?{}:arguments[1],d=i;return!i||u==i[0]||u===e.nullContext&&i[0]===null||(d=[u].concat(i)),n(e,u,e.helpers,e.partials,l.data||r,a&&[l.blockParams].concat(a),d)}return(o=jl(n,o,e,i,r,a)).program=t,o.depth=i?i.length:0,o.blockParams=s||0,o}function Qi(){return""}function jl(e,t,n,r,s,a){if(e.decorator){var i={};t=e.decorator(t,i,n,r&&r[0],s,a,r),Ke.extend(t,i)}return t}var qs={exports:{}};(function(e,t){t.__esModule=!0,t.default=function(n){typeof globalThis!="object"&&(Object.prototype.__defineGetter__("__magic__",function(){return this}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__);var r=globalThis.Handlebars;n.noConflict=function(){return globalThis.Handlebars===n&&(globalThis.Handlebars=r),n}},e.exports=t.default})(qs,qs.exports);var Vl=qs.exports;(function(e,t){function n(p){return p&&p.__esModule?p:{default:p}}function r(p){if(p&&p.__esModule)return p;var h={};if(p!=null)for(var m in p)Object.prototype.hasOwnProperty.call(p,m)&&(h[m]=p[m]);return h.default=p,h}t.__esModule=!0;var s=r(Te),a=n(xh),i=n(Re),o=r(B),u=r(Qe),l=n(Vl);function d(){var p=new s.HandlebarsEnvironment;return o.extend(p,s),p.SafeString=a.default,p.Exception=i.default,p.Utils=o,p.escapeExpression=o.escapeExpression,p.VM=u,p.template=function(h){return u.template(h,p)},p}var c=d();c.create=d,l.default(c),c.default=c,t.default=c,e.exports=t.default})(As,As.exports);var Oh=As.exports,Zs={exports:{}};(function(e,t){t.__esModule=!0;var n={helpers:{helperExpression:function(r){return r.type==="SubExpression"||(r.type==="MustacheStatement"||r.type==="BlockStatement")&&!!(r.params&&r.params.length||r.hash)},scopedId:function(r){return/^\.|this\b/.test(r.original)},simpleId:function(r){return r.parts.length===1&&!n.helpers.scopedId(r)&&!r.depth}}};t.default=n,e.exports=t.default})(Zs,Zs.exports);var Gl=Zs.exports,on={},Ks={exports:{}};(function(e,t){t.__esModule=!0;var n=function(){var r={trace:function(){},yy:{},symbols_:{error:2,root:3,program:4,EOF:5,program_repetition0:6,statement:7,mustache:8,block:9,rawBlock:10,partial:11,partialBlock:12,content:13,COMMENT:14,CONTENT:15,openRawBlock:16,rawBlock_repetition0:17,END_RAW_BLOCK:18,OPEN_RAW_BLOCK:19,helperName:20,openRawBlock_repetition0:21,openRawBlock_option0:22,CLOSE_RAW_BLOCK:23,openBlock:24,block_option0:25,closeBlock:26,openInverse:27,block_option1:28,OPEN_BLOCK:29,openBlock_repetition0:30,openBlock_option0:31,openBlock_option1:32,CLOSE:33,OPEN_INVERSE:34,openInverse_repetition0:35,openInverse_option0:36,openInverse_option1:37,openInverseChain:38,OPEN_INVERSE_CHAIN:39,openInverseChain_repetition0:40,openInverseChain_option0:41,openInverseChain_option1:42,inverseAndProgram:43,INVERSE:44,inverseChain:45,inverseChain_option0:46,OPEN_ENDBLOCK:47,OPEN:48,mustache_repetition0:49,mustache_option0:50,OPEN_UNESCAPED:51,mustache_repetition1:52,mustache_option1:53,CLOSE_UNESCAPED:54,OPEN_PARTIAL:55,partialName:56,partial_repetition0:57,partial_option0:58,openPartialBlock:59,OPEN_PARTIAL_BLOCK:60,openPartialBlock_repetition0:61,openPartialBlock_option0:62,param:63,sexpr:64,OPEN_SEXPR:65,sexpr_repetition0:66,sexpr_option0:67,CLOSE_SEXPR:68,hash:69,hash_repetition_plus0:70,hashSegment:71,ID:72,EQUALS:73,blockParams:74,OPEN_BLOCK_PARAMS:75,blockParams_repetition_plus0:76,CLOSE_BLOCK_PARAMS:77,path:78,dataName:79,STRING:80,NUMBER:81,BOOLEAN:82,UNDEFINED:83,NULL:84,DATA:85,pathSegments:86,SEP:87,$accept:0,$end:1},terminals_:{2:"error",5:"EOF",14:"COMMENT",15:"CONTENT",18:"END_RAW_BLOCK",19:"OPEN_RAW_BLOCK",23:"CLOSE_RAW_BLOCK",29:"OPEN_BLOCK",33:"CLOSE",34:"OPEN_INVERSE",39:"OPEN_INVERSE_CHAIN",44:"INVERSE",47:"OPEN_ENDBLOCK",48:"OPEN",51:"OPEN_UNESCAPED",54:"CLOSE_UNESCAPED",55:"OPEN_PARTIAL",60:"OPEN_PARTIAL_BLOCK",65:"OPEN_SEXPR",68:"CLOSE_SEXPR",72:"ID",73:"EQUALS",75:"OPEN_BLOCK_PARAMS",77:"CLOSE_BLOCK_PARAMS",80:"STRING",81:"NUMBER",82:"BOOLEAN",83:"UNDEFINED",84:"NULL",85:"DATA",87:"SEP"},productions_:[0,[3,2],[4,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[13,1],[10,3],[16,5],[9,4],[9,4],[24,6],[27,6],[38,6],[43,2],[45,3],[45,1],[26,3],[8,5],[8,5],[11,5],[12,3],[59,5],[63,1],[63,1],[64,5],[69,1],[71,3],[74,3],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[56,1],[56,1],[79,2],[78,1],[86,3],[86,1],[6,0],[6,2],[17,0],[17,2],[21,0],[21,2],[22,0],[22,1],[25,0],[25,1],[28,0],[28,1],[30,0],[30,2],[31,0],[31,1],[32,0],[32,1],[35,0],[35,2],[36,0],[36,1],[37,0],[37,1],[40,0],[40,2],[41,0],[41,1],[42,0],[42,1],[46,0],[46,1],[49,0],[49,2],[50,0],[50,1],[52,0],[52,2],[53,0],[53,1],[57,0],[57,2],[58,0],[58,1],[61,0],[61,2],[62,0],[62,1],[66,0],[66,2],[67,0],[67,1],[70,1],[70,2],[76,1],[76,2]],performAction:function(i,o,u,l,d,c,p){var h=c.length-1;switch(d){case 1:return c[h-1];case 2:this.$=l.prepareProgram(c[h]);break;case 3:case 4:case 5:case 6:case 7:case 8:case 20:case 27:case 28:case 33:case 34:case 40:case 41:this.$=c[h];break;case 9:this.$={type:"CommentStatement",value:l.stripComment(c[h]),strip:l.stripFlags(c[h],c[h]),loc:l.locInfo(this._$)};break;case 10:this.$={type:"ContentStatement",original:c[h],value:c[h],loc:l.locInfo(this._$)};break;case 11:this.$=l.prepareRawBlock(c[h-2],c[h-1],c[h],this._$);break;case 12:this.$={path:c[h-3],params:c[h-2],hash:c[h-1]};break;case 13:this.$=l.prepareBlock(c[h-3],c[h-2],c[h-1],c[h],!1,this._$);break;case 14:this.$=l.prepareBlock(c[h-3],c[h-2],c[h-1],c[h],!0,this._$);break;case 15:this.$={open:c[h-5],path:c[h-4],params:c[h-3],hash:c[h-2],blockParams:c[h-1],strip:l.stripFlags(c[h-5],c[h])};break;case 16:case 17:this.$={path:c[h-4],params:c[h-3],hash:c[h-2],blockParams:c[h-1],strip:l.stripFlags(c[h-5],c[h])};break;case 18:this.$={strip:l.stripFlags(c[h-1],c[h-1]),program:c[h]};break;case 19:var m=l.prepareBlock(c[h-2],c[h-1],c[h],c[h],!1,this._$),y=l.prepareProgram([m],c[h-1].loc);y.chained=!0,this.$={strip:c[h-2].strip,program:y,chain:!0};break;case 21:this.$={path:c[h-1],strip:l.stripFlags(c[h-2],c[h])};break;case 22:case 23:this.$=l.prepareMustache(c[h-3],c[h-2],c[h-1],c[h-4],l.stripFlags(c[h-4],c[h]),this._$);break;case 24:this.$={type:"PartialStatement",name:c[h-3],params:c[h-2],hash:c[h-1],indent:"",strip:l.stripFlags(c[h-4],c[h]),loc:l.locInfo(this._$)};break;case 25:this.$=l.preparePartialBlock(c[h-2],c[h-1],c[h],this._$);break;case 26:this.$={path:c[h-3],params:c[h-2],hash:c[h-1],strip:l.stripFlags(c[h-4],c[h])};break;case 29:this.$={type:"SubExpression",path:c[h-3],params:c[h-2],hash:c[h-1],loc:l.locInfo(this._$)};break;case 30:this.$={type:"Hash",pairs:c[h],loc:l.locInfo(this._$)};break;case 31:this.$={type:"HashPair",key:l.id(c[h-2]),value:c[h],loc:l.locInfo(this._$)};break;case 32:this.$=l.id(c[h-1]);break;case 35:this.$={type:"StringLiteral",value:c[h],original:c[h],loc:l.locInfo(this._$)};break;case 36:this.$={type:"NumberLiteral",value:Number(c[h]),original:Number(c[h]),loc:l.locInfo(this._$)};break;case 37:this.$={type:"BooleanLiteral",value:c[h]==="true",original:c[h]==="true",loc:l.locInfo(this._$)};break;case 38:this.$={type:"UndefinedLiteral",original:void 0,value:void 0,loc:l.locInfo(this._$)};break;case 39:this.$={type:"NullLiteral",original:null,value:null,loc:l.locInfo(this._$)};break;case 42:this.$=l.preparePath(!0,c[h],this._$);break;case 43:this.$=l.preparePath(!1,c[h],this._$);break;case 44:c[h-2].push({part:l.id(c[h]),original:c[h],separator:c[h-1]}),this.$=c[h-2];break;case 45:this.$=[{part:l.id(c[h]),original:c[h]}];break;case 46:case 48:case 50:case 58:case 64:case 70:case 78:case 82:case 86:case 90:case 94:this.$=[];break;case 47:case 49:case 51:case 59:case 65:case 71:case 79:case 83:case 87:case 91:case 95:case 99:case 101:c[h-1].push(c[h]);break;case 98:case 100:this.$=[c[h]]}},table:[{3:1,4:2,5:[2,46],6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{1:[3]},{5:[1,4]},{5:[2,2],7:5,8:6,9:7,10:8,11:9,12:10,13:11,14:[1,12],15:[1,20],16:17,19:[1,23],24:15,27:16,29:[1,21],34:[1,22],39:[2,2],44:[2,2],47:[2,2],48:[1,13],51:[1,14],55:[1,18],59:19,60:[1,24]},{1:[2,1]},{5:[2,47],14:[2,47],15:[2,47],19:[2,47],29:[2,47],34:[2,47],39:[2,47],44:[2,47],47:[2,47],48:[2,47],51:[2,47],55:[2,47],60:[2,47]},{5:[2,3],14:[2,3],15:[2,3],19:[2,3],29:[2,3],34:[2,3],39:[2,3],44:[2,3],47:[2,3],48:[2,3],51:[2,3],55:[2,3],60:[2,3]},{5:[2,4],14:[2,4],15:[2,4],19:[2,4],29:[2,4],34:[2,4],39:[2,4],44:[2,4],47:[2,4],48:[2,4],51:[2,4],55:[2,4],60:[2,4]},{5:[2,5],14:[2,5],15:[2,5],19:[2,5],29:[2,5],34:[2,5],39:[2,5],44:[2,5],47:[2,5],48:[2,5],51:[2,5],55:[2,5],60:[2,5]},{5:[2,6],14:[2,6],15:[2,6],19:[2,6],29:[2,6],34:[2,6],39:[2,6],44:[2,6],47:[2,6],48:[2,6],51:[2,6],55:[2,6],60:[2,6]},{5:[2,7],14:[2,7],15:[2,7],19:[2,7],29:[2,7],34:[2,7],39:[2,7],44:[2,7],47:[2,7],48:[2,7],51:[2,7],55:[2,7],60:[2,7]},{5:[2,8],14:[2,8],15:[2,8],19:[2,8],29:[2,8],34:[2,8],39:[2,8],44:[2,8],47:[2,8],48:[2,8],51:[2,8],55:[2,8],60:[2,8]},{5:[2,9],14:[2,9],15:[2,9],19:[2,9],29:[2,9],34:[2,9],39:[2,9],44:[2,9],47:[2,9],48:[2,9],51:[2,9],55:[2,9],60:[2,9]},{20:25,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:36,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:37,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],39:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{4:38,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{15:[2,48],17:39,18:[2,48]},{20:41,56:40,64:42,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:44,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{5:[2,10],14:[2,10],15:[2,10],18:[2,10],19:[2,10],29:[2,10],34:[2,10],39:[2,10],44:[2,10],47:[2,10],48:[2,10],51:[2,10],55:[2,10],60:[2,10]},{20:45,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:46,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:47,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:41,56:48,64:42,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[2,78],49:49,65:[2,78],72:[2,78],80:[2,78],81:[2,78],82:[2,78],83:[2,78],84:[2,78],85:[2,78]},{23:[2,33],33:[2,33],54:[2,33],65:[2,33],68:[2,33],72:[2,33],75:[2,33],80:[2,33],81:[2,33],82:[2,33],83:[2,33],84:[2,33],85:[2,33]},{23:[2,34],33:[2,34],54:[2,34],65:[2,34],68:[2,34],72:[2,34],75:[2,34],80:[2,34],81:[2,34],82:[2,34],83:[2,34],84:[2,34],85:[2,34]},{23:[2,35],33:[2,35],54:[2,35],65:[2,35],68:[2,35],72:[2,35],75:[2,35],80:[2,35],81:[2,35],82:[2,35],83:[2,35],84:[2,35],85:[2,35]},{23:[2,36],33:[2,36],54:[2,36],65:[2,36],68:[2,36],72:[2,36],75:[2,36],80:[2,36],81:[2,36],82:[2,36],83:[2,36],84:[2,36],85:[2,36]},{23:[2,37],33:[2,37],54:[2,37],65:[2,37],68:[2,37],72:[2,37],75:[2,37],80:[2,37],81:[2,37],82:[2,37],83:[2,37],84:[2,37],85:[2,37]},{23:[2,38],33:[2,38],54:[2,38],65:[2,38],68:[2,38],72:[2,38],75:[2,38],80:[2,38],81:[2,38],82:[2,38],83:[2,38],84:[2,38],85:[2,38]},{23:[2,39],33:[2,39],54:[2,39],65:[2,39],68:[2,39],72:[2,39],75:[2,39],80:[2,39],81:[2,39],82:[2,39],83:[2,39],84:[2,39],85:[2,39]},{23:[2,43],33:[2,43],54:[2,43],65:[2,43],68:[2,43],72:[2,43],75:[2,43],80:[2,43],81:[2,43],82:[2,43],83:[2,43],84:[2,43],85:[2,43],87:[1,50]},{72:[1,35],86:51},{23:[2,45],33:[2,45],54:[2,45],65:[2,45],68:[2,45],72:[2,45],75:[2,45],80:[2,45],81:[2,45],82:[2,45],83:[2,45],84:[2,45],85:[2,45],87:[2,45]},{52:52,54:[2,82],65:[2,82],72:[2,82],80:[2,82],81:[2,82],82:[2,82],83:[2,82],84:[2,82],85:[2,82]},{25:53,38:55,39:[1,57],43:56,44:[1,58],45:54,47:[2,54]},{28:59,43:60,44:[1,58],47:[2,56]},{13:62,15:[1,20],18:[1,61]},{33:[2,86],57:63,65:[2,86],72:[2,86],80:[2,86],81:[2,86],82:[2,86],83:[2,86],84:[2,86],85:[2,86]},{33:[2,40],65:[2,40],72:[2,40],80:[2,40],81:[2,40],82:[2,40],83:[2,40],84:[2,40],85:[2,40]},{33:[2,41],65:[2,41],72:[2,41],80:[2,41],81:[2,41],82:[2,41],83:[2,41],84:[2,41],85:[2,41]},{20:64,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{26:65,47:[1,66]},{30:67,33:[2,58],65:[2,58],72:[2,58],75:[2,58],80:[2,58],81:[2,58],82:[2,58],83:[2,58],84:[2,58],85:[2,58]},{33:[2,64],35:68,65:[2,64],72:[2,64],75:[2,64],80:[2,64],81:[2,64],82:[2,64],83:[2,64],84:[2,64],85:[2,64]},{21:69,23:[2,50],65:[2,50],72:[2,50],80:[2,50],81:[2,50],82:[2,50],83:[2,50],84:[2,50],85:[2,50]},{33:[2,90],61:70,65:[2,90],72:[2,90],80:[2,90],81:[2,90],82:[2,90],83:[2,90],84:[2,90],85:[2,90]},{20:74,33:[2,80],50:71,63:72,64:75,65:[1,43],69:73,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{72:[1,79]},{23:[2,42],33:[2,42],54:[2,42],65:[2,42],68:[2,42],72:[2,42],75:[2,42],80:[2,42],81:[2,42],82:[2,42],83:[2,42],84:[2,42],85:[2,42],87:[1,50]},{20:74,53:80,54:[2,84],63:81,64:75,65:[1,43],69:82,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{26:83,47:[1,66]},{47:[2,55]},{4:84,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],39:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{47:[2,20]},{20:85,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:86,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{26:87,47:[1,66]},{47:[2,57]},{5:[2,11],14:[2,11],15:[2,11],19:[2,11],29:[2,11],34:[2,11],39:[2,11],44:[2,11],47:[2,11],48:[2,11],51:[2,11],55:[2,11],60:[2,11]},{15:[2,49],18:[2,49]},{20:74,33:[2,88],58:88,63:89,64:75,65:[1,43],69:90,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{65:[2,94],66:91,68:[2,94],72:[2,94],80:[2,94],81:[2,94],82:[2,94],83:[2,94],84:[2,94],85:[2,94]},{5:[2,25],14:[2,25],15:[2,25],19:[2,25],29:[2,25],34:[2,25],39:[2,25],44:[2,25],47:[2,25],48:[2,25],51:[2,25],55:[2,25],60:[2,25]},{20:92,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,31:93,33:[2,60],63:94,64:75,65:[1,43],69:95,70:76,71:77,72:[1,78],75:[2,60],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,33:[2,66],36:96,63:97,64:75,65:[1,43],69:98,70:76,71:77,72:[1,78],75:[2,66],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,22:99,23:[2,52],63:100,64:75,65:[1,43],69:101,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,33:[2,92],62:102,63:103,64:75,65:[1,43],69:104,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[1,105]},{33:[2,79],65:[2,79],72:[2,79],80:[2,79],81:[2,79],82:[2,79],83:[2,79],84:[2,79],85:[2,79]},{33:[2,81]},{23:[2,27],33:[2,27],54:[2,27],65:[2,27],68:[2,27],72:[2,27],75:[2,27],80:[2,27],81:[2,27],82:[2,27],83:[2,27],84:[2,27],85:[2,27]},{23:[2,28],33:[2,28],54:[2,28],65:[2,28],68:[2,28],72:[2,28],75:[2,28],80:[2,28],81:[2,28],82:[2,28],83:[2,28],84:[2,28],85:[2,28]},{23:[2,30],33:[2,30],54:[2,30],68:[2,30],71:106,72:[1,107],75:[2,30]},{23:[2,98],33:[2,98],54:[2,98],68:[2,98],72:[2,98],75:[2,98]},{23:[2,45],33:[2,45],54:[2,45],65:[2,45],68:[2,45],72:[2,45],73:[1,108],75:[2,45],80:[2,45],81:[2,45],82:[2,45],83:[2,45],84:[2,45],85:[2,45],87:[2,45]},{23:[2,44],33:[2,44],54:[2,44],65:[2,44],68:[2,44],72:[2,44],75:[2,44],80:[2,44],81:[2,44],82:[2,44],83:[2,44],84:[2,44],85:[2,44],87:[2,44]},{54:[1,109]},{54:[2,83],65:[2,83],72:[2,83],80:[2,83],81:[2,83],82:[2,83],83:[2,83],84:[2,83],85:[2,83]},{54:[2,85]},{5:[2,13],14:[2,13],15:[2,13],19:[2,13],29:[2,13],34:[2,13],39:[2,13],44:[2,13],47:[2,13],48:[2,13],51:[2,13],55:[2,13],60:[2,13]},{38:55,39:[1,57],43:56,44:[1,58],45:111,46:110,47:[2,76]},{33:[2,70],40:112,65:[2,70],72:[2,70],75:[2,70],80:[2,70],81:[2,70],82:[2,70],83:[2,70],84:[2,70],85:[2,70]},{47:[2,18]},{5:[2,14],14:[2,14],15:[2,14],19:[2,14],29:[2,14],34:[2,14],39:[2,14],44:[2,14],47:[2,14],48:[2,14],51:[2,14],55:[2,14],60:[2,14]},{33:[1,113]},{33:[2,87],65:[2,87],72:[2,87],80:[2,87],81:[2,87],82:[2,87],83:[2,87],84:[2,87],85:[2,87]},{33:[2,89]},{20:74,63:115,64:75,65:[1,43],67:114,68:[2,96],69:116,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[1,117]},{32:118,33:[2,62],74:119,75:[1,120]},{33:[2,59],65:[2,59],72:[2,59],75:[2,59],80:[2,59],81:[2,59],82:[2,59],83:[2,59],84:[2,59],85:[2,59]},{33:[2,61],75:[2,61]},{33:[2,68],37:121,74:122,75:[1,120]},{33:[2,65],65:[2,65],72:[2,65],75:[2,65],80:[2,65],81:[2,65],82:[2,65],83:[2,65],84:[2,65],85:[2,65]},{33:[2,67],75:[2,67]},{23:[1,123]},{23:[2,51],65:[2,51],72:[2,51],80:[2,51],81:[2,51],82:[2,51],83:[2,51],84:[2,51],85:[2,51]},{23:[2,53]},{33:[1,124]},{33:[2,91],65:[2,91],72:[2,91],80:[2,91],81:[2,91],82:[2,91],83:[2,91],84:[2,91],85:[2,91]},{33:[2,93]},{5:[2,22],14:[2,22],15:[2,22],19:[2,22],29:[2,22],34:[2,22],39:[2,22],44:[2,22],47:[2,22],48:[2,22],51:[2,22],55:[2,22],60:[2,22]},{23:[2,99],33:[2,99],54:[2,99],68:[2,99],72:[2,99],75:[2,99]},{73:[1,108]},{20:74,63:125,64:75,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{5:[2,23],14:[2,23],15:[2,23],19:[2,23],29:[2,23],34:[2,23],39:[2,23],44:[2,23],47:[2,23],48:[2,23],51:[2,23],55:[2,23],60:[2,23]},{47:[2,19]},{47:[2,77]},{20:74,33:[2,72],41:126,63:127,64:75,65:[1,43],69:128,70:76,71:77,72:[1,78],75:[2,72],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{5:[2,24],14:[2,24],15:[2,24],19:[2,24],29:[2,24],34:[2,24],39:[2,24],44:[2,24],47:[2,24],48:[2,24],51:[2,24],55:[2,24],60:[2,24]},{68:[1,129]},{65:[2,95],68:[2,95],72:[2,95],80:[2,95],81:[2,95],82:[2,95],83:[2,95],84:[2,95],85:[2,95]},{68:[2,97]},{5:[2,21],14:[2,21],15:[2,21],19:[2,21],29:[2,21],34:[2,21],39:[2,21],44:[2,21],47:[2,21],48:[2,21],51:[2,21],55:[2,21],60:[2,21]},{33:[1,130]},{33:[2,63]},{72:[1,132],76:131},{33:[1,133]},{33:[2,69]},{15:[2,12],18:[2,12]},{14:[2,26],15:[2,26],19:[2,26],29:[2,26],34:[2,26],47:[2,26],48:[2,26],51:[2,26],55:[2,26],60:[2,26]},{23:[2,31],33:[2,31],54:[2,31],68:[2,31],72:[2,31],75:[2,31]},{33:[2,74],42:134,74:135,75:[1,120]},{33:[2,71],65:[2,71],72:[2,71],75:[2,71],80:[2,71],81:[2,71],82:[2,71],83:[2,71],84:[2,71],85:[2,71]},{33:[2,73],75:[2,73]},{23:[2,29],33:[2,29],54:[2,29],65:[2,29],68:[2,29],72:[2,29],75:[2,29],80:[2,29],81:[2,29],82:[2,29],83:[2,29],84:[2,29],85:[2,29]},{14:[2,15],15:[2,15],19:[2,15],29:[2,15],34:[2,15],39:[2,15],44:[2,15],47:[2,15],48:[2,15],51:[2,15],55:[2,15],60:[2,15]},{72:[1,137],77:[1,136]},{72:[2,100],77:[2,100]},{14:[2,16],15:[2,16],19:[2,16],29:[2,16],34:[2,16],44:[2,16],47:[2,16],48:[2,16],51:[2,16],55:[2,16],60:[2,16]},{33:[1,138]},{33:[2,75]},{33:[2,32]},{72:[2,101],77:[2,101]},{14:[2,17],15:[2,17],19:[2,17],29:[2,17],34:[2,17],39:[2,17],44:[2,17],47:[2,17],48:[2,17],51:[2,17],55:[2,17],60:[2,17]}],defaultActions:{4:[2,1],54:[2,55],56:[2,20],60:[2,57],73:[2,81],82:[2,85],86:[2,18],90:[2,89],101:[2,53],104:[2,93],110:[2,19],111:[2,77],116:[2,97],119:[2,63],122:[2,69],135:[2,75],136:[2,32]},parseError:function(i,o){throw new Error(i)},parse:function(i){var o=this,u=[0],l=[null],d=[],c=this.table,p="",h=0,m=0;this.lexer.setInput(i),this.lexer.yy=this.yy,this.yy.lexer=this.lexer,this.yy.parser=this,this.lexer.yylloc===void 0&&(this.lexer.yylloc={});var y=this.lexer.yylloc;d.push(y);var g=this.lexer.options&&this.lexer.options.ranges;typeof this.yy.parseError=="function"&&(this.parseError=this.yy.parseError);for(var v,E,b,I,A,P,M,H,j,D={};;){if(E=u[u.length-1],this.defaultActions[E]?b=this.defaultActions[E]:(v==null&&(j=void 0,typeof(j=o.lexer.lex()||1)!="number"&&(j=o.symbols_[j]||j),v=j),b=c[E]&&c[E][v]),b===void 0||!b.length||!b[0]){var ye="";for(A in H=[],c[E])this.terminals_[A]&&A>2&&H.push("'"+this.terminals_[A]+"'");ye=this.lexer.showPosition?"Parse error on line "+(h+1)+`:
`+this.lexer.showPosition()+`
Expecting `+H.join(", ")+", got '"+(this.terminals_[v]||v)+"'":"Parse error on line "+(h+1)+": Unexpected "+(v==1?"end of input":"'"+(this.terminals_[v]||v)+"'"),this.parseError(ye,{text:this.lexer.match,token:this.terminals_[v]||v,line:this.lexer.yylineno,loc:y,expected:H})}if(b[0]instanceof Array&&b.length>1)throw new Error("Parse Error: multiple actions possible at state: "+E+", token: "+v);switch(b[0]){case 1:u.push(v),l.push(this.lexer.yytext),d.push(this.lexer.yylloc),u.push(b[1]),v=null,m=this.lexer.yyleng,p=this.lexer.yytext,h=this.lexer.yylineno,y=this.lexer.yylloc;break;case 2:if(P=this.productions_[b[1]][1],D.$=l[l.length-P],D._$={first_line:d[d.length-(P||1)].first_line,last_line:d[d.length-1].last_line,first_column:d[d.length-(P||1)].first_column,last_column:d[d.length-1].last_column},g&&(D._$.range=[d[d.length-(P||1)].range[0],d[d.length-1].range[1]]),(I=this.performAction.call(D,p,m,h,this.yy,b[1],l,d))!==void 0)return I;P&&(u=u.slice(0,-1*P*2),l=l.slice(0,-1*P),d=d.slice(0,-1*P)),u.push(this.productions_[b[1]][0]),l.push(D.$),d.push(D._$),M=c[u[u.length-2]][u[u.length-1]],u.push(M);break;case 3:return!0}}return!0}},s=function(){var i={EOF:1,parseError:function(o,u){if(!this.yy.parser)throw new Error(o);this.yy.parser.parseError(o,u)},setInput:function(o){return this._input=o,this._more=this._less=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var o=this._input[0];return this.yytext+=o,this.yyleng++,this.offset++,this.match+=o,this.matched+=o,o.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),o},unput:function(o){var u=o.length,l=o.split(/(?:\r\n?|\n)/g);this._input=o+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-u-1),this.offset-=u;var d=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),l.length-1&&(this.yylineno-=l.length-1);var c=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:l?(l.length===d.length?this.yylloc.first_column:0)+d[d.length-l.length].length-l[0].length:this.yylloc.first_column-u},this.options.ranges&&(this.yylloc.range=[c[0],c[0]+this.yyleng-u]),this},more:function(){return this._more=!0,this},less:function(o){this.unput(this.match.slice(o))},pastInput:function(){var o=this.matched.substr(0,this.matched.length-this.match.length);return(o.length>20?"...":"")+o.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var o=this.match;return o.length<20&&(o+=this._input.substr(0,20-o.length)),(o.substr(0,20)+(o.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var o=this.pastInput(),u=new Array(o.length+1).join("-");return o+this.upcomingInput()+`
`+u+"^"},next:function(){if(this.done)return this.EOF;var o,u,l,d,c;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var p=this._currentRules(),h=0;h<p.length&&(!(l=this._input.match(this.rules[p[h]]))||u&&!(l[0].length>u[0].length)||(u=l,d=h,this.options.flex));h++);return u?((c=u[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=c.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:c?c[c.length-1].length-c[c.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+u[0].length},this.yytext+=u[0],this.match+=u[0],this.matches=u,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._input=this._input.slice(u[0].length),this.matched+=u[0],o=this.performAction.call(this,this.yy,this,p[d],this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),o||void 0):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var o=this.next();return o!==void 0?o:this.lex()},begin:function(o){this.conditionStack.push(o)},popState:function(){return this.conditionStack.pop()},_currentRules:function(){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules},topState:function(){return this.conditionStack[this.conditionStack.length-2]},pushState:function(o){this.begin(o)},options:{},performAction:function(o,u,l,d){function c(p,h){return u.yytext=u.yytext.substring(p,u.yyleng-h+p)}switch(l){case 0:if(u.yytext.slice(-2)==="\\\\"?(c(0,1),this.begin("mu")):u.yytext.slice(-1)==="\\"?(c(0,1),this.begin("emu")):this.begin("mu"),u.yytext)return 15;break;case 1:case 5:return 15;case 2:return this.popState(),15;case 3:return this.begin("raw"),15;case 4:return this.popState(),this.conditionStack[this.conditionStack.length-1]==="raw"?15:(c(5,9),"END_RAW_BLOCK");case 6:case 22:return this.popState(),14;case 7:return 65;case 8:return 68;case 9:return 19;case 10:return this.popState(),this.begin("raw"),23;case 11:return 55;case 12:return 60;case 13:return 29;case 14:return 47;case 15:case 16:return this.popState(),44;case 17:return 34;case 18:return 39;case 19:return 51;case 20:case 23:return 48;case 21:this.unput(u.yytext),this.popState(),this.begin("com");break;case 24:return 73;case 25:case 26:case 41:return 72;case 27:return 87;case 28:break;case 29:return this.popState(),54;case 30:return this.popState(),33;case 31:return u.yytext=c(1,2).replace(/\\"/g,'"'),80;case 32:return u.yytext=c(1,2).replace(/\\'/g,"'"),80;case 33:return 85;case 34:case 35:return 82;case 36:return 83;case 37:return 84;case 38:return 81;case 39:return 75;case 40:return 77;case 42:return u.yytext=u.yytext.replace(/\\([\\\]])/g,"$1"),72;case 43:return"INVALID";case 44:return 5}},rules:[/^(?:[^\x00]*?(?=(\{\{)))/,/^(?:[^\x00]+)/,/^(?:[^\x00]{2,}?(?=(\{\{|\\\{\{|\\\\\{\{|$)))/,/^(?:\{\{\{\{(?=[^/]))/,/^(?:\{\{\{\{\/[^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=[=}\s\/.])\}\}\}\})/,/^(?:[^\x00]+?(?=(\{\{\{\{)))/,/^(?:[\s\S]*?--(~)?\}\})/,/^(?:\()/,/^(?:\))/,/^(?:\{\{\{\{)/,/^(?:\}\}\}\})/,/^(?:\{\{(~)?>)/,/^(?:\{\{(~)?#>)/,/^(?:\{\{(~)?#\*?)/,/^(?:\{\{(~)?\/)/,/^(?:\{\{(~)?\^\s*(~)?\}\})/,/^(?:\{\{(~)?\s*else\s*(~)?\}\})/,/^(?:\{\{(~)?\^)/,/^(?:\{\{(~)?\s*else\b)/,/^(?:\{\{(~)?\{)/,/^(?:\{\{(~)?&)/,/^(?:\{\{(~)?!--)/,/^(?:\{\{(~)?![\s\S]*?\}\})/,/^(?:\{\{(~)?\*?)/,/^(?:=)/,/^(?:\.\.)/,/^(?:\.(?=([=~}\s\/.)|])))/,/^(?:[\/.])/,/^(?:\s+)/,/^(?:\}(~)?\}\})/,/^(?:(~)?\}\})/,/^(?:"(\\["]|[^"])*")/,/^(?:'(\\[']|[^'])*')/,/^(?:@)/,/^(?:true(?=([~}\s)])))/,/^(?:false(?=([~}\s)])))/,/^(?:undefined(?=([~}\s)])))/,/^(?:null(?=([~}\s)])))/,/^(?:-?[0-9]+(?:\.[0-9]+)?(?=([~}\s)])))/,/^(?:as\s+\|)/,/^(?:\|)/,/^(?:([^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=([=~}\s\/.)|]))))/,/^(?:\[(\\\]|[^\]])*\])/,/^(?:.)/,/^(?:$)/],conditions:{mu:{rules:[7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44],inclusive:!1},emu:{rules:[2],inclusive:!1},com:{rules:[6],inclusive:!1},raw:{rules:[3,4,5],inclusive:!1},INITIAL:{rules:[0,1,44],inclusive:!0}}};return i}();function a(){this.yy={}}return r.lexer=s,a.prototype=r,r.Parser=a,new a}();t.default=n,e.exports=t.default})(Ks,Ks.exports);var Mh=Ks.exports,Ys={exports:{}},Hs={exports:{}};(function(e,t){t.__esModule=!0;var n=function(o){return o&&o.__esModule?o:{default:o}}(Re);function r(){this.parents=[]}function s(o){this.acceptRequired(o,"path"),this.acceptArray(o.params),this.acceptKey(o,"hash")}function a(o){s.call(this,o),this.acceptKey(o,"program"),this.acceptKey(o,"inverse")}function i(o){this.acceptRequired(o,"name"),this.acceptArray(o.params),this.acceptKey(o,"hash")}r.prototype={constructor:r,mutating:!1,acceptKey:function(o,u){var l=this.accept(o[u]);if(this.mutating){if(l&&!r.prototype[l.type])throw new n.default('Unexpected node type "'+l.type+'" found when accepting '+u+" on "+o.type);o[u]=l}},acceptRequired:function(o,u){if(this.acceptKey(o,u),!o[u])throw new n.default(o.type+" requires "+u)},acceptArray:function(o){for(var u=0,l=o.length;u<l;u++)this.acceptKey(o,u),o[u]||(o.splice(u,1),u--,l--)},accept:function(o){if(o){if(!this[o.type])throw new n.default("Unknown type: "+o.type,o);this.current&&this.parents.unshift(this.current),this.current=o;var u=this[o.type](o);return this.current=this.parents.shift(),!this.mutating||u?u:u!==!1?o:void 0}},Program:function(o){this.acceptArray(o.body)},MustacheStatement:s,Decorator:s,BlockStatement:a,DecoratorBlock:a,PartialStatement:i,PartialBlockStatement:function(o){i.call(this,o),this.acceptKey(o,"program")},ContentStatement:function(){},CommentStatement:function(){},SubExpression:s,PathExpression:function(){},StringLiteral:function(){},NumberLiteral:function(){},BooleanLiteral:function(){},UndefinedLiteral:function(){},NullLiteral:function(){},Hash:function(o){this.acceptArray(o.pairs)},HashPair:function(o){this.acceptRequired(o,"value")}},t.default=r,e.exports=t.default})(Hs,Hs.exports);var ql=Hs.exports;(function(e,t){t.__esModule=!0;var n=function(u){return u&&u.__esModule?u:{default:u}}(ql);function r(){var u=arguments.length<=0||arguments[0]===void 0?{}:arguments[0];this.options=u}function s(u,l,d){l===void 0&&(l=u.length);var c=u[l-1],p=u[l-2];return c?c.type==="ContentStatement"?(p||!d?/\r?\n\s*?$/:/(^|\r?\n)\s*?$/).test(c.original):void 0:d}function a(u,l,d){l===void 0&&(l=-1);var c=u[l+1],p=u[l+2];return c?c.type==="ContentStatement"?(p||!d?/^\s*?\r?\n/:/^\s*?(\r?\n|$)/).test(c.original):void 0:d}function i(u,l,d){var c=u[l==null?0:l+1];if(c&&c.type==="ContentStatement"&&(d||!c.rightStripped)){var p=c.value;c.value=c.value.replace(d?/^\s+/:/^[ \t]*\r?\n?/,""),c.rightStripped=c.value!==p}}function o(u,l,d){var c=u[l==null?u.length-1:l-1];if(c&&c.type==="ContentStatement"&&(d||!c.leftStripped)){var p=c.value;return c.value=c.value.replace(d?/\s+$/:/[ \t]+$/,""),c.leftStripped=c.value!==p,c.leftStripped}}r.prototype=new n.default,r.prototype.Program=function(u){var l=!this.options.ignoreStandalone,d=!this.isRootSeen;this.isRootSeen=!0;for(var c=u.body,p=0,h=c.length;p<h;p++){var m=c[p],y=this.accept(m);if(y){var g=s(c,p,d),v=a(c,p,d),E=y.openStandalone&&g,b=y.closeStandalone&&v,I=y.inlineStandalone&&g&&v;y.close&&i(c,p,!0),y.open&&o(c,p,!0),l&&I&&(i(c,p),o(c,p)&&m.type==="PartialStatement"&&(m.indent=/([ \t]+$)/.exec(c[p-1].original)[1])),l&&E&&(i((m.program||m.inverse).body),o(c,p)),l&&b&&(i(c,p),o((m.inverse||m.program).body))}}return u},r.prototype.BlockStatement=r.prototype.DecoratorBlock=r.prototype.PartialBlockStatement=function(u){this.accept(u.program),this.accept(u.inverse);var l=u.program||u.inverse,d=u.program&&u.inverse,c=d,p=d;if(d&&d.chained)for(c=d.body[0].program;p.chained;)p=p.body[p.body.length-1].program;var h={open:u.openStrip.open,close:u.closeStrip.close,openStandalone:a(l.body),closeStandalone:s((c||l).body)};if(u.openStrip.close&&i(l.body,null,!0),d){var m=u.inverseStrip;m.open&&o(l.body,null,!0),m.close&&i(c.body,null,!0),u.closeStrip.open&&o(p.body,null,!0),!this.options.ignoreStandalone&&s(l.body)&&a(c.body)&&(o(l.body),i(c.body))}else u.closeStrip.open&&o(l.body,null,!0);return h},r.prototype.Decorator=r.prototype.MustacheStatement=function(u){return u.strip},r.prototype.PartialStatement=r.prototype.CommentStatement=function(u){var l=u.strip||{};return{inlineStandalone:!0,open:l.open,close:l.close}},t.default=r,e.exports=t.default})(Ys,Ys.exports);var Ph=Ys.exports,_e={};_e.__esModule=!0,_e.SourceLocation=function(e,t){this.source=e,this.start={line:t.first_line,column:t.first_column},this.end={line:t.last_line,column:t.last_column}},_e.id=function(e){return/^\[.*\]$/.test(e)?e.substring(1,e.length-1):e},_e.stripFlags=function(e,t){return{open:e.charAt(2)==="~",close:t.charAt(t.length-3)==="~"}},_e.stripComment=function(e){return e.replace(/^\{\{~?!-?-?/,"").replace(/-?-?~?\}\}$/,"")},_e.preparePath=function(e,t,n){n=this.locInfo(n);for(var r=e?"@":"",s=[],a=0,i=0,o=t.length;i<o;i++){var u=t[i].part,l=t[i].original!==u;if(r+=(t[i].separator||"")+u,l||u!==".."&&u!=="."&&u!=="this")s.push(u);else{if(s.length>0)throw new Ws.default("Invalid path: "+r,{loc:n});u===".."&&a++}}return{type:"PathExpression",data:e,depth:a,parts:s,original:r,loc:n}},_e.prepareMustache=function(e,t,n,r,s,a){var i=r.charAt(3)||r.charAt(2),o=i!=="{"&&i!=="&";return{type:/\*/.test(r)?"Decorator":"MustacheStatement",path:e,params:t,hash:n,escaped:o,strip:s,loc:this.locInfo(a)}},_e.prepareRawBlock=function(e,t,n,r){ns(e,n),r=this.locInfo(r);var s={type:"Program",body:t,strip:{},loc:r};return{type:"BlockStatement",path:e.path,params:e.params,hash:e.hash,program:s,openStrip:{},inverseStrip:{},closeStrip:{},loc:r}},_e.prepareBlock=function(e,t,n,r,s,a){r&&r.path&&ns(e,r);var i=/\*/.test(e.open);t.blockParams=e.blockParams;var o=void 0,u=void 0;if(n){if(i)throw new Ws.default("Unexpected inverse block on decorator",n);n.chain&&(n.program.body[0].closeStrip=r.strip),u=n.strip,o=n.program}return s&&(s=o,o=t,t=s),{type:i?"DecoratorBlock":"BlockStatement",path:e.path,params:e.params,hash:e.hash,program:t,inverse:o,openStrip:e.strip,inverseStrip:u,closeStrip:r&&r.strip,loc:this.locInfo(a)}},_e.prepareProgram=function(e,t){if(!t&&e.length){var n=e[0].loc,r=e[e.length-1].loc;n&&r&&(t={source:n.source,start:{line:n.start.line,column:n.start.column},end:{line:r.end.line,column:r.end.column}})}return{type:"Program",body:e,strip:{},loc:t}},_e.preparePartialBlock=function(e,t,n,r){return ns(e,n),{type:"PartialBlockStatement",name:e.path,params:e.params,hash:e.hash,program:t,openStrip:e.strip,closeStrip:n&&n.strip,loc:this.locInfo(r)}};var Ws=function(e){return e&&e.__esModule?e:{default:e}}(Re);function ns(e,t){if(t=t.path?t.path.original:t,e.path.original!==t){var n={loc:e.path.loc};throw new Ws.default(e.path.original+" doesn't match "+t,n)}}function Zl(e){return e&&e.__esModule?e:{default:e}}on.__esModule=!0,on.parseWithoutProcessing=eo,on.parse=function(e,t){var n=eo(e,t);return new Lh.default(t).accept(n)};var zs=Zl(Mh),Lh=Zl(Ph),Dh=function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(_e),Fh=B;on.parser=zs.default;var Xn={};function eo(e,t){return e.type==="Program"?e:(zs.default.yy=Xn,Xn.locInfo=function(n){return new Xn.SourceLocation(t&&t.srcName,n)},zs.default.parse(e))}Fh.extend(Xn,Dh);var Qt={};function Kl(e){return e&&e.__esModule?e:{default:e}}Qt.__esModule=!0,Qt.Compiler=Xs,Qt.precompile=function(e,t,n){if(e==null||typeof e!="string"&&e.type!=="Program")throw new ln.default("You must pass a string or Handlebars AST to Handlebars.precompile. You passed "+e);"data"in(t=t||{})||(t.data=!0),t.compat&&(t.useDepths=!0);var r=n.parse(e,t),s=new n.Compiler().compile(r,t);return new n.JavaScriptCompiler().compile(s,t)},Qt.compile=function(e,t,n){if(t===void 0&&(t={}),e==null||typeof e!="string"&&e.type!=="Program")throw new ln.default("You must pass a string or Handlebars AST to Handlebars.compile. You passed "+e);"data"in(t=Nn.extend({},t))||(t.data=!0),t.compat&&(t.useDepths=!0);var r=void 0;function s(){var i=n.parse(e,t),o=new n.Compiler().compile(i,t),u=new n.JavaScriptCompiler().compile(o,t,void 0,!0);return n.template(u)}function a(i,o){return r||(r=s()),r.call(this,i,o)}return a._setup=function(i){return r||(r=s()),r._setup(i)},a._child=function(i,o,u,l){return r||(r=s()),r._child(i,o,u,l)},a};var ln=Kl(Re),Nn=B,Kt=Kl(Gl),$h=[].slice;function Xs(){}function Yl(e,t){if(e===t)return!0;if(Nn.isArray(e)&&Nn.isArray(t)&&e.length===t.length){for(var n=0;n<e.length;n++)if(!Yl(e[n],t[n]))return!1;return!0}}function to(e){if(!e.path.parts){var t=e.path;e.path={type:"PathExpression",data:!1,depth:0,parts:[t.original+""],original:t.original+"",loc:t.loc}}}Xs.prototype={compiler:Xs,equals:function(e){var t=this.opcodes.length;if(e.opcodes.length!==t)return!1;for(var n=0;n<t;n++){var r=this.opcodes[n],s=e.opcodes[n];if(r.opcode!==s.opcode||!Yl(r.args,s.args))return!1}for(t=this.children.length,n=0;n<t;n++)if(!this.children[n].equals(e.children[n]))return!1;return!0},guid:0,compile:function(e,t){return this.sourceNode=[],this.opcodes=[],this.children=[],this.options=t,this.stringParams=t.stringParams,this.trackIds=t.trackIds,t.blockParams=t.blockParams||[],t.knownHelpers=Nn.extend(Object.create(null),{helperMissing:!0,blockHelperMissing:!0,each:!0,if:!0,unless:!0,with:!0,log:!0,lookup:!0},t.knownHelpers),this.accept(e)},compileProgram:function(e){var t=new this.compiler().compile(e,this.options),n=this.guid++;return this.usePartial=this.usePartial||t.usePartial,this.children[n]=t,this.useDepths=this.useDepths||t.useDepths,n},accept:function(e){if(!this[e.type])throw new ln.default("Unknown type: "+e.type,e);this.sourceNode.unshift(e);var t=this[e.type](e);return this.sourceNode.shift(),t},Program:function(e){this.options.blockParams.unshift(e.blockParams);for(var t=e.body,n=t.length,r=0;r<n;r++)this.accept(t[r]);return this.options.blockParams.shift(),this.isSimple=n===1,this.blockParams=e.blockParams?e.blockParams.length:0,this},BlockStatement:function(e){to(e);var t=e.program,n=e.inverse;t=t&&this.compileProgram(t),n=n&&this.compileProgram(n);var r=this.classifySexpr(e);r==="helper"?this.helperSexpr(e,t,n):r==="simple"?(this.simpleSexpr(e),this.opcode("pushProgram",t),this.opcode("pushProgram",n),this.opcode("emptyHash"),this.opcode("blockValue",e.path.original)):(this.ambiguousSexpr(e,t,n),this.opcode("pushProgram",t),this.opcode("pushProgram",n),this.opcode("emptyHash"),this.opcode("ambiguousBlockValue")),this.opcode("append")},DecoratorBlock:function(e){var t=e.program&&this.compileProgram(e.program),n=this.setupFullMustacheParams(e,t,void 0),r=e.path;this.useDecorators=!0,this.opcode("registerDecorator",n.length,r.original)},PartialStatement:function(e){this.usePartial=!0;var t=e.program;t&&(t=this.compileProgram(e.program));var n=e.params;if(n.length>1)throw new ln.default("Unsupported number of partial arguments: "+n.length,e);n.length||(this.options.explicitPartialContext?this.opcode("pushLiteral","undefined"):n.push({type:"PathExpression",parts:[],depth:0}));var r=e.name.original,s=e.name.type==="SubExpression";s&&this.accept(e.name),this.setupFullMustacheParams(e,t,void 0,!0);var a=e.indent||"";this.options.preventIndent&&a&&(this.opcode("appendContent",a),a=""),this.opcode("invokePartial",s,r,a),this.opcode("append")},PartialBlockStatement:function(e){this.PartialStatement(e)},MustacheStatement:function(e){this.SubExpression(e),e.escaped&&!this.options.noEscape?this.opcode("appendEscaped"):this.opcode("append")},Decorator:function(e){this.DecoratorBlock(e)},ContentStatement:function(e){e.value&&this.opcode("appendContent",e.value)},CommentStatement:function(){},SubExpression:function(e){to(e);var t=this.classifySexpr(e);t==="simple"?this.simpleSexpr(e):t==="helper"?this.helperSexpr(e):this.ambiguousSexpr(e)},ambiguousSexpr:function(e,t,n){var r=e.path,s=r.parts[0],a=t!=null||n!=null;this.opcode("getContext",r.depth),this.opcode("pushProgram",t),this.opcode("pushProgram",n),r.strict=!0,this.accept(r),this.opcode("invokeAmbiguous",s,a)},simpleSexpr:function(e){var t=e.path;t.strict=!0,this.accept(t),this.opcode("resolvePossibleLambda")},helperSexpr:function(e,t,n){var r=this.setupFullMustacheParams(e,t,n),s=e.path,a=s.parts[0];if(this.options.knownHelpers[a])this.opcode("invokeKnownHelper",r.length,a);else{if(this.options.knownHelpersOnly)throw new ln.default("You specified knownHelpersOnly, but used the unknown helper "+a,e);s.strict=!0,s.falsy=!0,this.accept(s),this.opcode("invokeHelper",r.length,s.original,Kt.default.helpers.simpleId(s))}},PathExpression:function(e){this.addDepth(e.depth),this.opcode("getContext",e.depth);var t=e.parts[0],n=Kt.default.helpers.scopedId(e),r=!e.depth&&!n&&this.blockParamIndex(t);r?this.opcode("lookupBlockParam",r,e.parts):t?e.data?(this.options.data=!0,this.opcode("lookupData",e.depth,e.parts,e.strict)):this.opcode("lookupOnContext",e.parts,e.falsy,e.strict,n):this.opcode("pushContext")},StringLiteral:function(e){this.opcode("pushString",e.value)},NumberLiteral:function(e){this.opcode("pushLiteral",e.value)},BooleanLiteral:function(e){this.opcode("pushLiteral",e.value)},UndefinedLiteral:function(){this.opcode("pushLiteral","undefined")},NullLiteral:function(){this.opcode("pushLiteral","null")},Hash:function(e){var t=e.pairs,n=0,r=t.length;for(this.opcode("pushHash");n<r;n++)this.pushParam(t[n].value);for(;n--;)this.opcode("assignToHash",t[n].key);this.opcode("popHash")},opcode:function(e){this.opcodes.push({opcode:e,args:$h.call(arguments,1),loc:this.sourceNode[0].loc})},addDepth:function(e){e&&(this.useDepths=!0)},classifySexpr:function(e){var t=Kt.default.helpers.simpleId(e.path),n=t&&!!this.blockParamIndex(e.path.parts[0]),r=!n&&Kt.default.helpers.helperExpression(e),s=!n&&(r||t);if(s&&!r){var a=e.path.parts[0],i=this.options;i.knownHelpers[a]?r=!0:i.knownHelpersOnly&&(s=!1)}return r?"helper":s?"ambiguous":"simple"},pushParams:function(e){for(var t=0,n=e.length;t<n;t++)this.pushParam(e[t])},pushParam:function(e){var t=e.value!=null?e.value:e.original||"";if(this.stringParams)t.replace&&(t=t.replace(/^(\.?\.\/)*/g,"").replace(/\//g,".")),e.depth&&this.addDepth(e.depth),this.opcode("getContext",e.depth||0),this.opcode("pushStringParam",t,e.type),e.type==="SubExpression"&&this.accept(e);else{if(this.trackIds){var n=void 0;if(!e.parts||Kt.default.helpers.scopedId(e)||e.depth||(n=this.blockParamIndex(e.parts[0])),n){var r=e.parts.slice(1).join(".");this.opcode("pushId","BlockParam",n,r)}else(t=e.original||t).replace&&(t=t.replace(/^this(?:\.|$)/,"").replace(/^\.\//,"").replace(/^\.$/,"")),this.opcode("pushId",e.type,t)}this.accept(e)}},setupFullMustacheParams:function(e,t,n,r){var s=e.params;return this.pushParams(s),this.opcode("pushProgram",t),this.opcode("pushProgram",n),e.hash?this.accept(e.hash):this.opcode("emptyHash",r),s},blockParamIndex:function(e){for(var t=0,n=this.options.blockParams.length;t<n;t++){var r=this.options.blockParams[t],s=r&&Nn.indexOf(r,e);if(r&&s>=0)return[t,s]}}};var no,ro,Js={exports:{}},Qs={exports:{}},Un={},rs={},Bn={},jn={};function Uh(){if(no)return jn;no=1;var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");return jn.encode=function(t){if(0<=t&&t<e.length)return e[t];throw new TypeError("Must be between 0 and 63: "+t)},jn.decode=function(t){return 65<=t&&t<=90?t-65:97<=t&&t<=122?t-97+26:48<=t&&t<=57?t-48+52:t==43?62:t==47?63:-1},jn}function Hl(){if(ro)return Bn;ro=1;var e=Uh();return Bn.encode=function(t){var n,r="",s=function(a){return a<0?1+(-a<<1):0+(a<<1)}(t);do n=31&s,(s>>>=5)>0&&(n|=32),r+=e.encode(n);while(s>0);return r},Bn.decode=function(t,n,r){var s,a,i,o,u=t.length,l=0,d=0;do{if(n>=u)throw new Error("Expected more digits in base 64 VLQ value.");if((a=e.decode(t.charCodeAt(n++)))===-1)throw new Error("Invalid base64 digit: "+t.charAt(n-1));s=!!(32&a),l+=(a&=31)<<d,d+=5}while(s);r.value=(o=(i=l)>>1,1&~i?o:-o),r.rest=n},Bn}var so,ao={};function In(){return so||(so=1,function(e){e.getArg=function(c,p,h){if(p in c)return c[p];if(arguments.length===3)return h;throw new Error('"'+p+'" is a required argument.')};var t=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,n=/^data:.+\,.+$/;function r(c){var p=c.match(t);return p?{scheme:p[1],auth:p[2],host:p[3],port:p[4],path:p[5]}:null}function s(c){var p="";return c.scheme&&(p+=c.scheme+":"),p+="//",c.auth&&(p+=c.auth+"@"),c.host&&(p+=c.host),c.port&&(p+=":"+c.port),c.path&&(p+=c.path),p}function a(c){var p=c,h=r(c);if(h){if(!h.path)return c;p=h.path}for(var m,y=e.isAbsolute(p),g=p.split(/\/+/),v=0,E=g.length-1;E>=0;E--)(m=g[E])==="."?g.splice(E,1):m===".."?v++:v>0&&(m===""?(g.splice(E+1,v),v=0):(g.splice(E,2),v--));return(p=g.join("/"))===""&&(p=y?"/":"."),h?(h.path=p,s(h)):p}function i(c,p){c===""&&(c="."),p===""&&(p=".");var h=r(p),m=r(c);if(m&&(c=m.path||"/"),h&&!h.scheme)return m&&(h.scheme=m.scheme),s(h);if(h||p.match(n))return p;if(m&&!m.host&&!m.path)return m.host=p,s(m);var y=p.charAt(0)==="/"?p:a(c.replace(/\/+$/,"")+"/"+p);return m?(m.path=y,s(m)):y}e.urlParse=r,e.urlGenerate=s,e.normalize=a,e.join=i,e.isAbsolute=function(c){return c.charAt(0)==="/"||t.test(c)},e.relative=function(c,p){c===""&&(c="."),c=c.replace(/\/$/,"");for(var h=0;p.indexOf(c+"/")!==0;){var m=c.lastIndexOf("/");if(m<0||(c=c.slice(0,m)).match(/^([^\/]+:\/)?\/*$/))return p;++h}return Array(h+1).join("../")+p.substr(c.length+1)};var o=!("__proto__"in Object.create(null));function u(c){return c}function l(c){if(!c)return!1;var p=c.length;if(p<9||c.charCodeAt(p-1)!==95||c.charCodeAt(p-2)!==95||c.charCodeAt(p-3)!==111||c.charCodeAt(p-4)!==116||c.charCodeAt(p-5)!==111||c.charCodeAt(p-6)!==114||c.charCodeAt(p-7)!==112||c.charCodeAt(p-8)!==95||c.charCodeAt(p-9)!==95)return!1;for(var h=p-10;h>=0;h--)if(c.charCodeAt(h)!==36)return!1;return!0}function d(c,p){return c===p?0:c===null?1:p===null?-1:c>p?1:-1}e.toSetString=o?u:function(c){return l(c)?"$"+c:c},e.fromSetString=o?u:function(c){return l(c)?c.slice(1):c},e.compareByOriginalPositions=function(c,p,h){var m=d(c.source,p.source);return m!==0||(m=c.originalLine-p.originalLine)!==0||(m=c.originalColumn-p.originalColumn)!==0||h||(m=c.generatedColumn-p.generatedColumn)!==0||(m=c.generatedLine-p.generatedLine)!==0?m:d(c.name,p.name)},e.compareByGeneratedPositionsDeflated=function(c,p,h){var m=c.generatedLine-p.generatedLine;return m!==0||(m=c.generatedColumn-p.generatedColumn)!==0||h||(m=d(c.source,p.source))!==0||(m=c.originalLine-p.originalLine)!==0||(m=c.originalColumn-p.originalColumn)!==0?m:d(c.name,p.name)},e.compareByGeneratedPositionsInflated=function(c,p){var h=c.generatedLine-p.generatedLine;return h!==0||(h=c.generatedColumn-p.generatedColumn)!==0||(h=d(c.source,p.source))!==0||(h=c.originalLine-p.originalLine)!==0||(h=c.originalColumn-p.originalColumn)!==0?h:d(c.name,p.name)},e.parseSourceMapInput=function(c){return JSON.parse(c.replace(/^\)]}'[^\n]*\n/,""))},e.computeSourceURL=function(c,p,h){if(p=p||"",c&&(c[c.length-1]!=="/"&&p[0]!=="/"&&(c+="/"),p=c+p),h){var m=r(h);if(!m)throw new Error("sourceMapURL could not be parsed");if(m.path){var y=m.path.lastIndexOf("/");y>=0&&(m.path=m.path.substring(0,y+1))}p=i(s(m),p)}return a(p)}}(ao)),ao}var io,ss={};function Wl(){if(io)return ss;io=1;var e=In(),t=Object.prototype.hasOwnProperty,n=typeof Map<"u";function r(){this._array=[],this._set=n?new Map:Object.create(null)}return r.fromArray=function(s,a){for(var i=new r,o=0,u=s.length;o<u;o++)i.add(s[o],a);return i},r.prototype.size=function(){return n?this._set.size:Object.getOwnPropertyNames(this._set).length},r.prototype.add=function(s,a){var i=n?s:e.toSetString(s),o=n?this.has(s):t.call(this._set,i),u=this._array.length;o&&!a||this._array.push(s),o||(n?this._set.set(s,u):this._set[i]=u)},r.prototype.has=function(s){if(n)return this._set.has(s);var a=e.toSetString(s);return t.call(this._set,a)},r.prototype.indexOf=function(s){if(n){var a=this._set.get(s);if(a>=0)return a}else{var i=e.toSetString(s);if(t.call(this._set,i))return this._set[i]}throw new Error('"'+s+'" is not in the set.')},r.prototype.at=function(s){if(s>=0&&s<this._array.length)return this._array[s];throw new Error("No element indexed by "+s)},r.prototype.toArray=function(){return this._array.slice()},ss.ArraySet=r,ss}var oo,lo,as={};function Bh(){if(oo)return as;oo=1;var e=In();function t(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}return t.prototype.unsortedForEach=function(n,r){this._array.forEach(n,r)},t.prototype.add=function(n){var r,s,a,i,o,u;r=this._last,s=n,a=r.generatedLine,i=s.generatedLine,o=r.generatedColumn,u=s.generatedColumn,i>a||i==a&&u>=o||e.compareByGeneratedPositionsInflated(r,s)<=0?(this._last=n,this._array.push(n)):(this._sorted=!1,this._array.push(n))},t.prototype.toArray=function(){return this._sorted||(this._array.sort(e.compareByGeneratedPositionsInflated),this._sorted=!0),this._array},as.MappingList=t,as}function uo(){if(lo)return rs;lo=1;var e=Hl(),t=In(),n=Wl().ArraySet,r=Bh().MappingList;function s(a){a||(a={}),this._file=t.getArg(a,"file",null),this._sourceRoot=t.getArg(a,"sourceRoot",null),this._skipValidation=t.getArg(a,"skipValidation",!1),this._sources=new n,this._names=new n,this._mappings=new r,this._sourcesContents=null}return s.prototype._version=3,s.fromSourceMap=function(a){var i=a.sourceRoot,o=new s({file:a.file,sourceRoot:i});return a.eachMapping(function(u){var l={generated:{line:u.generatedLine,column:u.generatedColumn}};u.source!=null&&(l.source=u.source,i!=null&&(l.source=t.relative(i,l.source)),l.original={line:u.originalLine,column:u.originalColumn},u.name!=null&&(l.name=u.name)),o.addMapping(l)}),a.sources.forEach(function(u){var l=u;i!==null&&(l=t.relative(i,u)),o._sources.has(l)||o._sources.add(l);var d=a.sourceContentFor(u);d!=null&&o.setSourceContent(u,d)}),o},s.prototype.addMapping=function(a){var i=t.getArg(a,"generated"),o=t.getArg(a,"original",null),u=t.getArg(a,"source",null),l=t.getArg(a,"name",null);this._skipValidation||this._validateMapping(i,o,u,l),u!=null&&(u=String(u),this._sources.has(u)||this._sources.add(u)),l!=null&&(l=String(l),this._names.has(l)||this._names.add(l)),this._mappings.add({generatedLine:i.line,generatedColumn:i.column,originalLine:o!=null&&o.line,originalColumn:o!=null&&o.column,source:u,name:l})},s.prototype.setSourceContent=function(a,i){var o=a;this._sourceRoot!=null&&(o=t.relative(this._sourceRoot,o)),i!=null?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[t.toSetString(o)]=i):this._sourcesContents&&(delete this._sourcesContents[t.toSetString(o)],Object.keys(this._sourcesContents).length===0&&(this._sourcesContents=null))},s.prototype.applySourceMap=function(a,i,o){var u=i;if(i==null){if(a.file==null)throw new Error(`SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map's "file" property. Both were omitted.`);u=a.file}var l=this._sourceRoot;l!=null&&(u=t.relative(l,u));var d=new n,c=new n;this._mappings.unsortedForEach(function(p){if(p.source===u&&p.originalLine!=null){var h=a.originalPositionFor({line:p.originalLine,column:p.originalColumn});h.source!=null&&(p.source=h.source,o!=null&&(p.source=t.join(o,p.source)),l!=null&&(p.source=t.relative(l,p.source)),p.originalLine=h.line,p.originalColumn=h.column,h.name!=null&&(p.name=h.name))}var m=p.source;m==null||d.has(m)||d.add(m);var y=p.name;y==null||c.has(y)||c.add(y)},this),this._sources=d,this._names=c,a.sources.forEach(function(p){var h=a.sourceContentFor(p);h!=null&&(o!=null&&(p=t.join(o,p)),l!=null&&(p=t.relative(l,p)),this.setSourceContent(p,h))},this)},s.prototype._validateMapping=function(a,i,o,u){if(i&&typeof i.line!="number"&&typeof i.column!="number")throw new Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if((!(a&&"line"in a&&"column"in a&&a.line>0&&a.column>=0)||i||o||u)&&!(a&&"line"in a&&"column"in a&&i&&"line"in i&&"column"in i&&a.line>0&&a.column>=0&&i.line>0&&i.column>=0&&o))throw new Error("Invalid mapping: "+JSON.stringify({generated:a,source:o,original:i,name:u}))},s.prototype._serializeMappings=function(){for(var a,i,o,u,l=0,d=1,c=0,p=0,h=0,m=0,y="",g=this._mappings.toArray(),v=0,E=g.length;v<E;v++){if(a="",(i=g[v]).generatedLine!==d)for(l=0;i.generatedLine!==d;)a+=";",d++;else if(v>0){if(!t.compareByGeneratedPositionsInflated(i,g[v-1]))continue;a+=","}a+=e.encode(i.generatedColumn-l),l=i.generatedColumn,i.source!=null&&(u=this._sources.indexOf(i.source),a+=e.encode(u-m),m=u,a+=e.encode(i.originalLine-1-p),p=i.originalLine-1,a+=e.encode(i.originalColumn-c),c=i.originalColumn,i.name!=null&&(o=this._names.indexOf(i.name),a+=e.encode(o-h),h=o)),y+=a}return y},s.prototype._generateSourcesContent=function(a,i){return a.map(function(o){if(!this._sourcesContents)return null;i!=null&&(o=t.relative(i,o));var u=t.toSetString(o);return Object.prototype.hasOwnProperty.call(this._sourcesContents,u)?this._sourcesContents[u]:null},this)},s.prototype.toJSON=function(){var a={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return this._file!=null&&(a.file=this._file),this._sourceRoot!=null&&(a.sourceRoot=this._sourceRoot),this._sourcesContents&&(a.sourcesContent=this._generateSourcesContent(a.sources,a.sourceRoot)),a},s.prototype.toString=function(){return JSON.stringify(this.toJSON())},rs.SourceMapGenerator=s,rs}var co,Yt={},po={};function jh(){return co||(co=1,function(e){function t(n,r,s,a,i,o){var u=Math.floor((r-n)/2)+n,l=i(s,a[u],!0);return l===0?u:l>0?r-u>1?t(u,r,s,a,i,o):o==e.LEAST_UPPER_BOUND?r<a.length?r:-1:u:u-n>1?t(n,u,s,a,i,o):o==e.LEAST_UPPER_BOUND?u:n<0?-1:n}e.GREATEST_LOWER_BOUND=1,e.LEAST_UPPER_BOUND=2,e.search=function(n,r,s,a){if(r.length===0)return-1;var i=t(-1,r.length,n,r,s,a||e.GREATEST_LOWER_BOUND);if(i<0)return-1;for(;i-1>=0&&s(r[i],r[i-1],!0)===0;)--i;return i}}(po)),po}var ho,mo,is={};function Vh(){if(ho)return is;function e(n,r,s){var a=n[r];n[r]=n[s],n[s]=a}function t(n,r,s,a){if(s<a){var i=s-1;e(n,(d=s,c=a,Math.round(d+Math.random()*(c-d))),a);for(var o=n[a],u=s;u<a;u++)r(n[u],o)<=0&&e(n,i+=1,u);e(n,i+1,u);var l=i+1;t(n,r,s,l-1),t(n,r,l+1,a)}var d,c}return ho=1,is.quickSort=function(n,r){t(n,r,0,n.length-1)},is}var fo,go,os={};function Gh(){return go||(go=1,Un.SourceMapGenerator=uo().SourceMapGenerator,Un.SourceMapConsumer=function(){if(mo)return Yt;mo=1;var e=In(),t=jh(),n=Wl().ArraySet,r=Hl(),s=Vh().quickSort;function a(l,d){var c=l;return typeof l=="string"&&(c=e.parseSourceMapInput(l)),c.sections!=null?new u(c,d):new i(c,d)}function i(l,d){var c=l;typeof l=="string"&&(c=e.parseSourceMapInput(l));var p=e.getArg(c,"version"),h=e.getArg(c,"sources"),m=e.getArg(c,"names",[]),y=e.getArg(c,"sourceRoot",null),g=e.getArg(c,"sourcesContent",null),v=e.getArg(c,"mappings"),E=e.getArg(c,"file",null);if(p!=this._version)throw new Error("Unsupported version: "+p);y&&(y=e.normalize(y)),h=h.map(String).map(e.normalize).map(function(b){return y&&e.isAbsolute(y)&&e.isAbsolute(b)?e.relative(y,b):b}),this._names=n.fromArray(m.map(String),!0),this._sources=n.fromArray(h,!0),this._absoluteSources=this._sources.toArray().map(function(b){return e.computeSourceURL(y,b,d)}),this.sourceRoot=y,this.sourcesContent=g,this._mappings=v,this._sourceMapURL=d,this.file=E}function o(){this.generatedLine=0,this.generatedColumn=0,this.source=null,this.originalLine=null,this.originalColumn=null,this.name=null}function u(l,d){var c=l;typeof l=="string"&&(c=e.parseSourceMapInput(l));var p=e.getArg(c,"version"),h=e.getArg(c,"sections");if(p!=this._version)throw new Error("Unsupported version: "+p);this._sources=new n,this._names=new n;var m={line:-1,column:0};this._sections=h.map(function(y){if(y.url)throw new Error("Support for url field in sections not implemented.");var g=e.getArg(y,"offset"),v=e.getArg(g,"line"),E=e.getArg(g,"column");if(v<m.line||v===m.line&&E<m.column)throw new Error("Section offsets must be ordered and non-overlapping.");return m=g,{generatedOffset:{generatedLine:v+1,generatedColumn:E+1},consumer:new a(e.getArg(y,"map"),d)}})}return a.fromSourceMap=function(l,d){return i.fromSourceMap(l,d)},a.prototype._version=3,a.prototype.__generatedMappings=null,Object.defineProperty(a.prototype,"_generatedMappings",{configurable:!0,enumerable:!0,get:function(){return this.__generatedMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__generatedMappings}}),a.prototype.__originalMappings=null,Object.defineProperty(a.prototype,"_originalMappings",{configurable:!0,enumerable:!0,get:function(){return this.__originalMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__originalMappings}}),a.prototype._charIsMappingSeparator=function(l,d){var c=l.charAt(d);return c===";"||c===","},a.prototype._parseMappings=function(l,d){throw new Error("Subclasses must implement _parseMappings")},a.GENERATED_ORDER=1,a.ORIGINAL_ORDER=2,a.GREATEST_LOWER_BOUND=1,a.LEAST_UPPER_BOUND=2,a.prototype.eachMapping=function(l,d,c){var p,h=d||null;switch(c||a.GENERATED_ORDER){case a.GENERATED_ORDER:p=this._generatedMappings;break;case a.ORIGINAL_ORDER:p=this._originalMappings;break;default:throw new Error("Unknown order of iteration.")}var m=this.sourceRoot;p.map(function(y){var g=y.source===null?null:this._sources.at(y.source);return{source:g=e.computeSourceURL(m,g,this._sourceMapURL),generatedLine:y.generatedLine,generatedColumn:y.generatedColumn,originalLine:y.originalLine,originalColumn:y.originalColumn,name:y.name===null?null:this._names.at(y.name)}},this).forEach(l,h)},a.prototype.allGeneratedPositionsFor=function(l){var d=e.getArg(l,"line"),c={source:e.getArg(l,"source"),originalLine:d,originalColumn:e.getArg(l,"column",0)};if(c.source=this._findSourceIndex(c.source),c.source<0)return[];var p=[],h=this._findMapping(c,this._originalMappings,"originalLine","originalColumn",e.compareByOriginalPositions,t.LEAST_UPPER_BOUND);if(h>=0){var m=this._originalMappings[h];if(l.column===void 0)for(var y=m.originalLine;m&&m.originalLine===y;)p.push({line:e.getArg(m,"generatedLine",null),column:e.getArg(m,"generatedColumn",null),lastColumn:e.getArg(m,"lastGeneratedColumn",null)}),m=this._originalMappings[++h];else for(var g=m.originalColumn;m&&m.originalLine===d&&m.originalColumn==g;)p.push({line:e.getArg(m,"generatedLine",null),column:e.getArg(m,"generatedColumn",null),lastColumn:e.getArg(m,"lastGeneratedColumn",null)}),m=this._originalMappings[++h]}return p},Yt.SourceMapConsumer=a,i.prototype=Object.create(a.prototype),i.prototype.consumer=a,i.prototype._findSourceIndex=function(l){var d,c=l;if(this.sourceRoot!=null&&(c=e.relative(this.sourceRoot,c)),this._sources.has(c))return this._sources.indexOf(c);for(d=0;d<this._absoluteSources.length;++d)if(this._absoluteSources[d]==l)return d;return-1},i.fromSourceMap=function(l,d){var c=Object.create(i.prototype),p=c._names=n.fromArray(l._names.toArray(),!0),h=c._sources=n.fromArray(l._sources.toArray(),!0);c.sourceRoot=l._sourceRoot,c.sourcesContent=l._generateSourcesContent(c._sources.toArray(),c.sourceRoot),c.file=l._file,c._sourceMapURL=d,c._absoluteSources=c._sources.toArray().map(function(A){return e.computeSourceURL(c.sourceRoot,A,d)});for(var m=l._mappings.toArray().slice(),y=c.__generatedMappings=[],g=c.__originalMappings=[],v=0,E=m.length;v<E;v++){var b=m[v],I=new o;I.generatedLine=b.generatedLine,I.generatedColumn=b.generatedColumn,b.source&&(I.source=h.indexOf(b.source),I.originalLine=b.originalLine,I.originalColumn=b.originalColumn,b.name&&(I.name=p.indexOf(b.name)),g.push(I)),y.push(I)}return s(c.__originalMappings,e.compareByOriginalPositions),c},i.prototype._version=3,Object.defineProperty(i.prototype,"sources",{get:function(){return this._absoluteSources.slice()}}),i.prototype._parseMappings=function(l,d){for(var c,p,h,m,y,g=1,v=0,E=0,b=0,I=0,A=0,P=l.length,M=0,H={},j={},D=[],ye=[];M<P;)if(l.charAt(M)===";")g++,M++,v=0;else if(l.charAt(M)===",")M++;else{for((c=new o).generatedLine=g,m=M;m<P&&!this._charIsMappingSeparator(l,m);m++);if(h=H[p=l.slice(M,m)])M+=p.length;else{for(h=[];M<m;)r.decode(l,M,j),y=j.value,M=j.rest,h.push(y);if(h.length===2)throw new Error("Found a source, but no line and column");if(h.length===3)throw new Error("Found a source and line, but no column");H[p]=h}c.generatedColumn=v+h[0],v=c.generatedColumn,h.length>1&&(c.source=I+h[1],I+=h[1],c.originalLine=E+h[2],E=c.originalLine,c.originalLine+=1,c.originalColumn=b+h[3],b=c.originalColumn,h.length>4&&(c.name=A+h[4],A+=h[4])),ye.push(c),typeof c.originalLine=="number"&&D.push(c)}s(ye,e.compareByGeneratedPositionsDeflated),this.__generatedMappings=ye,s(D,e.compareByOriginalPositions),this.__originalMappings=D},i.prototype._findMapping=function(l,d,c,p,h,m){if(l[c]<=0)throw new TypeError("Line must be greater than or equal to 1, got "+l[c]);if(l[p]<0)throw new TypeError("Column must be greater than or equal to 0, got "+l[p]);return t.search(l,d,h,m)},i.prototype.computeColumnSpans=function(){for(var l=0;l<this._generatedMappings.length;++l){var d=this._generatedMappings[l];if(l+1<this._generatedMappings.length){var c=this._generatedMappings[l+1];if(d.generatedLine===c.generatedLine){d.lastGeneratedColumn=c.generatedColumn-1;continue}}d.lastGeneratedColumn=1/0}},i.prototype.originalPositionFor=function(l){var d={generatedLine:e.getArg(l,"line"),generatedColumn:e.getArg(l,"column")},c=this._findMapping(d,this._generatedMappings,"generatedLine","generatedColumn",e.compareByGeneratedPositionsDeflated,e.getArg(l,"bias",a.GREATEST_LOWER_BOUND));if(c>=0){var p=this._generatedMappings[c];if(p.generatedLine===d.generatedLine){var h=e.getArg(p,"source",null);h!==null&&(h=this._sources.at(h),h=e.computeSourceURL(this.sourceRoot,h,this._sourceMapURL));var m=e.getArg(p,"name",null);return m!==null&&(m=this._names.at(m)),{source:h,line:e.getArg(p,"originalLine",null),column:e.getArg(p,"originalColumn",null),name:m}}}return{source:null,line:null,column:null,name:null}},i.prototype.hasContentsOfAllSources=function(){return!!this.sourcesContent&&this.sourcesContent.length>=this._sources.size()&&!this.sourcesContent.some(function(l){return l==null})},i.prototype.sourceContentFor=function(l,d){if(!this.sourcesContent)return null;var c=this._findSourceIndex(l);if(c>=0)return this.sourcesContent[c];var p,h=l;if(this.sourceRoot!=null&&(h=e.relative(this.sourceRoot,h)),this.sourceRoot!=null&&(p=e.urlParse(this.sourceRoot))){var m=h.replace(/^file:\/\//,"");if(p.scheme=="file"&&this._sources.has(m))return this.sourcesContent[this._sources.indexOf(m)];if((!p.path||p.path=="/")&&this._sources.has("/"+h))return this.sourcesContent[this._sources.indexOf("/"+h)]}if(d)return null;throw new Error('"'+h+'" is not in the SourceMap.')},i.prototype.generatedPositionFor=function(l){var d=e.getArg(l,"source");if((d=this._findSourceIndex(d))<0)return{line:null,column:null,lastColumn:null};var c={source:d,originalLine:e.getArg(l,"line"),originalColumn:e.getArg(l,"column")},p=this._findMapping(c,this._originalMappings,"originalLine","originalColumn",e.compareByOriginalPositions,e.getArg(l,"bias",a.GREATEST_LOWER_BOUND));if(p>=0){var h=this._originalMappings[p];if(h.source===c.source)return{line:e.getArg(h,"generatedLine",null),column:e.getArg(h,"generatedColumn",null),lastColumn:e.getArg(h,"lastGeneratedColumn",null)}}return{line:null,column:null,lastColumn:null}},Yt.BasicSourceMapConsumer=i,u.prototype=Object.create(a.prototype),u.prototype.constructor=a,u.prototype._version=3,Object.defineProperty(u.prototype,"sources",{get:function(){for(var l=[],d=0;d<this._sections.length;d++)for(var c=0;c<this._sections[d].consumer.sources.length;c++)l.push(this._sections[d].consumer.sources[c]);return l}}),u.prototype.originalPositionFor=function(l){var d={generatedLine:e.getArg(l,"line"),generatedColumn:e.getArg(l,"column")},c=t.search(d,this._sections,function(h,m){return h.generatedLine-m.generatedOffset.generatedLine||h.generatedColumn-m.generatedOffset.generatedColumn}),p=this._sections[c];return p?p.consumer.originalPositionFor({line:d.generatedLine-(p.generatedOffset.generatedLine-1),column:d.generatedColumn-(p.generatedOffset.generatedLine===d.generatedLine?p.generatedOffset.generatedColumn-1:0),bias:l.bias}):{source:null,line:null,column:null,name:null}},u.prototype.hasContentsOfAllSources=function(){return this._sections.every(function(l){return l.consumer.hasContentsOfAllSources()})},u.prototype.sourceContentFor=function(l,d){for(var c=0;c<this._sections.length;c++){var p=this._sections[c].consumer.sourceContentFor(l,!0);if(p)return p}if(d)return null;throw new Error('"'+l+'" is not in the SourceMap.')},u.prototype.generatedPositionFor=function(l){for(var d=0;d<this._sections.length;d++){var c=this._sections[d];if(c.consumer._findSourceIndex(e.getArg(l,"source"))!==-1){var p=c.consumer.generatedPositionFor(l);if(p)return{line:p.line+(c.generatedOffset.generatedLine-1),column:p.column+(c.generatedOffset.generatedLine===p.line?c.generatedOffset.generatedColumn-1:0)}}}return{line:null,column:null}},u.prototype._parseMappings=function(l,d){this.__generatedMappings=[],this.__originalMappings=[];for(var c=0;c<this._sections.length;c++)for(var p=this._sections[c],h=p.consumer._generatedMappings,m=0;m<h.length;m++){var y=h[m],g=p.consumer._sources.at(y.source);g=e.computeSourceURL(p.consumer.sourceRoot,g,this._sourceMapURL),this._sources.add(g),g=this._sources.indexOf(g);var v=null;y.name&&(v=p.consumer._names.at(y.name),this._names.add(v),v=this._names.indexOf(v));var E={source:g,generatedLine:y.generatedLine+(p.generatedOffset.generatedLine-1),generatedColumn:y.generatedColumn+(p.generatedOffset.generatedLine===y.generatedLine?p.generatedOffset.generatedColumn-1:0),originalLine:y.originalLine,originalColumn:y.originalColumn,name:v};this.__generatedMappings.push(E),typeof E.originalLine=="number"&&this.__originalMappings.push(E)}s(this.__generatedMappings,e.compareByGeneratedPositionsDeflated),s(this.__originalMappings,e.compareByOriginalPositions)},Yt.IndexedSourceMapConsumer=u,Yt}().SourceMapConsumer,Un.SourceNode=function(){if(fo)return os;fo=1;var e=uo().SourceMapGenerator,t=In(),n=/(\r?\n)/,r="$$$isSourceNode$$$";function s(a,i,o,u,l){this.children=[],this.sourceContents={},this.line=a??null,this.column=i??null,this.source=o??null,this.name=l??null,this[r]=!0,u!=null&&this.add(u)}return s.fromStringWithSourceMap=function(a,i,o){var u=new s,l=a.split(n),d=0,c=function(){return g()+(g()||"");function g(){return d<l.length?l[d++]:void 0}},p=1,h=0,m=null;return i.eachMapping(function(g){if(m!==null){if(!(p<g.generatedLine)){var v=(E=l[d]||"").substr(0,g.generatedColumn-h);return l[d]=E.substr(g.generatedColumn-h),h=g.generatedColumn,y(m,v),void(m=g)}y(m,c()),p++,h=0}for(;p<g.generatedLine;)u.add(c()),p++;if(h<g.generatedColumn){var E=l[d]||"";u.add(E.substr(0,g.generatedColumn)),l[d]=E.substr(g.generatedColumn),h=g.generatedColumn}m=g},this),d<l.length&&(m&&y(m,c()),u.add(l.splice(d).join(""))),i.sources.forEach(function(g){var v=i.sourceContentFor(g);v!=null&&(o!=null&&(g=t.join(o,g)),u.setSourceContent(g,v))}),u;function y(g,v){if(g===null||g.source===void 0)u.add(v);else{var E=o?t.join(o,g.source):g.source;u.add(new s(g.originalLine,g.originalColumn,E,v,g.name))}}},s.prototype.add=function(a){if(Array.isArray(a))a.forEach(function(i){this.add(i)},this);else{if(!a[r]&&typeof a!="string")throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+a);a&&this.children.push(a)}return this},s.prototype.prepend=function(a){if(Array.isArray(a))for(var i=a.length-1;i>=0;i--)this.prepend(a[i]);else{if(!a[r]&&typeof a!="string")throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+a);this.children.unshift(a)}return this},s.prototype.walk=function(a){for(var i,o=0,u=this.children.length;o<u;o++)(i=this.children[o])[r]?i.walk(a):i!==""&&a(i,{source:this.source,line:this.line,column:this.column,name:this.name})},s.prototype.join=function(a){var i,o,u=this.children.length;if(u>0){for(i=[],o=0;o<u-1;o++)i.push(this.children[o]),i.push(a);i.push(this.children[o]),this.children=i}return this},s.prototype.replaceRight=function(a,i){var o=this.children[this.children.length-1];return o[r]?o.replaceRight(a,i):typeof o=="string"?this.children[this.children.length-1]=o.replace(a,i):this.children.push("".replace(a,i)),this},s.prototype.setSourceContent=function(a,i){this.sourceContents[t.toSetString(a)]=i},s.prototype.walkSourceContents=function(a){for(var i=0,o=this.children.length;i<o;i++)this.children[i][r]&&this.children[i].walkSourceContents(a);var u=Object.keys(this.sourceContents);for(i=0,o=u.length;i<o;i++)a(t.fromSetString(u[i]),this.sourceContents[u[i]])},s.prototype.toString=function(){var a="";return this.walk(function(i){a+=i}),a},s.prototype.toStringWithSourceMap=function(a){var i={code:"",line:1,column:0},o=new e(a),u=!1,l=null,d=null,c=null,p=null;return this.walk(function(h,m){i.code+=h,m.source!==null&&m.line!==null&&m.column!==null?(l===m.source&&d===m.line&&c===m.column&&p===m.name||o.addMapping({source:m.source,original:{line:m.line,column:m.column},generated:{line:i.line,column:i.column},name:m.name}),l=m.source,d=m.line,c=m.column,p=m.name,u=!0):u&&(o.addMapping({generated:{line:i.line,column:i.column}}),l=null,u=!1);for(var y=0,g=h.length;y<g;y++)h.charCodeAt(y)===10?(i.line++,i.column=0,y+1===g?(l=null,u=!1):u&&o.addMapping({source:m.source,original:{line:m.line,column:m.column},generated:{line:i.line,column:i.column},name:m.name})):i.column++}),this.walkSourceContents(function(h,m){o.setSourceContent(h,m)}),{code:i.code,map:o}},os.SourceNode=s,os}().SourceNode),Un}(function(e,t){t.__esModule=!0;var n=B,r=void 0;try{var s=Gh();r=s.SourceNode}catch{}function a(o,u,l){if(n.isArray(o)){for(var d=[],c=0,p=o.length;c<p;c++)d.push(u.wrap(o[c],l));return d}return typeof o=="boolean"||typeof o=="number"?o+"":o}function i(o){this.srcFile=o,this.source=[]}r||((r=function(o,u,l,d){this.src="",d&&this.add(d)}).prototype={add:function(o){n.isArray(o)&&(o=o.join("")),this.src+=o},prepend:function(o){n.isArray(o)&&(o=o.join("")),this.src=o+this.src},toStringWithSourceMap:function(){return{code:this.toString()}},toString:function(){return this.src}}),i.prototype={isEmpty:function(){return!this.source.length},prepend:function(o,u){this.source.unshift(this.wrap(o,u))},push:function(o,u){this.source.push(this.wrap(o,u))},merge:function(){var o=this.empty();return this.each(function(u){o.add(["  ",u,`
`])}),o},each:function(o){for(var u=0,l=this.source.length;u<l;u++)o(this.source[u])},empty:function(){var o=this.currentLocation||{start:{}};return new r(o.start.line,o.start.column,this.srcFile)},wrap:function(o){var u=arguments.length<=1||arguments[1]===void 0?this.currentLocation||{start:{}}:arguments[1];return o instanceof r?o:(o=a(o,this,u),new r(u.start.line,u.start.column,this.srcFile,o))},functionCall:function(o,u,l){return l=this.generateList(l),this.wrap([o,u?"."+u+"(":"(",l,")"])},quotedString:function(o){return'"'+(o+"").replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")+'"'},objectLiteral:function(o){var u=this,l=[];Object.keys(o).forEach(function(c){var p=a(o[c],u);p!=="undefined"&&l.push([u.quotedString(c),":",p])});var d=this.generateList(l);return d.prepend("{"),d.add("}"),d},generateList:function(o){for(var u=this.empty(),l=0,d=o.length;l<d;l++)l&&u.add(","),u.add(a(o[l],this));return u},generateArray:function(o){var u=this.generateList(o);return u.prepend("["),u.add("]"),u}},t.default=i,e.exports=t.default})(Qs,Qs.exports);var qh=Qs.exports;(function(e,t){function n(l){return l&&l.__esModule?l:{default:l}}t.__esModule=!0;var r=Te,s=n(Re),a=B,i=n(qh);function o(l){this.value=l}function u(){}u.prototype={nameLookup:function(l,d){return this.internalNameLookup(l,d)},depthedLookup:function(l){return[this.aliasable("container.lookup"),"(depths, ",JSON.stringify(l),")"]},compilerInfo:function(){var l=r.COMPILER_REVISION;return[l,r.REVISION_CHANGES[l]]},appendToBuffer:function(l,d,c){return a.isArray(l)||(l=[l]),l=this.source.wrap(l,d),this.environment.isSimple?["return ",l,";"]:c?["buffer += ",l,";"]:(l.appendToBuffer=!0,l)},initializeBuffer:function(){return this.quotedString("")},internalNameLookup:function(l,d){return this.lookupPropertyFunctionIsUsed=!0,["lookupProperty(",l,",",JSON.stringify(d),")"]},lookupPropertyFunctionIsUsed:!1,compile:function(l,d,c,p){this.environment=l,this.options=d,this.stringParams=this.options.stringParams,this.trackIds=this.options.trackIds,this.precompile=!p,this.name=this.environment.name,this.isChild=!!c,this.context=c||{decorators:[],programs:[],environments:[]},this.preamble(),this.stackSlot=0,this.stackVars=[],this.aliases={},this.registers={list:[]},this.hashes=[],this.compileStack=[],this.inlineStack=[],this.blockParams=[],this.compileChildren(l,d),this.useDepths=this.useDepths||l.useDepths||l.useDecorators||this.options.compat,this.useBlockParams=this.useBlockParams||l.useBlockParams;var h=l.opcodes,m=void 0,y=void 0,g=void 0,v=void 0;for(g=0,v=h.length;g<v;g++)m=h[g],this.source.currentLocation=m.loc,y=y||m.loc,this[m.opcode].apply(this,m.args);if(this.source.currentLocation=y,this.pushSource(""),this.stackSlot||this.inlineStack.length||this.compileStack.length)throw new s.default("Compile completed with content left on stack");this.decorators.isEmpty()?this.decorators=void 0:(this.useDecorators=!0,this.decorators.prepend(["var decorators = container.decorators, ",this.lookupPropertyFunctionVarDeclaration(),`;
`]),this.decorators.push("return fn;"),p?this.decorators=Function.apply(this,["fn","props","container","depth0","data","blockParams","depths",this.decorators.merge()]):(this.decorators.prepend(`function(fn, props, container, depth0, data, blockParams, depths) {
`),this.decorators.push(`}
`),this.decorators=this.decorators.merge()));var E=this.createFunctionContext(p);if(this.isChild)return E;var b={compiler:this.compilerInfo(),main:E};this.decorators&&(b.main_d=this.decorators,b.useDecorators=!0);var I=this.context,A=I.programs,P=I.decorators;for(g=0,v=A.length;g<v;g++)A[g]&&(b[g]=A[g],P[g]&&(b[g+"_d"]=P[g],b.useDecorators=!0));return this.environment.usePartial&&(b.usePartial=!0),this.options.data&&(b.useData=!0),this.useDepths&&(b.useDepths=!0),this.useBlockParams&&(b.useBlockParams=!0),this.options.compat&&(b.compat=!0),p?b.compilerOptions=this.options:(b.compiler=JSON.stringify(b.compiler),this.source.currentLocation={start:{line:1,column:0}},b=this.objectLiteral(b),d.srcName?(b=b.toStringWithSourceMap({file:d.destName})).map=b.map&&b.map.toString():b=b.toString()),b},preamble:function(){this.lastContext=0,this.source=new i.default(this.options.srcName),this.decorators=new i.default(this.options.srcName)},createFunctionContext:function(l){var d=this,c="",p=this.stackVars.concat(this.registers.list);p.length>0&&(c+=", "+p.join(", "));var h=0;Object.keys(this.aliases).forEach(function(g){var v=d.aliases[g];v.children&&v.referenceCount>1&&(c+=", alias"+ ++h+"="+g,v.children[0]="alias"+h)}),this.lookupPropertyFunctionIsUsed&&(c+=", "+this.lookupPropertyFunctionVarDeclaration());var m=["container","depth0","helpers","partials","data"];(this.useBlockParams||this.useDepths)&&m.push("blockParams"),this.useDepths&&m.push("depths");var y=this.mergeSource(c);return l?(m.push(y),Function.apply(this,m)):this.source.wrap(["function(",m.join(","),`) {
  `,y,"}"])},mergeSource:function(l){var d=this.environment.isSimple,c=!this.forceBuffer,p=void 0,h=void 0,m=void 0,y=void 0;return this.source.each(function(g){g.appendToBuffer?(m?g.prepend("  + "):m=g,y=g):(m&&(h?m.prepend("buffer += "):p=!0,y.add(";"),m=y=void 0),h=!0,d||(c=!1))}),c?m?(m.prepend("return "),y.add(";")):h||this.source.push('return "";'):(l+=", buffer = "+(p?"":this.initializeBuffer()),m?(m.prepend("return buffer + "),y.add(";")):this.source.push("return buffer;")),l&&this.source.prepend("var "+l.substring(2)+(p?"":`;
`)),this.source.merge()},lookupPropertyFunctionVarDeclaration:function(){return`
      lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    }
    `.trim()},blockValue:function(l){var d=this.aliasable("container.hooks.blockHelperMissing"),c=[this.contextName(0)];this.setupHelperArgs(l,0,c);var p=this.popStack();c.splice(1,0,p),this.push(this.source.functionCall(d,"call",c))},ambiguousBlockValue:function(){var l=this.aliasable("container.hooks.blockHelperMissing"),d=[this.contextName(0)];this.setupHelperArgs("",0,d,!0),this.flushInline();var c=this.topStack();d.splice(1,0,c),this.pushSource(["if (!",this.lastHelper,") { ",c," = ",this.source.functionCall(l,"call",d),"}"])},appendContent:function(l){this.pendingContent?l=this.pendingContent+l:this.pendingLocation=this.source.currentLocation,this.pendingContent=l},append:function(){if(this.isInline())this.replaceStack(function(d){return[" != null ? ",d,' : ""']}),this.pushSource(this.appendToBuffer(this.popStack()));else{var l=this.popStack();this.pushSource(["if (",l," != null) { ",this.appendToBuffer(l,void 0,!0)," }"]),this.environment.isSimple&&this.pushSource(["else { ",this.appendToBuffer("''",void 0,!0)," }"])}},appendEscaped:function(){this.pushSource(this.appendToBuffer([this.aliasable("container.escapeExpression"),"(",this.popStack(),")"]))},getContext:function(l){this.lastContext=l},pushContext:function(){this.pushStackLiteral(this.contextName(this.lastContext))},lookupOnContext:function(l,d,c,p){var h=0;p||!this.options.compat||this.lastContext?this.pushContext():this.push(this.depthedLookup(l[h++])),this.resolvePath("context",l,h,d,c)},lookupBlockParam:function(l,d){this.useBlockParams=!0,this.push(["blockParams[",l[0],"][",l[1],"]"]),this.resolvePath("context",d,1)},lookupData:function(l,d,c){l?this.pushStackLiteral("container.data(data, "+l+")"):this.pushStackLiteral("data"),this.resolvePath("data",d,0,!0,c)},resolvePath:function(l,d,c,p,h){var m=this;if(this.options.strict||this.options.assumeObjects)this.push(function(g,v,E,b,I){var A=v.popStack(),P=E.length;for(g&&P--;b<P;b++)A=v.nameLookup(A,E[b],I);return g?[v.aliasable("container.strict"),"(",A,", ",v.quotedString(E[b]),", ",JSON.stringify(v.source.currentLocation)," )"]:A}(this.options.strict&&h,this,d,c,l));else for(var y=d.length;c<y;c++)this.replaceStack(function(g){var v=m.nameLookup(g,d[c],l);return p?[" && ",v]:[" != null ? ",v," : ",g]})},resolvePossibleLambda:function(){this.push([this.aliasable("container.lambda"),"(",this.popStack(),", ",this.contextName(0),")"])},pushStringParam:function(l,d){this.pushContext(),this.pushString(d),d!=="SubExpression"&&(typeof l=="string"?this.pushString(l):this.pushStackLiteral(l))},emptyHash:function(l){this.trackIds&&this.push("{}"),this.stringParams&&(this.push("{}"),this.push("{}")),this.pushStackLiteral(l?"undefined":"{}")},pushHash:function(){this.hash&&this.hashes.push(this.hash),this.hash={values:{},types:[],contexts:[],ids:[]}},popHash:function(){var l=this.hash;this.hash=this.hashes.pop(),this.trackIds&&this.push(this.objectLiteral(l.ids)),this.stringParams&&(this.push(this.objectLiteral(l.contexts)),this.push(this.objectLiteral(l.types))),this.push(this.objectLiteral(l.values))},pushString:function(l){this.pushStackLiteral(this.quotedString(l))},pushLiteral:function(l){this.pushStackLiteral(l)},pushProgram:function(l){l!=null?this.pushStackLiteral(this.programExpression(l)):this.pushStackLiteral(null)},registerDecorator:function(l,d){var c=this.nameLookup("decorators",d,"decorator"),p=this.setupHelperArgs(d,l);this.decorators.push(["fn = ",this.decorators.functionCall(c,"",["fn","props","container",p])," || fn;"])},invokeHelper:function(l,d,c){var p=this.popStack(),h=this.setupHelper(l,d),m=[];c&&m.push(h.name),m.push(p),this.options.strict||m.push(this.aliasable("container.hooks.helperMissing"));var y=["(",this.itemsSeparatedBy(m,"||"),")"],g=this.source.functionCall(y,"call",h.callParams);this.push(g)},itemsSeparatedBy:function(l,d){var c=[];c.push(l[0]);for(var p=1;p<l.length;p++)c.push(d,l[p]);return c},invokeKnownHelper:function(l,d){var c=this.setupHelper(l,d);this.push(this.source.functionCall(c.name,"call",c.callParams))},invokeAmbiguous:function(l,d){this.useRegister("helper");var c=this.popStack();this.emptyHash();var p=this.setupHelper(0,l,d),h=["(","(helper = ",this.lastHelper=this.nameLookup("helpers",l,"helper")," || ",c,")"];this.options.strict||(h[0]="(helper = ",h.push(" != null ? helper : ",this.aliasable("container.hooks.helperMissing"))),this.push(["(",h,p.paramsInit?["),(",p.paramsInit]:[],"),","(typeof helper === ",this.aliasable('"function"')," ? ",this.source.functionCall("helper","call",p.callParams)," : helper))"])},invokePartial:function(l,d,c){var p=[],h=this.setupParams(d,1,p);l&&(d=this.popStack(),delete h.name),c&&(h.indent=JSON.stringify(c)),h.helpers="helpers",h.partials="partials",h.decorators="container.decorators",l?p.unshift(d):p.unshift(this.nameLookup("partials",d,"partial")),this.options.compat&&(h.depths="depths"),h=this.objectLiteral(h),p.push(h),this.push(this.source.functionCall("container.invokePartial","",p))},assignToHash:function(l){var d=this.popStack(),c=void 0,p=void 0,h=void 0;this.trackIds&&(h=this.popStack()),this.stringParams&&(p=this.popStack(),c=this.popStack());var m=this.hash;c&&(m.contexts[l]=c),p&&(m.types[l]=p),h&&(m.ids[l]=h),m.values[l]=d},pushId:function(l,d,c){l==="BlockParam"?this.pushStackLiteral("blockParams["+d[0]+"].path["+d[1]+"]"+(c?" + "+JSON.stringify("."+c):"")):l==="PathExpression"?this.pushString(d):l==="SubExpression"?this.pushStackLiteral("true"):this.pushStackLiteral("null")},compiler:u,compileChildren:function(l,d){for(var c=l.children,p=void 0,h=void 0,m=0,y=c.length;m<y;m++){p=c[m],h=new this.compiler;var g=this.matchExistingProgram(p);if(g==null){this.context.programs.push("");var v=this.context.programs.length;p.index=v,p.name="program"+v,this.context.programs[v]=h.compile(p,d,this.context,!this.precompile),this.context.decorators[v]=h.decorators,this.context.environments[v]=p,this.useDepths=this.useDepths||h.useDepths,this.useBlockParams=this.useBlockParams||h.useBlockParams,p.useDepths=this.useDepths,p.useBlockParams=this.useBlockParams}else p.index=g.index,p.name="program"+g.index,this.useDepths=this.useDepths||g.useDepths,this.useBlockParams=this.useBlockParams||g.useBlockParams}},matchExistingProgram:function(l){for(var d=0,c=this.context.environments.length;d<c;d++){var p=this.context.environments[d];if(p&&p.equals(l))return p}},programExpression:function(l){var d=this.environment.children[l],c=[d.index,"data",d.blockParams];return(this.useBlockParams||this.useDepths)&&c.push("blockParams"),this.useDepths&&c.push("depths"),"container.program("+c.join(", ")+")"},useRegister:function(l){this.registers[l]||(this.registers[l]=!0,this.registers.list.push(l))},push:function(l){return l instanceof o||(l=this.source.wrap(l)),this.inlineStack.push(l),l},pushStackLiteral:function(l){this.push(new o(l))},pushSource:function(l){this.pendingContent&&(this.source.push(this.appendToBuffer(this.source.quotedString(this.pendingContent),this.pendingLocation)),this.pendingContent=void 0),l&&this.source.push(l)},replaceStack:function(l){var d=["("],c=void 0,p=void 0,h=void 0;if(!this.isInline())throw new s.default("replaceStack on non-inline");var m=this.popStack(!0);if(m instanceof o)d=["(",c=[m.value]],h=!0;else{p=!0;var y=this.incrStack();d=["((",this.push(y)," = ",m,")"],c=this.topStack()}var g=l.call(this,c);h||this.popStack(),p&&this.stackSlot--,this.push(d.concat(g,")"))},incrStack:function(){return this.stackSlot++,this.stackSlot>this.stackVars.length&&this.stackVars.push("stack"+this.stackSlot),this.topStackName()},topStackName:function(){return"stack"+this.stackSlot},flushInline:function(){var l=this.inlineStack;this.inlineStack=[];for(var d=0,c=l.length;d<c;d++){var p=l[d];if(p instanceof o)this.compileStack.push(p);else{var h=this.incrStack();this.pushSource([h," = ",p,";"]),this.compileStack.push(h)}}},isInline:function(){return this.inlineStack.length},popStack:function(l){var d=this.isInline(),c=(d?this.inlineStack:this.compileStack).pop();if(!l&&c instanceof o)return c.value;if(!d){if(!this.stackSlot)throw new s.default("Invalid stack pop");this.stackSlot--}return c},topStack:function(){var l=this.isInline()?this.inlineStack:this.compileStack,d=l[l.length-1];return d instanceof o?d.value:d},contextName:function(l){return this.useDepths&&l?"depths["+l+"]":"depth"+l},quotedString:function(l){return this.source.quotedString(l)},objectLiteral:function(l){return this.source.objectLiteral(l)},aliasable:function(l){var d=this.aliases[l];return d?(d.referenceCount++,d):((d=this.aliases[l]=this.source.wrap(l)).aliasable=!0,d.referenceCount=1,d)},setupHelper:function(l,d,c){var p=[];return{params:p,paramsInit:this.setupHelperArgs(d,l,p,c),name:this.nameLookup("helpers",d,"helper"),callParams:[this.aliasable(this.contextName(0)+" != null ? "+this.contextName(0)+" : (container.nullContext || {})")].concat(p)}},setupParams:function(l,d,c){var p={},h=[],m=[],y=[],g=!c,v=void 0;g&&(c=[]),p.name=this.quotedString(l),p.hash=this.popStack(),this.trackIds&&(p.hashIds=this.popStack()),this.stringParams&&(p.hashTypes=this.popStack(),p.hashContexts=this.popStack());var E=this.popStack(),b=this.popStack();(b||E)&&(p.fn=b||"container.noop",p.inverse=E||"container.noop");for(var I=d;I--;)v=this.popStack(),c[I]=v,this.trackIds&&(y[I]=this.popStack()),this.stringParams&&(m[I]=this.popStack(),h[I]=this.popStack());return g&&(p.args=this.source.generateArray(c)),this.trackIds&&(p.ids=this.source.generateArray(y)),this.stringParams&&(p.types=this.source.generateArray(m),p.contexts=this.source.generateArray(h)),this.options.data&&(p.data="data"),this.useBlockParams&&(p.blockParams="blockParams"),p},setupHelperArgs:function(l,d,c,p){var h=this.setupParams(l,d,c);return h.loc=JSON.stringify(this.source.currentLocation),h=this.objectLiteral(h),p?(this.useRegister("options"),c.push("options"),["options=",h]):c?(c.push(h),""):h}},function(){for(var l="break else new var case finally return void catch for switch while continue function this with default if throw delete in try do instanceof typeof abstract enum int short boolean export interface static byte extends long super char final native synchronized class float package throws const goto private transient debugger implements protected volatile double import public let yield await null true false".split(" "),d=u.RESERVED_WORDS={},c=0,p=l.length;c<p;c++)d[l[c]]=!0}(),u.isValidJavaScriptVariableName=function(l){return!u.RESERVED_WORDS[l]&&/^[a-zA-Z_$][0-9a-zA-Z_$]*$/.test(l)},t.default=u,e.exports=t.default})(Js,Js.exports);var Zh=Js.exports;(function(e,t){function n(h){return h&&h.__esModule?h:{default:h}}t.__esModule=!0;var r=n(Oh),s=n(Gl),a=on,i=Qt,o=n(Zh),u=n(ql),l=n(Vl),d=r.default.create;function c(){var h=d();return h.compile=function(m,y){return i.compile(m,y,h)},h.precompile=function(m,y){return i.precompile(m,y,h)},h.AST=s.default,h.Compiler=i.Compiler,h.JavaScriptCompiler=o.default,h.Parser=a.parser,h.parse=a.parse,h.parseWithoutProcessing=a.parseWithoutProcessing,h}var p=c();p.create=c,l.default(p),p.Visitor=u.default,p.default=p,t.default=p,e.exports=t.default})(Cs,Cs.exports);const Kh=kr(Cs.exports),Yh=`
<user>
{{{userMessage}}}
</user>
{{#if hasActions}}
<agent_actions>
{{#if filesModified}}
	<files_modified>
{{#each filesModified}}
		{{{this}}}
{{/each}}
	</files_modified>
{{/if}}
{{#if filesCreated}}
	<files_created>
{{#each filesCreated}}
		{{{this}}}
{{/each}}
	</files_created>
{{/if}}
{{#if filesDeleted}}
	<files_deleted>
{{#each filesDeleted}}
		{{{this}}}
{{/each}}
	</files_deleted>
{{/if}}
{{#if filesViewed}}
	<files_viewed>
{{#each filesViewed}}
		{{{this}}}
{{/each}}
	</files_viewed>
{{/if}}
{{#if terminalCommands}}
	<terminal_commands>
{{#each terminalCommands}}
		{{{this}}}
{{/each}}
	</terminal_commands>
{{/if}}
</agent_actions>
{{/if}}
{{#if agentResponse}}
<agent_response>
{{{agentResponse}}}
</agent_response>
{{else if wasInterrupted}}
<agent_was_interrupted/>
{{else if continues}}
<agent_continues/>
{{/if}}
`.trim(),vf=Kh.compile(Yh);function G(e,t){t=t||{},this._capacity=t.capacity,this._head=0,this._tail=0,Array.isArray(e)?this._fromArray(e):(this._capacityMask=3,this._list=new Array(4))}G.prototype.peekAt=function(e){var t=e;if(t===(0|t)){var n=this.size();if(!(t>=n||t<-n))return t<0&&(t+=n),t=this._head+t&this._capacityMask,this._list[t]}},G.prototype.get=function(e){return this.peekAt(e)},G.prototype.peek=function(){if(this._head!==this._tail)return this._list[this._head]},G.prototype.peekFront=function(){return this.peek()},G.prototype.peekBack=function(){return this.peekAt(-1)},Object.defineProperty(G.prototype,"length",{get:function(){return this.size()}}),G.prototype.size=function(){return this._head===this._tail?0:this._head<this._tail?this._tail-this._head:this._capacityMask+1-(this._head-this._tail)},G.prototype.unshift=function(e){if(arguments.length===0)return this.size();var t=this._list.length;return this._head=this._head-1+t&this._capacityMask,this._list[this._head]=e,this._tail===this._head&&this._growArray(),this._capacity&&this.size()>this._capacity&&this.pop(),this._head<this._tail?this._tail-this._head:this._capacityMask+1-(this._head-this._tail)},G.prototype.shift=function(){var e=this._head;if(e!==this._tail){var t=this._list[e];return this._list[e]=void 0,this._head=e+1&this._capacityMask,e<2&&this._tail>1e4&&this._tail<=this._list.length>>>2&&this._shrinkArray(),t}},G.prototype.push=function(e){if(arguments.length===0)return this.size();var t=this._tail;return this._list[t]=e,this._tail=t+1&this._capacityMask,this._tail===this._head&&this._growArray(),this._capacity&&this.size()>this._capacity&&this.shift(),this._head<this._tail?this._tail-this._head:this._capacityMask+1-(this._head-this._tail)},G.prototype.pop=function(){var e=this._tail;if(e!==this._head){var t=this._list.length;this._tail=e-1+t&this._capacityMask;var n=this._list[this._tail];return this._list[this._tail]=void 0,this._head<2&&e>1e4&&e<=t>>>2&&this._shrinkArray(),n}},G.prototype.removeOne=function(e){var t=e;if(t===(0|t)&&this._head!==this._tail){var n=this.size(),r=this._list.length;if(!(t>=n||t<-n)){t<0&&(t+=n),t=this._head+t&this._capacityMask;var s,a=this._list[t];if(e<n/2){for(s=e;s>0;s--)this._list[t]=this._list[t=t-1+r&this._capacityMask];this._list[t]=void 0,this._head=this._head+1+r&this._capacityMask}else{for(s=n-1-e;s>0;s--)this._list[t]=this._list[t=t+1+r&this._capacityMask];this._list[t]=void 0,this._tail=this._tail-1+r&this._capacityMask}return a}}},G.prototype.remove=function(e,t){var n,r=e,s=t;if(r===(0|r)&&this._head!==this._tail){var a=this.size(),i=this._list.length;if(!(r>=a||r<-a||t<1)){if(r<0&&(r+=a),t===1||!t)return(n=new Array(1))[0]=this.removeOne(r),n;if(r===0&&r+t>=a)return n=this.toArray(),this.clear(),n;var o;for(r+t>a&&(t=a-r),n=new Array(t),o=0;o<t;o++)n[o]=this._list[this._head+r+o&this._capacityMask];if(r=this._head+r&this._capacityMask,e+t===a){for(this._tail=this._tail-t+i&this._capacityMask,o=t;o>0;o--)this._list[r=r+1+i&this._capacityMask]=void 0;return n}if(e===0){for(this._head=this._head+t+i&this._capacityMask,o=t-1;o>0;o--)this._list[r=r+1+i&this._capacityMask]=void 0;return n}if(r<a/2){for(this._head=this._head+e+t+i&this._capacityMask,o=e;o>0;o--)this.unshift(this._list[r=r-1+i&this._capacityMask]);for(r=this._head-1+i&this._capacityMask;s>0;)this._list[r=r-1+i&this._capacityMask]=void 0,s--;e<0&&(this._tail=r)}else{for(this._tail=r,r=r+t+i&this._capacityMask,o=a-(t+e);o>0;o--)this.push(this._list[r++]);for(r=this._tail;s>0;)this._list[r=r+1+i&this._capacityMask]=void 0,s--}return this._head<2&&this._tail>1e4&&this._tail<=i>>>2&&this._shrinkArray(),n}}},G.prototype.splice=function(e,t){var n=e;if(n===(0|n)){var r=this.size();if(n<0&&(n+=r),!(n>r)){if(arguments.length>2){var s,a,i,o=arguments.length,u=this._list.length,l=2;if(!r||n<r/2){for(a=new Array(n),s=0;s<n;s++)a[s]=this._list[this._head+s&this._capacityMask];for(t===0?(i=[],n>0&&(this._head=this._head+n+u&this._capacityMask)):(i=this.remove(n,t),this._head=this._head+n+u&this._capacityMask);o>l;)this.unshift(arguments[--o]);for(s=n;s>0;s--)this.unshift(a[s-1])}else{var d=(a=new Array(r-(n+t))).length;for(s=0;s<d;s++)a[s]=this._list[this._head+n+t+s&this._capacityMask];for(t===0?(i=[],n!=r&&(this._tail=this._head+n+u&this._capacityMask)):(i=this.remove(n,t),this._tail=this._tail-d+u&this._capacityMask);l<o;)this.push(arguments[l++]);for(s=0;s<d;s++)this.push(a[s])}return i}return this.remove(n,t)}}},G.prototype.clear=function(){this._list=new Array(this._list.length),this._head=0,this._tail=0},G.prototype.isEmpty=function(){return this._head===this._tail},G.prototype.toArray=function(){return this._copyArray(!1)},G.prototype._fromArray=function(e){var t=e.length,n=this._nextPowerOf2(t);this._list=new Array(n),this._capacityMask=n-1,this._tail=t;for(var r=0;r<t;r++)this._list[r]=e[r]},G.prototype._copyArray=function(e,t){var n=this._list,r=n.length,s=this.length;if((t|=s)==s&&this._head<this._tail)return this._list.slice(this._head,this._tail);var a,i=new Array(t),o=0;if(e||this._head>this._tail){for(a=this._head;a<r;a++)i[o++]=n[a];for(a=0;a<this._tail;a++)i[o++]=n[a]}else for(a=this._head;a<this._tail;a++)i[o++]=n[a];return i},G.prototype._growArray=function(){if(this._head!=0){var e=this._copyArray(!0,this._list.length<<1);this._tail=this._list.length,this._head=0,this._list=e}else this._tail=this._list.length,this._list.length<<=1;this._capacityMask=this._capacityMask<<1|1},G.prototype._shrinkArray=function(){this._list.length>>>=1,this._capacityMask>>>=1},G.prototype._nextPowerOf2=function(e){var t=1<<Math.log(e)/Math.log(2)+1;return Math.max(t,4)};const bf=kr(G);function Sf(e,t){const n=e.customPersonalityPrompts;if(n)switch(t){case Se.DEFAULT:if(n.agent&&n.agent.trim()!=="")return n.agent;break;case Se.PROTOTYPER:if(n.prototyper&&n.prototyper.trim()!=="")return n.prototyper;break;case Se.BRAINSTORM:if(n.brainstorm&&n.brainstorm.trim()!=="")return n.brainstorm;break;case Se.REVIEWER:if(n.reviewer&&n.reviewer.trim()!=="")return n.reviewer}return Hh[t]}const Hh={[Se.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[Se.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[Se.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[Se.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};var Pe=(e=>(e.getRulesListRequest="get-rules-list-request",e.getRulesListResponse="get-rules-list-response",e.createRule="create-rule",e.createRuleResponse="create-rule-response",e.openRule="open-rule",e.openGuidelines="open-guidelines",e.deleteRule="delete-rule",e.updateRuleFile="update-rule-file",e.updateRuleFileResponse="update-rule-file-response",e.getWorkspaceRoot="get-workspace-root",e.getWorkspaceRootResponse="get-workspace-root-response",e.autoImportRules="auto-import-rules",e.autoImportRulesOptionsResponse="auto-import-rules-options-response",e.autoImportRulesSelectionRequest="auto-import-rules-selection-request",e.autoImportRulesResponse="auto-import-rules-response",e.processSelectedPathsRequest="process-selected-paths-request",e.processSelectedPathsResponse="process-selected-paths-response",e))(Pe||{}),Jn=(e=>(e.loadConversationToolUseStatesRequest="load-conversation-tooluse-states-request",e.loadConversationToolUseStatesResponse="load-conversation-tooluse-states-response",e.saveToolUseStatesRequest="save-tooluse-states-request",e.saveToolUseStatesResponse="save-tooluse-states-response",e.deleteConversationToolUseStatesRequest="delete-conversation-tooluse-states-request",e.deleteConversationToolUseStatesResponse="delete-conversation-tooluse-states-response",e))(Jn||{});const Ef={default:{isClassifyAndDistill:!0,promptKey:"classify_and_distill_prompt"},v1_success:{isClassifyAndDistill:!0,promptKey:"classify_and_distill_success_prompt"},v2_scope:{isClassifyAndDistill:!0,promptKey:"classify_and_distill_scope_prompt"}};var zl=(e=>(e.memoryCreated="memory-created",e.memoryCreatedResponse="memory-created-response",e.memoryProcessed="memory-processed",e.getMemoriesByState="get-memories-by-state",e.getMemoriesByStateResponse="get-memories-by-state-response",e.updateMemoryState="update-memory-state",e.updateMemoryStateResponse="update-memory-state-response",e.flushPendingMemories="flush-pending-memories",e.flushPendingMemoriesResponse="flush-pending-memories-response",e))(zl||{});class Tf{constructor(t,n,r){f(this,"_taskClient");f(this,"getChatInitData",async()=>{const t=await this._asyncMsgSender.send({type:k.chatLoaded},3e4);if(t.data.enableDebugFeatures)try{console.log("Running hello world test...");const n=await async function(r){return(await Ic(Mc,new hl({sendMessage:a=>{r.postMessage(a)},onReceiveMessage:a=>{const i=o=>{a(o.data)};return window.addEventListener("message",i),()=>{window.removeEventListener("message",i)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",n)}catch(n){console.error("Hello world error:",n)}return t.data});f(this,"reportWebviewClientEvent",t=>{this._asyncMsgSender.send({type:k.reportWebviewClientMetric,data:{webviewName:fl.chat,client_metric:t,value:1}})});f(this,"trackEventWithTypes",(t,n)=>{this._asyncMsgSender.sendToSidecar({type:vs.trackAnalyticsEvent,data:{eventName:t,properties:n}},5e3)});f(this,"trackExperimentViewed",(t,n,r)=>{this._asyncMsgSender.sendToSidecar({type:vs.trackExperimentViewedEvent,data:{experimentName:t,treatment:n,properties:r}},5e3)});f(this,"reportAgentSessionEvent",t=>{this._asyncMsgSender.sendToSidecar({type:W.reportAgentSessionEvent,data:t})});f(this,"reportAgentRequestEvent",t=>{this._asyncMsgSender.sendToSidecar({type:W.reportAgentRequestEvent,data:t})});f(this,"getSuggestions",async(t,n=!1)=>{const r={rootPath:"",relPath:t},s=this.findFiles(r,6),a=this.findRecentlyOpenedFiles(r,6),i=this.findFolders(r,3),o=this.findExternalSources(t,n),u=this._flags.enableRules?this.findRules(t,6):Promise.resolve([]),[l,d,c,p,h]=await Promise.all([Ht(s,[]),Ht(a,[]),Ht(i,[]),Ht(o,[]),Ht(u,[])]),m=(g,v)=>({...zd(g),[v]:g}),y=[...l.map(g=>m(g,"file")),...c.map(g=>m(g,"folder")),...d.map(g=>m(g,"recentFile")),...p.map(g=>({label:g.name,name:g.name,id:g.id,externalSource:g})),...h.map(g=>({...Xd(g),rule:g}))];if(this._flags.enablePersonalities){const g=this.getPersonalities(t);g.length>0&&y.push(...g)}return y});f(this,"getPersonalities",t=>{if(!this._flags.enablePersonalities)return[];if(t==="")return ji;const n=t.toLowerCase();return ji.filter(r=>{const s=r.personality.description.toLowerCase(),a=r.label.toLowerCase();return s.includes(n)||a.includes(n)})});f(this,"sendAction",t=>{this._host.postMessage({type:k.mainPanelPerformAction,data:t})});f(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:k.showAugmentPanel})});f(this,"showNotification",t=>{this._host.postMessage({type:k.showNotification,data:t})});f(this,"openConfirmationModal",async t=>(await this._asyncMsgSender.send({type:k.openConfirmationModal,data:t},1e9)).data.ok);f(this,"clearMetadataFor",t=>{this._host.postMessage({type:k.chatClearMetadata,data:t})});f(this,"resolvePath",async(t,n=void 0)=>{const r=await this._asyncMsgSender.send({type:k.resolveFileRequest,data:{...t,exactMatch:!0,maxResults:1,searchScope:n}},5e3);if(r.data)return r.data});f(this,"resolveSymbols",async(t,n)=>(await this._asyncMsgSender.send({type:k.findSymbolRequest,data:{query:t,searchScope:n}},3e4)).data);f(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:k.getDiagnosticsRequest},1e3)).data);f(this,"findFiles",async(t,n=12)=>(await this._asyncMsgSender.send({type:k.findFileRequest,data:{...t,maxResults:n}},5e3)).data);f(this,"findFolders",async(t,n=12)=>(await this._asyncMsgSender.send({type:k.findFolderRequest,data:{...t,maxResults:n}},5e3)).data);f(this,"findRecentlyOpenedFiles",async(t,n=12)=>(await this._asyncMsgSender.send({type:k.findRecentlyOpenedFilesRequest,data:{...t,maxResults:n}},5e3)).data);f(this,"findExternalSources",async(t,n=!1)=>this._flags.enableExternalSourcesInChat?n?[]:(await this._asyncMsgSender.send({type:k.findExternalSourcesRequest,data:{query:t,source_types:[]}},5e3)).data.sources??[]:[]);f(this,"findRules",async(t,n=12)=>(await this._asyncMsgSender.sendToSidecar({type:Pe.getRulesListRequest,data:{query:t,maxResults:n}})).data.rules);f(this,"openFile",t=>{this._host.postMessage({type:k.openFile,data:t})});f(this,"saveFile",t=>this._host.postMessage({type:k.saveFile,data:t}));f(this,"loadFile",t=>this._host.postMessage({type:k.loadFile,data:t}));f(this,"openMemoriesFile",()=>{this._host.postMessage({type:k.openMemoriesFile})});f(this,"canShowTerminal",async(t,n)=>{try{return(await this._asyncMsgSender.send({type:k.canShowTerminal,data:{terminalId:t,command:n}},5e3)).data.canShow}catch(r){return console.error("Failed to check if terminal can be shown:",r),!1}});f(this,"showTerminal",async(t,n)=>{try{return(await this._asyncMsgSender.send({type:k.showTerminal,data:{terminalId:t,command:n}},5e3)).data.success}catch(r){return console.error("Failed to show terminal:",r),!1}});f(this,"createFile",(t,n)=>{this._host.postMessage({type:k.chatCreateFile,data:{code:t,relPath:n}})});f(this,"openScratchFile",async(t,n="shellscript")=>{await this._asyncMsgSender.send({type:k.openScratchFileRequest,data:{content:t,language:n}},1e4)});f(this,"resolveWorkspaceFileChunk",async t=>{try{return(await this._asyncMsgSender.send({type:k.resolveWorkspaceFileChunkRequest,data:t},5e3)).data}catch{return}});f(this,"smartPaste",t=>{this._host.postMessage({type:k.chatSmartPaste,data:t})});f(this,"getHydratedTask",async t=>this._taskClient.getHydratedTask(t));f(this,"updateHydratedTask",async(t,n)=>this._taskClient.updateHydratedTask(t,n));f(this,"setCurrentRootTaskUuid",t=>{this._taskClient.setCurrentRootTaskUuid(t)});f(this,"createTask",async(t,n,r)=>this._taskClient.createTask(t,n,r));f(this,"updateTask",async(t,n,r)=>this._taskClient.updateTask(t,n,r));f(this,"saveChat",async(t,n,r)=>this._asyncMsgSender.send({type:k.saveChat,data:{conversationId:t,chatHistory:n,title:r}},5e3));f(this,"updateUserGuidelines",t=>{this._host.postMessage({type:k.updateUserGuidelines,data:t})});f(this,"updateWorkspaceGuidelines",t=>{this._host.postMessage({type:k.updateWorkspaceGuidelines,data:t})});f(this,"openSettingsPage",t=>{this._host.postMessage({type:k.openSettingsPage,data:t})});f(this,"_activeRetryStreams",new Map);f(this,"cancelChatStream",async t=>{var n;(n=this._activeRetryStreams.get(t))==null||n.cancel(),await this._asyncMsgSender.send({type:k.chatUserCancel,data:{requestId:t}},1e4)});f(this,"sendUserRating",async(t,n,r,s="")=>{const a={requestId:t,rating:r,note:s,mode:n},i={type:k.chatRating,data:a};return(await this._asyncMsgSender.send(i,3e4)).data});f(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:k.usedChat})});f(this,"createProject",t=>{this._host.postMessage({type:k.mainPanelCreateProject,data:{name:t}})});f(this,"openProjectFolder",()=>{this._host.postMessage({type:k.mainPanelPerformAction,data:"open-folder"})});f(this,"closeProjectFolder",()=>{this._host.postMessage({type:k.mainPanelPerformAction,data:"close-folder"})});f(this,"cloneRepository",()=>{this._host.postMessage({type:k.mainPanelPerformAction,data:"clone-repository"})});f(this,"grantSyncPermission",()=>{this._host.postMessage({type:k.mainPanelPerformAction,data:"grant-sync-permission"})});f(this,"startRemoteMCPAuth",t=>{this._host.postMessage({type:k.startRemoteMCPAuth,data:{name:t}})});f(this,"callTool",async(t,n,r,s,a,i)=>{const o={type:k.callTool,data:{chatRequestId:t,toolUseId:n,name:r,input:s,chatHistory:a,conversationId:i}};return(await this._asyncMsgSender.send(o,0)).data});f(this,"cancelToolRun",async(t,n)=>{const r={type:k.cancelToolRun,data:{requestId:t,toolUseId:n}};await this._asyncMsgSender.send(r,0)});f(this,"checkSafe",async t=>{const n={type:Wn.checkToolCallSafeRequest,data:t};return(await this._asyncMsgSender.sendToSidecar(n,0)).data});f(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:Wn.closeAllToolProcesses},0)});f(this,"getToolIdentifier",async t=>{const n={type:Wn.getToolIdentifierRequest,data:{toolName:t}};return(await this._asyncMsgSender.sendToSidecar(n,0)).data});f(this,"getChatMode",async()=>{const t={type:W.getChatModeRequest};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.chatMode});f(this,"setChatMode",t=>{this._asyncMsgSender.send({type:k.chatModeChanged,data:{mode:t}})});f(this,"getAgentEditList",async(t,n)=>{const r={type:W.getEditListRequest,data:{fromTimestamp:t,toTimestamp:n}};return(await this._asyncMsgSender.sendToSidecar(r,3e4)).data});f(this,"hasChangesSince",async t=>{const n={type:W.getEditListRequest,data:{fromTimestamp:t,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.edits.filter(r=>{var s,a;return((s=r.changesSummary)==null?void 0:s.totalAddedLines)||((a=r.changesSummary)==null?void 0:a.totalRemovedLines)}).length>0});f(this,"getToolCallCheckpoint",async t=>{const n={type:k.getToolCallCheckpoint,data:{requestId:t}};return(await this._asyncMsgSender.send(n,3e4)).data.checkpointNumber});f(this,"setCurrentConversation",t=>{this._asyncMsgSender.sendToSidecar({type:W.setCurrentConversation,data:{conversationId:t}},5e3)});f(this,"migrateConversationId",async(t,n)=>{await this._asyncMsgSender.sendToSidecar({type:W.migrateConversationId,data:{oldConversationId:t,newConversationId:n}},3e4)});f(this,"showAgentReview",(t,n,r,s=!0,a)=>{this._asyncMsgSender.sendToSidecar({type:W.chatReviewAgentFile,data:{qualifiedPathName:t,fromTimestamp:n,toTimestamp:r,retainFocus:s,useNativeDiffIfAvailable:a}})});f(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:W.chatAgentEditAcceptAll}),!0));f(this,"revertToTimestamp",async(t,n)=>(await this._asyncMsgSender.sendToSidecar({type:W.revertToTimestamp,data:{timestamp:t,qualifiedPathNames:n}}),!0));f(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:k.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);f(this,"getAgentEditChangesByRequestId",async t=>{const n={type:W.getEditChangesByRequestIdRequest,data:{requestId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});f(this,"getAgentEditContentsByRequestId",async t=>{const n={type:W.getAgentEditContentsByRequestId,data:{requestId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});f(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:k.triggerInitialOrientation})});f(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:k.getWorkspaceInfoRequest},5e3)).data}catch(t){return console.error("Error getting workspace info:",t),{}}});f(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:k.toggleCollapseUnchangedRegions})});f(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:k.checkAgentAutoModeApproval},5e3)).data);f(this,"setAgentAutoModeApproved",async t=>{await this._asyncMsgSender.send({type:k.setAgentAutoModeApproved,data:t},5e3)});f(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:W.checkHasEverUsedAgent},5e3)).data);f(this,"setHasEverUsedAgent",async t=>{await this._asyncMsgSender.sendToSidecar({type:W.setHasEverUsedAgent,data:t},5e3)});f(this,"checkHasEverUsedRemoteAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:W.checkHasEverUsedRemoteAgent},5e3)).data);f(this,"setHasEverUsedRemoteAgent",async t=>{await this._asyncMsgSender.sendToSidecar({type:W.setHasEverUsedRemoteAgent,data:t},5e3)});f(this,"getChatRequestIdeState",async()=>{const t={type:k.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(t,3e4)).data});f(this,"reportError",t=>{this._host.postMessage({type:k.reportError,data:t})});f(this,"sendMemoryCreated",async t=>{await this._asyncMsgSender.sendToSidecar(t,5e3)});f(this,"sendFlushPendingMemories",async(t=3e4)=>{try{const n={type:zl.flushPendingMemories,data:{}};await this._asyncMsgSender.sendToSidecar(n,t)}catch{return}});f(this,"sendGitMessage",async t=>await this._asyncMsgSender.sendToSidecar(t,3e4));this._host=t,this._asyncMsgSender=n,this._flags=r,this._taskClient=new Dc(n)}async*generateCommitMessage(){const t={type:k.generateCommitMessage},n=this._asyncMsgSender.stream(t,3e4,6e4);yield*ls(n,()=>{},this._flags.retryChatStreamTimeouts,this.trackEventWithTypes)}async*sendInstructionMessage(t,n){const r={instruction:t.request_message??"",selectedCodeDetails:n,requestId:t.request_id},s={type:k.chatInstructionMessage,data:r},a=this._asyncMsgSender.stream(s,3e4,6e4);yield*async function*(i){let o;try{for await(const u of i)o=u.data.requestId,yield{request_id:o,response_text:u.data.text,seen_state:De.unseen,status:pe.sent};yield{request_id:o,seen_state:De.unseen,status:pe.success}}catch(u){console.error("Error in chat instruction model reply stream:",u),yield{request_id:o,seen_state:De.unseen,status:pe.failed}}}(a)}async openGuidelines(t){this._host.postMessage({type:k.openGuidelines,data:t})}async*getExistingChatStream(t,n,r){const s=r==null?void 0:r.flags.enablePreferenceCollection,a=s?1e9:6e4,i=s?1e9:3e5,o={type:k.chatGetStreamRequest,data:{requestId:t,lastChunkId:n}},u=this._asyncMsgSender.stream(o,a,i);yield*ls(u,this.reportError,this._flags.retryChatStreamTimeouts,this.trackEventWithTypes)}async*startChatStream(t,n){const r=n==null?void 0:n.flags.enablePreferenceCollection,s=r?1e9:1e5,a=r?1e9:3e5,i={type:k.chatUserMessage,data:t},o=this._asyncMsgSender.stream(i,s,a);yield*ls(o,this.reportError,this._flags.retryChatStreamTimeouts,this.trackEventWithTypes)}async checkToolExists(t){return(await this._asyncMsgSender.send({type:k.checkToolExists,toolName:t},0)).exists}async saveImage(t,n){const r=Ai(await Vr(t)),s=n??`${await Ci(await Gr(r))}.${t.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:k.chatSaveImageRequest,data:{filename:s,data:r}},1e4)).data}async saveAttachment(t,n){const r=Ai(await Vr(t)),s=n??`${await Ci(await Gr(r))}.${t.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:k.chatSaveAttachmentRequest,data:{filename:s,data:r}},1e4)).data}async loadImage(t){const n=await this._asyncMsgSender.send({type:k.chatLoadImageRequest,data:t},1e4),r=n.data?await Gr(n.data):void 0;if(!r)return;let s="application/octet-stream";const a=t.split(".").at(-1);a==="png"?s="image/png":a!=="jpg"&&a!=="jpeg"||(s="image/jpeg");const i=new File([r],t,{type:s});return await Vr(i)}async deleteImage(t){await this._asyncMsgSender.send({type:k.chatDeleteImageRequest,data:t},1e4)}async*startChatStreamWithRetry(t,n,r){const s=new Lc(t,n,(a,i)=>this.startChatStream(a,i),(r==null?void 0:r.maxRetries)??5,4e3,r==null?void 0:r.flags);this._activeRetryStreams.set(t,s);try{yield*s.getStream()}finally{this._activeRetryStreams.delete(t)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:k.getSubscriptionInfo},5e3)}async loadExchanges(t,n){if(n.length===0)return[];const r={type:zn.loadExchangesByUuidsRequest,data:{conversationId:t,uuids:n}};return(await this._asyncMsgSender.sendToSidecar(r,3e4)).data.exchanges}async saveExchanges(t,n){if(n.length===0)return;const r={type:zn.saveExchangesRequest,data:{conversationId:t,exchanges:n}};await this._asyncMsgSender.sendToSidecar(r,3e4)}async deleteConversationExchanges(t){const n={type:zn.deleteConversationExchangesRequest,data:{conversationId:t}};await this._asyncMsgSender.sendToSidecar(n,3e4)}async loadConversationToolUseStates(t){const n={type:Jn.loadConversationToolUseStatesRequest,data:{conversationId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.toolUseStates}async saveToolUseStates(t,n){if(Object.keys(n).length===0)return;const r={type:Jn.saveToolUseStatesRequest,data:{conversationId:t,toolUseStates:n}};await this._asyncMsgSender.sendToSidecar(r,3e4)}async deleteConversationToolUseStates(t){const n={type:Jn.deleteConversationToolUseStatesRequest,data:{conversationId:t}};await this._asyncMsgSender.sendToSidecar(n,3e4)}}async function*ls(e,t=()=>{},n,r){let s;try{for await(const a of e){if(s=a.data.requestId,a.data.error)return console.error("Error in chat model reply stream:",a.data.error.displayErrorMessage),r==null||r(xc.MESSAGE_SEND_ERROR_DISPLAYED,{errorMessagePreview:a.data.error.displayErrorMessage.substring(0,100),requestId:s}),yield{request_id:s,seen_state:De.unseen,status:pe.failed,display_error_message:a.data.error.displayErrorMessage,isRetriable:a.data.error.isRetriable,shouldBackoff:a.data.error.shouldBackoff};const i={request_id:s,response_text:a.data.text,workspace_file_chunks:a.data.workspaceFileChunks,structured_output_nodes:Wh(a.data.nodes),seen_state:De.unseen,status:pe.sent,lastChunkId:a.data.chunkId};a.data.stop_reason!=null&&(i.stop_reason=a.data.stop_reason),yield i}yield{request_id:s,seen_state:De.unseen,status:pe.success}}catch(a){let i,o;if(t({originalRequestId:s||"",sanitizedMessage:a instanceof Error?a.message:String(a),stackTrace:a instanceof Error&&a.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),a instanceof nu&&n)switch(a.name){case"MessageTimeout":i=!0,o=!1;break;case"StreamTimeout":case"InvalidResponse":i=!1}console.error("Unexpected error in chat model reply stream:",a),yield{request_id:s,seen_state:De.unseen,status:pe.failed,isRetriable:i,shouldBackoff:o}}}async function Ht(e,t){try{return await e}catch(n){return console.warn(`Error while resolving promise: ${n}`),t}}function Wh(e){if(!e)return e;let t=!1;return e.filter(n=>n.type!==su.TOOL_USE||!t&&(t=!0,!0))}const zh={CONTROL:"control",OFF:"off"},Xl={...zh,ENABLED:"enabled"},wf={SUBSCRIPTION_BANNER_DISMISSIBILITY:{NAME:"subscription_banner_dismissibility",TREATMENTS:{IS_NON_DISMISSIBLE:Xl.ENABLED}}},kf=15,Nf=1e3,Xh=25e4,If=2e4;class Cf{constructor(t){f(this,"_enableEditableHistory",!1);f(this,"_enablePreferenceCollection",!1);f(this,"_enableRetrievalDataCollection",!1);f(this,"_enableDebugFeatures",!1);f(this,"_enableConversationDebugUtils",!1);f(this,"_enableRichTextHistory",!1);f(this,"_enableAgentSwarmMode",!1);f(this,"_modelDisplayNameToId",{});f(this,"_fullFeatured",!0);f(this,"_enableExternalSourcesInChat",!1);f(this,"_smallSyncThreshold",15);f(this,"_bigSyncThreshold",1e3);f(this,"_enableSmartPaste",!1);f(this,"_enableDirectApply",!1);f(this,"_summaryTitles",!1);f(this,"_suggestedEditsAvailable",!1);f(this,"_enableShareService",!1);f(this,"_maxTrackableFileCount",Xh);f(this,"_enableDesignSystemRichTextEditor",!1);f(this,"_enableSources",!1);f(this,"_enableChatMermaidDiagrams",!1);f(this,"_smartPastePrecomputeMode",ru.visibleHover);f(this,"_useNewThreadsMenu",!1);f(this,"_enableChatMermaidDiagramsMinVersion",!1);f(this,"_enablePromptEnhancer",!1);f(this,"_idleNewSessionNotificationTimeoutMs");f(this,"_idleNewSessionMessageTimeoutMs");f(this,"_enableChatMultimodal",!1);f(this,"_enableAgentMode",!1);f(this,"_enableAgentAutoMode",!1);f(this,"_enableRichCheckpointInfo",!1);f(this,"_agentMemoriesFilePathName");f(this,"_enableEnhancedDehydrationMode",!1);f(this,"_conversationHistorySizeThresholdBytes",44040192);f(this,"_userTier","unknown");f(this,"_eloModelConfiguration",{highPriorityModels:[],regularBattleModels:[],highPriorityThreshold:.5});f(this,"_truncateChatHistory",!1);f(this,"_enableBackgroundAgents",!1);f(this,"_enableNewThreadsList",!1);f(this,"_customPersonalityPrompts",{});f(this,"_enablePersonalities",!1);f(this,"_enableRules",!1);f(this,"_memoryClassificationOnFirstToken",!1);f(this,"_enableGenerateCommitMessage",!1);f(this,"_modelRegistry",{});f(this,"_modelInfoRegistry",{});f(this,"_agentChatModel","");f(this,"_enableModelRegistry",!1);f(this,"_enableTaskList",!1);f(this,"_clientAnnouncement","");f(this,"_useHistorySummary",!1);f(this,"_historySummaryParams","");f(this,"_enableExchangeStorage",!1);f(this,"_enableToolUseStateStorage",!1);f(this,"_retryChatStreamTimeouts",!1);f(this,"_enableCommitIndexing",!1);f(this,"_enableMemoryRetrieval",!1);f(this,"_enableAgentTabs",!1);f(this,"_isVscodeVersionOutdated",!1);f(this,"_vscodeMinVersion","");f(this,"_enableGroupedTools",!1);f(this,"_remoteAgentsResumeHintAvailableTtlDays",0);f(this,"_enableParallelTools",!1);f(this,"_enableAgentGitTracker",!1);f(this,"_enableLucideIcons",!1);f(this,"_memoriesParams",{});f(this,"_subscriptionBannerDismissibility",Xl.OFF);f(this,"_showThinkingSummary",!1);f(this,"_subscribers",new Set);f(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));f(this,"update",t=>{if(this._enableEditableHistory=t.enableEditableHistory??this._enableEditableHistory,this._enablePreferenceCollection=t.enablePreferenceCollection??this._enablePreferenceCollection,this._enableRetrievalDataCollection=t.enableRetrievalDataCollection??this._enableRetrievalDataCollection,this._enableDebugFeatures=t.enableDebugFeatures??this._enableDebugFeatures,this._enableConversationDebugUtils=t.enableConversationDebugUtils??this._enableConversationDebugUtils,this._enableRichTextHistory=t.enableRichTextHistory??this._enableRichTextHistory,this._enableAgentSwarmMode=t.enableAgentSwarmMode??this._enableAgentSwarmMode,this._modelDisplayNameToId={...t.modelDisplayNameToId},this._fullFeatured=t.fullFeatured??this._fullFeatured,this._enableExternalSourcesInChat=t.enableExternalSourcesInChat??this._enableExternalSourcesInChat,this._smallSyncThreshold=t.smallSyncThreshold??this._smallSyncThreshold,this._bigSyncThreshold=t.bigSyncThreshold??this._bigSyncThreshold,this._enableSmartPaste=t.enableSmartPaste??this._enableSmartPaste,this._enableDirectApply=t.enableDirectApply??this._enableDirectApply,this._summaryTitles=t.summaryTitles??this._summaryTitles,this._suggestedEditsAvailable=t.suggestedEditsAvailable??this._suggestedEditsAvailable,this._enableShareService=t.enableShareService??this._enableShareService,this._maxTrackableFileCount=t.maxTrackableFileCount??this._maxTrackableFileCount,this._enableDesignSystemRichTextEditor=t.enableDesignSystemRichTextEditor??this._enableDesignSystemRichTextEditor,this._enableSources=t.enableSources??this._enableSources,this._enableChatMermaidDiagrams=t.enableChatMermaidDiagrams??this._enableChatMermaidDiagrams,this._smartPastePrecomputeMode=t.smartPastePrecomputeMode??this._smartPastePrecomputeMode,this._useNewThreadsMenu=t.useNewThreadsMenu??this._useNewThreadsMenu,this._enableChatMermaidDiagramsMinVersion=t.enableChatMermaidDiagramsMinVersion??this._enableChatMermaidDiagramsMinVersion,this._enablePromptEnhancer=t.enablePromptEnhancer??this._enablePromptEnhancer,this._idleNewSessionMessageTimeoutMs=t.idleNewSessionMessageTimeoutMs??(t.enableDebugFeatures?this._idleNewSessionMessageTimeoutMs??3e5:this._idleNewSessionMessageTimeoutMs),this._idleNewSessionNotificationTimeoutMs=t.idleNewSessionNotificationTimeoutMs??0,this._enableChatMultimodal=t.enableChatMultimodal??this._enableChatMultimodal,this._enableAgentMode=t.enableAgentMode??this._enableAgentMode,this._enableAgentAutoMode=t.enableAgentAutoMode??this._enableAgentAutoMode,this._enableRichCheckpointInfo=t.enableRichCheckpointInfo??this._enableRichCheckpointInfo,this._agentMemoriesFilePathName=t.agentMemoriesFilePathName??this._agentMemoriesFilePathName,this._conversationHistorySizeThresholdBytes=t.conversationHistorySizeThresholdBytes??this._conversationHistorySizeThresholdBytes,this._userTier=t.userTier??this._userTier,this._eloModelConfiguration=t.eloModelConfiguration??this._eloModelConfiguration,this._truncateChatHistory=t.truncateChatHistory??this._truncateChatHistory,this._enableBackgroundAgents=t.enableBackgroundAgents??this._enableBackgroundAgents,this._enableNewThreadsList=t.enableNewThreadsList??this._enableNewThreadsList,this._customPersonalityPrompts=t.customPersonalityPrompts??this._customPersonalityPrompts,this._enablePersonalities=t.enablePersonalities??this._enablePersonalities,this._enableRules=t.enableRules??this._enableRules,this._memoryClassificationOnFirstToken=t.memoryClassificationOnFirstToken??this._memoryClassificationOnFirstToken,this._enableGenerateCommitMessage=t.enableGenerateCommitMessage??this._enableGenerateCommitMessage,this._modelRegistry=t.modelRegistry??this._modelRegistry,t.modelInfoRegistry){const n={};for(const[r,s]of Object.entries(t.modelInfoRegistry))if(typeof s=="string")try{n[r]=JSON.parse(s)}catch(a){console.error(`Failed to parse modelInfoRegistry entry for key "${r}"`,a)}else typeof s=="object"&&s!==null&&(n[r]=s);this._modelInfoRegistry=n}this._enableModelRegistry=t.enableModelRegistry??this._enableModelRegistry,this._agentChatModel=t.agentChatModel??this._agentChatModel,this._enableTaskList=t.enableTaskList??this._enableTaskList,this._clientAnnouncement=t.clientAnnouncement??this._clientAnnouncement,this._useHistorySummary=t.useHistorySummary??this._useHistorySummary,this._historySummaryParams=t.historySummaryParams??this._historySummaryParams,this._enableExchangeStorage=t.enableExchangeStorage??this._enableExchangeStorage,this._retryChatStreamTimeouts=t.retryChatStreamTimeouts??this._retryChatStreamTimeouts,this._enableCommitIndexing=t.enableCommitIndexing??this._enableCommitIndexing,this._enableMemoryRetrieval=t.enableMemoryRetrieval??this._enableMemoryRetrieval,this._enableAgentTabs=t.enableAgentTabs??this._enableAgentTabs,this._isVscodeVersionOutdated=t.isVscodeVersionOutdated??this._isVscodeVersionOutdated,this._vscodeMinVersion=t.vscodeMinVersion??this._vscodeMinVersion,this._enableGroupedTools=t.enableGroupedTools??this._enableGroupedTools,this._remoteAgentsResumeHintAvailableTtlDays=t.remoteAgentsResumeHintAvailableTtlDays??this._remoteAgentsResumeHintAvailableTtlDays,this._enableToolUseStateStorage=t.enableToolUseStateStorage??this._enableToolUseStateStorage,this._enableParallelTools=t.enableParallelTools??this._enableParallelTools,this._enableAgentGitTracker=t.enableAgentGitTracker??this._enableAgentGitTracker,this._enableLucideIcons=t.enableLucideIcons??this._enableLucideIcons,this._memoriesParams=t.memoriesParams??this._memoriesParams,this._subscriptionBannerDismissibility=t.subscriptionBannerDismissibility??this._subscriptionBannerDismissibility,this._enableEnhancedDehydrationMode=t.enableEnhancedDehydrationMode??this._enableEnhancedDehydrationMode,this._showThinkingSummary=t.showThinkingSummary??this._showThinkingSummary,this._subscribers.forEach(n=>n(this))});f(this,"isModelIdValid",t=>t!==void 0&&(Object.values(this._modelDisplayNameToId).includes(t)||Object.values(this._modelRegistry).includes(t??"")||Object.keys(this._modelInfoRegistry).includes(t??"")));f(this,"getModelDisplayName",t=>{if(t!==void 0)return Object.keys(this._modelDisplayNameToId).find(n=>this._modelDisplayNameToId[n]===t)});t&&this.update(t)}get enableEditableHistory(){return this._fullFeatured&&(this._enableEditableHistory||this._enableDebugFeatures)}get enablePreferenceCollection(){return this._enablePreferenceCollection}get enableRetrievalDataCollection(){return this._enableRetrievalDataCollection}get enableDebugFeatures(){return this._enableDebugFeatures}get enableConversationDebugUtils(){return this._enableConversationDebugUtils||this._enableDebugFeatures}get enableGenerateCommitMessage(){return this._enableGenerateCommitMessage}get enableRichTextHistory(){return this._enableRichTextHistory||this._enableDebugFeatures}get enableAgentSwarmMode(){return this._enableAgentSwarmMode}get modelDisplayNameToId(){return this._modelDisplayNameToId}get orderedModelDisplayNames(){return Object.keys(this._modelDisplayNameToId).sort((t,n)=>{const r=t.toLowerCase(),s=n.toLowerCase();return r==="default"&&s!=="default"?-1:s==="default"&&r!=="default"?1:t.localeCompare(n)})}get fullFeatured(){return this._fullFeatured}get enableExternalSourcesInChat(){return this._enableExternalSourcesInChat}get smallSyncThreshold(){return this._smallSyncThreshold}get bigSyncThreshold(){return this._bigSyncThreshold}get enableSmartPaste(){return this._enableDebugFeatures||this._enableSmartPaste}get enableDirectApply(){return this._enableDirectApply||this._enableDebugFeatures}get enableShareService(){return this._enableShareService}get summaryTitles(){return this._summaryTitles}get suggestedEditsAvailable(){return this._suggestedEditsAvailable}get maxTrackableFileCount(){return this._maxTrackableFileCount}get enableSources(){return this._enableDebugFeatures||this._enableSources}get enableChatMermaidDiagrams(){return this._enableDebugFeatures||this._enableChatMermaidDiagrams}get smartPastePrecomputeMode(){return this._smartPastePrecomputeMode}get useNewThreadsMenu(){return this._useNewThreadsMenu}get enableChatMermaidDiagramsMinVersion(){return this._enableChatMermaidDiagramsMinVersion}get enablePromptEnhancer(){return this._enablePromptEnhancer}get enableDesignSystemRichTextEditor(){return this._enableDesignSystemRichTextEditor}get idleNewSessionNotificationTimeoutMs(){return this._idleNewSessionNotificationTimeoutMs??0}get idleNewSessionMessageTimeoutMs(){return this._idleNewSessionMessageTimeoutMs??0}get enableChatMultimodal(){return this._enableChatMultimodal}get enableAgentMode(){return this._enableAgentMode}get enableAgentAutoMode(){return this._enableAgentAutoMode}get enableRichCheckpointInfo(){return this._enableRichCheckpointInfo}get agentMemoriesFilePathName(){return this._agentMemoriesFilePathName}get conversationHistorySizeThresholdBytes(){return this._conversationHistorySizeThresholdBytes}get userTier(){return this._userTier}get eloModelConfiguration(){return this._eloModelConfiguration}get truncateChatHistory(){return this._truncateChatHistory}get enableBackgroundAgents(){return this._enableBackgroundAgents}get enableNewThreadsList(){return this._enableNewThreadsList}get customPersonalityPrompts(){return this._customPersonalityPrompts}get enablePersonalities(){return this._enablePersonalities||this._enableDebugFeatures}get enableRules(){return this._enableRules}get memoryClassificationOnFirstToken(){return this._memoryClassificationOnFirstToken}get agentChatModel(){return this._agentChatModel}get modelRegistry(){return this._modelRegistry}get modelInfoRegistry(){return this._modelInfoRegistry}get enableModelRegistry(){return this._enableModelRegistry}get enableTaskList(){return this._enableTaskList}get clientAnnouncement(){return this._clientAnnouncement}get useHistorySummary(){return this._useHistorySummary}get historySummaryParams(){return this._historySummaryParams}get enableExchangeStorage(){return this._enableExchangeStorage}get enableToolUseStateStorage(){return this._enableToolUseStateStorage}get retryChatStreamTimeouts(){return this._retryChatStreamTimeouts}get enableCommitIndexing(){return this._enableCommitIndexing}get enableMemoryRetrieval(){return this._enableMemoryRetrieval}get enableAgentTabs(){return this._enableAgentTabs}get isVscodeVersionOutdated(){return this._isVscodeVersionOutdated}get vscodeMinVersion(){return this._vscodeMinVersion}get enableErgonomicsUpdate(){return this._enableDebugFeatures}get enableGroupedTools(){return this._enableGroupedTools}get remoteAgentsResumeHintAvailableTtlDays(){return this._remoteAgentsResumeHintAvailableTtlDays}get enableParallelTools(){return this._enableParallelTools}get enableAgentGitTracker(){return this._enableAgentGitTracker}get enableLucideIcons(){return this._enableLucideIcons}get memoriesParams(){return this._memoriesParams}get subscriptionBannerDismissibility(){return this._subscriptionBannerDismissibility}get enableEnhancedDehydrationMode(){return this._enableEnhancedDehydrationMode}get showThinkingSummary(){return this._showThinkingSummary}}var Jh=fu,Qh=/\s/,em=function(e){for(var t=e.length;t--&&Qh.test(e.charAt(t)););return t},tm=/^\s+/,nm=yu,rm=gu,sm=function(e){return e&&e.slice(0,em(e)+1).replace(tm,"")},yo=na,am=function(e){return typeof e=="symbol"||rm(e)&&nm(e)=="[object Symbol]"},im=/^[-+]0x[0-9a-f]+$/i,om=/^0b[01]+$/i,lm=/^0o[0-7]+$/i,um=parseInt,cm=na,us=function(){return Jh.Date.now()},_o=function(e){if(typeof e=="number")return e;if(am(e))return NaN;if(yo(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=yo(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=sm(e);var n=om.test(e);return n||lm.test(e)?um(e.slice(2),n?2:8):im.test(e)?NaN:+e},dm=Math.max,pm=Math.min,hm=function(e,t,n){var r,s,a,i,o,u,l=0,d=!1,c=!1,p=!0;if(typeof e!="function")throw new TypeError("Expected a function");function h(E){var b=r,I=s;return r=s=void 0,l=E,i=e.apply(I,b)}function m(E){var b=E-u;return u===void 0||b>=t||b<0||c&&E-l>=a}function y(){var E=us();if(m(E))return g(E);o=setTimeout(y,function(b){var I=t-(b-u);return c?pm(I,a-(b-l)):I}(E))}function g(E){return o=void 0,p&&r?h(E):(r=s=void 0,i)}function v(){var E=us(),b=m(E);if(r=arguments,s=this,u=E,b){if(o===void 0)return function(I){return l=I,o=setTimeout(y,t),d?h(I):i}(u);if(c)return clearTimeout(o),o=setTimeout(y,t),h(u)}return o===void 0&&(o=setTimeout(y,t)),i}return t=_o(t)||0,cm(n)&&(d=!!n.leading,a=(c="maxWait"in n)?dm(_o(n.maxWait)||0,t):a,p="trailing"in n?!!n.trailing:p),v.cancel=function(){o!==void 0&&clearTimeout(o),l=0,r=u=s=o=void 0},v.flush=function(){return o===void 0?i:g(us())},v},mm=na;const fm=kr(function(e,t,n){var r=!0,s=!0;if(typeof e!="function")throw new TypeError("Expected a function");return mm(n)&&(r="leading"in n?!!n.leading:r,s="trailing"in n?!!n.trailing:s),hm(e,t,{leading:r,maxWait:t,trailing:s})});class gm{constructor(t){f(this,"SIDECAR_TIMEOUT_MS",5e3);f(this,"getRulesList",async(t=!0)=>{const n={type:Pe.getRulesListRequest,data:{includeGuidelines:t}};return(await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS)).data.rules});f(this,"createRule",async t=>{const n={type:Pe.createRule,data:{ruleName:t.trim()}};return(await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS)).data.createdRule||null});f(this,"getWorkspaceRoot",async()=>{const t={type:Pe.getWorkspaceRoot};return(await this._asyncMsgSender.sendToSidecar(t,this.SIDECAR_TIMEOUT_MS)).data.workspaceRoot||""});f(this,"updateRuleFile",async(t,n)=>{const r={type:Pe.updateRuleFile,data:{path:t,content:n}};await this._asyncMsgSender.sendToSidecar(r,this.SIDECAR_TIMEOUT_MS)});f(this,"deleteRule",async(t,n=!0)=>{const r={type:Pe.deleteRule,data:{path:t,confirmed:n}};await this._asyncMsgSender.sendToSidecar(r,this.SIDECAR_TIMEOUT_MS)});f(this,"processSelectedPaths",async(t,n=!0)=>{const r={type:Pe.processSelectedPathsRequest,data:{selectedPaths:t,autoImport:n}},s=await this._asyncMsgSender.sendToSidecar(r,this.SIDECAR_TIMEOUT_MS);return{importedRulesCount:s.data.importedRulesCount,directoryOrFile:s.data.directoryOrFile,errors:s.data.errors}});f(this,"getAutoImportOptions",async()=>{const t={type:Pe.autoImportRules};return await this._asyncMsgSender.sendToSidecar(t,this.SIDECAR_TIMEOUT_MS)});f(this,"processAutoImportSelection",async t=>{const n={type:Pe.autoImportRulesSelectionRequest,data:{selectedLabel:t}},r=await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS);return{importedRulesCount:r.data.importedRulesCount,duplicatesCount:r.data.duplicatesCount,totalAttempted:r.data.totalAttempted,source:r.data.source}});this._asyncMsgSender=t}}class Af{constructor(t,n=!0){f(this,"_rulesFiles",Qn([]));f(this,"_loading",Qn(!0));f(this,"_extensionClientRules");f(this,"_requestRulesThrottled",fm(async()=>{this._loading.set(!0);try{const t=await this._extensionClientRules.getRulesList(this.includeGuidelines);this._rulesFiles.set(t)}catch(t){console.error("Failed to get rules list:",t)}finally{this._loading.set(!1)}},250,{leading:!0,trailing:!0}));this._msgBroker=t,this.includeGuidelines=n,this._extensionClientRules=new gm(this._msgBroker),this.requestRules()}handleMessageFromExtension(t){return!(!t.data||t.data.type!==k.getRulesListResponse)&&(this._rulesFiles.set(t.data.data),this._loading.set(!1),!0)}async requestRules(){return this._requestRulesThrottled()}async createRule(t){try{const n=await this._extensionClientRules.createRule(t);return await this.requestRules(),n}catch(n){throw console.error("Failed to create rule:",n),n}}async getWorkspaceRoot(){try{return await this._extensionClientRules.getWorkspaceRoot()}catch(t){return console.error("Failed to get workspace root:",t),""}}async updateRuleContent(t){const n=Es.formatRuleFileForMarkdown(t);try{await this._extensionClientRules.updateRuleFile(t.path,n)}catch(r){console.error("Failed to update rule file:",r)}await this.requestRules()}async deleteRule(t){try{await this._extensionClientRules.deleteRule(t,!0),await this.requestRules()}catch(n){throw console.error("Failed to delete rule:",n),n}}async processSelectedPaths(t){try{const n=await this._extensionClientRules.processSelectedPaths(t,!0);return await this.requestRules(),n}catch(n){throw console.error("Failed to process selected paths:",n),n}}async getAutoImportOptions(){return await this._extensionClientRules.getAutoImportOptions()}async processAutoImportSelection(t){try{const n=await this._extensionClientRules.processAutoImportSelection(t.label);return await this.requestRules(),n}catch(n){throw console.error("Failed to process auto-import selection:",n),n}}getCachedRules(){return this._rulesFiles}getLoading(){return this._loading}}var ym=ko('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.84182 3.13514C9.04327 3.32401 9.05348 3.64042 8.86462 3.84188L5.43521 7.49991L8.86462 11.1579C9.05348 11.3594 9.04327 11.6758 8.84182 11.8647C8.64036 12.0535 8.32394 12.0433 8.13508 11.8419L4.38508 7.84188C4.20477 7.64955 4.20477 7.35027 4.38508 7.15794L8.13508 3.15794C8.32394 2.95648 8.64036 2.94628 8.84182 3.13514Z" fill="currentColor"></path></svg>');function _m(e){var t=ym();U(e,t)}const un=class un{constructor(t=void 0){f(this,"_lastFocusAnchorElement");f(this,"_focusedIndexStore",Qn(void 0));f(this,"focusedIndex",this._focusedIndexStore);f(this,"_rootElement");f(this,"_triggerElement");f(this,"_getItems",()=>{var r;const t=(r=this._rootElement)==null?void 0:r.querySelectorAll(`.${un.ITEM_CLASS}`),n=t==null?void 0:t[0];return n instanceof HTMLElement&&this._recomputeFocusAnchor(n),Array.from(t??[])});f(this,"_recomputeFocusAnchor",t=>{var a;const n=(a=this._parentContext)==null?void 0:a._getItems(),r=n==null?void 0:n.indexOf(t);if(r===void 0||n===void 0)return;const s=Math.max(r-1,0);this._lastFocusAnchorElement=n[s]});f(this,"registerRoot",t=>{this._rootElement=t,t.addEventListener("keydown",this._onKeyDown);const n=()=>{this.getCurrentFocusedIdx()},r=s=>{t.contains(s.relatedTarget)||this._focusedIndexStore.set(void 0)};return t.addEventListener("focusin",n),t.addEventListener("focusout",r),this._getItems(),{destroy:()=>{this._removeFromTrapStack(),this._rootElement=void 0,t.removeEventListener("keydown",this._onKeyDown),t.removeEventListener("focusin",n),t.removeEventListener("focusout",r),this._focusedIndexStore.set(void 0)}}});f(this,"registerTrigger",t=>(this._triggerElement=t.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])')??t,{destroy:()=>{this._triggerElement=void 0}}));f(this,"_onKeyDown",t=>{var n;switch(t.key){case"ArrowUp":t.preventDefault(),this.focusPrev();break;case"ArrowDown":t.preventDefault(),this.focusNext();break;case"ArrowLeft":this._requestClose();break;case"ArrowRight":this.clickFocusedItem();break;case"Tab":{const r=this.getCurrentFocusedIdx();if(r===void 0||this.parentContext)break;(!t.shiftKey&&r===this._getItems().length-1||t.shiftKey&&r===0)&&(t.preventDefault(),(n=this._triggerElement)==null||n.focus());break}}});f(this,"_requestClose",()=>{var t;(t=this._rootElement)==null||t.dispatchEvent(new bu)});f(this,"getCurrentFocusedIdx",()=>{const t=this._getItems().findIndex(r=>r===document.activeElement),n=t===-1?void 0:t;return this._focusedIndexStore.set(n),n});f(this,"setFocusedIdx",t=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const r=Vn(t,n.length);this._focusedIndexStore.set(r)});f(this,"focusIdx",t=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const r=Vn(t,n.length),s=n[r];s==null||s.focus(),this._focusedIndexStore.set(r)});f(this,"popNestedFocus",()=>{if(this._parentContext){this._focusedIndexStore.set(void 0);const t=this._lastFocusAnchorElement,n=t?this._parentContext._getItems().indexOf(t):void 0;return n===void 0?(this._parentContext.focusIdx(0),!0):(this._parentContext.focusIdx(n),!0)}return!1});f(this,"focusNext",()=>{const t=this._getItems();if(t.length===0)return;const n=Vn(t.findIndex(r=>r===document.activeElement)+1,t.length);t[n].focus(),this._focusedIndexStore.set(n)});f(this,"focusPrev",()=>{var r;const t=this._getItems();if(t.length===0)return;const n=Vn(t.findIndex(s=>s===document.activeElement)-1,t.length);(r=t[n])==null||r.focus(),this._focusedIndexStore.set(n)});f(this,"clickFocusedItem",async()=>{const t=document.activeElement;t&&(t.click(),await cs())});f(this,"_addToTrapStack",()=>{this._rootElement&&xa.add(this._rootElement)});f(this,"_removeFromTrapStack",()=>{this._rootElement&&xa.remove(this._rootElement)});f(this,"handleOpenChange",t=>{t?this._addToTrapStack():this._removeFromTrapStack()});this._parentContext=t}get rootElement(){return this._rootElement}get triggerElement(){return this._triggerElement}get parentContext(){return this._parentContext}};f(un,"CONTEXT_KEY","augment-dropdown-menu-focus"),f(un,"ITEM_CLASS","js-dropdown-menu__focusable-item");let fe=un;function Vn(e,t){return(e%t+t)%t}const Mt="augment-dropdown-menu-content";var vm=oe("<div><!></div>"),bm=oe('<div class="l-dropdown-menu-augment__container svelte-o54ind"><!></div>');function vo(e,t){ve(t,!1);const[n,r]=et(),s=()=>Le(h,"$sizeState",n),a=Ue();let i=F(t,"size",8,2),o=F(t,"onEscapeKeyDown",8,()=>{}),u=F(t,"onClickOutside",8,()=>{}),l=F(t,"onRequestClose",8,()=>{}),d=F(t,"side",8,"top"),c=F(t,"align",8,"center");const p={size:Qn(i())},h=p.size;ta(Mt,p);const m=se(fe.CONTEXT_KEY),y=se(Gn.CONTEXT_KEY);Fe(()=>st(i()),()=>{h.set(i())}),Fe(()=>{},()=>{au($e(a,y.state),"$openState",n)}),Pt(),we(),Me("keydown",lu,function(g){if(Le(q(a),"$openState",n).open&&g.key==="Tab"&&!g.shiftKey){if(m.getCurrentFocusedIdx()!==void 0)return;g.preventDefault(),m==null||m.focusIdx(0)}}),Su(e,{onEscapeKeyDown:o(),onClickOutside:function(g){var v;return y.forceControlSetOpen(!1),(v=u())==null?void 0:v(g)},onRequestClose:l(),get side(){return d()},get align(){return c()},$$events:{keydown(g){Ze.call(this,t,g)}},children:(g,v)=>{var E=bm(),b=he(E);Eu(b,{get size(){return s()},insetContent:!0,includeBackground:!1,children:(I,A)=>{var P=vm(),M=he(P);ae(M,t,"default",{},null),ps(P,H=>{var j;return(j=m.registerRoot)==null?void 0:j.call(m,H)}),Lt(()=>Dt(P,1,`l-dropdown-menu-augment__contents l-dropdown-menu-augment__contents--size-${s()}`,"svelte-o54ind")),U(I,P)},$$slots:{default:!0}}),U(g,E)},$$slots:{default:!0}}),be(),r()}var Sm=oe('<div class="c-dropdown-menu-augment__item-icon svelte-48oly1"><!></div>'),Em=oe('<div class="c-dropdown-menu-augment__item-icon svelte-48oly1"><!></div>'),Tm=oe("<!> <!> <!>",1);function ea(e,t){const n=Eo(t),r=X(t,["children","$$slots","$$events","$$legacy"]),s=X(r,["highlight","disabled","color","onSelect"]);ve(t,!1);const[a,i]=et(),o=()=>Le(v,"$sizeState",a),u=Ue(),l=Ue(),d=Ue();let c=F(t,"highlight",24,()=>{}),p=F(t,"disabled",24,()=>{}),h=F(t,"color",24,()=>{}),m=F(t,"onSelect",8,()=>{});const y=se(Mt),g=se(fe.CONTEXT_KEY),v=y.size;function E(M){var D;if(p())return;const H=(D=g.rootElement)==null?void 0:D.querySelectorAll(`.${fe.ITEM_CLASS}`);if(!H)return;const j=Array.from(H).findIndex(ye=>ye===M);j!==-1&&g.setFocusedIdx(j)}Fe(()=>(q(u),q(l),st(s)),()=>{$e(u,s.class),$e(l,No(s,["class"]))}),Fe(()=>(st(p()),st(c()),q(u)),()=>{$e(d,[p()?"":fe.ITEM_CLASS,"c-dropdown-menu-augment__item",c()?"c-dropdown-menu-augment__item--highlighted":"",q(u)].join(" "))}),Pt(),we();const b=ds(()=>h()??"neutral"),I=ds(()=>!h());var A=Ra(()=>Aa("dropdown-menu-item","highlighted",c())),P=Ra(()=>Aa("dropdown-menu-item","disabled",p()));_u(e,gt({get class(){return q(d)},get size(){return o()},variant:"ghost",get color(){return q(b)},get highContrast(){return q(I)},alignment:"left",get disabled(){return p()}},()=>q(A),()=>q(P),()=>q(l),{$$events:{click:M=>{M.currentTarget instanceof HTMLElement&&E(M.currentTarget),m()(M)},mouseover:M=>{M.currentTarget instanceof HTMLElement&&E(M.currentTarget)},mousedown:M=>{M.preventDefault(),M.stopPropagation()}},children:(M,H)=>{var j=Tm(),D=Ne(j),ye=ce=>{var kt=Sm(),Nt=he(kt);ae(Nt,t,"iconLeft",{},null),U(ce,kt)};en(D,ce=>{yt(()=>n.iconLeft)&&ce(ye)});var Rn=tn(D,2);To(Rn,{get size(){return o()},children:(ce,kt)=>{var Nt=We(),Ql=Ne(Nt);ae(Ql,t,"default",{},null),U(ce,Nt)},$$slots:{default:!0}});var $=tn(Rn,2),pt=ce=>{var kt=Em(),Nt=he(kt);ae(Nt,t,"iconRight",{},null),U(ce,kt)};en($,ce=>{yt(()=>n.iconRight)&&ce(pt)}),U(M,j)},$$slots:{default:!0}})),be(),i()}var wm=ko("<svg><!></svg>");function km(e,t){const n=X(t,["children","$$slots","$$events","$$legacy"]);var r=wm();Io(r,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 320 512",...n}));var s=he(r);vu(s,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M305 239c9.4 9.4 9.4 24.6 0 33.9L113 465c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9l175-175L79 81c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0z"/>',!0),U(e,r)}function bo(e,t){const n=X(t,["children","$$slots","$$events","$$legacy"]),r=X(n,[]);ea(e,gt({class:"c-dropdown-menu-augment__breadcrumb-chevron"},()=>r,{children:(s,a)=>{var i=We(),o=Ne(i);ae(o,t,"default",{},null),U(s,i)},$$slots:{default:!0,iconRight:(s,a)=>{km(s,{slot:"iconRight"})}}}))}var Nm=oe("<div><!></div>");const Jl=Symbol("command-scope-node");function Im(){return se(Jl)}function xf(e){const t=[];let n=e;for(;n;)t.push(n.value),n=n.parent??void 0;return t.reverse()}function Cm(e,t){ve(t,!0);const n=Im()??null,r=function(i,o){const u={value:i,parent:o,children:new Set};return o&&o.children.add(u),u}(t.scope,n);(function(i){ta(Jl,i)})(r),uu(()=>{r.value=t.scope}),cu(()=>{(function(i){i.parent&&i.parent.children.delete(i),i.parent=null})(r)});var s=We(),a=Ne(s);iu(a,()=>t.children??du),U(e,s),be()}function So(e,t){const n=X(t,["children","$$slots","$$events","$$legacy"]),r=X(n,["requestOpen","requestClose","focusIdx","setFocusedIdx","getCurrentFocusedIdx","focusedIndex","defaultOpen","open","onOpenChange","delayDurationMs","nested","onHoverStart","onHoverEnd","triggerOn"]);ve(t,!1);let s=F(t,"defaultOpen",24,()=>{}),a=F(t,"open",24,()=>{}),i=F(t,"onOpenChange",24,()=>{}),o=F(t,"delayDurationMs",24,()=>{}),u=F(t,"nested",24,()=>{}),l=F(t,"onHoverStart",8,()=>{}),d=F(t,"onHoverEnd",8,()=>{}),c=F(t,"triggerOn",24,()=>[qn.Click]),p=Ue();const h=()=>{var A;return(A=q(p))==null?void 0:A.requestOpen()},m=()=>{var A;return(A=q(p))==null?void 0:A.requestClose()},y=A=>b.focusIdx(A),g=A=>b.setFocusedIdx(A),v=()=>b.getCurrentFocusedIdx(),E=se(fe.CONTEXT_KEY),b=new fe(E);ta(fe.CONTEXT_KEY,b);const I=b.focusedIndex;return we(),wo(Tu(e,gt({get defaultOpen(){return s()},get open(){return a()},onOpenChange:function(A){var P;b.handleOpenChange(A),(P=i())==null||P(A)},get delayDurationMs(){return o()},onHoverStart:l(),onHoverEnd:d(),get triggerOn(){return c()},get nested(){return u()}},()=>r,{children:(A,P)=>{Cm(A,{scope:"dropdown-menu",children:(M,H)=>{var j=We(),D=Ne(j);ae(D,t,"default",{},null),U(M,j)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0})),A=>$e(p,A),()=>q(p)),It(t,"requestOpen",h),It(t,"requestClose",m),It(t,"focusIdx",y),It(t,"setFocusedIdx",g),It(t,"getCurrentFocusedIdx",v),It(t,"focusedIndex",I),be({requestOpen:h,requestClose:m,focusIdx:y,setFocusedIdx:g,getCurrentFocusedIdx:v,focusedIndex:I})}var Am=oe("<div></div>");function xm(e,t){let n=F(t,"size",3,1),r=F(t,"orientation",3,"horizontal"),s=F(t,"useCurrentColor",3,!1),a=F(t,"class",3,"");var i=Am();let o;Lt(u=>o=Dt(i,1,`c-separator c-separator--size-${n()===.5?"0_5":n()} c-separator--orientation-${r()} ${a()}`,"svelte-o0csoy",o,u),[()=>({"c-separator--current-color":s()})]),U(e,i)}var Rm=oe("<div><!></div>"),Om=oe('<label class="c-text-field-label svelte-vuqlvc"><!></label>'),Mm=oe('<div class="c-text-field__slot c-base-text-input__slot"><!></div>'),Pm=oe('<div class="c-text-field__slot c-base-text-input__slot"><!></div>'),Lm=oe("<!> <input/> <!>",1),Dm=oe("<div><!> <!></div>");function Fm(e,t){const n=Eo(t),r=X(t,["children","$$slots","$$events","$$legacy"]),s=X(r,["variant","size","color","textInput","value","id"]);ve(t,!1);const a=Ue(),i=Ue(),o=Ue(),u=hu();let l=F(t,"variant",8,"surface"),d=F(t,"size",8,2),c=F(t,"color",24,()=>{}),p=F(t,"textInput",28,()=>{}),h=F(t,"value",12,""),m=F(t,"id",24,()=>{});const y=`text-field-${Math.random().toString(36).substring(2,11)}`;function g(A){u("change",A)}Fe(()=>st(m()),()=>{$e(a,m()||y)}),Fe(()=>(q(i),q(o),st(s)),()=>{$e(i,s.class),$e(o,No(s,["class"]))}),Pt(),we();var v=Dm();Dt(v,1,"c-text-field svelte-vuqlvc",null,{},{"c-text-field--has-left-icon":n.iconLeft!==void 0,"c-text-field--has-right-icon":n.iconRight!==void 0});var E=he(v),b=A=>{var P=Om(),M=he(P);ae(M,t,"label",{},null),Lt(()=>mu(P,"for",q(a))),U(A,P)};en(E,A=>{yt(()=>n.label)&&A(b)});var I=tn(E,2);ku(I,{get variant(){return l()},get size(){return d()},get color(){return c()},children:(A,P)=>{var M=Lm(),H=Ne(M),j=$=>{var pt=Mm(),ce=he(pt);ae(ce,t,"iconLeft",{},null),U($,pt)};en(H,$=>{yt(()=>n.iconLeft)&&$(j)});var D=tn(H,2);Io(D,()=>({spellCheck:"false",class:`c-text-field__input c-base-text-input__input ${q(i)}`,id:q(a),...q(o)}),void 0,"svelte-vuqlvc"),wo(D,$=>p($),()=>p());var ye=tn(D,2),Rn=$=>{var pt=Pm(),ce=he(pt);ae(ce,t,"iconRight",{},null),U($,pt)};en(ye,$=>{yt(()=>n.iconRight)&&$(Rn)}),wu(D,h),Me("change",D,g),Me("click",D,function($){Ze.call(this,t,$)}),Me("keydown",D,function($){Ze.call(this,t,$)}),Me("input",D,function($){Ze.call(this,t,$)}),Me("blur",D,function($){Ze.call(this,t,$)}),Me("dblclick",D,function($){Ze.call(this,t,$)}),Me("focus",D,function($){Ze.call(this,t,$)}),Me("mouseup",D,function($){Ze.call(this,t,$)}),Me("selectionchange",D,function($){Ze.call(this,t,$)}),U(A,M)},$$slots:{default:!0}}),U(e,v),be()}var $m=oe("<div><!></div>"),Um=oe("<div><!></div>");const Rf={BreadcrumbBackItem:function(e,t){const n=X(t,["children","$$slots","$$events","$$legacy"]),r=X(n,[]);ea(e,gt({class:"c-dropdown-menu-augment__breadcrumb-back-chevron"},()=>r,{children:(s,a)=>{var i=We(),o=Ne(i);ae(o,t,"default",{},null),U(s,i)},$$slots:{default:!0,iconLeft:(s,a)=>{_m(s)}}}))},BreadcrumbItem:bo,Content:vo,Item:ea,Label:function(e,t){ve(t,!1);const[n,r]=et(),s=()=>Le(i,"$sizeState",n),a=Ue(),i=se(Mt).size;Fe(()=>s(),()=>{$e(a,["c-dropdown-menu-augment__label-item",`c-dropdown-menu-augment__label-item--size-${s()}`].join(" "))}),Pt(),we();var o=Nm(),u=he(o);To(u,{get size(){return s()},weight:"regular",children:(l,d)=>{var c=We(),p=Ne(c);ae(p,t,"default",{},null),U(l,c)},$$slots:{default:!0}}),Lt(()=>Dt(o,1,Oa(q(a)),"svelte-gehsvg")),U(e,o),be(),r()},Root:So,Separator:function(e,t){ve(t,!1);const[n,r]=et(),s=se(Mt).size;we();var a=Rm();xm(he(a),{size:4,orientation:"horizontal"}),Lt(()=>Dt(a,1,`c-dropdown-menu-augment__separator c-dropdown-menu-augment__separator--size-${Le(s,"$sizeState",n)}`,"svelte-24h9u")),U(e,a),be(),r()},Sub:function(e,t){const n=X(t,["children","$$slots","$$events","$$legacy"]),r=X(n,[]);ve(t,!1),we();const s=ds(()=>(st(qn),yt(()=>[qn.Click,qn.Hover])));So(e,gt({nested:!0,get triggerOn(){return q(s)}},()=>r,{children:(a,i)=>{var o=We(),u=Ne(o);ae(u,t,"default",{},null),U(a,o)},$$slots:{default:!0}})),be()},SubContent:function(e,t){const n=X(t,["children","$$slots","$$events","$$legacy"]),r=X(n,[]);ve(t,!1);const[s,a]=et(),i=()=>Le(d,"$didOpen",s),o=se(Mt).size,u=se(fe.CONTEXT_KEY),l=se(Gn.CONTEXT_KEY),d=pu(l.state,c=>c.open);Fe(()=>(i(),cs),()=>{i()&&cs().then(()=>u==null?void 0:u.focusIdx(0))}),Fe(()=>i(),()=>{!i()&&(u==null||u.popNestedFocus())}),Pt(),we(),vo(e,gt(()=>r,{side:"right",align:"start",get size(){return Le(o,"$sizeState",s)},children:(c,p)=>{var h=We(),m=Ne(h);ae(m,t,"default",{},null),U(c,h)},$$slots:{default:!0}})),be(),a()},SubTrigger:function(e,t){ve(t,!1);const[n,r]=et(),s=se(Gn.CONTEXT_KEY).state;we(),Ma(e,{children:(a,i)=>{bo(a,{get highlight(){return Le(s,"$stateStore",n).open},children:(o,u)=>{var l=We(),d=Ne(l);ae(d,t,"default",{},null),U(o,l)},$$slots:{default:!0}})},$$slots:{default:!0}}),be(),r()},TextFieldItem:function(e,t){const n=X(t,["children","$$slots","$$events","$$legacy"]),r=X(n,["value"]);ve(t,!1);const[s,a]=et(),i=()=>Le(l,"$sizeState",s),o=Ue();let u=F(t,"value",12,"");const l=se(Mt).size;Fe(()=>i(),()=>{$e(o,["c-dropdown-menu-augment__text-field-item",`c-dropdown-menu-augment__text-field-item--size-${i()}`].join(" "))}),Pt(),we();var d=$m();Fm(he(d),gt({get class(){return st(fe),yt(()=>fe.ITEM_CLASS)},get size(){return i()}},()=>r,{get value(){return u()},set value(c){u(c)},$$legacy:!0})),Lt(()=>Dt(d,1,Oa(q(o)),"svelte-1xu00bc")),U(e,d),be(),a()},Trigger:function(e,t){ve(t,!1);const[n,r]=et(),s=()=>Le(u,"$openState",n);let a=F(t,"referenceClientRect",24,()=>{});const i=se(fe.CONTEXT_KEY),o=se(Gn.CONTEXT_KEY),u=o.state;we(),Ma(e,{get referenceClientRect(){return a()},$$events:{keydown:async l=>{switch(l.key){case"ArrowUp":l.preventDefault(),l.stopPropagation(),s().open||await i.clickFocusedItem(),i==null||i.focusIdx(-1);break;case"ArrowDown":l.preventDefault(),l.stopPropagation(),s().open||await i.clickFocusedItem(),i==null||i.focusIdx(0);break;case"Enter":l.preventDefault(),l.stopPropagation(),i==null||i.clickFocusedItem()}}},children:(l,d)=>{var c=Um(),p=he(c);ae(p,t,"default",{},null),ps(c,h=>{var m;return(m=i.registerTrigger)==null?void 0:m.call(i,h)}),ps(c,h=>{var m;return(m=o.registerTrigger)==null?void 0:m.call(o,h)}),U(l,c)},$$slots:{default:!0}}),be(),r()}};export{ff as $,rf as A,Vd as B,Cf as C,Rf as D,Tf as E,bd as F,Gd as G,qd as H,of as I,Zd as J,Kd as K,Xp as L,Sd as M,Yd as N,cf as O,gf as P,Wd as Q,Af as R,xd as S,Fm as T,zd as U,ea as V,_f as W,yf as X,lf as Y,hf as Z,mf as _,Es as a,uf as a0,th as a1,re as a2,km as a3,Ed as a4,Md as a5,fm as a6,xl as a7,kd as a8,wd as a9,Jm as aA,wa as aB,yd as aC,tf as aD,ef as aE,If as aa,Rd as ab,Od as ac,af as ad,df as ae,xm as af,Cm as ag,$d as ah,Fd as ai,me as aj,_m as ak,Bd as al,Ud as am,wf as an,Dd as ao,Ld as ap,Im as aq,xf as ar,Pd as as,zl as at,Rl as au,Cd as av,Ef as aw,gd as ax,kl as ay,Qm as az,Nd as b,_d as c,vd as d,Ad as e,W as f,eh as g,Jp as h,Ac as i,sf as j,Qp as k,vf as l,Id as m,bf as n,xc as o,Rc as p,Hd as q,Sf as r,pf as s,nf as t,zh as u,Xl as v,Xh as w,Nf as x,kf as y,jd as z};
