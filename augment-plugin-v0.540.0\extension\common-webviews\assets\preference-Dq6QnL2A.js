import{a4 as mt,x as K,y as h,D as H,o,E as a,N,a1 as z,b as v,G as U,O as tt,a3 as ht,a5 as bt,S as B,T as ct,a6 as gt,z as e,P as _,A as st,B as ot,L as dt,a8 as ft,ag as yt,au as wt}from"./legacy-AoIeRrIA.js";import{p as D,i as kt}from"./SpinnerAugment-mywmfXFR.js";import{h as x,W as G}from"./host-qgbK079d.js";import{c as _t,M as At}from"./index-BdF7sLLk.js";import{aJ as lt}from"./AugmentMessage-DuZeVL4h.js";import{b as qt,a as Bt}from"./input-DCBQtNgo.js";import{C as Dt,S as xt}from"./folder-opened-D5l3axuC.js";import{M as Ct}from"./message-broker-EhzME3pO.js";import{s as Mt}from"./chat-model-context-DPgWxlAp.js";import"./index-Dtf_gCqL.js";import"./CalloutAugment-Db5scVK5.js";import"./CardAugment-DwIptXof.js";import"./IconButtonAugment-DZyIKjh7.js";import"./event-modifiers-Bz4QCcZc.js";import"./index-BLDiLrXG.js";import"./async-messaging-Dmg2N9Pf.js";import"./chat-types-BfwvR7Kn.js";import"./focusTrapStack-CaEmYw0i.js";import"./isObjectLike-CnrzNkq5.js";import"./BaseTextInput-D2MYbf3a.js";import"./lodash-CwNMWAFx.js";import"./Filespan-BqOh8yIt.js";import"./diff-operations-DEY-phZ8.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./index-CJlvVQMt.js";import"./keypress-DD1aQVr0.js";import"./await-BdVIougb.js";import"./OpenFileButton-CWm-PsCk.js";import"./index-B528snJk.js";import"./remote-agents-client-BMR_qMG5.js";import"./types-CGlLNakm.js";import"./SuccessfulButton-xgZ5Aax4.js";import"./ButtonAugment-D7YBjBq5.js";import"./CollapseButtonAugment-BXWAqMp6.js";import"./partner-mcp-utils-BH31APX7.js";import"./MaterialIcon-B_3jxWk_.js";import"./CopyButton-DfcpqvjI.js";import"./TextAreaAugment-B1LKPxPr.js";import"./ellipsis-5yZhsJie.js";import"./LanguageIcon-D_oFq7vE.js";import"./augment-logo-BqyYuvys.js";import"./BadgeRoot-CMDpgWKP.js";import"./repository-utils-DzBkqZ7a.js";import"./file-type-utils-D6OEcQY2.js";var Rt=h('<div class="header svelte-1894wv4"> </div>'),St=(p,t)=>t("A3"),Wt=(p,t)=>t("A2"),Pt=(p,t)=>t("A1"),$t=(p,t)=>t("="),zt=(p,t)=>t("B1"),Et=(p,t)=>t("B2"),It=(p,t)=>t("B3"),Ot=h('<div class="container svelte-1894wv4"><!> <div class="buttons svelte-1894wv4"><button type="button">A</button> <button type="button">A</button> <button type="button">A</button> <button type="button">=</button> <button type="button">B</button> <button type="button">B</button> <button type="button">B</button></div></div>');function nt(p,t){K(t,!0);let i=D(t,"selected",15,null),E=D(t,"question",3,null);function r(w){i(w)}var g=Ot(),C=o(g),l=w=>{var F=Rt(),j=o(F);N(()=>tt(j,E())),v(w,F)};H(C,w=>{E()&&w(l)});var c=a(C,2),f=o(c);let d;f.__click=[St,r];var b=a(f,2);let I;b.__click=[Wt,r];var M=a(b,2);let A;M.__click=[Pt,r];var q=a(M,2);let R;q.__click=[$t,r];var S=a(q,2);let y;S.__click=[zt,r];var O=a(S,2);let T;O.__click=[Et,r];var V=a(O,2);let X;V.__click=[It,r],N((w,F,j,et,s,u,Y)=>{d=z(f,1,"button large svelte-1894wv4",null,d,w),I=z(b,1,"button medium svelte-1894wv4",null,I,F),A=z(M,1,"button small svelte-1894wv4",null,A,j),R=z(q,1,"button equal svelte-1894wv4",null,R,et),y=z(S,1,"button small svelte-1894wv4",null,y,s),T=z(O,1,"button medium svelte-1894wv4",null,T,u),X=z(V,1,"button large svelte-1894wv4",null,X,Y)},[()=>({highlighted:i()==="A3"}),()=>({highlighted:i()==="A2"}),()=>({highlighted:i()==="A1"}),()=>({highlighted:i()==="="}),()=>({highlighted:i()==="B1"}),()=>({highlighted:i()==="B2"}),()=>({highlighted:i()==="B3"})]),v(p,g),U()}mt(["click"]);var Ft=h('<div class="question svelte-1i0f73l"> </div>'),Lt=h('<div class="container svelte-1i0f73l"><!> <textarea class="input svelte-1i0f73l" rows="3"></textarea></div>'),Nt=h('<button class="button svelte-2k5n"> </button>');mt(["click"]);var Gt=h("<div> </div>"),Ht=h('<div class="container svelte-n0uy88"><!> <label class="custom-checkbox svelte-n0uy88"><input type="checkbox" class="svelte-n0uy88"/> <span class="svelte-n0uy88"></span></label></div>'),Tt=h("<!> <!> <!> <!> <!> <!>",1),jt=h("<p>Streaming in progress... Please wait for both responses to complete.</p>"),Jt=h('<main><div class="l-pref svelte-751nif"><h1 class="svelte-751nif">Input message</h1> <!> <hr class="l-side-by-side svelte-751nif"/> <div class="l-side-by-side svelte-751nif"><div class="l-side-by-side__child svelte-751nif"><h1 class="svelte-751nif">Option A</h1> <!></div> <div class="divider svelte-751nif"></div> <div class="l-side-by-side__child svelte-751nif"><h1 class="svelte-751nif">Option B</h1> <!></div></div> <hr class="svelte-751nif"/> <!></div></main>');function Qt(p,t){K(t,!0);const i=bt();let E=new Dt(new Ct(x),x,new xt);Mt(E);let r=B(null),g=B(null),C=null,l=B(null),c=B(""),f=B(!1),d=B(ct({a:null,b:null})),b=B(ct(t.inputData.data.a.response.length>0&&t.inputData.data.b.response.length>0));function I(){if(C="=",e(l)===null)return void i("notify","Overall rating is required");const s={overallRating:e(l),formattingRating:e(r)||"=",hallucinationRating:C||"=",instructionFollowingRating:e(g)||"=",isHighQuality:e(f),textFeedback:e(c)};i("result",s)}gt(()=>{window.addEventListener("message",s=>{const u=s.data;u.type===G.chatModelReply?(u.stream==="A"?e(d).a=u.data.text:u.stream==="B"&&(e(d).b=u.data.text),_(d,e(d),!0)):u.type===G.chatStreamDone&&_(b,!0)})});let M=st(()=>{return(s=e(l))==="="||s===null?"Is this a high quality comparison?":`Are you completely happy with response '${s.startsWith("A")?"A":"B"}'?`;var s}),A=st(()=>e(d).a!==null?e(d).a:t.inputData.data.a.response),q=st(()=>e(d).b!==null?e(d).b:t.inputData.data.b.response);kt(()=>{_(b,t.inputData.data.a.response.length>0&&t.inputData.data.b.response.length>0,!0)});var R=Jt(),S=o(R),y=a(o(S),2);lt(y,{get markdown(){return t.inputData.data.a.message}});var O=a(y,4),T=o(O),V=a(o(T),2);lt(V,{get markdown(){return e(A)}});var X=a(T,4),w=a(o(X),2);lt(w,{get markdown(){return e(q)}});var F=a(O,4),j=s=>{var u=Tt(),Y=ot(u);nt(Y,{question:"Which response is formatted better? (e.g. level of detail style, structure)?",get selected(){return e(r)},set selected(n){_(r,n,!0)}});var rt=a(Y,2);nt(rt,{question:"Which response follows your instruction better?",get selected(){return e(g)},set selected(n){_(g,n,!0)}});var ut=a(rt,2);nt(ut,{question:"Which response is better overall?",get selected(){return e(l)},set selected(n){_(l,n,!0)}});var vt=a(ut,2);(function(n,m){K(m,!0);let J=D(m,"isChecked",15,!1),k=D(m,"question",3,null);var L=Ht(),W=o(L),P=$=>{var Q=Gt(),it=o(Q);N(()=>tt(it,k())),v($,Q)};H(W,$=>{k()&&$(P)});var at=a(W,2),Z=o(at);Bt(Z,J),v(n,L),U()})(vt,{get question(){return e(M)},get isChecked(){return e(f)},set isChecked(n){_(f,n,!0)}});var pt=a(vt,2);(function(n,m){K(m,!0);let J=D(m,"value",15,""),k=D(m,"question",3,null),L=D(m,"placeholder",3,"");var W=Lt(),P=o(W),at=$=>{var Q=Ft(),it=o(Q);N(()=>tt(it,k())),v($,Q)};H(P,$=>{k()&&$(at)});var Z=a(P,2);N(()=>ht(Z,"placeholder",L())),qt(Z,J),v(n,W),U()})(pt,{question:"Any additional feedback?",placeholder:"Please explain your answers to the above questions.",get value(){return e(c)},set value(n){_(c,n,!0)}}),function(n,m){let J=D(m,"label",3,"Submit");var k=Nt();k.__click=function(...W){var P;(P=m.onClick)==null||P.apply(this,W)};var L=o(k);N(()=>tt(L,J())),v(n,k)}(a(pt,2),{label:"Submit",onClick:I}),v(s,u)},et=s=>{var u=jt();v(s,u)};H(F,s=>{e(b)?s(j):s(et,!1)}),v(p,R),U()}var Kt=h("<main><!></main>");function Ut(p,t){K(t,!0);let i=B(void 0);function E(l){const c=l.detail;x.postMessage({type:G.preferenceResultMessage,data:c})}function r(l){x.postMessage({type:G.preferenceNotify,data:l.detail})}x.postMessage({type:G.preferencePanelLoaded});var g=dt();ft("message",yt,function(l){const c=l.data;c.type===G.preferenceInit&&_(i,c.data,!0)});var C=ot(g);_t(C,()=>At.Root,(l,c)=>{c(l,{children:(f,d)=>{var b=Kt(),I=o(b),M=A=>{var q=dt(),R=ot(q),S=y=>{Qt(y,{get inputData(){return e(i)},$$events:{result:E,notify:r}})};H(R,y=>{e(i).type==="Chat"&&y(S)}),v(A,q)};H(I,A=>{e(i)&&A(M)}),v(f,b)},$$slots:{default:!0}})}),v(p,g),U()}(async function(){x&&x.initialize&&await x.initialize(),wt(Ut,{target:document.getElementById("app")})})();
