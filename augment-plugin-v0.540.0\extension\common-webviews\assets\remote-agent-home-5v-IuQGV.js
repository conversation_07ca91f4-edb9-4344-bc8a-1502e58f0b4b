import{f as at,a as nt,o as m,b as i,x as be,S as Ge,T as rt,W as Xe,y as u,M as ae,N as G,O as de,E as l,B as j,D as S,z as e,G as xe,P as T,A as x,a1 as Ze,I as ye,m as pe,Q as z,J as it,K as Ve,u as W,R as se,a3 as ot,L as lt,a8 as Ye,X as Ce,Y as Je,a6 as dt,ag as ct,au as gt}from"./legacy-AoIeRrIA.js";import{l as vt,p as L,T as ne,a as et,b as tt,i as ut,S as mt}from"./SpinnerAugment-mywmfXFR.js";import"./design-system-init-Creeq9bS.js";/* empty css                                */import{e as he,i as pt,h as ft}from"./host-qgbK079d.js";import{M as ht}from"./message-broker-EhzME3pO.js";import{S as _t,T as wt,a as Ke,R as $t,B as St,b as yt,c as je,d as kt,v as At,e as bt}from"./StatusIndicator-CWKt8jxE.js";import{R as ce,a as Me,s as xt}from"./index-B528snJk.js";import{T as Ee,a as fe,C as Pt}from"./CardAugment-DwIptXof.js";import{C as Rt}from"./CalloutAugment-Db5scVK5.js";import{h as zt,I as Be}from"./IconButtonAugment-DZyIKjh7.js";import{E as Ht}from"./exclamation-triangle-DKq0zBWN.js";import{d as Ft,s as It,R as De}from"./remote-agents-client-BMR_qMG5.js";import{g as Qe}from"./repository-utils-DzBkqZ7a.js";import{A as Ot}from"./augment-logo-BqyYuvys.js";import"./async-messaging-Dmg2N9Pf.js";import"./github-BdMD2Kls.js";import"./BadgeRoot-CMDpgWKP.js";import"./event-modifiers-Bz4QCcZc.js";import"./chat-types-BfwvR7Kn.js";import"./types-CGlLNakm.js";var Tt=at("<svg><!></svg>"),qt=u(" <!>",1),Et=u('<div class="agent-card-footer svelte-1qwlkoj"><!> <div class="time-container"><!></div></div>'),Dt=u('<div class="task-text-container svelte-1tatwxk"><!></div>'),Ct=u('<div class="task-status-indicator svelte-1tatwxk"><!></div>'),Wt=u('<div class="task-item svelte-1tatwxk"><div></div> <!> <!></div>'),Lt=u(' <button class="error-dismiss svelte-96mn5f" aria-label="Dismiss error">×</button>',1),Nt=u('<div class="deletion-error svelte-96mn5f"><!></div>'),Ut=u('<span class="setup-script-title svelte-96mn5f">Generate a setup script</span>'),Mt=u('<div class="setup-script-title-container svelte-96mn5f"><div class="setup-script-badge svelte-96mn5f"><!></div> <!></div>'),Bt=u('<div class="tasks-list svelte-96mn5f"></div>'),Gt=u('<div class="card-commit-info svelte-96mn5f"><!> <!></div>'),jt=u('<div class="card-header svelte-96mn5f"><div class="session-summary-container svelte-96mn5f"><!></div> <div class="card-info"><!></div></div> <div class="card-content svelte-96mn5f"><!> <!></div> <div class="card-actions svelte-96mn5f"><!> <!> <!></div> <!>',1),Xt=u("<div><!> <!></div>");function Zt(ge,c){be(c,!1);const H=pe(),q=pe(),F=pe(),re=pe(),f=pe(),N=pe(),X=pe();let s=L(c,"agent",8),I=L(c,"selected",8,!1),ve=L(c,"isPinned",8,!1),_e=L(c,"onSelect",8),y=L(c,"onDelete",8),V=L(c,"deletionError",12,null),ue=L(c,"isDeleting",8,!1),U=L(c,"onTogglePinned",24,()=>{}),g=L(c,"sshConfig",24,()=>{});function Z(){V(null)}Xe(()=>{Z()}),ye(()=>(e(H),e(q),z(g())),()=>{var t;t=g()||{onSSH:()=>Promise.resolve(!1),canSSH:!1},T(H,t.onSSH),T(q,t.canSSH)}),ye(()=>z(s()),()=>{T(F,s().turn_summaries||[])}),ye(()=>{},()=>{T(re,!0)}),ye(()=>z(s()),()=>{var t,r,$;T(f,($=(r=(t=s().workspace_setup)==null?void 0:t.starting_files)==null?void 0:r.github_commit_ref)==null?void 0:$.repository_url)}),ye(()=>(e(f),Qe),()=>{T(N,e(f)?Qe(e(f)):void 0)}),ye(()=>z(s()),()=>{var t,r,$;T(X,($=(r=(t=s().workspace_setup)==null?void 0:t.starting_files)==null?void 0:r.github_commit_ref)==null?void 0:$.git_ref)}),it(),Ve();var p=Xt();let ie;var k=m(p),M=t=>{var r=Nt(),$=m(r);Rt($,{variant:"soft",color:"error",size:1,icon:A=>{Ht(A,{})},children:(A,E)=>{var J=Lt(),K=j(J),me=l(K);G(()=>de(K,`${V()??""} `)),Ye("click",me,Z),i(A,J)},$$slots:{icon:!0,default:!0}}),i(t,r)};S(k,t=>{V()&&t(M)});var We=l(k,2);Pt(We,{variant:"surface",size:2,interactive:!0,class:"agent-card",$$events:{click:()=>_e()(s().remote_agent_id),keydown:t=>t.key==="Enter"&&_e()(s().remote_agent_id)},children:(t,r)=>{var $=jt(),oe=j($),A=m(oe),E=m(A),J=n=>{var a=Mt(),b=m(a),w=m(b);Ke(w);var Y=l(b,2);ne(Y,{size:2,weight:"medium",children:(P,R)=>{var ee=Ut();i(P,ee)},$$slots:{default:!0}}),i(n,a)},K=n=>{ne(n,{size:2,weight:"medium",class:"session-text",children:(a,b)=>{var w=ae();G(()=>de(w,(z(s()),W(()=>s().session_summary)))),i(a,w)},$$slots:{default:!0}})};S(E,n=>{z(s()),W(()=>s().is_setup_script_agent)?n(J):n(K,!1)});var me=l(A,2),Pe=m(me);_t(Pe,{get status(){return z(s()),W(()=>s().status)},get workspaceStatus(){return z(s()),W(()=>s().workspace_status)},isExpanded:!0,get hasUpdates(){return z(s()),W(()=>s().has_updates)}});var Re=l(oe,2),Fe=m(Re),Ie=n=>{var a=Bt();he(a,5,()=>(e(F),W(()=>e(F).slice(0,3))),pt,(b,w)=>{(function(Y,P){be(P,!0);let R=L(P,"status",3,"info"),ee=x(()=>function(B){switch(B){case"success":return"task-success";case"warning":return"task-warning";case"error":return"task-error";default:return"task-info"}}(R()));var we=Wt(),Q=m(we),$e=l(Q,2);const O=x(()=>[fe.Hover]);Ee($e,{get content(){return P.text},get triggerOn(){return e(O)},maxWidth:"400px",children:(B,te)=>{var C=Dt(),le=m(C);ne(le,{size:1,color:"secondary",children:(Ue,st)=>{var He=ae();G(()=>de(He,P.text)),i(Ue,He)},$$slots:{default:!0}}),i(B,C)},$$slots:{default:!0}});var Se=l($e,2),ze=B=>{var te=Ct(),C=m(te);const le=x(()=>R()==="error"?"error":"neutral");ne(C,{size:1,get color(){return e(le)},children:(Ue,st)=>{var He=ae();G(()=>de(He,R()==="error"?"!":R()==="warning"?"⚠":"")),i(Ue,He)},$$slots:{default:!0}}),i(B,te)};S(Se,B=>{R()!=="error"&&R()!=="warning"||B(ze)}),G(()=>Ze(Q,1,`bullet-point ${e(ee)??""}`,"svelte-1tatwxk")),i(Y,we),xe()})(b,{get text(){return e(w)},status:"success"})}),i(n,a)};S(Fe,n=>{e(F),W(()=>e(F).length>0)&&n(Ie)});var Le=l(Fe,2),Oe=n=>{var a=Gt(),b=m(a);$t(b,{get repoUrl(){return e(f)},get repoName(){return e(N)}});var w=l(b,2);St(w,{get repoUrl(){return e(f)},get branchName(){return e(X)}}),i(n,a)};S(Le,n=>{e(f)&&e(N)&&e(X)&&n(Oe)});var Te=l(Re,2),qe=m(Te),Ne=n=>{const a=se(()=>ve()?"Unpin agent":"Pin agent"),b=se(()=>(z(fe),W(()=>[fe.Hover])));Ee(n,{get content(){return e(a)},get triggerOn(){return e(b)},side:"top",children:(w,Y)=>{Be(w,{variant:"ghost",color:"neutral",size:1,$$events:{click:P=>{P.stopPropagation(),U()()}},children:(P,R)=>{var ee=lt(),we=j(ee),Q=O=>{(function(Se,ze){const B=vt(ze,["children","$$slots","$$events","$$legacy"]);var te=Tt();nt(te,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 640 512",...B}));var C=m(te);zt(C,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2s-6.3 25.5 4.1 33.7l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L481.4 352c9.4-.4 18.1-4.9 23.9-12.3 6.1-7.8 8.2-17.9 5.8-27.5l-6.2-25c-10.3-41.3-35.4-75.7-68.7-98.3L428.9 96l-3.7-48H456c4.4 0 8.6-1.2 12.2-3.3 7-4.2 11.8-11.9 11.8-20.7 0-13.3-10.7-24-24-24H184c-13.3 0-24 10.7-24 24 0 8.8 4.8 16.5 11.8 20.7 3.6 2.1 7.7 3.3 12.2 3.3h30.8l-3.7 48-3.2 41.6zm214.5 168.1 9.3-121.5c.1-1.2.1-2.5.1-3.7h114.5c0 1.2 0 2.5.1 3.7l10.8 140.9c1.1 14.6 8.8 27.8 20.9 36 23.9 16.2 41.7 40.8 49.1 70.2l1.3 5.1H420l-76-59.6V216c0-13.3-10.7-24-24-24-10.4 0-19.2 6.6-22.6 15.8l-44.2-34.6zM344 367l-80-63h-83.5l1.3-5.1c4-16.1 11.2-30.7 20.7-43.3l-37.7-29.7c-13.7 17.8-23.9 38.6-29.6 61.4l-6.2 25c-2.4 9.6-.2 19.7 5.8 27.5s15.4 12.3 25.2 12.3h136v136c0 13.3 10.7 24 24 24s24-10.7 24-24v-121z"/>',!0),i(Se,te)})(O,{})},$e=O=>{yt(O,{})};S(we,O=>{ve()?O(Q):O($e,!1)}),i(P,ee)},$$slots:{default:!0}})},$$slots:{default:!0}})};S(qe,n=>{U()&&n(Ne)});var d=l(qe,2),h=n=>{const a=se(()=>(z(fe),W(()=>[fe.Hover])));Ee(n,{content:"SSH to agent",get triggerOn(){return e(a)},side:"top",children:(b,w)=>{const Y=se(()=>!e(q)),P=se(()=>e(q)?"SSH to agent":"SSH to agent (agent must be running or idle)");Be(b,{get disabled(){return e(Y)},variant:"ghost",color:"neutral",size:1,get title(){return e(P)},$$events:{click:R=>{R.stopPropagation(),e(H)()}},children:(R,ee)=>{Ke(R)},$$slots:{default:!0}})},$$slots:{default:!0}})};S(d,n=>{g()&&n(h)});var _=l(d,2);const D=se(()=>(z(fe),W(()=>[fe.Hover])));Ee(_,{content:"Delete agent",get triggerOn(){return e(D)},side:"top",children:(n,a)=>{const b=se(()=>ue()?"Deleting agent...":"Delete agent");Be(n,{variant:"ghost",color:"neutral",size:1,get disabled(){return ue()},get title(){return e(b)},$$events:{click:w=>{w.stopPropagation(),y()(s().remote_agent_id)}},children:(w,Y)=>{wt(w)},$$slots:{default:!0}})},$$slots:{default:!0}});var v=l(Te,2);const o=se(()=>(z(s()),W(()=>s().updated_at||s().started_at)));(function(n,a){be(a,!0);let b=L(a,"isRemote",3,!1),w=Ge(rt(Ft(a.timestamp)));const Y=It(a.timestamp,Q=>{T(w,Q,!0)});Xe(()=>{Y()});var P=Et(),R=m(P);ne(R,{size:1,color:"secondary",class:"location-text",children:(Q,$e)=>{var O=ae();G(()=>de(O,b()?"Running in the cloud":"Running locally")),i(Q,O)},$$slots:{default:!0}});var ee=l(R,2),we=m(ee);ne(we,{size:1,color:"secondary",class:"time-text",children:(Q,$e)=>{var O=qt(),Se=j(O),ze=l(Se),B=C=>{var le=ae();G(()=>de(le,e(w))),i(C,le)},te=C=>{var le=ae("Unknown time");i(C,le)};S(ze,C=>{a.timestamp?C(B):C(te,!1)}),G(()=>de(Se,(a.status===ce.agentRunning?"Last updated":"Started")+" ")),i(Q,O)},$$slots:{default:!0}}),i(n,P),xe()})(v,{get isRemote(){return e(re)},get status(){return z(s()),W(()=>s().status)},get timestamp(){return e(o)}}),G(()=>ot(A,"title",(z(s()),W(()=>s().is_setup_script_agent?"Generate a setup script":s().session_summary)))),i(t,$)},$$slots:{default:!0}}),G(t=>ie=Ze(p,1,"card-wrapper svelte-96mn5f",null,ie,t),[()=>({"selected-card":I(),"setup-script-card":s().is_setup_script_agent,deleting:ue()})],se),i(ge,p),xe()}function ke(ge,c){be(c,!0);const[H,q]=et(),F=()=>tt(N,"$sharedWebviewStore",H);let re=L(c,"selected",3,!1);const f=Ce(De.key),N=Ce(je);let X=Ge(!1),s=Ge(null),I=null;function ve(){T(s,null),I&&(clearTimeout(I),I=null)}let _e=x(()=>{var g;return((g=F().state)==null?void 0:g.pinnedAgents)||{}}),y=x(()=>{var g;return((g=e(_e))==null?void 0:g[c.agent.remote_agent_id])===!0}),V=x(()=>c.agent.status===ce.agentRunning||c.agent.status===ce.agentIdle);async function ue(){return!!e(V)&&await(async g=>await f.sshToRemoteAgent(g.remote_agent_id))(c.agent)}const U=x(()=>({onSSH:ue,canSSH:e(V)}));Zt(ge,{get agent(){return c.agent},get selected(){return re()},get isPinned(){return e(y)},get onSelect(){return c.onSelect},onDelete:()=>async function(g){var p,ie;ve(),T(X,!0);const Z=((p=F().state)==null?void 0:p.agentOverviews)||[];try{if(!await f.deleteRemoteAgent(g))throw new Error("Failed to delete agent");if(N.update(k=>{if(k)return{...k,agentOverviews:k.agentOverviews.filter(M=>M.remote_agent_id!==g)}}),(((ie=F().state)==null?void 0:ie.pinnedAgents)||{})[g])try{await f.deletePinnedAgentFromStore(g);const k=await f.getPinnedAgentsFromStore();N.update(M=>{if(M)return{...M,pinnedAgents:k}})}catch(k){console.error("Failed to remove pinned status:",k)}}catch(k){console.error("Failed to delete agent:",k),N.update(M=>{if(M)return{...M,agentOverviews:Z}}),T(s,k instanceof Error?k.message:"Failed to delete agent",!0),I=setTimeout(()=>{ve()},5e3)}finally{T(X,!1)}}(c.agent.remote_agent_id),onTogglePinned:()=>async function(g){try{e(y)?await f.deletePinnedAgentFromStore(g):await f.savePinnedAgentToStore(g,!0);const Z=await f.getPinnedAgentsFromStore();N.update(p=>{if(p)return{...p,pinnedAgents:Z}})}catch(Z){console.error("Failed to toggle pinned status:",Z)}}(c.agent.remote_agent_id),get sshConfig(){return e(U)},get deletionError(){return e(s)},set deletionError(g){T(s,g,!0)},get isDeleting(){return e(X)},set isDeleting(g){T(X,g,!0)}}),xe(),q()}var Jt=u('<div class="section-header svelte-1tegnqi"><!></div>');function Ae(ge,c){var H=Jt(),q=m(H);ne(q,{size:2,color:"secondary",children:(F,re)=>{var f=ae();G(()=>de(f,c.title)),i(F,f)},$$slots:{default:!0}}),i(ge,H)}var Kt=u('<div class="empty-state svelte-aiqmvp"><div class="l-loading-container svelte-aiqmvp"><!> <!></div></div>'),Qt=u('<div class="empty-state svelte-aiqmvp"><!></div>'),Vt=u('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Yt=u('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),es=u('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),ts=u('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),ss=u('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),as=u('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),ns=u("<!> <!> <!> <!> <!> <!>",1),rs=u('<div class="agent-list svelte-aiqmvp"><!></div>'),is=u('<div class="l-main svelte-1941nw6"><h1 class="l-main__title svelte-1941nw6"><span class="l-main__title-logo svelte-1941nw6"><!></span> Remote Agents</h1> <!></div>');gt(function(ge,c){be(c,!1);const H=new ht(ft),q=new kt(H,void 0,At,bt);H.registerConsumer(q),Je(je,q);const F=new De(H);Je(De.key,F),dt(()=>(q.fetchStateFromExtension().then(()=>{q.update(s=>{if(!s)return;const I=[...s.activeWebviews,"home"];return s.pinnedAgents?{...s,activeWebviews:I}:{...s,activeWebviews:I,pinnedAgents:{}}})}),()=>{H.dispose(),F.dispose()})),Ve();var re=is();Ye("message",ct,function(...s){var I;(I=H.onMessageFromExtension)==null||I.apply(this,s)});var f=m(re),N=m(f),X=m(N);Ot(X),function(s,I){be(I,!0);const[ve,_e]=et(),y=()=>tt(V,"$sharedWebviewStore",ve),V=Ce(je),ue=Ce(De.key);function U(t){V.update(r=>{if(r)return{...r,selectedAgentId:t}})}let g=x(()=>{var t;return xt(((t=y().state)==null?void 0:t.agentOverviews)||[])}),Z=x(()=>{var t;return((t=y().state)==null?void 0:t.pinnedAgents)||{}}),p=x(()=>e(g).reduce((t,r)=>{var $;return(($=e(Z))==null?void 0:$[r.remote_agent_id])===!0?t.pinned.push(r):r.status===ce.agentIdle&&r.has_updates?t.readyToReview.push(r):r.status===ce.agentRunning||r.status===ce.agentStarting||r.workspace_status===Me.workspaceResuming?t.running.push(r):r.status===ce.agentFailed?t.failed.push(r):r.status===ce.agentIdle||r.workspace_status===Me.workspacePaused||r.workspace_status===Me.workspacePausing?t.idle.push(r):t.additional.push(r),t},{pinned:[],readyToReview:[],running:[],idle:[],failed:[],additional:[]}));ut(()=>{var t;(t=y().state)!=null&&t.agentOverviews||ue.focusAugmentPanel()});var ie=rs(),k=m(ie),M=t=>{var r=Kt(),$=m(r),oe=m($);mt(oe,{});var A=l(oe,2);ne(A,{size:3,color:"secondary",children:(E,J)=>{var K=ae("Loading the Augment panel...");i(E,K)},$$slots:{default:!0}}),i(t,r)},We=(t,r)=>{var $=A=>{var E=Qt(),J=m(E);ne(J,{size:3,color:"secondary",children:(K,me)=>{var Pe=ae("No agents available");i(K,Pe)},$$slots:{default:!0}}),i(A,E)},oe=A=>{var E=ns(),J=j(E),K=d=>{var h=Vt(),_=j(h);Ae(_,{title:"Pinned"});var D=l(_,2);he(D,23,()=>e(p).pinned,(v,o)=>v.remote_agent_id+o,(v,o)=>{const n=x(()=>{var a;return e(o).remote_agent_id===((a=y().state)==null?void 0:a.selectedAgentId)});ke(v,{get agent(){return e(o)},get selected(){return e(n)},onSelect:U})}),i(d,h)};S(J,d=>{e(p).pinned.length>0&&d(K)});var me=l(J,2),Pe=d=>{var h=Yt(),_=j(h);Ae(_,{title:"Ready to review"});var D=l(_,2);he(D,23,()=>e(p).readyToReview,(v,o)=>v.remote_agent_id+o,(v,o)=>{const n=x(()=>{var a;return e(o).remote_agent_id===((a=y().state)==null?void 0:a.selectedAgentId)});ke(v,{get agent(){return e(o)},get selected(){return e(n)},onSelect:U})}),i(d,h)};S(me,d=>{e(p).readyToReview.length>0&&d(Pe)});var Re=l(me,2),Fe=d=>{var h=es(),_=j(h);Ae(_,{title:"Running agents"});var D=l(_,2);he(D,23,()=>e(p).running,(v,o)=>v.remote_agent_id+o,(v,o)=>{const n=x(()=>{var a;return e(o).remote_agent_id===((a=y().state)==null?void 0:a.selectedAgentId)});ke(v,{get agent(){return e(o)},get selected(){return e(n)},onSelect:U})}),i(d,h)};S(Re,d=>{e(p).running.length>0&&d(Fe)});var Ie=l(Re,2),Le=d=>{var h=ts(),_=j(h);Ae(_,{title:"Idle agents"});var D=l(_,2);he(D,23,()=>e(p).idle,(v,o)=>v.remote_agent_id+o,(v,o)=>{const n=x(()=>{var a;return e(o).remote_agent_id===((a=y().state)==null?void 0:a.selectedAgentId)});ke(v,{get agent(){return e(o)},get selected(){return e(n)},onSelect:U})}),i(d,h)};S(Ie,d=>{e(p).idle.length>0&&d(Le)});var Oe=l(Ie,2),Te=d=>{var h=ss(),_=j(h);Ae(_,{title:"Failed agents"});var D=l(_,2);he(D,23,()=>e(p).failed,(v,o)=>v.remote_agent_id+o,(v,o)=>{const n=x(()=>{var a;return e(o).remote_agent_id===((a=y().state)==null?void 0:a.selectedAgentId)});ke(v,{get agent(){return e(o)},get selected(){return e(n)},onSelect:U})}),i(d,h)};S(Oe,d=>{e(p).failed.length>0&&d(Te)});var qe=l(Oe,2),Ne=d=>{var h=as(),_=j(h);Ae(_,{title:"Other agents"});var D=l(_,2);he(D,23,()=>e(p).additional,(v,o)=>v.remote_agent_id+o,(v,o)=>{const n=x(()=>{var a;return e(o).remote_agent_id===((a=y().state)==null?void 0:a.selectedAgentId)});ke(v,{get agent(){return e(o)},get selected(){return e(n)},onSelect:U})}),i(d,h)};S(qe,d=>{e(p).additional.length>0&&d(Ne)}),i(A,E)};S(t,A=>{var E;((E=y().state)==null?void 0:E.agentOverviews.length)===0?A($):A(oe,!1)},r)};S(k,t=>{var r;(r=y().state)!=null&&r.agentOverviews?t(We,!1):t(M)}),i(s,ie),xe(),_e()}(l(f,2),{}),i(ge,re),xe()},{target:document.getElementById("app")});
