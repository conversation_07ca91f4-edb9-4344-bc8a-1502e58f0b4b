import{y as b,F,o as w,N as G,a1 as I,b as s,x as K,K as V,u as C,L as v,B as d,D as E,z as h,R as H,m as J,G as M,P as x}from"./legacy-AoIeRrIA.js";import{p as e,s as O,h as Q,g as $}from"./SpinnerAugment-mywmfXFR.js";import{C as U}from"./TextAreaAugment-B1LKPxPr.js";import{S as W}from"./SuccessfulButton-xgZ5Aax4.js";var X=b("<div><!></div>");function Y(c,t){let n=e(t,"marginRight",3,0);var i=X(),u=w(i);O(u,()=>t.children??F),G(()=>I(i,1,`c-icon-size c-icon-size--size-${t.size??""} c-icon-size--margin-right-${n()??""}`,"svelte-1x5zy5h")),s(c,i)}var Z=b('<span class="c-copy-button svelte-tq93gm"><!></span>');function ot(c,t){const n=Q(t);K(t,!1);let i=e(t,"size",8,1),u=e(t,"iconSize",24,()=>{}),k=e(t,"variant",8,"ghost-block"),N=e(t,"color",8,"neutral"),l=e(t,"text",24,()=>{}),P=e(t,"tooltip",8,"Copy"),L=e(t,"stickyColor",8,!1),R=e(t,"onCopy",8,async()=>{if(l()!==void 0){x(p,!0);try{await Promise.all([navigator.clipboard.writeText(typeof l()=="string"?l():await l()()),(a=250,new Promise(o=>setTimeout(o,a,g)))])}finally{x(p,!1)}var a,g;return"success"}}),S=e(t,"tooltipNested",24,()=>{}),p=J(!1);V();var f=Z(),T=w(f);const q=H(()=>({neutral:P(),success:"Copied!"}));W(T,{get defaultColor(){return N()},get size(){return i()},get variant(){return k()},get loading(){return h(p)},get stickyColor(){return L()},get tooltip(){return h(q)},stateVariant:{success:"soft"},onClick:R(),icon:C(()=>!n.text),get tooltipNested(){return S()},children:(a,g)=>{var o=v(),m=d(o);$(m,t,"text",{},null),s(a,o)},$$slots:{default:!0,iconLeft:(a,g)=>{Y(a,{get size(){return u()},children:(o,m)=>{var z=v(),B=d(z),j=r=>{var y=v(),D=d(y);$(D,t,"icon",{},null),s(r,y)},A=r=>{U(r,{})};E(B,r=>{C(()=>n.icon)?r(j):r(A,!1)}),s(o,z)},$$slots:{default:!0}})}}}),s(c,f),M()}export{ot as C,Y as I};
