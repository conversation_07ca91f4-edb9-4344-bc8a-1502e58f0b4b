import{x as ee,I as Q,J as ae,K as se,L as te,B as y,D as k,z as e,m as v,u as h,b as a,G as le,M as A,y as E,N as U,O as _,o as oe,E as q,P as p,Q as G,R as re}from"./legacy-AoIeRrIA.js";import{e as ie,i as ne}from"./host-qgbK079d.js";import{p as H,a as de,b as ce,e as ue}from"./SpinnerAugment-mywmfXFR.js";import{A as ve,D as d}from"./index-BLDiLrXG.js";import{B as J}from"./ButtonAugment-D7YBjBq5.js";import{C as he}from"./chevron-down-CUZr9PGN.js";import{T as pe}from"./CardAugment-DwIptXof.js";import{R}from"./chat-types-BfwvR7Kn.js";var fe=E('<div class="c-dropdown-label svelte-9n7h82"><!></div>'),me=E("<!> <!>",1),ge=E("<!> <!>",1),$e=E("<!> <!>",1);function xe(K,T){ee(T,!1);const[B,O]=de(),f=()=>ce(e(I),"$focusedIndex",B),w=v(),S=v(),s=v();let P=H(T,"onSave",8),i=H(T,"rule",8);const x=[{label:"Always",value:R.ALWAYS_ATTACHED,description:"These Rules will be included in every message you send to the agent."},{label:"Manual",value:R.MANUAL,description:"These Rules will be included when manually tagged in your message. You can tag Rules by @-mentioning them."},{label:"Auto",value:R.AGENT_REQUESTED,description:"These Rules will be included when the Agent decides to fetch them based on this file's description."}];let I=v(void 0),C=v(()=>{});Q(()=>G(i()),()=>{p(w,i().path)}),Q(()=>G(i()),()=>{p(S,i().type)}),Q(()=>e(S),()=>{p(s,x.find(t=>t.value===e(S)))}),ae(),se();var W=te(),j=y(W),F=t=>{pe(t,{content:"Workspace guidelines are always applied",children:(r,X)=>{J(r,{color:"accent",size:1,disabled:!0,children:(m,D)=>{var L=A("Always");a(m,L)},$$slots:{default:!0}})},$$slots:{default:!0}})},V=t=>{d.Root(t,{get requestClose(){return e(C)},set requestClose(r){p(C,r)},get focusedIndex(){return e(I)},set focusedIndex(r){ue(p(I,r),"$focusedIndex",B)},children:(r,X)=>{var m=$e(),D=y(m);d.Trigger(D,{children:(M,Z)=>{var c=fe(),g=oe(c);J(g,{color:"neutral",size:1,variant:"soft",children:(u,N)=>{var l=A();U(()=>_(l,(e(s),h(()=>e(s).label)))),a(u,l)},$$slots:{default:!0,iconRight:(u,N)=>{he(u,{slot:"iconRight"})}}}),a(M,c)},$$slots:{default:!0}});var L=q(D,2);d.Content(L,{side:"bottom",align:"start",children:(M,Z)=>{var c=ge(),g=y(c);ie(g,1,()=>x,ne,(l,o)=>{const $=re(()=>(e(s),e(o),h(()=>e(s).label===e(o).label)));d.Item(l,{onSelect:()=>async function(n){e(C)();try{await P()(n.value,n.value!==R.AGENT_REQUESTED||i().description?i().description:"Example description")}catch(b){console.error("RulesModeSelector: Error in onSave:",b)}}(e(o)),get highlight(){return e($)},children:(n,b)=>{var z=A();U(()=>_(z,(e(o),h(()=>e(o).label)))),a(n,z)},$$slots:{default:!0}})});var u=q(g,2),N=l=>{var o=me(),$=y(o);d.Separator($,{});var n=q($,2);d.Label(n,{children:(b,z)=>{var Y=A();U(()=>_(Y,(f(),e(s),h(()=>f()!==void 0?x[f()].description:e(s).description)))),a(b,Y)},$$slots:{default:!0}}),a(l,o)};k(u,l=>{(f()!==void 0||e(s))&&l(N)}),a(M,c)},$$slots:{default:!0}}),a(r,m)},$$slots:{default:!0},$$legacy:!0})};k(j,t=>{e(w),h(()=>e(w)===ve)?t(F):t(V,!1)}),a(K,W),le(),O()}export{xe as R};
