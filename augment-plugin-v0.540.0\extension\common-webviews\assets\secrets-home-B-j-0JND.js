import{x as Ht,a6 as Jt,P as t,m as h,z as e,aq as g,K as Kt,y as S,B as Xt,E as l,o as s,D as $,N as q,a8 as m,b,G as Zt,M as Ge,R as lt,O as J,ar as He,A as kt,as as xt,a3 as Je,at as Qt,au as ea}from"./legacy-AoIeRrIA.js";import"./design-system-init-Creeq9bS.js";import{h as ta,W as ee,e as nt,i as St,b as Ke}from"./host-qgbK079d.js";import{b as te,a as aa}from"./input-DCBQtNgo.js";import{s as Et}from"./event-modifiers-Bz4QCcZc.js";import{M as sa}from"./message-broker-EhzME3pO.js";import"./async-messaging-Dmg2N9Pf.js";var la=S('<div class="loading-state svelte-f2wma1"><div>Loading secrets...</div></div>'),na=S('<div class="error-state svelte-f2wma1"><div class="error-state-title svelte-f2wma1">Error Loading Secrets</div> <div class="error-state-description svelte-f2wma1"> </div></div>'),ia=S(`<div class="empty-state svelte-f2wma1"><div class="empty-state-title svelte-f2wma1">No Secrets Found</div> <div class="empty-state-description svelte-f2wma1">You haven't created any secrets yet. Secrets allow you to securely store sensitive
          information like API keys, passwords, and tokens that can be used by remote agents and
          other Augment features.</div></div>`),ra=S('<div class="secret-description svelte-f2wma1"> </div>'),ca=S('<div class="value-update-section svelte-f2wma1"><label class="svelte-f2wma1">New Value:</label> <div class="value-input-container svelte-f2wma1"><textarea class="secret-value-input svelte-f2wma1" placeholder="Enter new secret value" rows="3"></textarea> <div class="value-buttons svelte-f2wma1"><button class="update-button svelte-f2wma1" title="Update value">Update</button> <button class="cancel-button svelte-f2wma1" title="Cancel">Cancel</button></div></div></div>'),oa=S('<div class="tag-item svelte-f2wma1"><input type="text" class="tag-key-input svelte-f2wma1" placeholder="Tag key" title="Tag key"/> <span class="tag-separator svelte-f2wma1">:</span> <input type="text" class="tag-value-input svelte-f2wma1" placeholder="Tag value" title="Tag value"/> <button class="delete-tag-button svelte-f2wma1" title="Delete tag">×</button></div>'),da=S('<div class="tag-item add-tag-item svelte-f2wma1"><input type="text" class="tag-key-input svelte-f2wma1" placeholder="Tag key" title="Tag key"/> <span class="tag-separator svelte-f2wma1">:</span> <input type="text" class="tag-value-input svelte-f2wma1" placeholder="Tag value" title="Tag value"/> <button class="add-tag-confirm-button svelte-f2wma1" title="Add tag">✓</button> <button class="add-tag-cancel-button svelte-f2wma1" title="Cancel">×</button></div>'),va=S('<div class="no-tags svelte-f2wma1">No tags</div>'),ma=S('<button class="add-tag-button svelte-f2wma1" title="Add tag">Add Tag</button>'),ua=S('<div class="mount-path-container svelte-f2wma1"><input type="text" class="mount-path-input svelte-f2wma1" placeholder="Enter mount path (e.g., /tmp/secret)"/></div>'),fa=S('<div class="mount-submit-container svelte-f2wma1"><button class="mount-submit-button svelte-f2wma1" title="Apply mount settings"><!></button></div>'),pa=S('<div class="secret-details svelte-f2wma1"><div class="details-meta svelte-f2wma1"><span class="secret-created"> </span> <span class="secret-version"> </span></div> <div class="secret-tags svelte-f2wma1"><div class="tags-header svelte-f2wma1"><strong class="svelte-f2wma1">Tags:</strong></div> <div class="tags-container svelte-f2wma1"><!> <!> <!></div> <div class="tags-bottom-actions svelte-f2wma1"><!></div></div> <div class="secret-mount svelte-f2wma1"><div class="mount-header svelte-f2wma1"><strong class="svelte-f2wma1">Mount to Filesystem:</strong></div> <div class="mount-container svelte-f2wma1"><div class="mount-checkbox-container svelte-f2wma1"><input type="checkbox" class="mount-checkbox svelte-f2wma1"/> <label class="mount-checkbox-label svelte-f2wma1">Mount secret to filesystem</label></div> <!> <!></div></div></div>'),wa=S('<div class="secret-item svelte-f2wma1"><div class="secret-header svelte-f2wma1"><div class="secret-info svelte-f2wma1"><h3 class="secret-name svelte-f2wma1"> </h3> <!> <div class="secret-meta svelte-f2wma1"><span class="secret-updated"> </span> <span class="secret-size"> </span></div></div> <div class="secret-actions svelte-f2wma1"><button class="update-value-button svelte-f2wma1" title="Edit secret value">Edit</button> <button class="details-toggle svelte-f2wma1" title="Toggle details"> </button> <button class="delete-button svelte-f2wma1" title="Delete secret">Delete</button></div></div> <!> <!></div>'),ga=S('<div class="secrets-list svelte-f2wma1"></div>'),ba=S('<div class="error-message svelte-f2wma1"><strong>Error:</strong> </div>'),ha=S('<div class="tag-item svelte-f2wma1"><input type="text" class="tag-key-input svelte-f2wma1"/> <span class="tag-separator svelte-f2wma1">=</span> <input type="text" class="tag-value-input svelte-f2wma1"/> <button class="remove-tag-button svelte-f2wma1" title="Remove tag">×</button></div>'),ya=S('<div class="mount-path-container svelte-f2wma1"><input type="text" class="mount-path-input svelte-f2wma1" placeholder="Enter mount path (e.g., /tmp/secret)"/></div>'),ka=S('<div class="modal-overlay svelte-f2wma1" role="dialog" aria-modal="true" tabindex="-1"><div class="modal-dialog create-secret-dialog svelte-f2wma1" role="document"><div class="modal-header svelte-f2wma1"><h2 class="svelte-f2wma1">Create New Secret</h2> <button class="close-button svelte-f2wma1" title="Close">×</button></div> <div class="modal-body svelte-f2wma1"><!> <div class="form-group svelte-f2wma1"><label for="secret-name" class="svelte-f2wma1">Secret Name *</label> <input id="secret-name" type="text" placeholder="Enter secret name" class="svelte-f2wma1"/></div> <div class="form-group svelte-f2wma1"><label for="secret-value" class="svelte-f2wma1">Secret Value *</label> <textarea id="secret-value" placeholder="Enter secret value" rows="4" class="svelte-f2wma1"></textarea></div> <div class="form-group svelte-f2wma1"><label for="secret-description" class="svelte-f2wma1">Description</label> <input id="secret-description" type="text" placeholder="Optional description" class="svelte-f2wma1"/></div> <div class="form-group svelte-f2wma1"><label for="tags-section" class="svelte-f2wma1">Tags</label> <div id="tags-section" class="tags-section svelte-f2wma1"><!> <div class="add-tag-section svelte-f2wma1"><input type="text" placeholder="Tag key" class="tag-key-input svelte-f2wma1"/> <span class="tag-separator svelte-f2wma1">=</span> <input type="text" placeholder="Tag value" class="tag-value-input svelte-f2wma1"/> <button class="add-tag-button svelte-f2wma1" title="Add tag">+</button></div></div></div> <div class="form-group svelte-f2wma1"><label for="new-secret-mount" class="svelte-f2wma1">Mount to Filesystem</label> <div class="mount-container svelte-f2wma1"><div class="mount-checkbox-container svelte-f2wma1"><input type="checkbox" id="new-secret-mount" class="mount-checkbox svelte-f2wma1"/> <label for="new-secret-mount" class="mount-checkbox-label svelte-f2wma1">Mount secret to filesystem</label></div> <!></div></div></div> <div class="modal-footer svelte-f2wma1"><button class="cancel-button svelte-f2wma1">Cancel</button> <button class="create-button svelte-f2wma1"> </button></div></div></div>'),xa=S('<div class="modal-overlay svelte-f2wma1" role="dialog" aria-modal="true" tabindex="-1"><div class="modal-dialog svelte-f2wma1" role="document"><div class="modal-header svelte-f2wma1"><h3 class="svelte-f2wma1">Delete Secret</h3></div> <div class="modal-body svelte-f2wma1"><p class="svelte-f2wma1">Are you sure you want to delete the secret <strong> </strong>?</p> <p class="svelte-f2wma1">This action cannot be undone.</p></div> <div class="modal-footer svelte-f2wma1"><button class="cancel-button svelte-f2wma1">Cancel</button> <button class="confirm-delete-button svelte-f2wma1">Delete</button></div></div></div>'),Sa=S('<div class="secrets-panel svelte-f2wma1"><div class="secrets-header svelte-f2wma1"><h1 class="secrets-title svelte-f2wma1">Secrets Manager</h1> <div class="secrets-actions svelte-f2wma1"><button class="refresh-button svelte-f2wma1"><!></button> <button class="new-secret-button svelte-f2wma1">New Secret</button></div></div> <div class="secrets-content svelte-f2wma1"><!></div></div> <!> <!>',1);ea(function(Tt,_e){Ht(_e,!1);let f=h([]),he=h(!0),ye=h(null),E=h({}),K=h({}),me=h(null),c=h({}),y=h({}),j=h(""),L=h(""),_=h({}),k=h({}),ae={},U={},se={},N=h({});function Ft(){e(f)&&e(f).length>0&&(e(f).forEach(a=>{if(!(a.name in e(_))){const i=ut(a.tags),o=!!i,p=i||"";t(_,{...e(_),[a.name]:o}),t(k,{...e(k),[a.name]:p}),ae={...ae,[a.name]:p},U={...U,[a.name]:p},se={...se,[a.name]:o},t(N,{...e(N),[a.name]:!1})}}),t(it,JSON.stringify({enabled:e(_),paths:e(k),originalPaths:ae,originalEnabled:se})))}let it=h(""),Xe=h(!1),re=h(""),le=h(""),ke=h(""),O=h({}),R=h(!1),xe=h(null),Se=h(!1),Ee=h("");const ue=new sa(ta);function rt(){t(he,!0),t(ye,null),ue.send({type:ee.listSecretsRequest,data:{}},1e4).then(a=>{var i;i=a.data,t(he,!1),t(f,i.secrets),e(f).forEach(o=>{e(E)[o.name]||g(E,e(E)[o.name]={showValueInput:!1,newValue:""}),e(c)[o.name]||g(c,e(c)[o.name]={tags:{...o.tags}}),e(y)[o.name]||g(y,e(y)[o.name]=!1),ft(o.name,o.tags)}),Ft(),e(f).length===0&&t(ye,null)}).catch(a=>{t(he,!1),a.name==="MessageTimeout"?t(ye,"Request timed out. Please try again."):t(ye,`Failed to load secrets: ${a.message||a}`),console.error("Failed to load secrets:",a)})}function Ie(){t(Xe,!1),t(re,""),t(le,""),t(ke,""),t(O,{}),t(R,!1),t(xe,null),t(j,""),t(L,""),t(Se,!1),t(Ee,"")}async function Rt(){var a;if(e(re).trim()&&e(le).trim()){t(R,!0),t(xe,null);try{const i={...e(O)};e(Se)&&e(Ee).trim()&&(i["augment:mount_point"]=e(Ee).trim());const o=await ue.send({type:ee.createSecretRequest,data:{name:e(re).trim(),value:e(le),tags:i,description:e(ke).trim()}});if(o.type!==ee.createSecretResponse||!o.data.success)throw new Error(((a=o.data)==null?void 0:a.error)||"Failed to create secret");{const p={name:e(re).trim(),value:e(le),tags:i,description:e(ke).trim(),created_at:new Date().toISOString(),updated_at:o.data.updatedAt||new Date().toISOString(),version:o.data.version||"1",value_size_bytes:new TextEncoder().encode(e(le)).length};t(f,[p,...e(f)]),g(c,e(c)[p.name]={tags:{...p.tags}}),t(c,{...e(c)}),ft(p.name,p.tags),Ie()}}catch(i){console.error("Failed to create secret:",i),t(xe,i instanceof Error?i.message:"Failed to create secret")}finally{t(R,!1)}}}function ct(a,i){a.trim()&&(g(O,e(O)[a.trim()]=i),t(O,{...e(O)}))}function ot(a){delete e(O)[a],t(O,{...e(O)})}function dt(a){g(E,e(E)[a]={showValueInput:!1,newValue:""}),t(E,{...e(E)})}async function Ve(a){var i,o;try{const p=e(f).find(D=>D.name===a);if(!p)throw new Error("Secret not found");const I=((i=e(c)[a])==null?void 0:i.tags)||{},C=await ue.send({type:ee.updateSecretRequest,data:{name:a,value:"",tags:I,description:p.description,expectedVersion:p.version}});if(C.type!==ee.updateSecretResponse||!C.data.success)throw new Error(((o=C.data)==null?void 0:o.error)||"Failed to update secret tags");{const D=e(f).findIndex(ne=>ne.name===a);D!==-1&&(g(f,e(f)[D]={...e(f)[D],tags:I,updated_at:C.data.updatedAt||new Date().toISOString(),version:C.data.version||e(f)[D].version}),t(f,[...e(f)]))}}catch(p){console.error("Failed to update secret tags:",p)}}function Ze(){t(me,null)}function vt(a){try{return new Date(a).toLocaleString()}catch{return a}}function mt(a){const i={};for(const[o,p]of Object.entries(a))o.startsWith("augment:")||(i[o]=p);return i}function ut(a){return a["augment:mount_point"]||null}function ft(a,i){const o=ut(i),p=!!o,I=o||"";g(_,e(_)[a]=p),g(k,e(k)[a]=I),ae[a]=I,U[a]=I,se[a]=p,g(N,e(N)[a]=!1),t(_,{...e(_)}),t(k,{...e(k)}),ae={...ae},U={...U},se={...se},t(N,{...e(N)})}Jt(()=>(ue.send({type:ee.secretsHomePanelLoaded,data:{}}),rt(),()=>{ue.dispose()})),Kt();var pt=Sa(),wt=Xt(pt),gt=s(wt),_t=l(s(gt),2),qe=s(_t),It=s(qe),Vt=a=>{var i=Ge("Refreshing...");b(a,i)},$t=a=>{var i=Ge("Refresh");b(a,i)};$(It,a=>{e(he)?a(Vt):a($t,!1)});var At=l(qe,2),Ot=l(gt,2),Ct=s(Ot),Dt=a=>{var i=la();b(a,i)},Mt=(a,i)=>{var o=I=>{var C=na(),D=l(s(C),2),ne=s(D);q(()=>J(ne,e(ye))),b(I,C)},p=(I,C)=>{var D=z=>{var ie=ia();b(z,ie)},ne=z=>{var ie=ga();nt(ie,5,()=>e(f),T=>T.name,(T,n)=>{var W=wa(),fe=s(W),$e=s(fe),je=s($e),ze=s(je),Be=l(je,2),Ae=d=>{var x=ra(),X=s(x);q(()=>J(X,e(n).description)),b(d,x)};$(Be,d=>{e(n).description&&d(Ae)});var Oe=l(Be,2),Ce=s(Oe),Qe=s(Ce),et=l(Ce,2),Pe=s(et),Le=l($e,2),Ue=s(Le),De=l(Ue,2),tt=s(De),Me=l(De,2),Te=l(fe,2),at=d=>{var x=ca(),X=s(x),pe=l(X,2),Z=s(pe),ce=l(Z,2),we=s(ce),oe=l(we,2);q(()=>{Je(X,"for",`secret-value-${e(n).name??""}`),Je(Z,"id",`secret-value-${e(n).name??""}`)}),te(Z,()=>e(E)[e(n).name].newValue,V=>g(E,e(E)[e(n).name].newValue=V)),m("click",we,()=>async function(V){var Ne,We;const G=(Ne=e(E)[V])==null?void 0:Ne.newValue;if(G)try{const de=e(f).find(ve=>ve.name===V);if(!de)throw new Error("Secret not found");const ge=await ue.send({type:ee.updateSecretRequest,data:{name:V,value:G,tags:de.tags,description:de.description,expectedVersion:de.version}});if(ge.type!==ee.updateSecretResponse||!ge.data.success)throw new Error(((We=ge.data)==null?void 0:We.error)||"Failed to update secret");{const ve=e(f).findIndex(st=>st.name===V);ve!==-1&&(g(f,e(f)[ve]={...e(f)[ve],value:G,updated_at:ge.data.updatedAt||new Date().toISOString(),version:ge.data.version||e(f)[ve].version,value_size_bytes:new TextEncoder().encode(G).length}),t(f,[...e(f)])),dt(V)}}catch(de){console.error("Failed to update secret value:",de)}}(e(n).name)),m("click",oe,()=>dt(e(n).name)),b(d,x)};$(Te,d=>{var x;(x=e(E)[e(n).name])!=null&&x.showValueInput&&d(at)});var u=l(Te,2),Y=d=>{var x=pa(),X=s(x),pe=s(X),Z=s(pe),ce=l(pe,2),we=s(ce),oe=l(X,2),V=l(s(oe),2),G=s(V);nt(G,1,()=>{var v;return Object.entries(mt(((v=e(c)[e(n).name])==null?void 0:v.tags)||{}))},St,(v,w)=>{var F=kt(()=>xt(e(w),2));let M=()=>e(F)[0];var H=oa(),be=s(H),r=l(be,4),B=l(r,2);q(()=>{He(be,M()),He(r,e(F)[1])}),m("input",be,A=>{const P=A.target;(function(Q,Fe,Re){if(Re!==Fe&&Re.trim()){const Gt=e(c)[Q].tags[Fe];delete e(c)[Q].tags[Fe],g(c,e(c)[Q].tags[Re.trim()]=Gt),t(c,{...e(c)}),Ve(Q)}})(e(n).name,M(),P?P.value:"")}),m("input",r,A=>{const P=A.target;var Q,Fe,Re;Q=e(n).name,Fe=M(),Re=P?P.value:"",g(c,e(c)[Q].tags[Fe]=Re),t(c,{...e(c)}),Ve(Q)}),m("click",B,()=>{return A=e(n).name,P=M(),delete e(c)[A].tags[P],t(c,{...e(c)}),void Ve(A);var A,P}),b(v,H)});var Ne=l(G,2),We=v=>{var w=da(),F=s(w),M=l(F,4),H=l(M,2),be=l(H,2);te(F,()=>e(j),r=>t(j,r)),te(M,()=>e(L),r=>t(L,r)),m("click",H,()=>{return r=e(n).name,B=e(j),A=e(L),B.trim()&&(g(c,e(c)[r].tags[B.trim()]=A),t(c,{...e(c)}),Ve(r)),t(j,""),t(L,""),g(y,e(y)[r]=!1),void t(y,{...e(y)});var r,B,A}),m("click",be,()=>{return r=e(n).name,t(j,""),t(L,""),g(y,e(y)[r]=!1),void t(y,{...e(y)});var r}),b(v,w)};$(Ne,v=>{e(y)[e(n).name]&&v(We)});var de=l(Ne,2),ge=v=>{var w=va();b(v,w)};$(de,v=>{var w;Object.keys(mt(((w=e(c)[e(n).name])==null?void 0:w.tags)||{})).length!==0||e(y)[e(n).name]||v(ge)});var ve=l(V,2),st=s(ve),zt=v=>{var w=ma();m("click",w,()=>{return F=e(n).name,g(y,e(y)[F]=!0),void t(y,{...e(y)});var F}),b(v,w)};$(st,v=>{e(y)[e(n).name]||v(zt)});var Bt=l(oe,2),Pt=l(s(Bt),2),ht=s(Pt),Ye=s(ht),Lt=l(Ye,2),yt=l(ht,2),Ut=v=>{var w=ua(),F=s(w);te(F,()=>e(k)[e(n).name],M=>g(k,e(k)[e(n).name]=M)),b(v,w)};$(yt,v=>{e(_)[e(n).name]&&v(Ut)});var Wt=l(yt,2),Yt=v=>{var w=fa(),F=s(w),M=s(F),H=r=>{var B=Ge("⏳");b(r,B)},be=r=>{var B=Ge("✓");b(r,B)};$(M,r=>{e(N)[e(n).name]?r(H):r(be,!1)}),q(()=>F.disabled=e(N)[e(n).name]),m("click",F,()=>async function(r){var B;if(!e(N)[r]){t(N,{...e(N),[r]:!0});try{const A=e(f).find(Q=>Q.name===r);if(!A)throw new Error("Secret not found");e(c)[r]||g(c,e(c)[r]={tags:{...A.tags}});const P={...A.tags,...e(c)[r].tags};e(_)[r]&&((B=e(k)[r])!=null&&B.trim())?(P["augment:mount_point"]=e(k)[r].trim(),U[r]=e(k)[r].trim(),U={...U}):delete P["augment:mount_point"],g(c,e(c)[r].tags=P),t(c,{...e(c)}),await Ve(r),ae={...ae,[r]:e(k)[r]},se={...se,[r]:e(_)[r]}}catch(A){console.error("Failed to update mount settings:",A)}finally{t(N,{...e(N),[r]:!1})}}}(e(n).name)),b(v,w)};$(Wt,v=>{e(it)&&function(w){const F=e(k)[w]||"",M=ae[w]||"",H=e(_)[w]||!1;return H!==(se[w]||!1)||(H?F.trim()!==M.trim():!H&&M.trim()!=="")}(e(n).name)&&v(Yt)}),q(v=>{J(Z,`Created: ${v??""}`),J(we,`Version: ${e(n).version??""}`),Je(Ye,"id",`mount-${e(n).name??""}`),Qt(Ye,e(_)[e(n).name]||!1),Je(Lt,"for",`mount-${e(n).name??""}`)},[()=>vt(e(n).created_at)],lt),m("change",Ye,()=>function(v){var w;if(t(_,{...e(_),[v]:!e(_)[v]}),e(_)[v]){const F=U[v];F&&t(k,{...e(k),[v]:F})}else(w=e(k)[v])!=null&&w.trim()&&(U={...U,[v]:e(k)[v]}),t(k,{...e(k),[v]:""})}(e(n).name)),b(d,x)};$(u,d=>{e(K)[e(n).name]&&d(Y)}),q((d,x)=>{J(ze,e(n).name),J(Qe,`Updated: ${d??""}`),J(Pe,`Size: ${x??""}`),J(tt,(e(K)[e(n).name]?"▼":"▶")+" Details")},[()=>vt(e(n).updated_at),()=>{return(d=e(n).value_size_bytes)<1024?`${d} B`:d<1048576?`${(d/1024).toFixed(1)} KB`:`${(d/1048576).toFixed(1)} MB`;var d}],lt),m("click",Ue,()=>{return d=e(n).name,g(E,e(E)[d]={showValueInput:!0,newValue:""}),void t(E,{...e(E)});var d}),m("click",De,()=>{return d=e(n).name,g(K,e(K)[d]=!e(K)[d]),void t(K,{...e(K)});var d}),m("click",Me,()=>{return d=e(n).name,void t(me,d);var d}),b(T,W)}),b(z,ie)};$(I,z=>{e(f).length===0?z(D):z(ne,!1)},C)};$(a,I=>{e(ye)?I(o):I(p,!1)},i)};$(Ct,a=>{e(he)?a(Dt):a(Mt,!1)});var bt=l(wt,2),Nt=a=>{var i=ka(),o=s(i),p=s(o),I=l(s(p),2),C=l(p,2),D=s(C),ne=u=>{var Y=ba(),d=l(s(Y));q(()=>J(d,` ${e(xe)??""}`)),b(u,Y)};$(D,u=>{e(xe)&&u(ne)});var z=l(D,2),ie=l(s(z),2),T=l(z,2),n=l(s(T),2),W=l(T,2),fe=l(s(W),2),$e=l(W,2),je=l(s($e),2),ze=s(je);nt(ze,1,()=>Object.entries(e(O)),St,(u,Y)=>{var d=kt(()=>xt(e(Y),2));let x=()=>e(d)[0],X=()=>e(d)[1];var pe=ha(),Z=s(pe),ce=l(Z,4),we=l(ce,2);q(()=>{He(Z,x()),Z.disabled=e(R),He(ce,X()),ce.disabled=e(R),we.disabled=e(R)}),m("blur",Z,oe=>{const V=oe.target,G=V&&V.value?V.value.trim():"";G!==x()&&G&&(ot(x()),ct(G,X()))}),m("input",ce,oe=>{const V=oe.target;g(O,e(O)[x()]=V?V.value:""),t(O,{...e(O)})}),m("click",we,()=>ot(x())),b(u,pe)});var Be=l(ze,2),Ae=s(Be),Oe=l(Ae,4),Ce=l(Oe,2),Qe=l($e,2),et=l(s(Qe),2),Pe=s(et),Le=s(Pe),Ue=l(Pe,2),De=u=>{var Y=ya(),d=s(Y);q(()=>d.disabled=e(R)),te(d,()=>e(Ee),x=>t(Ee,x)),b(u,Y)};$(Ue,u=>{e(Se)&&u(De)});var tt=l(C,2),Me=s(tt),Te=l(Me,2),at=s(Te);q((u,Y)=>{ie.disabled=e(R),n.disabled=e(R),fe.disabled=e(R),Ae.disabled=e(R),Oe.disabled=e(R),Ce.disabled=u,Le.disabled=e(R),Me.disabled=e(R),Te.disabled=Y,J(at,e(R)?"Creating...":"Create Secret")},[()=>e(R)||!e(j).trim(),()=>e(R)||!e(re).trim()||!e(le).trim()],lt),m("click",I,Ie),te(ie,()=>e(re),u=>t(re,u)),te(n,()=>e(le),u=>t(le,u)),te(fe,()=>e(ke),u=>t(ke,u)),te(Ae,()=>e(j),u=>t(j,u)),te(Oe,()=>e(L),u=>t(L,u)),m("click",Ce,()=>{ct(e(j),e(L)),t(j,""),t(L,"")}),aa(Le,()=>e(Se),u=>t(Se,u)),m("click",Me,Ie),m("click",Te,Rt),m("click",o,Et(function(u){Ke.call(this,_e,u)})),m("keydown",o,function(u){Ke.call(this,_e,u)}),m("click",i,Ie),m("keydown",i,u=>u.key==="Escape"&&Ie()),b(a,i)};$(bt,a=>{e(Xe)&&a(Nt)});var qt=l(bt,2),jt=a=>{var i=xa(),o=s(i),p=l(s(o),2),I=s(p),C=l(s(I)),D=s(C),ne=l(p,2),z=s(ne),ie=l(z,2);q(()=>J(D,e(me))),m("click",z,Ze),m("click",ie,()=>e(me)&&async function(T){var n;try{const W=await ue.send({type:ee.deleteSecretRequest,data:{name:T}});if(W.type!==ee.deleteSecretResponse||!W.data.success)throw new Error(((n=W.data)==null?void 0:n.error)||"Failed to delete secret");t(f,e(f).filter(fe=>fe.name!==T)),delete e(E)[T],delete e(c)[T],delete e(y)[T],delete e(K)[T],t(E,{...e(E)}),t(c,{...e(c)}),t(y,{...e(y)}),t(K,{...e(K)})}catch(W){console.error("Failed to delete secret:",W)}finally{t(me,null)}}(e(me))),m("click",o,Et(function(T){Ke.call(this,_e,T)})),m("keydown",o,function(T){Ke.call(this,_e,T)}),m("click",i,Ze),m("keydown",i,T=>T.key==="Escape"&&Ze()),b(a,i)};$(qt,a=>{e(me)&&a(jt)}),q(()=>qe.disabled=e(he)),m("click",qe,rt),m("click",At,function(){t(Xe,!0),t(re,""),t(le,""),t(ke,""),t(O,{}),t(R,!1),t(xe,null),t(j,""),t(L,""),t(Se,!1),t(Ee,"")}),b(Tt,pt),Zt()},{target:document.getElementById("app")});
