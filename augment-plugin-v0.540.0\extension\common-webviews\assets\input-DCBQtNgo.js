import{i as h,t as o,u as s,v as d}from"./legacy-AoIeRrIA.js";function p(a,e,t=e){var r=h();o(a,"input",l=>{var n=l?a.defaultValue:a.value;if(n=v(a)?c(n):n,t(n),r&&n!==(n=e())){var f=a.selectionStart,i=a.selectionEnd;a.value=n??"",i!==null&&(a.selectionStart=f,a.selectionEnd=Math.min(i,a.value.length))}}),s(e)==null&&a.value&&t(v(a)?c(a.value):a.value),d(()=>{var l=e();v(a)&&l===c(a.value)||(a.type!=="date"||l||a.value)&&l!==a.value&&(a.value=l??"")})}function k(a,e,t=e){o(a,"change",r=>{var l=r?a.defaultChecked:a.checked;t(l)}),s(e)==null&&t(a.checked),d(()=>{var r=e();a.checked=!!r})}function v(a){var e=a.type;return e==="number"||e==="range"}function c(a){return a===""?null:+a}export{k as a,p as b};
