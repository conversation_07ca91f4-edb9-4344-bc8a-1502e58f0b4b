var u=Object.defineProperty;var o=(c,t,e)=>t in c?u(c,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):c[t]=e;var s=(c,t,e)=>o(c,typeof t!="symbol"?t+"":t,e);function g(c){return c.replace(/\\/g,"/")}function r(c){return c.split("/").filter(t=>t.length>0)}function d(c){return r(c).at(-1)??""}function S(c){return r(c).slice(0,-1).join("/")}const n=class n{static getStack(){const t=typeof document<"u"?document:null;return t?(t[n.STACK_KEY]||(t[n.STACK_KEY]=[]),t[n.STACK_KEY]):[]}static add(t){const e=n.getStack();e.includes(t)||e.push(t)}static remove(t){const e=n.getStack(),i=e.indexOf(t);return i!==-1&&(e.splice(i,1),!0)}static isActive(t){const e=n.getStack();return e.length>0&&e[e.length-1]===t}static getActive(){const t=n.getStack();return t.length>0?t[t.length-1]:void 0}static size(){return n.getStack().length}static isEmpty(){return n.size()===0}static clear(){const t=typeof document<"u"?document:null;t&&(t[n.STACK_KEY]=[])}static getAll(){return[...n.getStack()]}};s(n,"STACK_KEY","__focusTrapStack");let a=n;export{a as F,S as a,d as g,g as n};
