var le=Object.defineProperty;var ue=(t,e,n)=>e in t?le(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var T=(t,e,n)=>ue(t,typeof e!="symbol"?e+"":e,n);import{W as P}from"./host-qgbK079d.js";import{Z as Q}from"./legacy-AoIeRrIA.js";class ce{constructor(e){T(this,"_applyingFilePaths",Q([]));T(this,"_appliedFilePaths",Q([]));this._asyncMsgSender=e}get applyingFilePaths(){let e=[];return this._applyingFilePaths.subscribe(n=>{e=n})(),e}get appliedFilePaths(){let e=[];return this._appliedFilePaths.subscribe(n=>{e=n})(),e}async getDiffExplanation(e,n,r=3e4){try{return(await this._asyncMsgSender.send({type:P.diffExplanationRequest,data:{changedFiles:e,apikey:n}},r)).data.explanation}catch(a){return console.error("Failed to get diff explanation:",a),[]}}async groupChanges(e,n=!1,r){try{return(await this._asyncMsgSender.send({type:P.diffGroupChangesRequest,data:{changedFiles:e,changesById:n,apikey:r}})).data.groupedChanges}catch(a){return console.error("Failed to group changes:",a),[]}}async getDescriptions(e,n){try{const r=await this._asyncMsgSender.send({type:P.diffDescriptionsRequest,data:{groupedChanges:e,apikey:n}},1e5);return{explanation:r.data.explanation,error:r.data.error}}catch(r){return console.error("Failed to get descriptions:",r),{explanation:[],error:`Failed to get descriptions: ${r instanceof Error?r.message:String(r)}`}}}async canApplyChanges(){try{return(await this._asyncMsgSender.send({type:P.canApplyChangesRequest},1e4)).data}catch(e){return console.error("Failed to check if can apply changes:",e),{canApply:!1,hasUnstagedChanges:!1,error:`Failed to check if can apply changes: ${e instanceof Error?e.message:String(e)}`}}}async applyChanges(e,n,r){this._applyingFilePaths.update(a=>[...a.filter(o=>o!==e),e]);try{const a=await this._asyncMsgSender.send({type:P.applyChangesRequest,data:{path:e,originalCode:n,newCode:r}},3e4),{success:o,hasConflicts:s,error:i}=a.data;return o?this._appliedFilePaths.update(u=>[...u.filter(l=>l!==e),e]):i&&console.error("Failed to apply changes:",i),{success:o,hasConflicts:s,error:i}}catch(a){return console.error("applyChanges error",a),{success:!1,error:`Error: ${a instanceof Error?a.message:String(a)}`}}finally{this._applyingFilePaths.update(a=>a.filter(o=>o!==e))}}async previewApplyChanges(e,n,r){try{return(await this._asyncMsgSender.send({type:P.previewApplyChangesRequest,data:{path:e,originalCode:n,newCode:r}},3e4)).data}catch(a){return console.error("previewApplyChanges error",a),{mergedContent:"",hasConflicts:!1,error:`Error: ${a instanceof Error?a.message:String(a)}`}}}async openFile(e){try{const n=await this._asyncMsgSender.send({type:P.openFileRequest,data:{path:e}},1e4);return n.data.success||console.error("Failed to open file:",n.data.error),n.data.success}catch(n){console.error("openFile error",n)}return!1}async stashUnstagedChanges(){try{const e=await this._asyncMsgSender.send({type:P.stashUnstagedChangesRequest},1e4);return e.data.success||console.error("Failed to stash unstaged changes:",e.data.error),e.data.success}catch(e){console.error("stashUnstagedChanges error",e)}return!1}async reportApplyChangesEvent(){await this._asyncMsgSender.send({type:P.reportAgentChangesApplied})}}T(ce,"key","remoteAgentsDiffOpsModel");function S(){}function V(t,e,n,r,a){for(var o,s=[];e;)s.push(e),o=e.previousComponent,delete e.previousComponent,e=o;s.reverse();for(var i=0,u=s.length,l=0,c=0;i<u;i++){var p=s[i];if(p.removed)p.value=t.join(r.slice(c,c+p.count)),c+=p.count;else{if(!p.added&&a){var d=n.slice(l,l+p.count);d=d.map(function(f,g){var m=r[c+g];return m.length>f.length?m:f}),p.value=t.join(d)}else p.value=t.join(n.slice(l,l+p.count));l+=p.count,p.added||(c+=p.count)}}return s}function X(t,e){var n;for(n=0;n<t.length&&n<e.length;n++)if(t[n]!=e[n])return t.slice(0,n);return t.slice(0,n)}function Y(t,e){var n;if(!t||!e||t[t.length-1]!=e[e.length-1])return"";for(n=0;n<t.length&&n<e.length;n++)if(t[t.length-(n+1)]!=e[e.length-(n+1)])return t.slice(-n);return t.slice(-n)}function H(t,e,n){if(t.slice(0,e.length)!=e)throw Error("string ".concat(JSON.stringify(t)," doesn't start with prefix ").concat(JSON.stringify(e),"; this is a bug"));return n+t.slice(e.length)}function J(t,e,n){if(!e)return t+n;if(t.slice(-e.length)!=e)throw Error("string ".concat(JSON.stringify(t)," doesn't end with suffix ").concat(JSON.stringify(e),"; this is a bug"));return t.slice(0,-e.length)+n}function j(t,e){return H(t,e,"")}function N(t,e){return J(t,e,"")}function ee(t,e){return e.slice(0,function(n,r){var a=0;n.length>r.length&&(a=n.length-r.length);var o=r.length;n.length<r.length&&(o=n.length);var s=Array(o),i=0;s[0]=0;for(var u=1;u<o;u++){for(r[u]==r[i]?s[u]=s[i]:s[u]=i;i>0&&r[u]!=r[i];)i=s[i];r[u]==r[i]&&i++}i=0;for(var l=a;l<n.length;l++){for(;i>0&&n[l]!=r[i];)i=s[i];n[l]==r[i]&&i++}return i}(t,e))}S.prototype={diff:function(t,e){var n,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},a=r.callback;typeof r=="function"&&(a=r,r={});var o=this;function s(h){return h=o.postProcess(h,r),a?(setTimeout(function(){a(h)},0),!0):h}t=this.castInput(t,r),e=this.castInput(e,r),t=this.removeEmpty(this.tokenize(t,r));var i=(e=this.removeEmpty(this.tokenize(e,r))).length,u=t.length,l=1,c=i+u;r.maxEditLength!=null&&(c=Math.min(c,r.maxEditLength));var p=(n=r.timeout)!==null&&n!==void 0?n:1/0,d=Date.now()+p,f=[{oldPos:-1,lastComponent:void 0}],g=this.extractCommon(f[0],e,t,0,r);if(f[0].oldPos+1>=u&&g+1>=i)return s(V(o,f[0].lastComponent,e,t,o.useLongestToken));var m=-1/0,F=1/0;function C(){for(var h=Math.max(m,-l);h<=Math.min(F,l);h+=2){var b=void 0,y=f[h-1],k=f[h+1];y&&(f[h-1]=void 0);var w=!1;if(k){var v=k.oldPos-h;w=k&&0<=v&&v<i}var O=y&&y.oldPos+1<u;if(w||O){if(b=!O||w&&y.oldPos<k.oldPos?o.addToPath(k,!0,!1,0,r):o.addToPath(y,!1,!0,1,r),g=o.extractCommon(b,e,t,h,r),b.oldPos+1>=u&&g+1>=i)return s(V(o,b.lastComponent,e,t,o.useLongestToken));f[h]=b,b.oldPos+1>=u&&(F=Math.min(F,h-1)),g+1>=i&&(m=Math.max(m,h+1))}else f[h]=void 0}l++}if(a)(function h(){setTimeout(function(){if(l>c||Date.now()>d)return a();C()||h()},0)})();else for(;l<=c&&Date.now()<=d;){var E=C();if(E)return E}},addToPath:function(t,e,n,r,a){var o=t.lastComponent;return o&&!a.oneChangePerToken&&o.added===e&&o.removed===n?{oldPos:t.oldPos+r,lastComponent:{count:o.count+1,added:e,removed:n,previousComponent:o.previousComponent}}:{oldPos:t.oldPos+r,lastComponent:{count:1,added:e,removed:n,previousComponent:o}}},extractCommon:function(t,e,n,r,a){for(var o=e.length,s=n.length,i=t.oldPos,u=i-r,l=0;u+1<o&&i+1<s&&this.equals(n[i+1],e[u+1],a);)u++,i++,l++,a.oneChangePerToken&&(t.lastComponent={count:1,previousComponent:t.lastComponent,added:!1,removed:!1});return l&&!a.oneChangePerToken&&(t.lastComponent={count:l,previousComponent:t.lastComponent,added:!1,removed:!1}),t.oldPos=i,u},equals:function(t,e,n){return n.comparator?n.comparator(t,e):t===e||n.ignoreCase&&t.toLowerCase()===e.toLowerCase()},removeEmpty:function(t){for(var e=[],n=0;n<t.length;n++)t[n]&&e.push(t[n]);return e},castInput:function(t){return t},tokenize:function(t){return Array.from(t)},join:function(t){return t.join("")},postProcess:function(t){return t}};var _="a-zA-Z0-9_\\u{C0}-\\u{FF}\\u{D8}-\\u{F6}\\u{F8}-\\u{2C6}\\u{2C8}-\\u{2D7}\\u{2DE}-\\u{2FF}\\u{1E00}-\\u{1EFF}",pe=new RegExp("[".concat(_,"]+|\\s+|[^").concat(_,"]"),"ug"),M=new S;function te(t,e,n,r){if(e&&n){var a=e.value.match(/^\s*/)[0],o=e.value.match(/\s*$/)[0],s=n.value.match(/^\s*/)[0],i=n.value.match(/\s*$/)[0];if(t){var u=X(a,s);t.value=J(t.value,s,u),e.value=j(e.value,u),n.value=j(n.value,u)}if(r){var l=Y(o,i);r.value=H(r.value,i,l),e.value=N(e.value,l),n.value=N(n.value,l)}}else if(n)t&&(n.value=n.value.replace(/^\s*/,"")),r&&(r.value=r.value.replace(/^\s*/,""));else if(t&&r){var c=r.value.match(/^\s*/)[0],p=e.value.match(/^\s*/)[0],d=e.value.match(/\s*$/)[0],f=X(c,p);e.value=j(e.value,f);var g=Y(j(c,f),d);e.value=N(e.value,g),r.value=H(r.value,c,g),t.value=J(t.value,c,c.slice(0,c.length-g.length))}else if(r){var m=r.value.match(/^\s*/)[0],F=ee(e.value.match(/\s*$/)[0],m);e.value=N(e.value,F)}else if(t){var C=ee(t.value.match(/\s*$/)[0],e.value.match(/^\s*/)[0]);e.value=j(e.value,C)}}M.equals=function(t,e,n){return n.ignoreCase&&(t=t.toLowerCase(),e=e.toLowerCase()),t.trim()===e.trim()},M.tokenize=function(t){var e,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(n.intlSegmenter){if(n.intlSegmenter.resolvedOptions().granularity!="word")throw new Error('The segmenter passed must have a granularity of "word"');e=Array.from(n.intlSegmenter.segment(t),function(o){return o.segment})}else e=t.match(pe)||[];var r=[],a=null;return e.forEach(function(o){/\s/.test(o)?a==null?r.push(o):r.push(r.pop()+o):/\s/.test(a)?r[r.length-1]==a?r.push(r.pop()+o):r.push(a+o):r.push(o),a=o}),r},M.join=function(t){return t.map(function(e,n){return n==0?e:e.replace(/^\s+/,"")}).join("")},M.postProcess=function(t,e){if(!t||e.oneChangePerToken)return t;var n=null,r=null,a=null;return t.forEach(function(o){o.added?r=o:o.removed?a=o:((r||a)&&te(n,a,r,o),n=o,r=null,a=null)}),(r||a)&&te(n,a,r,null),t},new S().tokenize=function(t){var e=new RegExp("(\\r?\\n)|[".concat(_,"]+|[^\\S\\n\\r]+|[^").concat(_,"]"),"ug");return t.match(e)||[]};var I=new S;function ne(t,e,n){return I.diff(t,e,n)}function re(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),n.push.apply(n,r)}return n}function $(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?re(Object(n),!0).forEach(function(r){he(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):re(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function fe(t){var e=function(n,r){if(typeof n!="object"||!n)return n;var a=n[Symbol.toPrimitive];if(a!==void 0){var o=a.call(n,r);if(typeof o!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(r==="string"?String:Number)(n)}(t,"string");return typeof e=="symbol"?e:e+""}function W(t){return W=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},W(t)}function he(t,e,n){return(e=fe(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function D(t){return function(e){if(Array.isArray(e))return R(e)}(t)||function(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}(t)||function(e,n){if(e){if(typeof e=="string")return R(e,n);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return R(e,n)}}(t)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function R(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}I.tokenize=function(t,e){e.stripTrailingCr&&(t=t.replace(/\r\n/g,`
`));var n=[],r=t.split(/(\n|\r\n)/);r[r.length-1]||r.pop();for(var a=0;a<r.length;a++){var o=r[a];a%2&&!e.newlineIsToken?n[n.length-1]+=o:n.push(o)}return n},I.equals=function(t,e,n){return n.ignoreWhitespace?(n.newlineIsToken&&t.includes(`
`)||(t=t.trim()),n.newlineIsToken&&e.includes(`
`)||(e=e.trim())):n.ignoreNewlineAtEof&&!n.newlineIsToken&&(t.endsWith(`
`)&&(t=t.slice(0,-1)),e.endsWith(`
`)&&(e=e.slice(0,-1))),S.prototype.equals.call(this,t,e,n)},new S().tokenize=function(t){return t.split(/(\S.+?[.!?])(?=\s+|$)/)},new S().tokenize=function(t){return t.split(/([{}:;,]|\s+)/)};var x=new S;function U(t,e,n,r,a){var o,s;for(e=e||[],n=n||[],r&&(t=r(a,t)),o=0;o<e.length;o+=1)if(e[o]===t)return n[o];if(Object.prototype.toString.call(t)==="[object Array]"){for(e.push(t),s=new Array(t.length),n.push(s),o=0;o<t.length;o+=1)s[o]=U(t[o],e,n,r,a);return e.pop(),n.pop(),s}if(t&&t.toJSON&&(t=t.toJSON()),W(t)==="object"&&t!==null){e.push(t),s={},n.push(s);var i,u=[];for(i in t)Object.prototype.hasOwnProperty.call(t,i)&&u.push(i);for(u.sort(),o=0;o<u.length;o+=1)s[i=u[o]]=U(t[i],e,n,r,i);e.pop(),n.pop()}else s=t;return s}x.useLongestToken=!0,x.tokenize=I.tokenize,x.castInput=function(t,e){var n=e.undefinedReplacement,r=e.stringifyReplacer,a=r===void 0?function(o,s){return s===void 0?n:s}:r;return typeof t=="string"?t:JSON.stringify(U(t,null,null,a),a,"  ")},x.equals=function(t,e,n){return S.prototype.equals.call(x,t.replace(/,([\r\n])/g,"$1"),e.replace(/,([\r\n])/g,"$1"),n)};var z=new S;function me(t){var e=t.split(/\n/),n=[],r=0;function a(){var i={};for(n.push(i);r<e.length;){var u=e[r];if(/^(\-\-\-|\+\+\+|@@)\s/.test(u))break;var l=/^(?:Index:|diff(?: -r \w+)+)\s+(.+?)\s*$/.exec(u);l&&(i.index=l[1]),r++}for(o(i),o(i),i.hunks=[];r<e.length;){var c=e[r];if(/^(Index:\s|diff\s|\-\-\-\s|\+\+\+\s|===================================================================)/.test(c))break;if(/^@@/.test(c))i.hunks.push(s());else{if(c)throw new Error("Unknown line "+(r+1)+" "+JSON.stringify(c));r++}}}function o(i){var u=/^(---|\+\+\+)\s+(.*)\r?$/.exec(e[r]);if(u){var l=u[1]==="---"?"old":"new",c=u[2].split("	",2),p=c[0].replace(/\\\\/g,"\\");/^".*"$/.test(p)&&(p=p.substr(1,p.length-2)),i[l+"FileName"]=p,i[l+"Header"]=(c[1]||"").trim(),r++}}function s(){var i=r,u=e[r++].split(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/),l={oldStart:+u[1],oldLines:u[2]===void 0?1:+u[2],newStart:+u[3],newLines:u[4]===void 0?1:+u[4],lines:[]};l.oldLines===0&&(l.oldStart+=1),l.newLines===0&&(l.newStart+=1);for(var c=0,p=0;r<e.length&&(p<l.oldLines||c<l.newLines||(d=e[r])!==null&&d!==void 0&&d.startsWith("\\"));r++){var d,f=e[r].length==0&&r!=e.length-1?" ":e[r][0];if(f!=="+"&&f!=="-"&&f!==" "&&f!=="\\")throw new Error("Hunk at line ".concat(i+1," contained invalid line ").concat(e[r]));l.lines.push(e[r]),f==="+"?c++:f==="-"?p++:f===" "&&(c++,p++)}if(c||l.newLines!==1||(l.newLines=0),p||l.oldLines!==1||(l.oldLines=0),c!==l.newLines)throw new Error("Added line count did not match for hunk at line "+(i+1));if(p!==l.oldLines)throw new Error("Removed line count did not match for hunk at line "+(i+1));return l}for(;r<e.length;)a();return n}function oe(t,e,n,r,a,o,s){if(s||(s={}),typeof s=="function"&&(s={callback:s}),s.context===void 0&&(s.context=4),s.newlineIsToken)throw new Error("newlineIsToken may not be used with patch-generation functions, only with diffing functions");if(!s.callback)return u(ne(n,r,s));var i=s.callback;function u(l){if(l){l.push({value:"",lines:[]});for(var c=[],p=0,d=0,f=[],g=1,m=1,F=function(){var w=l[C],v=w.lines||function(A){var ie=A.endsWith(`
`),L=A.split(`
`).map(function(se){return se+`
`});return ie?L.pop():L.push(L.pop().slice(0,-1)),L}(w.value);if(w.lines=v,w.added||w.removed){var O;if(!p){var B=l[C-1];p=g,d=m,B&&(f=s.context>0?k(B.lines.slice(-s.context)):[],p-=f.length,d-=f.length)}(O=f).push.apply(O,D(v.map(function(A){return(w.added?"+":"-")+A}))),w.added?m+=v.length:g+=v.length}else{if(p)if(v.length<=2*s.context&&C<l.length-2){var G;(G=f).push.apply(G,D(k(v)))}else{var K,q=Math.min(v.length,s.context);(K=f).push.apply(K,D(k(v.slice(0,q))));var ae={oldStart:p,oldLines:g-p+q,newStart:d,newLines:m-d+q,lines:f};c.push(ae),p=0,d=0,f=[]}g+=v.length,m+=v.length}},C=0;C<l.length;C++)F();for(var E=0,h=c;E<h.length;E++)for(var b=h[E],y=0;y<b.lines.length;y++)b.lines[y].endsWith(`
`)?b.lines[y]=b.lines[y].slice(0,-1):(b.lines.splice(y+1,0,"\\ No newline at end of file"),y++);return{oldFileName:t,newFileName:e,oldHeader:a,newHeader:o,hunks:c}}function k(w){return w.map(function(v){return" "+v})}}ne(n,r,$($({},s),{},{callback:function(l){var c=u(l);i(c)}}))}function Z(t){if(Array.isArray(t))return t.map(Z).join(`
`);var e=[];t.oldFileName==t.newFileName&&e.push("Index: "+t.oldFileName),e.push("==================================================================="),e.push("--- "+t.oldFileName+(t.oldHeader===void 0?"":"	"+t.oldHeader)),e.push("+++ "+t.newFileName+(t.newHeader===void 0?"":"	"+t.newHeader));for(var n=0;n<t.hunks.length;n++){var r=t.hunks[n];r.oldLines===0&&(r.oldStart-=1),r.newLines===0&&(r.newStart-=1),e.push("@@ -"+r.oldStart+","+r.oldLines+" +"+r.newStart+","+r.newLines+" @@"),e.push.apply(e,r.lines)}return e.join(`
`)+`
`}function ye(t,e,n,r,a,o,s){var i;if(typeof s=="function"&&(s={callback:s}),(i=s)===null||i===void 0||!i.callback){var u=oe(t,e,n,r,a,o,s);return u?Z(u):void 0}var l=s.callback;oe(t,e,n,r,a,o,$($({},s),{},{callback:function(c){c?l(Z(c)):l()}}))}z.tokenize=function(t){return t.slice()},z.join=z.removeEmpty=function(t){return t};export{ce as R,ye as c,me as p};
